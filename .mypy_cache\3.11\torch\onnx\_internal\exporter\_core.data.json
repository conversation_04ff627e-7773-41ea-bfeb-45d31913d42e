{".class": "MypyFile", "_fullname": "torch.onnx._internal.exporter._core", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TorchTensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.exporter._core.TorchTensor", "name": "TorchTensor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "torch.onnx._internal.exporter._core.TorchTensor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.exporter._core", "mro": ["torch.onnx._internal.exporter._core.TorchTensor", "builtins.object"], "names": {".class": "SymbolTable", "__array__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "dtype", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core.TorchTensor.__array__", "name": "__array__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "dtype", "copy"], "arg_types": ["torch.onnx._internal.exporter._core.TorchTensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__array__ of TorchTensor", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tensor", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core.TorchTensor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tensor", "name"], "arg_types": ["torch.onnx._internal.exporter._core.TorchTensor", "torch._tensor.Tensor", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TorchTensor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core.TorchTensor.numpy", "name": "numpy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.exporter._core.TorchTensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "numpy of TorchTensor", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._core.TorchTensor.raw", "name": "raw", "type": "torch._tensor.Tensor"}}, "tobytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core.TorchTensor.tobytes", "name": "tobytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.exporter._core.TorchTensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tobytes of TorchTensor", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.exporter._core.TorchTensor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.exporter._core.TorchTensor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BLUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._core._BLUE", "name": "_BLUE", "type": "builtins.str"}}, "_END": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._core._END", "name": "_END", "type": "builtins.str"}}, "_STEP_ONE_ERROR_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._core._STEP_ONE_ERROR_MESSAGE", "name": "_STEP_ONE_ERROR_MESSAGE", "type": "builtins.str"}}, "_STEP_THREE_ERROR_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._core._STEP_THREE_ERROR_MESSAGE", "name": "_STEP_THREE_ERROR_MESSAGE", "type": "builtins.str"}}, "_STEP_TWO_ERROR_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._core._STEP_TWO_ERROR_MESSAGE", "name": "_STEP_TWO_ERROR_MESSAGE", "type": "builtins.str"}}, "_TORCH_DTYPE_TO_ONNX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._core._TORCH_DTYPE_TO_ONNX", "name": "_TORCH_DTYPE_TO_ONNX", "type": {".class": "Instance", "args": ["torch._C.dtype", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._core.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._core.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._core.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._core.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._core.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._core.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_analysis": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._analysis", "kind": "Gdef"}, "_building": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._building", "kind": "Gdef"}, "_capture_strategies": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._capture_strategies", "kind": "Gdef"}, "_constants": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._constants", "kind": "Gdef"}, "_convert_fx_arg_to_onnx_arg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["arg", "node_name_to_values", "node_name_to_local_functions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._convert_fx_arg_to_onnx_arg", "name": "_convert_fx_arg_to_onnx_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["arg", "node_name_to_values", "node_name_to_local_functions"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_fx_arg_to_onnx_arg", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dispatching": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._dispatching", "kind": "Gdef"}, "_errors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._errors", "kind": "Gdef"}, "_exported_program_to_onnx_program": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["exported_program", "registry", "lower"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._exported_program_to_onnx_program", "name": "_exported_program_to_onnx_program", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["exported_program", "registry", "lower"], "arg_types": ["torch.export.exported_program.ExportedProgram", "torch.onnx._internal.exporter._registration.ONNXRegistry", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "at_conversion"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exported_program_to_onnx_program", "ret_type": "torch.onnx._internal.exporter._onnx_program.ONNXProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flags": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._flags", "kind": "Gdef"}, "_format_exception": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._format_exception", "name": "_format_exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["e"], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_exception", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_exceptions_for_all_strategies": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._format_exceptions_for_all_strategies", "name": "_format_exceptions_for_all_strategies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["results"], "arg_types": [{".class": "Instance", "args": ["torch.onnx._internal.exporter._capture_strategies.Result"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_exceptions_for_all_strategies", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fx_passes": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._fx_passes", "kind": "Gdef"}, "_get_inputs_and_attributes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._get_inputs_and_attributes", "name": "_get_inputs_and_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_inputs_and_attributes", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_node_namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._get_node_namespace", "name": "_get_node_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_node_namespace", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_onnxscript_opset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["opset_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._get_onnxscript_opset", "name": "_get_onnxscript_opset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["opset_version"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_onnxscript_opset", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.onnxscript", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_qualified_module_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._get_qualified_module_name", "name": "_get_qualified_module_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_qualified_module_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_scope_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["scoped_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._get_scope_name", "name": "_get_scope_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["scoped_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_scope_name", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_call_function_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["graph_like", "node", "node_name_to_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._handle_call_function_node", "name": "_handle_call_function_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["graph_like", "node", "node_name_to_values"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "torch.fx.node.Node", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_call_function_node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_call_function_node_with_lowering": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 3, 3, 3], "arg_names": ["model", "node", "node_name_to_values", "graph_like", "constant_farm", "registry", "opset", "node_name_to_local_functions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._handle_call_function_node_with_lowering", "name": "_handle_call_function_node_with_lowering", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 3, 3], "arg_names": ["model", "node", "node_name_to_values", "graph_like", "constant_farm", "registry", "opset", "node_name_to_local_functions"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, "torch.fx.node.Node", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.onnx._internal.exporter._registration.ONNXRegistry", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_call_function_node_with_lowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_get_attr_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["node", "owned_graphs", "node_name_to_local_functions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._handle_get_attr_node", "name": "_handle_get_attr_node", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["node", "owned_graphs", "node_name_to_local_functions"], "arg_types": ["torch.fx.node.Node", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_get_attr_node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_getitem_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node", "node_name_to_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._handle_getitem_node", "name": "_handle_getitem_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node", "node_name_to_values"], "arg_types": ["torch.fx.node.Node", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_getitem_node", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_output_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["node", "node_name_to_values", "graph_like"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._handle_output_node", "name": "_handle_output_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["node", "node_name_to_values", "graph_like"], "arg_types": ["torch.fx.node.Node", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_output_node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_placeholder_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["node", "node_name_to_values", "graph_like", "lower", "opset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._handle_placeholder_node", "name": "_handle_placeholder_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["node", "node_name_to_values", "graph_like", "lower", "opset"], "arg_types": ["torch.fx.node.Node", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "builtins.str", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.onnxscript", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_placeholder_node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ir_passes": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._ir_passes", "kind": "Gdef"}, "_is_onnx_op": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._is_onnx_op", "name": "_is_onnx_op", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_onnx_op", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_maybe_start_profiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["should_profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._maybe_start_profiler", "name": "_maybe_start_profiler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["should_profile"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_maybe_start_profiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_maybe_stop_profiler_and_get_result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["profiler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._maybe_stop_profiler_and_get_result", "name": "_maybe_stop_profiler_and_get_result", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["profiler"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_maybe_stop_profiler_and_get_result", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_onnx_program": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._onnx_program", "kind": "Gdef"}, "_parse_onnx_op": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._parse_onnx_op", "name": "_parse_onnx_op", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_onnx_op", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_exported_program_for_export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["exported_program", "registry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._prepare_exported_program_for_export", "name": "_prepare_exported_program_for_export", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["exported_program", "registry"], "arg_types": ["torch.export.exported_program.ExportedProgram", "torch.onnx._internal.exporter._registration.ONNXRegistry"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_exported_program_for_export", "ret_type": "torch.export.exported_program.ExportedProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_registration": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._registration", "kind": "Gdef"}, "_reporting": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._reporting", "kind": "Gdef"}, "_set_node_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fx_node", "ir_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._set_node_metadata", "name": "_set_node_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fx_node", "ir_node"], "arg_types": ["torch.fx.node.Node", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_node_metadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_shape_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["value", "meta_val", "complex_to_float"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._set_shape_type", "name": "_set_shape_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "meta_val", "complex_to_float"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["torch._tensor.Tensor", "torch.SymBool", "torch.SymInt", "torch.SymFloat", {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_shape_type", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_shape_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["values", "meta_vals", "complex_to_float"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._set_shape_types", "name": "_set_shape_types", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["values", "meta_vals", "complex_to_float"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_shape_types", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_summarize_exception_stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._summarize_exception_stack", "name": "_summarize_exception_stack", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["e"], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_summarize_exception_stack", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tensors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._tensors", "kind": "Gdef"}, "_translate_fx_graph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3], "arg_names": ["fx_graph", "model", "graph_like", "owned_graphs", "lower", "registry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._translate_fx_graph", "name": "_translate_fx_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3, 3], "arg_names": ["fx_graph", "model", "graph_like", "owned_graphs", "lower", "registry"], "arg_types": ["torch.fx.graph.Graph", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "at_conversion"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": false}, "torch.onnx._internal.exporter._registration.ONNXRegistry"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_translate_fx_graph", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_casting": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._type_casting", "kind": "Gdef"}, "_verbose_printer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core._verbose_printer", "name": "_verbose_printer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["verbose"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verbose_printer", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_verification": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._verification", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "ctypes": {".class": "SymbolTableNode", "cross_ref": "ctypes", "kind": "Gdef"}, "current_tracer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._core.current_tracer", "name": "current_tracer", "type": {".class": "UnionType", "items": ["torch.onnx._internal.exporter._building.OpRecorder", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model", "args", "kwargs", "registry", "dynamic_shapes", "input_names", "output_names", "report", "verify", "profile", "dump_exported_program", "artifacts_dir", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx._internal.exporter._core.export", "name": "export", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model", "args", "kwargs", "registry", "dynamic_shapes", "input_names", "output_names", "report", "verify", "profile", "dump_exported_program", "artifacts_dir", "verbose"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", "torch.export.exported_program.ExportedProgram", "torch.fx.graph_module.GraphModule", "torch.jit._script.ScriptModule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["torch.onnx._internal.exporter._registration.ONNXRegistry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export", "ret_type": "torch.onnx._internal.exporter._onnx_program.ONNXProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._core.export", "name": "export", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model", "args", "kwargs", "registry", "dynamic_shapes", "input_names", "output_names", "report", "verify", "profile", "dump_exported_program", "artifacts_dir", "verbose"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", "torch.export.exported_program.ExportedProgram", "torch.fx.graph_module.GraphModule", "torch.jit._script.ScriptModule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["torch.onnx._internal.exporter._registration.ONNXRegistry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export", "ret_type": "torch.onnx._internal.exporter._onnx_program.ONNXProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exported_program_to_ir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["exported_program", "registry", "lower"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core.exported_program_to_ir", "name": "exported_program_to_ir", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["exported_program", "registry", "lower"], "arg_types": ["torch.export.exported_program.ExportedProgram", {".class": "UnionType", "items": ["torch.onnx._internal.exporter._registration.ONNXRegistry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "at_conversion"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exported_program_to_ir", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph_signature": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._core.ir", "name": "ir", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}}}, "ir_convenience": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._core.ir_convenience", "name": "ir_convenience", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir_convenience", "source_any": null, "type_of_any": 3}}}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._core.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "npt": {".class": "SymbolTableNode", "cross_ref": "numpy.typing", "kind": "Gdef"}, "onnxscript": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._core.onnxscript", "name": "onnxscript", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.onnxscript", "source_any": null, "type_of_any": 3}}}, "onnxscript_apis": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal._lazy_import.onnxscript_apis", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pathlib": {".class": "SymbolTableNode", "cross_ref": "pathlib", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "torch_dtype_to_onnx_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._core.torch_dtype_to_onnx_dtype", "name": "torch_dtype_to_onnx_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": ["torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "torch_dtype_to_onnx_dtype", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._core.ir", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_core.py"}