{"data_mtime": 1755649448, "dep_lines": [8, 10, 18, 28, 8, 26, 4, 8, 9, 2, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 5, 20, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["torch.distributed.fsdp._traversal_utils", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp._runtime_utils", "torch.distributed.fsdp._flat_param", "torch.distributed.fsdp", "torch.distributed.utils", "collections.abc", "torch.distributed", "torch.nn", "contextlib", "warnings", "typing", "torch", "builtins", "collections", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "_frozen_importlib", "abc", "torch.autograd", "torch.autograd.grad_mode", "torch.distributed._composable_state", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "torch.utils._contextlib"], "hash": "f2609717f688830d80c18d80cd46bedc4741d482", "id": "torch.distributed.fsdp._unshard_param_utils", "ignore_all": true, "interface_hash": "ce7101ab948d058683536711ed231563c61ef877", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_unshard_param_utils.py", "plugin_data": null, "size": 11861, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}