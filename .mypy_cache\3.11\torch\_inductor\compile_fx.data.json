{".class": "MypyFile", "_fullname": "torch._inductor.compile_fx", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "AbstractContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib.AbstractContextManager", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BoxedBool": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.BoxedBool", "kind": "Gdef"}, "BoxedDeviceIndex": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.BoxedDeviceIndex", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CompileEventLogger": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.CompileEventLogger", "kind": "Gdef"}, "CompiledAOTI": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.CompiledAOTI", "kind": "Gdef"}, "CompiledFxGraph": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.CompiledFxGraph", "kind": "Gdef"}, "CompiledFxGraphConstantsWithGm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.CompiledFxGraphConstantsWithGm", "kind": "Gdef"}, "DebugContext": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.debug.DebugContext", "kind": "Gdef"}, "ExternKernelNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.ExternKernelNode", "kind": "Gdef"}, "FQN": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.schemas.FQN", "kind": "Gdef"}, "FakeScriptObject": {".class": "SymbolTableNode", "cross_ref": "torch._library.fake_class_registry.FakeScriptObject", "kind": "Gdef"}, "FakeTensorProp": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.fake_tensor_prop.FakeTensorProp", "kind": "Gdef"}, "FxCompile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["codegen_and_compile", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.compile_fx.FxCompile", "name": "FxCompile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch._inductor.compile_fx.FxCompile", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._inductor.compile_fx", "mro": ["torch._inductor.compile_fx.FxCompile", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_compile_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.FxCompile._compile_stats", "name": "_compile_stats", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "torch._inductor.compile_fx.FxCompile"}, "torch._inductor.compile_fx._FxCompileStat"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_reset_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.compile_fx.FxCompile._reset_stats", "name": "_reset_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.compile_fx.FxCompile"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reset_stats of FxCompile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.compile_fx.FxCompile._reset_stats", "name": "_reset_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.compile_fx.FxCompile"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reset_stats of FxCompile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "codegen_and_compile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gm", "example_inputs", "inputs_to_check", "graph_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.compile_fx.FxCompile.codegen_and_compile", "name": "codegen_and_compile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gm", "example_inputs", "inputs_to_check", "graph_kwargs"], "arg_types": ["torch._inductor.compile_fx.FxCompile", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.compile_fx._CompileFxKwargs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_and_compile of FxCompile", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.compile_fx.FxCompile.codegen_and_compile", "name": "codegen_and_compile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gm", "example_inputs", "inputs_to_check", "graph_kwargs"], "arg_types": ["torch._inductor.compile_fx.FxCompile", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.compile_fx._CompileFxKwargs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_and_compile of FxCompile", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx.FxCompile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.compile_fx.FxCompile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FxCompileMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.compile_fx.FxCompileMode", "name": "FxCompileMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch._inductor.compile_fx.FxCompileMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch._inductor.compile_fx", "mro": ["torch._inductor.compile_fx.FxCompileMode", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NORMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.FxCompileMode.NORMAL", "name": "NORMAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "SERIALIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.FxCompileMode.SERIALIZE", "name": "SERIALIZE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "SUBPROCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.FxCompileMode.SUBPROCESS", "name": "SUBPROCESS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx.FxCompileMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.compile_fx.FxCompileMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FxGraphCache": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.FxGraphCache", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "GraphInputName": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.schemas.GraphInputName", "kind": "Gdef"}, "GraphLowering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.graph.GraphLowering", "kind": "Gdef"}, "GraphModule": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph_module.GraphModule", "kind": "Gdef"}, "GraphSignature": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.schemas.GraphSignature", "kind": "Gdef"}, "IRNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.IRNode", "kind": "Gdef"}, "InductorError": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.exc.InductorError", "kind": "Gdef"}, "InputType": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.InputType", "kind": "Gdef"}, "Never": {".class": "SymbolTableNode", "cross_ref": "typing.Never", "kind": "Gdef"}, "OpOverload": {".class": "SymbolTableNode", "cross_ref": "torch._ops.OpOverload", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "OutputCode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.OutputCode", "kind": "Gdef"}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef"}, "PlaceholderInfo": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.PlaceholderInfo", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SerializableAOTDispatchCompiler": {".class": "SymbolTableNode", "cross_ref": "torch._functorch.aot_autograd.SerializableAOTDispatchCompiler", "kind": "Gdef"}, "ShortenTraceback": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.exc.ShortenTraceback", "kind": "Gdef"}, "SkipFrame": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.exc.SkipFrame", "kind": "Gdef"}, "SymExprPrinter": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.SymExprPrinter", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TritonBundler": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.triton_bundler.TritonBundler", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "Weights": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive._package_weights.Weights", "kind": "Gdef"}, "_CompileFxCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.compile_fx._CompileFxCallable", "name": "_CompileFxCallable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "torch._inductor.compile_fx._CompileFxCallable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._inductor.compile_fx", "mro": ["torch._inductor.compile_fx._CompileFxCallable", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "gm", "example_inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "torch._inductor.compile_fx._CompileFxCallable.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "gm", "example_inputs", "kwargs"], "arg_types": ["torch._inductor.compile_fx._CompileFxCallable", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypedDictType", "fallback": "torch._inductor.compile_fx._CompileFxKwargs", "items": [["cudagraphs", {".class": "UnionType", "items": ["torch._inductor.utils.BoxedBool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["static_input_idxs", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["is_backward", "builtins.bool"], ["graph_id", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["cpp_wrapper", "builtins.bool"], ["aot_mode", "builtins.bool"], ["is_inference", "builtins.bool"], ["layout_opt", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["extern_node_serializer", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["boxed_forward_device_index", {".class": "UnionType", "items": ["torch._inductor.cudagraph_utils.BoxedDeviceIndex", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _CompileFxCallable", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx._CompileFxCallable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.compile_fx._CompileFxCallable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CompileFxKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.compile_fx._CompileFxKwargs", "name": "_CompileFxKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._CompileFxKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.compile_fx", "mro": ["torch._inductor.compile_fx._CompileFxKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["cudagraphs", {".class": "UnionType", "items": ["torch._inductor.utils.BoxedBool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["static_input_idxs", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["is_backward", "builtins.bool"], ["graph_id", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["cpp_wrapper", "builtins.bool"], ["aot_mode", "builtins.bool"], ["is_inference", "builtins.bool"], ["layout_opt", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["extern_node_serializer", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["boxed_forward_device_index", {".class": "UnionType", "items": ["torch._inductor.cudagraph_utils.BoxedDeviceIndex", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}}}, "_FxCompileStat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.compile_fx._FxCompileStat", "name": "_FxCompileStat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._FxCompileStat", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.compile_fx", "mro": ["torch._inductor.compile_fx._FxCompileStat", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._FxCompileStat.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.compile_fx._FxCompileStat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of _FxCompileStat", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_and_compile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.compile_fx._FxCompileStat.codegen_and_compile", "name": "codegen_and_compile", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx._FxCompileStat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.compile_fx._FxCompileStat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_InProcessFxCompile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.compile_fx.FxCompile"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.compile_fx._InProcessFxCompile", "name": "_InProcessFxCompile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._InProcessFxCompile", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._inductor.compile_fx", "mro": ["torch._inductor.compile_fx._InProcessFxCompile", "torch._inductor.compile_fx.FxCompile", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "codegen_and_compile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gm", "example_inputs", "inputs_to_check", "graph_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.compile_fx._InProcessFxCompile.codegen_and_compile", "name": "codegen_and_compile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gm", "example_inputs", "inputs_to_check", "graph_kwargs"], "arg_types": ["torch._inductor.compile_fx._InProcessFxCompile", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.compile_fx._CompileFxKwargs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_and_compile of _InProcessFxCompile", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.compile_fx._InProcessFxCompile.codegen_and_compile", "name": "codegen_and_compile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gm", "example_inputs", "inputs_to_check", "graph_kwargs"], "arg_types": ["torch._inductor.compile_fx._InProcessFxCompile", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.compile_fx._CompileFxKwargs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_and_compile of _InProcessFxCompile", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx._InProcessFxCompile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.compile_fx._InProcessFxCompile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx._P", "name": "_P", "upper_bound": "builtins.object", "variance": 0}}, "_PyTreeCodeGen": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph._PyTreeCodeGen", "kind": "Gdef"}, "_StrideExprStr": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code._StrideExprStr", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_WaitCounter": {".class": "SymbolTableNode", "cross_ref": "torch._C._monitor._Wait<PERSON><PERSON>nter", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.compile_fx.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.compile_fx.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.compile_fx.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.compile_fx.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.compile_fx.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.compile_fx.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_aoti_flatten_inputs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["gm", "args", "kwargs", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._aoti_flatten_inputs", "name": "_aoti_flatten_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["gm", "args", "kwargs", "options"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_aoti_flatten_inputs", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_triton_bf16_support": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._check_triton_bf16_support", "name": "_check_triton_bf16_support", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_triton_bf16_support", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compile_fx_inner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["gm", "example_inputs", "graph_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.compile_fx._compile_fx_inner", "name": "_compile_fx_inner", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["gm", "example_inputs", "graph_kwargs"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypedDictType", "fallback": "torch._inductor.compile_fx._CompileFxKwargs", "items": [["cudagraphs", {".class": "UnionType", "items": ["torch._inductor.utils.BoxedBool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["static_input_idxs", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["is_backward", "builtins.bool"], ["graph_id", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["cpp_wrapper", "builtins.bool"], ["aot_mode", "builtins.bool"], ["is_inference", "builtins.bool"], ["layout_opt", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["extern_node_serializer", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["boxed_forward_device_index", {".class": "UnionType", "items": ["torch._inductor.cudagraph_utils.BoxedDeviceIndex", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compile_fx_inner", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.compile_fx._compile_fx_inner", "name": "_compile_fx_inner", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["gm", "example_inputs", "cudagraphs", "static_input_idxs", "is_backward", "graph_id", "cpp_wrapper", "aot_mode", "is_inference", "layout_opt", "extern_node_serializer", "boxed_forward_device_index"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["torch._inductor.utils.BoxedBool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.cudagraph_utils.BoxedDeviceIndex", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compile_fx_inner", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_fx_compile_mode_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._fx_compile_mode_default", "name": "_fx_compile_mode_default", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fx_compile_mode_default", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._inductor.compile_fx.FxCompileMode", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_subgraph_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["gm", "skip_invoke_subgraph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._get_subgraph_names", "name": "_get_subgraph_names", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["gm", "skip_invoke_subgraph"], "arg_types": ["torch.fx.graph_module.GraphModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_subgraph_names", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_graph_counter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx._graph_counter", "name": "_graph_counter", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "_recursive_joint_graph_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["gm", "skip_invoke_subgraph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._recursive_joint_graph_passes", "name": "_recursive_joint_graph_passes", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["gm", "skip_invoke_subgraph"], "arg_types": ["torch.fx.graph_module.GraphModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_recursive_joint_graph_passes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_recursive_post_grad_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["gm", "is_inference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._recursive_post_grad_passes", "name": "_recursive_post_grad_passes", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["gm", "is_inference"], "arg_types": ["torch.fx.graph_module.GraphModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_recursive_post_grad_passes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_recursive_pre_grad_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "example_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._recursive_pre_grad_passes", "name": "_recursive_pre_grad_passes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["gm", "example_inputs"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_recursive_pre_grad_passes", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_recursive_record_original_output_strides": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._recursive_record_original_output_strides", "name": "_recursive_record_original_output_strides", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_recursive_record_original_output_strides", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_recursive_record_user_visible_output_idxs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._recursive_record_user_visible_output_idxs", "name": "_recursive_record_user_visible_output_idxs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_recursive_record_user_visible_output_idxs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_name_collision": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mod", "gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._resolve_name_collision", "name": "_resolve_name_collision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mod", "gm"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_name_collision", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_step_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.compile_fx._step_logger", "name": "_step_logger", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_step_logger", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.compile_fx._step_logger", "name": "_step_logger", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_unlift_graph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mod", "gm", "graph_signature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx._unlift_graph", "name": "_unlift_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mod", "gm", "graph_signature"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.fx.graph_module.GraphModule", "torch._functorch._aot_autograd.schemas.GraphSignature"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unlift_graph", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_use_lazy_graph_module": {".class": "SymbolTableNode", "cross_ref": "torch.fx._lazy_graph_module._use_lazy_graph_module", "kind": "Gdef"}, "_warn_tf32_disabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.compile_fx._warn_tf32_disabled", "name": "_warn_tf32_disabled", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_tf32_disabled", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.compile_fx._warn_tf32_disabled", "name": "_warn_tf32_disabled", "type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "align_inputs_from_check_idxs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.align_inputs_from_check_idxs", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "aot_autograd": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.backends.common.aot_autograd", "kind": "Gdef"}, "aot_export_module": {".class": "SymbolTableNode", "cross_ref": "torch._functorch.aot_autograd.aot_export_module", "kind": "Gdef"}, "attrgetter": {".class": "SymbolTableNode", "cross_ref": "operator.attrgetter", "kind": "Gdef"}, "cache_dir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.cache_dir_utils.cache_dir", "kind": "Gdef"}, "chromium_event_timed": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.chromium_event_timed", "kind": "Gdef"}, "clone_preserve_strides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.clone_preserve_strides", "kind": "Gdef"}, "code_hash": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.code_hash", "kind": "Gdef"}, "compile_fx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["model_", "example_inputs_", "inner_compile", "config_patches", "decompositions", "ignore_shape_env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.compile_fx", "name": "compile_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["model_", "example_inputs_", "inner_compile", "config_patches", "decompositions", "ignore_shape_env"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_fx", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.export.pt2_archive._package_weights.Weights"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_fx_aot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["model_", "example_inputs_", "inner_compile", "config_patches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.compile_fx_aot", "name": "compile_fx_aot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["model_", "example_inputs_", "inner_compile", "config_patches"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.compile_fx._CompileFxCallable", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_fx_aot", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "torch.export.pt2_archive._package_weights.Weights"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_fx_inner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["gm", "example_inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.compile_fx_inner", "name": "compile_fx_inner", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["gm", "example_inputs", "kwargs"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypedDictType", "fallback": "torch._inductor.compile_fx._CompileFxKwargs", "items": [["cudagraphs", {".class": "UnionType", "items": ["torch._inductor.utils.BoxedBool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["static_input_idxs", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["is_backward", "builtins.bool"], ["graph_id", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["cpp_wrapper", "builtins.bool"], ["aot_mode", "builtins.bool"], ["is_inference", "builtins.bool"], ["layout_opt", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["extern_node_serializer", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["boxed_forward_device_index", {".class": "UnionType", "items": ["torch._inductor.cudagraph_utils.BoxedDeviceIndex", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_fx_inner", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "compile_time_strobelight_meta": {".class": "SymbolTableNode", "cross_ref": "torch._utils_internal.compile_time_strobelight_meta", "kind": "Gdef"}, "compiled_autograd": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.compiled_autograd", "kind": "Gdef"}, "complex_memory_overlap": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.complex_memory_overlap", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "copy_misaligned_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.copy_misaligned_inputs", "kind": "Gdef"}, "count": {".class": "SymbolTableNode", "cross_ref": "itertools.count", "kind": "Gdef"}, "count_tangents": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.count_tangents", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "cudagraphify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["model", "static_input_idxs", "device_index", "stack_traces", "is_backward", "is_inference", "constants", "placeholders", "mutated_input_idxs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.cudagraphify", "name": "cudagraphify", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["model", "static_input_idxs", "device_index", "stack_traces", "is_backward", "is_inference", "constants", "placeholders", "mutated_input_idxs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.PlaceholderInfo"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cudagraphify", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cudagraphify_impl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["model", "inputs", "static_input_idxs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.cudagraphify_impl", "name": "cudagraphify_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["model", "inputs", "static_input_idxs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cudagraphify_impl", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "currentframe": {".class": "SymbolTableNode", "cross_ref": "inspect.currentframe", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "detect_fake_mode": {".class": "SymbolTableNode", "cross_ref": "torch._guards.detect_fake_mode", "kind": "Gdef"}, "dynamo_config": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.config", "kind": "Gdef"}, "dynamo_logging": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.logging", "kind": "Gdef"}, "dynamo_timed": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.dynamo_timed", "kind": "Gdef"}, "dynamo_utils": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils", "kind": "Gdef"}, "enable_python_dispatcher": {".class": "SymbolTableNode", "cross_ref": "torch._dispatch.python.enable_python_dispatcher", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "fake_tensor_prop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["gm", "example_inputs", "force_allow_non_fake_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.fake_tensor_prop", "name": "fake_tensor_prop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["gm", "example_inputs", "force_allow_non_fake_inputs"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fake_tensor_prop", "ret_type": "torch._subclasses.fake_tensor.FakeTensorMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flatten_graph_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.flatten_graph_inputs", "kind": "Gdef"}, "format_default_skip_message": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.format_default_skip_message", "kind": "Gdef"}, "free_unbacked_symbols": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.free_unbacked_symbols", "kind": "Gdef"}, "fresh_cache": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.fresh_cache", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "functorch_config": {".class": "SymbolTableNode", "cross_ref": "torch._functorch.config", "kind": "Gdef"}, "fw_compiler_freezing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["aot_autograd_model", "aot_example_inputs", "dynamo_model", "num_example_inputs", "inner_compile", "cudagraphs", "graph_id", "forward_device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.fw_compiler_freezing", "name": "fw_compiler_freezing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["aot_autograd_model", "aot_example_inputs", "dynamo_model", "num_example_inputs", "inner_compile", "cudagraphs", "graph_id", "forward_device"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "torch.fx.graph_module.GraphModule", "builtins.int", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch._inductor.utils.BoxedBool", "builtins.int", "torch._inductor.cudagraph_utils.BoxedDeviceIndex"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fw_compiler_freezing", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fx": {".class": "SymbolTableNode", "cross_ref": "torch.fx", "kind": "Gdef"}, "fx_codegen_and_compile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["gm", "example_inputs", "inputs_to_check", "graph_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.fx_codegen_and_compile", "name": "fx_codegen_and_compile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["gm", "example_inputs", "inputs_to_check", "graph_kwargs"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypedDictType", "fallback": "torch._inductor.compile_fx._CompileFxKwargs", "items": [["cudagraphs", {".class": "UnionType", "items": ["torch._inductor.utils.BoxedBool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["static_input_idxs", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["is_backward", "builtins.bool"], ["graph_id", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["cpp_wrapper", "builtins.bool"], ["aot_mode", "builtins.bool"], ["is_inference", "builtins.bool"], ["layout_opt", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["extern_node_serializer", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["boxed_forward_device_index", {".class": "UnionType", "items": ["torch._inductor.cudagraph_utils.BoxedDeviceIndex", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fx_codegen_and_compile", "ret_type": "torch._inductor.output_code.OutputCode", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "fx_compile_async": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.fx_compile_async", "name": "fx_compile_async", "type": "builtins.bool"}}, "fx_compile_mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.fx_compile_mode", "name": "fx_compile_mode", "type": "torch._inductor.compile_fx.FxCompileMode"}}, "get_all_devices": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_all_devices", "kind": "Gdef"}, "get_cloned_parameter_buffer_name": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_cloned_parameter_buffer_name", "kind": "Gdef"}, "get_cpp_wrapper_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.get_cpp_wrapper_config", "name": "get_cpp_wrapper_config", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cpp_wrapper_config", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cuda_device_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.get_cuda_device_context", "name": "get_cuda_device_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cuda_device_context", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_device_type": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.get_device_type", "kind": "Gdef"}, "get_expanded_dims": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.get_expanded_dims", "kind": "Gdef"}, "get_first_incompatible_cudagraph_node": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_first_incompatible_cudagraph_node", "kind": "Gdef"}, "get_input_idxs_to_check": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["inputs", "static_input_idxs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.get_input_idxs_to_check", "name": "get_input_idxs_to_check", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["inputs", "static_input_idxs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_input_idxs_to_check", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_interface_for_device": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.device_interface.get_interface_for_device", "kind": "Gdef"}, "get_metrics_context": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.get_metrics_context", "kind": "Gdef"}, "get_patched_config_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["config_patches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.get_patched_config_dict", "name": "get_patched_config_dict", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["config_patches"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_patched_config_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_static_input_idxs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["num_fixed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.get_static_input_idxs", "name": "get_static_input_idxs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["num_fixed"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_static_input_idxs", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_wrapper_codegen_for_device": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.get_wrapper_codegen_for_device", "kind": "Gdef"}, "graph_returns_tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.graph_returns_tuple", "name": "graph_returns_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "graph_returns_tuple", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_dynamo_export_graph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "inputs", "compile_gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.handle_dynamo_export_graph", "name": "handle_dynamo_export_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["gm", "inputs", "compile_gm"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_dynamo_export_graph", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_triton": {".class": "SymbolTableNode", "cross_ref": "torch.utils._triton.has_triton", "kind": "Gdef"}, "index_expanded_dims": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.index_expanded_dims", "kind": "Gdef"}, "index_expanded_dims_and_copy_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dst", "src", "expanded_dims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.index_expanded_dims_and_copy_", "name": "index_expanded_dims_and_copy_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["dst", "src", "expanded_dims"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_expanded_dims_and_copy_", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inductor_metrics_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.inductor_metrics_log", "name": "inductor_metrics_log", "type": "logging.Logger"}}, "init_backend_registration": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.init_backend_registration", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "is_gpu": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.is_gpu", "kind": "Gdef"}, "is_tf32_warning_applicable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.is_tf32_warning_applicable", "name": "is_tf32_warning_applicable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_tf32_warning_applicable", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "joint_graph_passes": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.joint_graph.joint_graph_passes", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "lazy_format_graph_code": {".class": "SymbolTableNode", "cross_ref": "torch.fx._utils.lazy_format_graph_code", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.log", "name": "log", "type": "logging.Logger"}}, "log_cudagraph_skip_and_bump_counter": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.log_cudagraph_skip_and_bump_counter", "kind": "Gdef"}, "log_optimus_to_scuba": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "torch._inductor.compile_fx.log_optimus_to_scuba", "name": "log_optimus_to_scuba", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": ["builtins.object", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_optimus_to_scuba", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_boxed_func": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.utils.make_boxed_func", "kind": "Gdef"}, "make_graph_return_tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "inputs", "compile_gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.make_graph_return_tuple", "name": "make_graph_return_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["gm", "inputs", "compile_gm"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_graph_return_tuple", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_disable_comprehensive_padding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["example_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.maybe_disable_comprehensive_padding", "name": "maybe_disable_comprehensive_padding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["example_inputs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maybe_disable_comprehensive_padding", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_disable_graph_partition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cpp_wrapper", "aot_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.maybe_disable_graph_partition", "name": "maybe_disable_graph_partition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cpp_wrapper", "aot_mode"], "arg_types": ["builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maybe_disable_graph_partition", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_get_suppress_shape_guards_ctx": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.maybe_get_suppress_shape_guards_ctx", "kind": "Gdef"}, "metrics": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.metrics", "kind": "Gdef"}, "min_cut_rematerialization_partition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.compile_fx.min_cut_rematerialization_partition", "name": "min_cut_rematerialization_partition", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.compile_fx.min_cut_rematerialization_partition", "source_any": null, "type_of_any": 3}}}, "mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "output_code_log": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.output_code_log", "kind": "Gdef"}, "output_node": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.output_node", "kind": "Gdef"}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef"}, "perf_hint_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.perf_hint_log", "name": "perf_hint_log", "type": "logging.Logger"}}, "post_grad_graphs_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.post_grad_graphs_log", "name": "post_grad_graphs_log", "type": "logging.Logger"}}, "post_grad_passes": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.post_grad.post_grad_passes", "kind": "Gdef"}, "pre_grad_graphs_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.pre_grad_graphs_log", "name": "pre_grad_graphs_log", "type": "logging.Logger"}}, "pre_grad_passes": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.pre_grad.pre_grad_passes", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "record_original_output_strides": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.record_original_output_strides", "name": "record_original_output_strides", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record_original_output_strides", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_unaligned_input_idxs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.remove_unaligned_input_idxs", "kind": "Gdef"}, "save_args_for_compile_fx_inner": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.debug.save_args_for_compile_fx_inner", "kind": "Gdef"}, "select_decomp_table": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.decomposition.select_decomp_table", "kind": "Gdef"}, "set_feature_use": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.set_feature_use", "kind": "Gdef"}, "shape_env_from_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.shape_env_from_inputs", "kind": "Gdef"}, "should_assume_input_aligned": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.should_assume_input_aligned", "kind": "Gdef"}, "should_use_remote_fx_graph_cache": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.should_use_remote_fx_graph_cache", "kind": "Gdef"}, "split_const_gm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["gm", "skip_constructor", "lifted_constant_names", "skip_folding_node_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.split_const_gm", "name": "split_const_gm", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["gm", "skip_constructor", "lifted_constant_names", "skip_folding_node_fn"], "arg_types": ["torch.fx.graph_module.GraphModule", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_const_gm", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "static_input": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.compile_fx.static_input", "name": "static_input", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "static_input", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "static_inputs_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.compile_fx.static_inputs_log", "name": "static_inputs_log", "type": "logging.Logger"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tensor_is_aligned": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.tensor_is_aligned", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "time_and_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "torch._inductor.compile_fx.time_and_log", "name": "time_and_log", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["attr"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "time_and_log", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "torch._inductor.compile_fx._P", "id": -1, "name": "_P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "torch._inductor.compile_fx._P", "id": -1, "name": "_P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "torch._inductor.compile_fx._P", "id": -1, "name": "_P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "torch._inductor.compile_fx._P", "id": -1, "name": "_P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "torch._inductor.compile_fx._P", "id": -1, "name": "_P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.compile_fx._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "trace_structured": {".class": "SymbolTableNode", "cross_ref": "torch._logging._internal.trace_structured", "kind": "Gdef"}, "unwrap_tensor_subclass_parameters": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.subclass_parametrization.unwrap_tensor_subclass_parameters", "kind": "Gdef"}, "view_to_reshape": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.post_grad.view_to_reshape", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "with_fresh_cache_if_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.compile_fx.with_fresh_cache_if_config", "name": "with_fresh_cache_if_config", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_fresh_cache_if_config", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.compile_fx.with_fresh_cache_if_config", "name": "with_fresh_cache_if_config", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_fresh_cache_if_config", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wrap_compiler_debug": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.repro.after_aot.wrap_compiler_debug", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\compile_fx.py"}