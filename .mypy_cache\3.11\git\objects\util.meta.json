{"data_mtime": 1755656862, "dep_lines": [61, 59, 60, 62, 63, 31, 51, 22, 23, 24, 25, 26, 27, 28, 29, 35, 54, 55, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 5, 5, 5, 10, 5, 5, 10, 5, 10, 10, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.base", "git.objects.blob", "git.objects.commit", "git.objects.tag", "git.objects.tree", "git.util", "git.types", "abc", "calendar", "collections", "datetime", "re", "string", "time", "warnings", "typing", "io", "subprocess", "builtins", "_frozen_importlib", "_io", "enum", "git.diff", "git.objects.base", "git.objects.submodule", "types"], "hash": "f79c09315f6a05deb11cd0bbbd37e2c9808e3c18", "id": "git.objects.util", "ignore_all": true, "interface_hash": "27b8dae195416d4195c02e2cea21d3b3db2e6b49", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\util.py", "plugin_data": null, "size": 23846, "suppressed": [], "version_id": "1.15.0"}