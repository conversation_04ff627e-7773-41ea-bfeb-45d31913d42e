{".class": "MypyFile", "_fullname": "torch.onnx._internal.fx.passes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Decompose": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.passes.decomp.Decompose", "kind": "Gdef"}, "Functionalize": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.passes.functionalization.Functionalize", "kind": "Gdef"}, "InsertTypePromotion": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion", "kind": "Gdef"}, "Modularize": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.passes.modularization.Modularize", "kind": "Gdef"}, "MovePlaceholderToFront": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.passes.virtualization.MovePlaceholderToFront", "kind": "Gdef"}, "RemoveInputMutation": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.passes.functionalization.RemoveInputMutation", "kind": "Gdef"}, "ReplaceGetAttrWithPlaceholder": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.passes.virtualization.ReplaceGetAttrWithPlaceholder", "kind": "Gdef"}, "RestoreParameterAndBufferNames": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.passes.readability.RestoreParameterAndBufferNames", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.passes.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\passes\\__init__.py"}