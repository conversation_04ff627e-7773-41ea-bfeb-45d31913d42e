{".class": "MypyFile", "_fullname": "git.index.base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Actor": {".class": "SymbolTableNode", "cross_ref": "git.util.Actor", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BaseIndexEntry": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntry", "kind": "Gdef", "module_public": false}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef", "module_public": false}, "Blob": {".class": "SymbolTableNode", "cross_ref": "git.objects.blob.Blob", "kind": "Gdef", "module_public": false}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CheckoutError": {".class": "SymbolTableNode", "cross_ref": "git.exc.CheckoutError", "kind": "Gdef"}, "Commit": {".class": "SymbolTableNode", "cross_ref": "git.objects.commit.Commit", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "GitCommandError": {".class": "SymbolTableNode", "cross_ref": "git.exc.GitCommandError", "kind": "Gdef", "module_public": false}, "GitError": {".class": "SymbolTableNode", "cross_ref": "git.exc.GitError", "kind": "Gdef", "module_public": false}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_public": false}, "IStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.index.base.IStream", "name": "IStream", "type": {".class": "AnyType", "missing_import_name": "git.index.base.IStream", "source_any": null, "type_of_any": 3}}}, "IndexEntry": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.IndexEntry", "kind": "Gdef", "module_public": false}, "IndexFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.diff.Diffable", "git.objects.util.Serializable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.index.base.IndexFile", "name": "IndexFile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "git.index.base.IndexFile", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.index.base", "mro": ["git.index.base.IndexFile", "git.diff.Diffable", "git.objects.util.Serializable", "builtins.object"], "names": {".class": "SymbolTable", "S_IFGITLINK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.index.base.IndexFile.S_IFGITLINK", "name": "S_IFGITLINK", "type": "builtins.int"}}, "_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.index.base.IndexFile._VERSION", "name": "_VERSION", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "repo", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "repo", "file_path"], "arg_types": ["git.index.base.IndexFile", "git.repo.base.Repo", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IndexFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.index.base.IndexFile.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_commit_editmsg_filepath": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._commit_editmsg_filepath", "name": "_commit_editmsg_filepath", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_commit_editmsg_filepath of IndexFile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_delete_entries_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._delete_entries_cache", "name": "_delete_entries_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_delete_entries_cache of IndexFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deserialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._deserialize", "name": "_deserialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["git.index.base.IndexFile", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deserialize of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_entries_for_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "paths", "path_rewriter", "fprogress", "entries"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.index.base.IndexFile._entries_for_paths", "name": "_entries_for_paths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "paths", "path_rewriter", "fprogress", "entries"], "arg_types": ["git.index.base.IndexFile", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_entries_for_paths of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile._entries_for_paths", "name": "_entries_for_paths", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "_entries_for_paths of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_entries_sorted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._entries_sorted", "name": "_entries_sorted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_entries_sorted of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extension_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.index.base.IndexFile._extension_data", "name": "_extension_data", "type": "builtins.bytes"}}, "_file_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile._file_path", "name": "_file_path", "type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}}}, "_flush_stdin_and_wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "proc", "ignore_stdout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._flush_stdin_and_wait", "name": "_flush_stdin_and_wait", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "proc", "ignore_stdout"], "arg_types": ["git.index.base.IndexFile", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "subprocess.Popen"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flush_stdin_and_wait of IndexFile", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_index_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._index_path", "name": "_index_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_index_path of IndexFile", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_items_to_rela_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._items_to_rela_paths", "name": "_items_to_rela_paths", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "git.objects.blob.Blob", "git.objects.submodule.base.Submodule"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_items_to_rela_paths of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iter_expand_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "paths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.index.base.IndexFile._iter_expand_paths", "name": "_iter_expand_paths", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "paths"], "arg_types": ["git.index.base.IndexFile", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iter_expand_paths of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile._iter_expand_paths", "name": "_iter_expand_paths", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "_iter_expand_paths of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_preprocess_add_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._preprocess_add_items", "name": "_preprocess_add_items", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "git.objects.blob.Blob", {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "git.objects.submodule.base.Submodule"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_preprocess_add_items of IndexFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_diff_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._process_diff_args", "name": "_process_diff_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "args"], "arg_types": ["git.index.base.IndexFile", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "git.diff.Diffable"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_diff_args of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "git.diff.Diffable"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_commit_editmsg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._read_commit_editmsg", "name": "_read_commit_editmsg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_commit_editmsg of IndexFile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_commit_editmsg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._remove_commit_editmsg", "name": "_remove_commit_editmsg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_commit_editmsg of IndexFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "stream", "ignore_extension_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._serialize", "name": "_serialize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "stream", "ignore_extension_data"], "arg_types": ["git.index.base.IndexFile", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serialize of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_cache_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._set_cache_", "name": "_set_cache_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": ["git.index.base.IndexFile", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_cache_ of IndexFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_store_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "filepath", "fprogress"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._store_path", "name": "_store_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "filepath", "fprogress"], "arg_types": ["git.index.base.IndexFile", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_store_path of IndexFile", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_to_relative_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._to_relative_path", "name": "_to_relative_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["git.index.base.IndexFile", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_relative_path of IndexFile", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_write_commit_editmsg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._write_commit_editmsg", "name": "_write_commit_editmsg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["git.index.base.IndexFile", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_write_commit_editmsg of IndexFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_write_path_to_stdin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "proc", "filepath", "item", "fmakeexc", "fprogress", "read_from_stdout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile._write_path_to_stdin", "name": "_write_path_to_stdin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "proc", "filepath", "item", "fmakeexc", "fprogress", "read_from_stdout"], "arg_types": ["git.index.base.IndexFile", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "subprocess.Popen"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "git.exc.GitError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_write_path_to_stdin of IndexFile", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "items", "force", "fprogress", "path_rewriter", "write", "write_extension_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "items", "force", "fprogress", "path_rewriter", "write", "write_extension_data"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "git.objects.blob.Blob", {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "git.objects.submodule.base.Submodule"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "checkout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "paths", "force", "fprogress", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.index.base.IndexFile.checkout", "name": "checkout", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "paths", "force", "fprogress", "kwargs"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "checkout of IndexFile", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.checkout", "name": "checkout", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "checkout of IndexFile", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "message", "parent_commits", "head", "author", "committer", "author_date", "commit_date", "skip_hooks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "message", "parent_commits", "head", "author", "committer", "author_date", "commit_date", "skip_hooks"], "arg_types": ["git.index.base.IndexFile", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "git.util.Actor"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "git.util.Actor"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of IndexFile", "ret_type": "git.objects.commit.Commit", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "diff": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "other", "paths", "create_patch", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.diff", "name": "diff", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "other", "paths", "create_patch", "kwargs"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "git.diff.DiffConstants", "value": "INDEX"}, "git.objects.tree.Tree", "git.objects.commit.Commit", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "diff of IndexFile", "ret_type": {".class": "Instance", "args": ["git.diff.Diff"], "extra_attrs": null, "type_ref": "git.diff.DiffIndex"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "entries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.entries", "name": "entries", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "entry_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["cls", "entry"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.index.base.IndexFile.entry_key", "name": "entry_key", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["cls", "entry"], "arg_types": [{".class": "TypeType", "item": "git.index.base.IndexFile"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entry_key of IndexFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.entry_key", "name": "entry_key", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["cls", "entry"], "arg_types": [{".class": "TypeType", "item": "git.index.base.IndexFile"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entry_key of IndexFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "repo", "treeish", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.index.base.IndexFile.from_tree", "name": "from_tree", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "repo", "treeish", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.index.base.IndexFile"}, "git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.index.base.Treeish"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_tree of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.from_tree", "name": "from_tree", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "repo", "treeish", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.index.base.IndexFile"}, "git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.index.base.Treeish"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_tree of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "iter_blobs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "predicate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "git.index.base.IndexFile.iter_blobs", "name": "iter_blobs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "predicate"], "arg_types": ["git.index.base.IndexFile", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "git.objects.blob.Blob"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_blobs of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "git.objects.blob.Blob"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "rhs", "base"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.index.base.IndexFile.merge_tree", "name": "merge_tree", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "rhs", "base"], "arg_types": ["git.index.base.IndexFile", {".class": "TypeAliasType", "args": [], "type_ref": "git.index.base.Treeish"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.index.base.Treeish"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_tree of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.merge_tree", "name": "merge_tree", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "merge_tree of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "move": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "items", "skip_errors", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.index.base.IndexFile.move", "name": "move", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "items", "skip_errors", "kwargs"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "git.objects.blob.Blob", {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "git.objects.submodule.base.Submodule"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.move", "name": "move", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "move of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["cls", "repo", "tree_sha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.index.base.IndexFile.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["cls", "repo", "tree_sha"], "arg_types": [{".class": "TypeType", "item": "git.index.base.IndexFile"}, "git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", "git.objects.tree.Tree"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["cls", "repo", "tree_sha"], "arg_types": [{".class": "TypeType", "item": "git.index.base.IndexFile"}, "git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", "git.objects.tree.Tree"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.index.base.IndexFile.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of IndexFile", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of IndexFile", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "items", "working_tree", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.index.base.IndexFile.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "items", "working_tree", "kwargs"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "git.objects.blob.Blob", {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "git.objects.submodule.base.Submodule"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of IndexFile", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "remove of IndexFile", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "commit", "working_tree", "paths", "head", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.index.base.IndexFile.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "commit", "working_tree", "paths", "head", "kwargs"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": ["git.objects.commit.Commit", "git.refs.reference.Reference", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.index.base.IndexFile.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "reset of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "resolve_blobs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "iter_blobs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.resolve_blobs", "name": "resolve_blobs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "iter_blobs"], "arg_types": ["git.index.base.IndexFile", {".class": "Instance", "args": ["git.objects.blob.Blob"], "extra_attrs": null, "type_ref": "typing.Iterator"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_blobs of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unmerged_blobs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.unmerged_blobs", "name": "unmerged_blobs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unmerged_blobs of IndexFile", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "git.objects.blob.Blob"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of IndexFile", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.index.base.IndexFile.version", "name": "version", "type": "builtins.int"}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "file_path", "ignore_extension_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "file_path", "ignore_extension_data"], "arg_types": ["git.index.base.IndexFile", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of IndexFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.base.IndexFile.write_tree", "name": "write_tree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.index.base.IndexFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_tree of IndexFile", "ret_type": "git.objects.tree.Tree", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.base.IndexFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.index.base.IndexFile", "values": [], "variance": 0}, "slots": ["_extension_data", "_file_path", "entries", "repo", "version"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidGitRepositoryError": {".class": "SymbolTableNode", "cross_ref": "git.exc.InvalidGitRepositoryError", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "LazyMixin": {".class": "SymbolTableNode", "cross_ref": "git.util.LazyMixin", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "LockedFD": {".class": "SymbolTableNode", "cross_ref": "git.util.LockedFD", "kind": "Gdef", "module_public": false}, "MemoryDB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.index.base.MemoryDB", "name": "MemoryDB", "type": {".class": "AnyType", "missing_import_name": "git.index.base.MemoryDB", "source_any": null, "type_of_any": 3}}}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_public": false}, "Object": {".class": "SymbolTableNode", "cross_ref": "git.objects.base.Object", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "Popen": {".class": "SymbolTableNode", "cross_ref": "subprocess.Popen", "kind": "Gdef", "module_public": false}, "Reference": {".class": "SymbolTableNode", "cross_ref": "git.refs.reference.Reference", "kind": "Gdef", "module_public": false}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef", "module_public": false}, "S_IFGITLINK": {".class": "SymbolTableNode", "cross_ref": "git.index.fun.S_IFGITLINK", "kind": "Gdef", "module_public": false}, "S_ISLNK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISLNK", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Serializable": {".class": "SymbolTableNode", "cross_ref": "git.objects.util.Serializable", "kind": "Gdef", "module_public": false}, "StageType": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.StageType", "kind": "Gdef"}, "Submodule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.Submodule", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TemporaryFileSwap": {".class": "SymbolTableNode", "cross_ref": "git.index.util.TemporaryFileSwap", "kind": "Gdef", "module_public": false}, "Tree": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.Tree", "kind": "Gdef", "module_public": false}, "Treeish": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.index.base.Treeish", "line": 82, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["git.objects.tree.Tree", "git.objects.commit.Commit", "builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.index.base.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_named_temporary_file_for_subprocess": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["directory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.index.base._named_temporary_file_for_subprocess", "name": "_named_temporary_file_for_subprocess", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["directory"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_named_temporary_file_for_subprocess", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.index.base._named_temporary_file_for_subprocess", "name": "_named_temporary_file_for_subprocess", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["directory"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_named_temporary_file_for_subprocess", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "aggressive_tree_merge": {".class": "SymbolTableNode", "cross_ref": "git.index.fun.aggressive_tree_merge", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_public": false}, "default_index": {".class": "SymbolTableNode", "cross_ref": "git.index.util.default_index", "kind": "Gdef", "module_public": false}, "defenc": {".class": "SymbolTableNode", "cross_ref": "git.compat.defenc", "kind": "Gdef", "module_public": false}, "entry_key": {".class": "SymbolTableNode", "cross_ref": "git.index.fun.entry_key", "kind": "Gdef", "module_public": false}, "file_contents_ro": {".class": "SymbolTableNode", "cross_ref": "git.util.file_contents_ro", "kind": "Gdef", "module_public": false}, "force_bytes": {".class": "SymbolTableNode", "cross_ref": "git.compat.force_bytes", "kind": "Gdef", "module_public": false}, "git_diff": {".class": "SymbolTableNode", "cross_ref": "git.diff", "kind": "Gdef", "module_public": false}, "git_working_dir": {".class": "SymbolTableNode", "cross_ref": "git.index.util.git_working_dir", "kind": "Gdef", "module_public": false}, "glob": {".class": "SymbolTableNode", "cross_ref": "glob", "kind": "Gdef", "module_public": false}, "join_path_native": {".class": "SymbolTableNode", "cross_ref": "git.util.join_path_native", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "osp": {".class": "SymbolTableNode", "cross_ref": "os.path", "kind": "Gdef", "module_public": false}, "post_clear_cache": {".class": "SymbolTableNode", "cross_ref": "git.index.util.post_clear_cache", "kind": "Gdef", "module_public": false}, "read_cache": {".class": "SymbolTableNode", "cross_ref": "git.index.fun.read_cache", "kind": "Gdef", "module_public": false}, "run_commit_hook": {".class": "SymbolTableNode", "cross_ref": "git.index.fun.run_commit_hook", "kind": "Gdef", "module_public": false}, "stat_mode_to_index_mode": {".class": "SymbolTableNode", "cross_ref": "git.index.fun.stat_mode_to_index_mode", "kind": "Gdef", "module_public": false}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef", "module_public": false}, "to_bin_sha": {".class": "SymbolTableNode", "cross_ref": "git.util.to_bin_sha", "kind": "Gdef", "module_public": false}, "to_native_path_linux": {".class": "SymbolTableNode", "cross_ref": "git.util.to_native_path_linux", "kind": "Gdef", "module_public": false}, "unbare_repo": {".class": "SymbolTableNode", "cross_ref": "git.util.unbare_repo", "kind": "Gdef", "module_public": false}, "write_cache": {".class": "SymbolTableNode", "cross_ref": "git.index.fun.write_cache", "kind": "Gdef", "module_public": false}, "write_tree_from_cache": {".class": "SymbolTableNode", "cross_ref": "git.index.fun.write_tree_from_cache", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\index\\base.py"}