{".class": "MypyFile", "_fullname": "torch.export._unlift", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ExportGraphSignature": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature.ExportGraphSignature", "kind": "Gdef"}, "ExportedProgram": {".class": "SymbolTableNode", "cross_ref": "torch.export.exported_program.ExportedProgram", "kind": "Gdef"}, "InputKind": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature.InputKind", "kind": "Gdef"}, "NodeSource": {".class": "SymbolTableNode", "cross_ref": "torch.fx.traceback.NodeSource", "kind": "Gdef"}, "NodeSourceAction": {".class": "SymbolTableNode", "cross_ref": "torch.fx.traceback.NodeSourceAction", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OutputKind": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature.OutputKind", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "_AttrKind": {".class": "SymbolTableNode", "cross_ref": "torch.export.unflatten._AttrKind", "kind": "Gdef"}, "_PyTreeCodeGen": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph._PyTreeCodeGen", "kind": "Gdef"}, "_PyTreeInfo": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph._PyTreeInfo", "kind": "Gdef"}, "_StatefulGraphModule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.fx.graph_module.GraphModule"], "dataclass_transform_spec": null, "declared_metaclass": "torch.export._unlift._StatefulGraphModuleFactory", "defn": {".class": "ClassDef", "fullname": "torch.export._unlift._StatefulGraphModule", "name": "_StatefulGraphModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export._unlift._StatefulGraphModule", "has_param_spec_type": false, "metaclass_type": "torch.export._unlift._StatefulGraphModuleFactory", "metadata": {}, "module_name": "torch.export._unlift", "mro": ["torch.export._unlift._StatefulGraphModule", "torch.fx.graph_module.GraphModule", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "root", "graph", "range_constraints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._StatefulGraphModule.__init__", "name": "__init__", "type": null}}, "range_constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.export._unlift._StatefulGraphModule.range_constraints", "name": "range_constraints", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "validate_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.export._unlift._StatefulGraphModule.validate_inputs", "name": "validate_inputs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export._unlift._StatefulGraphModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export._unlift._StatefulGraphModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_StatefulGraphModuleFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.export._unlift._StatefulGraphModuleFactory", "name": "_StatefulGraphModuleFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export._unlift._StatefulGraphModuleFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.export._unlift", "mro": ["torch.export._unlift._StatefulGraphModuleFactory", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._StatefulGraphModuleFactory.__call__", "name": "__call__", "type": null}}, "_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "root", "graph", "range_constraints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._StatefulGraphModuleFactory._create", "name": "_create", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export._unlift._StatefulGraphModuleFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export._unlift._StatefulGraphModuleFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._unlift.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._unlift.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._unlift.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._unlift.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._unlift.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._unlift.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_assign_attr": {".class": "SymbolTableNode", "cross_ref": "torch.export.unflatten._assign_attr", "kind": "Gdef"}, "_check_input_constraints_for_graph": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils._check_input_constraints_for_graph", "kind": "Gdef"}, "_check_input_constraints_pre_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.export._unlift._check_input_constraints_pre_hook", "name": "_check_input_constraints_pre_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.export._unlift._check_input_constraints_pre_hook", "name": "_check_input_constraints_pre_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_check_inputs_match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["args", "kwargs", "in_spec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._check_inputs_match", "name": "_check_inputs_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["args", "kwargs", "in_spec"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch.utils._pytree.TreeSpec"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_inputs_match", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_stateful_graph_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["plain_graph_module", "range_constraints", "ep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._create_stateful_graph_module", "name": "_create_stateful_graph_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["plain_graph_module", "range_constraints", "ep"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch.export.exported_program.ExportedProgram"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_stateful_graph_module", "ret_type": "torch.export._unlift._StatefulGraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_enter_enable_graph_inputs_of_type_nn_module": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils._enter_enable_graph_inputs_of_type_nn_module", "kind": "Gdef"}, "_exit_enable_graph_inputs_of_type_nn_module": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils._exit_enable_graph_inputs_of_type_nn_module", "kind": "Gdef"}, "_get_codegen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["in_spec", "out_spec", "forward_arg_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._get_codegen", "name": "_get_codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["in_spec", "out_spec", "forward_arg_names"], "arg_types": ["torch.utils._pytree.TreeSpec", {".class": "UnionType", "items": ["torch.utils._pytree.TreeSpec", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_codegen", "ret_type": "torch.fx.graph._PyTreeCodeGen", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_graph_inputs_of_type_nn_module": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils._get_graph_inputs_of_type_nn_module", "kind": "Gdef"}, "_insert_copy_for_mutations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["gm", "mutated_outputs", "unlifted_name_to_node", "input_name_to_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._insert_copy_for_mutations", "name": "_insert_copy_for_mutations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["gm", "mutated_outputs", "unlifted_name_to_node", "input_name_to_node"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", "torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_insert_copy_for_mutations", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pytree_subclasses_that_lose_info": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor._pytree_subclasses_that_lose_info", "kind": "Gdef"}, "_register_attrs_to_new_gm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["new_gm", "graph_signature", "state_dict", "constants"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._register_attrs_to_new_gm", "name": "_register_attrs_to_new_gm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["new_gm", "graph_signature", "state_dict", "constants"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.export.graph_signature.ExportGraphSignature", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_attrs_to_new_gm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_effect_tokens": {".class": "SymbolTableNode", "cross_ref": "torch.export._remove_effect_tokens_pass._remove_effect_tokens", "kind": "Gdef"}, "_unlift": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["gm", "lifted_inputs", "mutated_outputs", "in_spec", "out_spec", "state_dict", "constants", "forward_arg_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._unlift", "name": "_unlift", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["gm", "lifted_inputs", "mutated_outputs", "in_spec", "out_spec", "state_dict", "constants", "forward_arg_names"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "torch.utils._pytree.TreeSpec", {".class": "UnionType", "items": ["torch.utils._pytree.TreeSpec", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unlift", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unlift_exported_program_lifted_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._unlift_exported_program_lifted_states", "name": "_unlift_exported_program_lifted_states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ep"], "arg_types": ["torch.export.exported_program.ExportedProgram"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unlift_exported_program_lifted_states", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unlift_inputs_as_getattr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "lifted_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift._unlift_inputs_as_getattr", "name": "_unlift_inputs_as_getattr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["gm", "lifted_inputs"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unlift_inputs_as_getattr", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "eq_spec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._unlift.eq_spec", "name": "eq_spec", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["torch.utils._pytree.TreeSpec", "torch.utils._pytree.TreeSpec"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eq_spec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "reorder_kwargs": {".class": "SymbolTableNode", "cross_ref": "torch.export._tree_utils.reorder_kwargs", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\export\\_unlift.py"}