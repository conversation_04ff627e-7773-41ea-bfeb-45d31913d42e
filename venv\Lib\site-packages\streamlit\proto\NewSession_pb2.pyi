"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import streamlit.proto.AppPage_pb2
import streamlit.proto.SessionStatus_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class NewSession(google.protobuf.message.Message):
    """NOTE: These proto types are used by some external services so need to
    remain relatively stable. While they aren't entirely set in stone, changing
    them may require a good amount of effort so should be avoided if possible.

    This is the first message that is sent when a new session starts.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INITIALIZE_FIELD_NUMBER: builtins.int
    SCRIPT_RUN_ID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    MAIN_SCRIPT_PATH_FIELD_NUMBER: builtins.int
    CONFIG_FIELD_NUMBER: builtins.int
    CUSTOM_THEME_FIELD_NUMBER: builtins.int
    APP_PAGES_FIELD_NUMBER: builtins.int
    PAGE_SCRIPT_HASH_FIELD_NUMBER: builtins.int
    FRAGMENT_IDS_THIS_RUN_FIELD_NUMBER: builtins.int
    MAIN_SCRIPT_HASH_FIELD_NUMBER: builtins.int
    script_run_id: builtins.str
    """The script run ID"""
    name: builtins.str
    """The basename of the script that launched this app. Example: 'foo.py'"""
    main_script_path: builtins.str
    """The full path of the script that launched this app. Example:
    '/foo/bar/foo.py'
    """
    page_script_hash: builtins.str
    """A hash of the script corresponding to the page currently being viewed."""
    main_script_hash: builtins.str
    """Hash of the main script running (ie streamlit run main_script.py)"""
    @property
    def initialize(self) -> global___Initialize:
        """Initialization data. This data does *not* change from rerun to rerun,
        but we send it every time so that we don't have to track whether the
        client has already received it. The client is responsible for
        performing initialization logic only once.
        """

    @property
    def config(self) -> global___Config:
        """Config options that are (mostly) defined in the .streamlit/config.toml
        file.
        """

    @property
    def custom_theme(self) -> global___CustomThemeConfig:
        """Theme configuration options defined in the .streamlit/config.toml file.
        See the "theme" config section.
        """

    @property
    def app_pages(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[streamlit.proto.AppPage_pb2.AppPage]:
        """A list of all of this app's pages, in order and including the main page."""

    @property
    def fragment_ids_this_run(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """The fragment IDs being run in this session if it corresponds to a fragment
        script run.
        """

    def __init__(
        self,
        *,
        initialize: global___Initialize | None = ...,
        script_run_id: builtins.str = ...,
        name: builtins.str = ...,
        main_script_path: builtins.str = ...,
        config: global___Config | None = ...,
        custom_theme: global___CustomThemeConfig | None = ...,
        app_pages: collections.abc.Iterable[streamlit.proto.AppPage_pb2.AppPage] | None = ...,
        page_script_hash: builtins.str = ...,
        fragment_ids_this_run: collections.abc.Iterable[builtins.str] | None = ...,
        main_script_hash: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["config", b"config", "custom_theme", b"custom_theme", "initialize", b"initialize"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["app_pages", b"app_pages", "config", b"config", "custom_theme", b"custom_theme", "fragment_ids_this_run", b"fragment_ids_this_run", "initialize", b"initialize", "main_script_hash", b"main_script_hash", "main_script_path", b"main_script_path", "name", b"name", "page_script_hash", b"page_script_hash", "script_run_id", b"script_run_id"]) -> None: ...

global___NewSession = NewSession

@typing.final
class Initialize(google.protobuf.message.Message):
    """Contains the session status that existed at the time the user connected.
    The contents of this message don't change over the lifetime of the app by
    definition.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_INFO_FIELD_NUMBER: builtins.int
    ENVIRONMENT_INFO_FIELD_NUMBER: builtins.int
    SESSION_STATUS_FIELD_NUMBER: builtins.int
    COMMAND_LINE_FIELD_NUMBER: builtins.int
    SESSION_ID_FIELD_NUMBER: builtins.int
    IS_HELLO_FIELD_NUMBER: builtins.int
    command_line: builtins.str
    """DEPRECATED: We no longer send this to the frontend for security reasons.
    The actual command line as a string
    """
    session_id: builtins.str
    """The AppSession.id for this connection's AppSession.
    This is used to associate uploaded files with the client that uploaded
    them.
    """
    is_hello: builtins.bool
    """True if the command used to start this app was `streamlit hello`."""
    @property
    def user_info(self) -> global___UserInfo: ...
    @property
    def environment_info(self) -> global___EnvironmentInfo: ...
    @property
    def session_status(self) -> streamlit.proto.SessionStatus_pb2.SessionStatus:
        """The session status at the time the connection was established"""

    def __init__(
        self,
        *,
        user_info: global___UserInfo | None = ...,
        environment_info: global___EnvironmentInfo | None = ...,
        session_status: streamlit.proto.SessionStatus_pb2.SessionStatus | None = ...,
        command_line: builtins.str = ...,
        session_id: builtins.str = ...,
        is_hello: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["environment_info", b"environment_info", "session_status", b"session_status", "user_info", b"user_info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["command_line", b"command_line", "environment_info", b"environment_info", "is_hello", b"is_hello", "session_id", b"session_id", "session_status", b"session_status", "user_info", b"user_info"]) -> None: ...

global___Initialize = Initialize

@typing.final
class Config(google.protobuf.message.Message):
    """App configuration options, initialized mainly from the
    .streamlit/config.toml file.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _ToolbarMode:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ToolbarModeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Config._ToolbarMode.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        AUTO: Config._ToolbarMode.ValueType  # 0
        DEVELOPER: Config._ToolbarMode.ValueType  # 1
        VIEWER: Config._ToolbarMode.ValueType  # 2
        MINIMAL: Config._ToolbarMode.ValueType  # 3

    class ToolbarMode(_ToolbarMode, metaclass=_ToolbarModeEnumTypeWrapper):
        """See config option "client.toolbarMode"."""

    AUTO: Config.ToolbarMode.ValueType  # 0
    DEVELOPER: Config.ToolbarMode.ValueType  # 1
    VIEWER: Config.ToolbarMode.ValueType  # 2
    MINIMAL: Config.ToolbarMode.ValueType  # 3

    GATHER_USAGE_STATS_FIELD_NUMBER: builtins.int
    MAX_CACHED_MESSAGE_AGE_FIELD_NUMBER: builtins.int
    MAPBOX_TOKEN_FIELD_NUMBER: builtins.int
    ALLOW_RUN_ON_SAVE_FIELD_NUMBER: builtins.int
    HIDE_TOP_BAR_FIELD_NUMBER: builtins.int
    HIDE_SIDEBAR_NAV_FIELD_NUMBER: builtins.int
    TOOLBAR_MODE_FIELD_NUMBER: builtins.int
    gather_usage_stats: builtins.bool
    """See config option "browser.gatherUsageStats"."""
    max_cached_message_age: builtins.int
    """See config option "global.maxCachedMessageAge"."""
    mapbox_token: builtins.str
    """DEPRECATED: the mapbox token was moved to the DeckGlJsonChart message."""
    allow_run_on_save: builtins.bool
    """See config option "server.allowRunOnSave"."""
    hide_top_bar: builtins.bool
    """See config option "ui.hideTopBar"."""
    hide_sidebar_nav: builtins.bool
    """See config option "client.showSidebarNavigation"."""
    toolbar_mode: global___Config.ToolbarMode.ValueType
    def __init__(
        self,
        *,
        gather_usage_stats: builtins.bool = ...,
        max_cached_message_age: builtins.int = ...,
        mapbox_token: builtins.str = ...,
        allow_run_on_save: builtins.bool = ...,
        hide_top_bar: builtins.bool = ...,
        hide_sidebar_nav: builtins.bool = ...,
        toolbar_mode: global___Config.ToolbarMode.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["allow_run_on_save", b"allow_run_on_save", "gather_usage_stats", b"gather_usage_stats", "hide_sidebar_nav", b"hide_sidebar_nav", "hide_top_bar", b"hide_top_bar", "mapbox_token", b"mapbox_token", "max_cached_message_age", b"max_cached_message_age", "toolbar_mode", b"toolbar_mode"]) -> None: ...

global___Config = Config

@typing.final
class CustomThemeConfig(google.protobuf.message.Message):
    """Custom theme configuration options. Like other config options, these are set
    in .streamlit/config.toml.

    IMPORTANT: This message is passed in JSON format in a host-to-guest postMessage. So DO NOT
    rename its proto fields!
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _BaseTheme:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _BaseThemeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[CustomThemeConfig._BaseTheme.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        LIGHT: CustomThemeConfig._BaseTheme.ValueType  # 0
        DARK: CustomThemeConfig._BaseTheme.ValueType  # 1

    class BaseTheme(_BaseTheme, metaclass=_BaseThemeEnumTypeWrapper): ...
    LIGHT: CustomThemeConfig.BaseTheme.ValueType  # 0
    DARK: CustomThemeConfig.BaseTheme.ValueType  # 1

    class _FontFamily:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _FontFamilyEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[CustomThemeConfig._FontFamily.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        SANS_SERIF: CustomThemeConfig._FontFamily.ValueType  # 0
        SERIF: CustomThemeConfig._FontFamily.ValueType  # 1
        MONOSPACE: CustomThemeConfig._FontFamily.ValueType  # 2

    class FontFamily(_FontFamily, metaclass=_FontFamilyEnumTypeWrapper):
        """DEPRECATED: Use body_font instead:"""

    SANS_SERIF: CustomThemeConfig.FontFamily.ValueType  # 0
    SERIF: CustomThemeConfig.FontFamily.ValueType  # 1
    MONOSPACE: CustomThemeConfig.FontFamily.ValueType  # 2

    PRIMARY_COLOR_FIELD_NUMBER: builtins.int
    SECONDARY_BACKGROUND_COLOR_FIELD_NUMBER: builtins.int
    BACKGROUND_COLOR_FIELD_NUMBER: builtins.int
    TEXT_COLOR_FIELD_NUMBER: builtins.int
    FONT_FIELD_NUMBER: builtins.int
    BASE_FIELD_NUMBER: builtins.int
    WIDGET_BACKGROUND_COLOR_FIELD_NUMBER: builtins.int
    WIDGET_BORDER_COLOR_FIELD_NUMBER: builtins.int
    RADII_FIELD_NUMBER: builtins.int
    HEADING_FONT_FIELD_NUMBER: builtins.int
    BODY_FONT_FIELD_NUMBER: builtins.int
    CODE_FONT_FIELD_NUMBER: builtins.int
    FONT_FACES_FIELD_NUMBER: builtins.int
    FONT_SIZES_FIELD_NUMBER: builtins.int
    SKELETON_BACKGROUND_COLOR_FIELD_NUMBER: builtins.int
    BASE_RADIUS_FIELD_NUMBER: builtins.int
    BUTTON_RADIUS_FIELD_NUMBER: builtins.int
    BORDER_COLOR_FIELD_NUMBER: builtins.int
    DATAFRAME_BORDER_COLOR_FIELD_NUMBER: builtins.int
    SHOW_WIDGET_BORDER_FIELD_NUMBER: builtins.int
    LINK_COLOR_FIELD_NUMBER: builtins.int
    LINK_UNDERLINE_FIELD_NUMBER: builtins.int
    BASE_FONT_SIZE_FIELD_NUMBER: builtins.int
    BASE_FONT_WEIGHT_FIELD_NUMBER: builtins.int
    CODE_FONT_WEIGHT_FIELD_NUMBER: builtins.int
    CODE_FONT_SIZE_FIELD_NUMBER: builtins.int
    HEADING_FONT_SIZES_FIELD_NUMBER: builtins.int
    HEADING_FONT_WEIGHTS_FIELD_NUMBER: builtins.int
    SHOW_SIDEBAR_BORDER_FIELD_NUMBER: builtins.int
    SIDEBAR_FIELD_NUMBER: builtins.int
    CODE_BACKGROUND_COLOR_FIELD_NUMBER: builtins.int
    DATAFRAME_HEADER_BACKGROUND_COLOR_FIELD_NUMBER: builtins.int
    CHART_CATEGORICAL_COLORS_FIELD_NUMBER: builtins.int
    CHART_SEQUENTIAL_COLORS_FIELD_NUMBER: builtins.int
    primary_color: builtins.str
    secondary_background_color: builtins.str
    background_color: builtins.str
    text_color: builtins.str
    font: global___CustomThemeConfig.FontFamily.ValueType
    """DEPRECATED: Use body_font instead:"""
    base: global___CustomThemeConfig.BaseTheme.ValueType
    widget_background_color: builtins.str
    """DEPRECATED: This color is not applied anymore:"""
    widget_border_color: builtins.str
    """DEPRECATED: Use the border_color and show_widget_border instead:"""
    heading_font: builtins.str
    body_font: builtins.str
    code_font: builtins.str
    skeleton_background_color: builtins.str
    """DEPRECATED: This color is not applied anymore:"""
    base_radius: builtins.str
    button_radius: builtins.str
    border_color: builtins.str
    dataframe_border_color: builtins.str
    show_widget_border: builtins.bool
    link_color: builtins.str
    link_underline: builtins.bool
    base_font_size: builtins.int
    base_font_weight: builtins.int
    code_font_weight: builtins.int
    code_font_size: builtins.str
    show_sidebar_border: builtins.bool
    code_background_color: builtins.str
    dataframe_header_background_color: builtins.str
    @property
    def radii(self) -> global___Radii:
        """DEPRECATED: Please use the base_radius theme config instead:"""

    @property
    def font_faces(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FontFace]: ...
    @property
    def font_sizes(self) -> global___FontSizes:
        """DEPRECATED: Please use the base_font_size theme config instead:"""

    @property
    def heading_font_sizes(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def heading_font_weights(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def sidebar(self) -> global___CustomThemeConfig: ...
    @property
    def chart_categorical_colors(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def chart_sequential_colors(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        primary_color: builtins.str = ...,
        secondary_background_color: builtins.str = ...,
        background_color: builtins.str = ...,
        text_color: builtins.str = ...,
        font: global___CustomThemeConfig.FontFamily.ValueType = ...,
        base: global___CustomThemeConfig.BaseTheme.ValueType = ...,
        widget_background_color: builtins.str = ...,
        widget_border_color: builtins.str = ...,
        radii: global___Radii | None = ...,
        heading_font: builtins.str = ...,
        body_font: builtins.str = ...,
        code_font: builtins.str = ...,
        font_faces: collections.abc.Iterable[global___FontFace] | None = ...,
        font_sizes: global___FontSizes | None = ...,
        skeleton_background_color: builtins.str = ...,
        base_radius: builtins.str | None = ...,
        button_radius: builtins.str | None = ...,
        border_color: builtins.str | None = ...,
        dataframe_border_color: builtins.str | None = ...,
        show_widget_border: builtins.bool | None = ...,
        link_color: builtins.str | None = ...,
        link_underline: builtins.bool | None = ...,
        base_font_size: builtins.int | None = ...,
        base_font_weight: builtins.int | None = ...,
        code_font_weight: builtins.int | None = ...,
        code_font_size: builtins.str | None = ...,
        heading_font_sizes: collections.abc.Iterable[builtins.str] | None = ...,
        heading_font_weights: collections.abc.Iterable[builtins.int] | None = ...,
        show_sidebar_border: builtins.bool | None = ...,
        sidebar: global___CustomThemeConfig | None = ...,
        code_background_color: builtins.str | None = ...,
        dataframe_header_background_color: builtins.str | None = ...,
        chart_categorical_colors: collections.abc.Iterable[builtins.str] | None = ...,
        chart_sequential_colors: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_base_font_size", b"_base_font_size", "_base_font_weight", b"_base_font_weight", "_base_radius", b"_base_radius", "_border_color", b"_border_color", "_button_radius", b"_button_radius", "_code_background_color", b"_code_background_color", "_code_font_size", b"_code_font_size", "_code_font_weight", b"_code_font_weight", "_dataframe_border_color", b"_dataframe_border_color", "_dataframe_header_background_color", b"_dataframe_header_background_color", "_link_color", b"_link_color", "_link_underline", b"_link_underline", "_show_sidebar_border", b"_show_sidebar_border", "_show_widget_border", b"_show_widget_border", "_sidebar", b"_sidebar", "base_font_size", b"base_font_size", "base_font_weight", b"base_font_weight", "base_radius", b"base_radius", "border_color", b"border_color", "button_radius", b"button_radius", "code_background_color", b"code_background_color", "code_font_size", b"code_font_size", "code_font_weight", b"code_font_weight", "dataframe_border_color", b"dataframe_border_color", "dataframe_header_background_color", b"dataframe_header_background_color", "font_sizes", b"font_sizes", "link_color", b"link_color", "link_underline", b"link_underline", "radii", b"radii", "show_sidebar_border", b"show_sidebar_border", "show_widget_border", b"show_widget_border", "sidebar", b"sidebar"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_base_font_size", b"_base_font_size", "_base_font_weight", b"_base_font_weight", "_base_radius", b"_base_radius", "_border_color", b"_border_color", "_button_radius", b"_button_radius", "_code_background_color", b"_code_background_color", "_code_font_size", b"_code_font_size", "_code_font_weight", b"_code_font_weight", "_dataframe_border_color", b"_dataframe_border_color", "_dataframe_header_background_color", b"_dataframe_header_background_color", "_link_color", b"_link_color", "_link_underline", b"_link_underline", "_show_sidebar_border", b"_show_sidebar_border", "_show_widget_border", b"_show_widget_border", "_sidebar", b"_sidebar", "background_color", b"background_color", "base", b"base", "base_font_size", b"base_font_size", "base_font_weight", b"base_font_weight", "base_radius", b"base_radius", "body_font", b"body_font", "border_color", b"border_color", "button_radius", b"button_radius", "chart_categorical_colors", b"chart_categorical_colors", "chart_sequential_colors", b"chart_sequential_colors", "code_background_color", b"code_background_color", "code_font", b"code_font", "code_font_size", b"code_font_size", "code_font_weight", b"code_font_weight", "dataframe_border_color", b"dataframe_border_color", "dataframe_header_background_color", b"dataframe_header_background_color", "font", b"font", "font_faces", b"font_faces", "font_sizes", b"font_sizes", "heading_font", b"heading_font", "heading_font_sizes", b"heading_font_sizes", "heading_font_weights", b"heading_font_weights", "link_color", b"link_color", "link_underline", b"link_underline", "primary_color", b"primary_color", "radii", b"radii", "secondary_background_color", b"secondary_background_color", "show_sidebar_border", b"show_sidebar_border", "show_widget_border", b"show_widget_border", "sidebar", b"sidebar", "skeleton_background_color", b"skeleton_background_color", "text_color", b"text_color", "widget_background_color", b"widget_background_color", "widget_border_color", b"widget_border_color"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_base_font_size", b"_base_font_size"]) -> typing.Literal["base_font_size"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_base_font_weight", b"_base_font_weight"]) -> typing.Literal["base_font_weight"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_base_radius", b"_base_radius"]) -> typing.Literal["base_radius"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_border_color", b"_border_color"]) -> typing.Literal["border_color"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_button_radius", b"_button_radius"]) -> typing.Literal["button_radius"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_code_background_color", b"_code_background_color"]) -> typing.Literal["code_background_color"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_code_font_size", b"_code_font_size"]) -> typing.Literal["code_font_size"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_code_font_weight", b"_code_font_weight"]) -> typing.Literal["code_font_weight"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_dataframe_border_color", b"_dataframe_border_color"]) -> typing.Literal["dataframe_border_color"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_dataframe_header_background_color", b"_dataframe_header_background_color"]) -> typing.Literal["dataframe_header_background_color"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_link_color", b"_link_color"]) -> typing.Literal["link_color"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_link_underline", b"_link_underline"]) -> typing.Literal["link_underline"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_show_sidebar_border", b"_show_sidebar_border"]) -> typing.Literal["show_sidebar_border"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_show_widget_border", b"_show_widget_border"]) -> typing.Literal["show_widget_border"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_sidebar", b"_sidebar"]) -> typing.Literal["sidebar"] | None: ...

global___CustomThemeConfig = CustomThemeConfig

@typing.final
class FontFace(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URL_FIELD_NUMBER: builtins.int
    FAMILY_FIELD_NUMBER: builtins.int
    WEIGHT_FIELD_NUMBER: builtins.int
    WEIGHT_RANGE_FIELD_NUMBER: builtins.int
    STYLE_FIELD_NUMBER: builtins.int
    UNICODE_RANGE_FIELD_NUMBER: builtins.int
    url: builtins.str
    family: builtins.str
    """Equivalent to font-family @font-face CSS property."""
    weight: builtins.int
    """DEPRECATED: Use weight_range instead which supports single weights & weight ranges."""
    weight_range: builtins.str
    """Equivalent to font-weight @font-face CSS property."""
    style: builtins.str
    """Equivalent to font-style @font-face CSS property."""
    unicode_range: builtins.str
    """Equivalent to unicode-range @font-face CSS property."""
    def __init__(
        self,
        *,
        url: builtins.str = ...,
        family: builtins.str = ...,
        weight: builtins.int = ...,
        weight_range: builtins.str = ...,
        style: builtins.str = ...,
        unicode_range: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["family", b"family", "style", b"style", "unicode_range", b"unicode_range", "url", b"url", "weight", b"weight", "weight_range", b"weight_range"]) -> None: ...

global___FontFace = FontFace

@typing.final
class Radii(google.protobuf.message.Message):
    """DEPRECATED: Please use the base_radius theme config instead."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BASE_WIDGET_RADIUS_FIELD_NUMBER: builtins.int
    CHECKBOX_RADIUS_FIELD_NUMBER: builtins.int
    base_widget_radius: builtins.int
    """In pixels."""
    checkbox_radius: builtins.int
    def __init__(
        self,
        *,
        base_widget_radius: builtins.int = ...,
        checkbox_radius: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["base_widget_radius", b"base_widget_radius", "checkbox_radius", b"checkbox_radius"]) -> None: ...

global___Radii = Radii

@typing.final
class FontSizes(google.protobuf.message.Message):
    """DEPRECATED: Please use the base_font_size theme config instead:"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TINY_FONT_SIZE_FIELD_NUMBER: builtins.int
    SMALL_FONT_SIZE_FIELD_NUMBER: builtins.int
    BASE_FONT_SIZE_FIELD_NUMBER: builtins.int
    tiny_font_size: builtins.int
    """In pixels."""
    small_font_size: builtins.int
    base_font_size: builtins.int
    def __init__(
        self,
        *,
        tiny_font_size: builtins.int = ...,
        small_font_size: builtins.int = ...,
        base_font_size: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["base_font_size", b"base_font_size", "small_font_size", b"small_font_size", "tiny_font_size", b"tiny_font_size"]) -> None: ...

global___FontSizes = FontSizes

@typing.final
class UserInfo(google.protobuf.message.Message):
    """Data that identifies the Streamlit app creator.
    Does not change over the app's lifetime.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INSTALLATION_ID_FIELD_NUMBER: builtins.int
    INSTALLATION_ID_V3_FIELD_NUMBER: builtins.int
    INSTALLATION_ID_V4_FIELD_NUMBER: builtins.int
    installation_id: builtins.str
    installation_id_v3: builtins.str
    installation_id_v4: builtins.str
    def __init__(
        self,
        *,
        installation_id: builtins.str = ...,
        installation_id_v3: builtins.str = ...,
        installation_id_v4: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["installation_id", b"installation_id", "installation_id_v3", b"installation_id_v3", "installation_id_v4", b"installation_id_v4"]) -> None: ...

global___UserInfo = UserInfo

@typing.final
class EnvironmentInfo(google.protobuf.message.Message):
    """Data that identifies the Streamlit app's environment.
    Does not change over the app lifetime.

    NB: unlike most of our protobuf data, the EnvironmentInfo message (and all
    its ancestors' IDs) *must* maintain backward- and forward-compatibility.
    When a Streamlit instance is updated to a new version, all connected clients
    will be outdated. Those clients need to be able to read the
    `streamlit_version` property so they can auto-update to the new version.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STREAMLIT_VERSION_FIELD_NUMBER: builtins.int
    PYTHON_VERSION_FIELD_NUMBER: builtins.int
    SERVER_OS_FIELD_NUMBER: builtins.int
    HAS_DISPLAY_FIELD_NUMBER: builtins.int
    streamlit_version: builtins.str
    python_version: builtins.str
    server_os: builtins.str
    """The name of the OS. Typically "windows", "mac", "linux",
    but can take other values like "ios", "android", "freebsd8".
    See https://docs.python.org/3/library/sys.html#sys.platform
    """
    has_display: builtins.bool
    """True if Linux/BSD and DISPLAY or WAYLAND_DISPLAY environment variables are
    set. This is used so we can tell when Streamlit is being executed in order
    to develop the app (has_display = true) or to serve the app (has_display =
    false).
    """
    def __init__(
        self,
        *,
        streamlit_version: builtins.str = ...,
        python_version: builtins.str = ...,
        server_os: builtins.str = ...,
        has_display: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["has_display", b"has_display", "python_version", b"python_version", "server_os", b"server_os", "streamlit_version", b"streamlit_version"]) -> None: ...

global___EnvironmentInfo = EnvironmentInfo
