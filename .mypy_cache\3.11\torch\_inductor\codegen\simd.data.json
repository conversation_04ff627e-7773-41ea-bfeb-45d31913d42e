{".class": "MypyFile", "_fullname": "torch._inductor.codegen.simd", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseSchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseSchedulerNode", "kind": "Gdef"}, "BaseScheduling": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseScheduling", "kind": "Gdef"}, "BlockPatternMatcher": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.block_analysis.BlockPatternMatcher", "kind": "Gdef"}, "CSEVariable": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.CSEVariable", "kind": "Gdef"}, "CSEVariableType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "name": "CSEVariableType", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CandidateTiling": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.simd.CandidateTiling", "name": "CandidateTiling", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.CandidateTiling", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 2450, "name": "tiling", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 2451, "name": "score", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 2452, "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.simd", "mro": ["torch._inductor.codegen.simd.CandidateTiling", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tiling", "score", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.CandidateTiling.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tiling", "score", "name"], "arg_types": ["torch._inductor.codegen.simd.CandidateTiling", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CandidateTiling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tiling"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "score"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["tiling", "score", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["tiling", "score", "name"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CandidateTiling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["tiling", "score", "name"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CandidateTiling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "is_good_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.is_good_size", "name": "is_good_size", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.is_good_size", "name": "is_good_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_good_size of CandidateTiling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "score": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.score", "name": "score", "type": "builtins.int"}}, "tiling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.codegen.simd.CandidateTiling.tiling", "name": "tiling", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.simd.CandidateTiling.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.simd.CandidateTiling", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CantSplit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.simd.CantSplit", "name": "CantSplit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.CantSplit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.simd", "mro": ["torch._inductor.codegen.simd.CantSplit", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.simd.CantSplit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.simd.CantSplit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CoalesceVarAnalysis": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.tiling_utils.CoalesceVarAnalysis", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "DisableReduction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.simd_kernel_features.DisableReduction", "kind": "Gdef"}, "EnableReduction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.simd_kernel_features.EnableReduction", "kind": "Gdef"}, "FloorDiv": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.functions.FloorDiv", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "IRNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.IRNode", "kind": "Gdef"}, "Identity": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.functions.Identity", "kind": "Gdef"}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "IterationRanges": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.simd.IterationRanges", "name": "IterationRanges", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRanges", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.simd", "mro": ["torch._inductor.codegen.simd.IterationRanges", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.simd.IterationRanges.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 3, 5, 5, 3], "arg_names": ["self", "name", "var_list", "var_ranges", "numel", "prefix", "kernel", "divisor", "length", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRanges.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 3, 5, 5, 3], "arg_names": ["self", "name", "var_list", "var_ranges", "numel", "prefix", "kernel", "divisor", "length", "root"], "arg_types": ["torch._inductor.codegen.simd.IterationRanges", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "builtins.str", {".class": "Instance", "args": ["torch._inductor.codegen.common.CSEVariable"], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._inductor.codegen.simd.IterationRangesRoot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IterationRanges", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.simd.IterationRanges.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.simd.IterationRanges.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of IterationRanges", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.simd.IterationRanges.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of IterationRanges", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "divisor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.divisor", "name": "divisor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.codegen.simd.IterationRanges.is_reduction", "name": "is_reduction", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.is_reduction", "name": "is_reduction", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "kernel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.kernel", "name": "kernel", "type": {".class": "Instance", "args": ["torch._inductor.codegen.common.CSEVariable"], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}}}, "length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.length", "name": "length", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.name", "name": "name", "type": "builtins.str"}}, "numel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.numel", "name": "numel", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}}}, "prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.prefix", "name": "prefix", "type": "builtins.str"}}, "root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.root", "name": "root", "type": "torch._inductor.codegen.simd.IterationRangesRoot"}}, "symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRanges.symbol", "name": "symbol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.simd.IterationRanges"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "symbol of IterationRanges", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "symt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.codegen.simd.IterationRanges.symt", "name": "symt", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.symt", "name": "symt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "var_list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.var_list", "name": "var_list", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "var_ranges": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRanges.var_ranges", "name": "var_ranges", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.simd.IterationRanges.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.simd.IterationRanges", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IterationRangesEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.simd.IterationRanges"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.simd.IterationRangesEntry", "name": "IterationRangesEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.simd", "mro": ["torch._inductor.codegen.simd.IterationRangesEntry", "torch._inductor.codegen.simd.IterationRanges", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._inductor.codegen.simd.IterationRangesEntry", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of IterationRangesEntry", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesEntry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of IterationRangesEntry", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "divisor", "length", "expr", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "divisor", "length", "expr", "parent"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesEntry", "builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.codegen.simd.IterationRanges"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IterationRangesEntry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.codegen.simd.IterationRangesEntry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of IterationRangesEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry._codegen", "name": "_codegen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesEntry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_codegen of IterationRangesEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.cache_clear", "name": "cache_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesEntry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cache_clear of IterationRangesEntry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.codegen", "name": "codegen", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}, "expr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.expr", "name": "expr", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.parent", "name": "parent", "type": "torch._inductor.codegen.simd.IterationRanges"}}, "precomputed_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.precomputed_args", "name": "precomputed_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesEntry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "precomputed_args of IterationRangesEntry", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.set_name", "name": "set_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesEntry", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_name of IterationRangesEntry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.simd.IterationRangesEntry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.simd.IterationRangesEntry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IterationRangesRoot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.simd.IterationRanges"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.simd.IterationRangesRoot", "name": "IterationRangesRoot", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.simd", "mro": ["torch._inductor.codegen.simd.IterationRangesRoot", "torch._inductor.codegen.simd.IterationRanges", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3, 3, 3, 3], "arg_names": ["self", "name", "numel", "prefix", "index", "kernel", "pid_cache", "is_loop", "tensor_dim", "grid_dim", "has_zdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3, 3, 3, 3], "arg_names": ["self", "name", "numel", "prefix", "index", "kernel", "pid_cache", "is_loop", "tensor_dim", "grid_dim", "has_zdim"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesRoot", "builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.int", {".class": "Instance", "args": ["torch._inductor.codegen.common.CSEVariable"], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IterationRangesRoot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.codegen.simd.IterationRangesRoot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of IterationRangesRoot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.cache_clear", "name": "cache_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesRoot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cache_clear of IterationRangesRoot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.construct", "name": "construct", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lengths"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesRoot", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct of IterationRangesRoot", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_entries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.construct_entries", "name": "construct_entries", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lengths"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesRoot", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_entries of IterationRangesRoot", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.simd.IterationRangesEntry"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "grid_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.grid_dim", "name": "grid_dim", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "has_zdim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.has_zdim", "name": "has_zdim", "type": "builtins.bool"}}, "index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.index", "name": "index", "type": "builtins.int"}}, "index_sym": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.index_sym", "name": "index_sym", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesRoot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_sym of IterationRangesRoot", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.is_loop", "name": "is_loop", "type": "builtins.bool"}}, "lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "divisor", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.lookup", "name": "lookup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "divisor", "length"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesRoot", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lookup of IterationRangesRoot", "ret_type": "torch._inductor.codegen.simd.IterationRangesEntry", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.nodes", "name": "nodes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.codegen.simd.IterationRangesEntry"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "pid_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.pid_cache", "name": "pid_cache", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tensor_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.tensor_dim", "name": "tensor_dim", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "vars_and_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.vars_and_sizes", "name": "vars_and_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["torch._inductor.codegen.simd.IterationRangesRoot", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vars_and_sizes of IterationRangesRoot", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.simd.IterationRangesRoot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.simd.IterationRangesRoot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Kernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.Kernel", "kind": "Gdef"}, "MemoryDep": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.dependencies.MemoryDep", "kind": "Gdef"}, "ModularIndexing": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.functions.ModularIndexing", "kind": "Gdef"}, "MultiKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.multi_kernel.MultiKernel", "kind": "Gdef"}, "NodeScheduleEntry": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.simd_kernel_features.NodeScheduleEntry", "kind": "Gdef"}, "NodeScheduleMarker": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.simd_kernel_features.NodeScheduleMarker", "kind": "Gdef"}, "OpsWrapper": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.OpsWrapper", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "Placeholder": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.Placeholder", "kind": "Gdef"}, "PythonPrinter": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.PythonPrinter", "kind": "Gdef"}, "SIMDKernel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.common.Kernel"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.simd.SIMDKernel", "name": "SIMDKernel", "type_vars": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.simd", "mro": ["torch._inductor.codegen.simd.SIMDKernel", "torch._inductor.codegen.common.Kernel", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "tiling", "features", "pid_cache", "override_persistent_reduction", "override_cooperative_reduction", "tiling_scores"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "tiling", "features", "pid_cache", "override_persistent_reduction", "override_cooperative_reduction", "tiling_scores"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SIMDKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_combine_contiguous_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "index", "tree"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel._combine_contiguous_dims", "name": "_combine_contiguous_dims", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "index", "tree"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.codegen.simd.IterationRangesRoot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_combine_contiguous_dims of SIMDKernel", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_map_tuple_or_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fn", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDKernel._map_tuple_or_scalar", "name": "_map_tuple_or_scalar", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel._map_tuple_or_scalar", "name": "_map_tuple_or_scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fn", "value"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_map_tuple_or_scalar of SIMDKernel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_split_iteration_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["groups", "lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDKernel._split_iteration_ranges", "name": "_split_iteration_ranges", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["groups", "lengths"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_iteration_ranges of SIMDKernel", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel._split_iteration_ranges", "name": "_split_iteration_ranges", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["groups", "lengths"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_iteration_ranges of SIMDKernel", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "active_range_trees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.active_range_trees", "name": "active_range_trees", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active_range_trees of SIMDKernel", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.simd.IterationRangesRoot"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allow_block_ptr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.allow_block_ptr", "name": "allow_block_ptr", "type": "builtins.bool"}}, "body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.body", "name": "body", "type": "torch._inductor.utils.IndentedBuffer"}}, "call_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.call_kernel", "name": "call_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "node"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, "builtins.str", {".class": "UnionType", "items": ["torch._inductor.ir.IRNode", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_kernel of SIMDKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code_hash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.code_hash", "name": "code_hash", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "codegen_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.codegen_body", "name": "codegen_body", "type": null}}, "codegen_indexing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.codegen_indexing", "name": "codegen_indexing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_indexing of SIMDKernel", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_iteration_ranges_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.codegen_iteration_ranges_entry", "name": "codegen_iteration_ranges_entry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entry"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, "torch._inductor.codegen.simd.IterationRangesEntry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_iteration_ranges_entry of SIMDKernel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.codegen_kernel", "name": "codegen_kernel", "type": null}}, "codegen_nan_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.codegen_nan_check", "name": "codegen_nan_check", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_nan_check of SIMDKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "combine_contiguous_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "index", "tree"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.combine_contiguous_dims", "name": "combine_contiguous_dims", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "index", "tree"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.codegen.simd.IterationRangesRoot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "combine_contiguous_dims of SIMDKernel", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "combine_modular_indexing_pairs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.combine_modular_indexing_pairs", "name": "combine_modular_indexing_pairs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "combine_modular_indexing_pairs of SIMDKernel", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_range_trees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "pid_cache", "inside_reduction", "is_reduction", "numels", "no_x_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.construct_range_trees", "name": "construct_range_trees", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "pid_cache", "inside_reduction", "is_reduction", "numels", "no_x_dim"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_range_trees of SIMDKernel", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.simd.IterationRangesRoot"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cooperative_reduction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.cooperative_reduction", "name": "cooperative_reduction", "type": "builtins.bool"}}, "dense_size_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.dense_size_list", "name": "dense_size_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dense_size_list of SIMDKernel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dense_size_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.dense_size_str", "name": "dense_size_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dense_size_str of SIMDKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disable_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.disable_reduction", "name": "disable_reduction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_reduction of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dtype_to_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.dtype_to_str", "name": "dtype_to_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, "torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype_to_str of SIMDKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "estimate_kernel_num_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.estimate_kernel_num_bytes", "name": "estimate_kernel_num_bytes", "type": null}}, "features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.features", "name": "features", "type": "torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures"}}, "finalize_indexing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.finalize_indexing", "name": "finalize_indexing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "indices"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize_indexing of SIMDKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_index_dtype_as_torch_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.get_index_dtype_as_torch_dtype", "name": "get_index_dtype_as_torch_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_index_dtype_as_torch_dtype of SIMDKernel", "ret_type": "torch._C.dtype", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_strides_of_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.get_strides_of_load", "name": "get_strides_of_load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_strides_of_load of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.index_dtype", "name": "index_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_dtype of SIMDKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.index_dtype", "name": "index_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_dtype of SIMDKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "index_to_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.index_to_str", "name": "index_to_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_to_str of SIMDKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "indexing_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.indexing_code", "name": "indexing_code", "type": "torch._inductor.utils.IndentedBuffer"}}, "indexing_size_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.indexing_size_str", "name": "indexing_size_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "i"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "indexing_size_str of SIMDKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize_range_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pid_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.initialize_range_tree", "name": "initialize_range_tree", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid_cache"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize_range_tree of SIMDKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inside_reduction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.inside_reduction", "name": "inside_reduction", "type": "builtins.bool"}}, "is_broadcasted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.is_broadcasted", "name": "is_broadcasted", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_broadcasted of SIMDKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "groups", "lengths", "reduction_numel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.is_compatible", "name": "is_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "groups", "lengths", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_compatible of SIMDKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.is_compatible", "name": "is_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "groups", "lengths", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_compatible of SIMDKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_indirect_indexing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.is_indirect_indexing", "name": "is_indirect_indexing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_indirect_indexing of SIMDKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_vars_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.iter_vars_count", "name": "iter_vars_count", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "kernel_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.kernel_name", "name": "kernel_name", "type": "builtins.str"}}, "kexpr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.kexpr", "name": "kexpr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "map_kernel_groups_to_node_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "groups", "lengths", "set_ranges"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.map_kernel_groups_to_node_sizes", "name": "map_kernel_groups_to_node_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "groups", "lengths", "set_ranges"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map_kernel_groups_to_node_sizes of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.map_kernel_groups_to_node_sizes", "name": "map_kernel_groups_to_node_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "groups", "lengths", "set_ranges"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map_kernel_groups_to_node_sizes of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mask_loads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mask", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.mask_loads", "name": "mask_loads", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mask", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "UnionType", "items": ["builtins.str", "torch._inductor.virtualized.OpsWrapper"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mask_loads of SIMDKernel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.mask_loads", "name": "mask_loads", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mask", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "UnionType", "items": ["builtins.str", "torch._inductor.virtualized.OpsWrapper"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mask_loads of SIMDKernel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mutations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.mutations", "name": "mutations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "no_x_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.no_x_dim", "name": "no_x_dim", "type": "builtins.bool"}}, "num_reduction_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.num_reduction_dims", "name": "num_reduction_dims", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.num_reduction_dims", "name": "num_reduction_dims", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "numels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.numels", "name": "numels", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "persistent_reduction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.persistent_reduction", "name": "persistent_reduction", "type": "builtins.bool"}}, "prepare_indexing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.prepare_indexing", "name": "prepare_indexing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_indexing of SIMDKernel", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_softmax_twopass_fallback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dtype", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.prepare_softmax_twopass_fallback", "name": "prepare_softmax_twopass_fallback", "type": null}}, "prepare_split_iteration_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "groups", "lengths", "reduction_numel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.prepare_split_iteration_lengths", "name": "prepare_split_iteration_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "groups", "lengths", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_split_iteration_lengths of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.prepare_split_iteration_lengths", "name": "prepare_split_iteration_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "groups", "lengths", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_split_iteration_lengths of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "range_tree_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.range_tree_nodes", "name": "range_tree_nodes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.codegen.simd.IterationRangesEntry"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "range_trees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.range_trees", "name": "range_trees", "type": {".class": "Instance", "args": ["torch._inductor.codegen.simd.IterationRangesRoot"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "set_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.set_ranges", "name": "set_ranges", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "lengths"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ranges of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sexpr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.sexpr", "name": "sexpr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_use_cooperative_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.should_use_cooperative_reduction", "name": "should_use_cooperative_reduction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_use_cooperative_reduction of SIMDKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_use_persistent_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.should_use_persistent_reduction", "name": "should_use_persistent_reduction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_use_persistent_reduction of SIMDKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "simplify_indexing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.simplify_indexing", "name": "simplify_indexing", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}, "split_and_set_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.split_and_set_ranges", "name": "split_and_set_ranges", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lengths"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_and_set_ranges of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "store_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "index", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.store_reduction", "name": "store_reduction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "index", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, "builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.codegen.common.CSEVariable"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "store_reduction of SIMDKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tiling_scores": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDKernel.tiling_scores", "name": "tiling_scores", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "triton_tensor_ndim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.triton_tensor_ndim", "name": "triton_tensor_ndim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "triton_tensor_ndim of SIMDKernel", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "var_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.var_ranges", "name": "var_ranges", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "var_ranges of SIMDKernel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "want_no_x_dim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.want_no_x_dim", "name": "want_no_x_dim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "want_no_x_dim of SIMDKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warn_mix_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.warn_mix_layout", "name": "warn_mix_layout", "type": null}}, "welford_reduce_fallback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dtype", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDKernel.welford_reduce_fallback", "name": "welford_reduce_fallback", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.simd.SIMDKernel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "torch._inductor.codegen.common.CSEVariable", "fullname": "torch._inductor.codegen.simd.CSEVariableType", "id": 1, "name": "CSEVariableType", "namespace": "torch._inductor.codegen.simd.SIMDKernel", "upper_bound": "torch._inductor.codegen.common.CSEVariable", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["CSEVariableType"], "typeddict_type": null}}, "SIMDKernelFeatures": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures", "kind": "Gdef"}, "SIMDScheduling": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.scheduler.BaseScheduling"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.simd.SIMDScheduling", "name": "SIMDScheduling", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.simd", "mro": ["torch._inductor.codegen.simd.SIMDScheduling", "torch._inductor.scheduler.BaseScheduling", "builtins.object"], "names": {".class": "SymbolTable", "can_fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node1", "node2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.can_fuse", "name": "can_fuse", "type": null}}, "can_fuse_horizontal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.can_fuse_horizontal", "name": "can_fuse_horizontal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node1", "node2"], "arg_types": ["torch._inductor.codegen.simd.SIMDScheduling", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_fuse_vertical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.can_fuse_vertical", "name": "can_fuse_vertical", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node1", "node2"], "arg_types": ["torch._inductor.codegen.simd.SIMDScheduling", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_use_32bit_indexing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["numel", "buffers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.can_use_32bit_indexing", "name": "can_use_32bit_indexing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["numel", "buffers"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.TorchBindObject", "torch._inductor.ir.IRNode"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_use_32bit_indexing of SIMDScheduling", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.can_use_32bit_indexing", "name": "can_use_32bit_indexing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["numel", "buffers"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.TorchBindObject", "torch._inductor.ir.IRNode"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_use_32bit_indexing of SIMDScheduling", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "candidate_tilings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "node", "numel", "reduction_numel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.candidate_tilings", "name": "candidate_tilings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "node", "numel", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "candidate_tilings of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.simd.CandidateTiling"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.candidate_tilings", "name": "candidate_tilings", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["torch._inductor.codegen.simd.CandidateTiling"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "codegen_combo_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "combo_kernel_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.codegen_combo_kernel", "name": "codegen_combo_kernel", "type": null}}, "codegen_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_schedule"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.codegen_comment", "name": "codegen_comment", "type": null}}, "codegen_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.codegen_node", "name": "codegen_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.simd.SIMDScheduling", {".class": "UnionType", "items": ["torch._inductor.scheduler.FusedSchedulerNode", "torch._inductor.scheduler.SchedulerNode"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_node of SIMDScheduling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_node_schedule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel_features"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.codegen_node_schedule", "name": "codegen_node_schedule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel_features"], "arg_types": ["torch._inductor.codegen.simd.SIMDScheduling", "torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_node_schedule of SIMDScheduling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_node_schedule_with_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node_schedule", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.codegen_node_schedule_with_kernel", "name": "codegen_node_schedule_with_kernel", "type": null}}, "codegen_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.codegen_sync", "name": "codegen_sync", "type": null}}, "codegen_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "template_node", "epilogue_nodes", "prologue_nodes", "only_gen_src_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.codegen_template", "name": "codegen_template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "template_node", "epilogue_nodes", "prologue_nodes", "only_gen_src_code"], "arg_types": ["torch._inductor.codegen.simd.SIMDScheduling", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_template of SIMDScheduling", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "complete_partial_tiling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "tiling", "numel", "reduction_numel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.complete_partial_tiling", "name": "complete_partial_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "tiling", "numel", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "complete_partial_tiling of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.complete_partial_tiling", "name": "complete_partial_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "tiling", "numel", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "complete_partial_tiling of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "compute_tiling_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "pointwise_numel", "reduction_numel", "coalesce_analysis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.compute_tiling_strategy", "name": "compute_tiling_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "pointwise_numel", "reduction_numel", "coalesce_analysis"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.simd_kernel_features.NodeScheduleEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.tiling_utils.CoalesceVarAnalysis"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_tiling_strategy of SIMDScheduling", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.compute_tiling_strategy", "name": "compute_tiling_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "pointwise_numel", "reduction_numel", "coalesce_analysis"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.simd_kernel_features.NodeScheduleEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.tiling_utils.CoalesceVarAnalysis"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_tiling_strategy of SIMDScheduling", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_kernel_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kernel_features", "kernel_args", "kernel_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.create_kernel_choices", "name": "create_kernel_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kernel_features", "kernel_args", "kernel_kwargs"], "arg_types": ["torch._inductor.codegen.simd.SIMDScheduling", "torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_kernel_choices of SIMDScheduling", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["torch._inductor.codegen.common.CSEVariable"], "extra_attrs": null, "type_ref": "torch._inductor.codegen.simd.SIMDKernel"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_partial_tiling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "tiling", "is_pointwise"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.create_partial_tiling", "name": "create_partial_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "tiling", "is_pointwise"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_partial_tiling of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.create_partial_tiling", "name": "create_partial_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "tiling", "is_pointwise"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_partial_tiling of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_tiling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pw_tiling", "reduction_tiling"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.create_tiling", "name": "create_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pw_tiling", "reduction_tiling"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_tiling of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.create_tiling", "name": "create_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pw_tiling", "reduction_tiling"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_tiling of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "define_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src_code", "node_schedule", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.define_kernel", "name": "define_kernel", "type": null}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.flush", "name": "flush", "type": null}}, "generate_combo_kernel_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "subkernel_nodes", "custom_part_algorithm", "enable_autotune", "mixed_sizes", "only_gen_src_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.generate_combo_kernel_code", "name": "generate_combo_kernel_code", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "subkernel_nodes", "custom_part_algorithm", "enable_autotune", "mixed_sizes", "only_gen_src_code"], "arg_types": ["torch._inductor.codegen.simd.SIMDScheduling", {".class": "Instance", "args": ["torch._inductor.scheduler.BaseSchedulerNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_combo_kernel_code of SIMDScheduling", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_kernel_code_from_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "nodes", "benchmark_kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.generate_kernel_code_from_nodes", "name": "generate_kernel_code_from_nodes", "type": null}}, "generate_node_schedule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nodes", "numel", "rnumel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.generate_node_schedule", "name": "generate_node_schedule", "type": null}}, "get_first_compatible_tiling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "ranked_tilings"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.get_first_compatible_tiling", "name": "get_first_compatible_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "ranked_tilings"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.simd_kernel_features.NodeScheduleEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_first_compatible_tiling of SIMDScheduling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.get_first_compatible_tiling", "name": "get_first_compatible_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "ranked_tilings"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.simd_kernel_features.NodeScheduleEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_first_compatible_tiling of SIMDScheduling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_nd_tilings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "pointwise_numel", "reduction_numel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.get_nd_tilings", "name": "get_nd_tilings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "pointwise_numel", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nd_tilings of SIMDScheduling", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.get_nd_tilings", "name": "get_nd_tilings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "pointwise_numel", "reduction_numel"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nd_tilings of SIMDScheduling", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_tiling_and_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "coalesce_analysis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.get_tiling_and_scores", "name": "get_tiling_and_scores", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "coalesce_analysis"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch._inductor.tiling_utils.CoalesceVarAnalysis", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tiling_and_scores of SIMDScheduling", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.get_tiling_and_scores", "name": "get_tiling_and_scores", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "coalesce_analysis"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch._inductor.tiling_utils.CoalesceVarAnalysis", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tiling_and_scores of SIMDScheduling", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "group_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.group_fn", "name": "group_fn", "type": null}}, "kernel_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.kernel_type", "name": "kernel_type", "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "ready_to_flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.ready_to_flush", "name": "ready_to_flush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.simd.SIMDScheduling"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ready_to_flush of SIMDScheduling", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select_tiling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "coalesce_analysis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.select_tiling", "name": "select_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "coalesce_analysis"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch._inductor.tiling_utils.CoalesceVarAnalysis", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_tiling of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.select_tiling", "name": "select_tiling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "coalesce_analysis"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch._inductor.tiling_utils.CoalesceVarAnalysis", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_tiling of SIMDScheduling", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tiling_is_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "tiling"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.tiling_is_compatible", "name": "tiling_is_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "tiling"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.simd_kernel_features.NodeScheduleEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tiling_is_compatible of SIMDScheduling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.SIMDScheduling.tiling_is_compatible", "name": "tiling_is_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "node_schedule", "numel", "reduction_numel", "tiling"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.simd.SIMDScheduling"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.simd_kernel_features.NodeScheduleEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tiling_is_compatible of SIMDScheduling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.simd.SIMDScheduling.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.simd.SIMDScheduling", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "StarDep": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.dependencies.StarDep", "kind": "Gdef"}, "SymT": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.SymT", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "WeakDep": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.dependencies.WeakDep", "kind": "Gdef"}, "WhyNoFuse": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.WhyNoFuse", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.simd.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.simd.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.simd.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.simd.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.simd.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.simd.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "all_prefixes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.all_prefixes", "name": "all_prefixes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "analyze_memory_coalescing": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.tiling_utils.analyze_memory_coalescing", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cache_on_self": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.cache_on_self", "kind": "Gdef"}, "code_hash": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.code_hash", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "constant_repr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.constant_repr", "name": "constant_repr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constant_repr", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "expr_fits_within_32bit": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.expr_fits_within_32bit", "kind": "Gdef"}, "free_symbol_is_type": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.free_symbol_is_type", "kind": "Gdef"}, "free_unbacked_symbols": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.free_unbacked_symbols", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "fusion_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.fusion_log", "name": "fusion_log", "type": "logging.Logger"}}, "get_dtype_size": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_dtype_size", "kind": "Gdef"}, "get_max_tiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.simd.get_max_tiles", "name": "get_max_tiles", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["default"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_max_tiles", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "green_text": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.runtime_utils.green_text", "kind": "Gdef"}, "immutable_dict": {".class": "SymbolTableNode", "cross_ref": "torch.fx.immutable_collections.immutable_dict", "kind": "Gdef"}, "index_prevent_reordering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.index_prevent_reordering", "kind": "Gdef"}, "indexing_dtype_strength_reduction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.optimize_indexing.indexing_dtype_strength_reduction", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "no_type_check": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.ops", "kind": "Gdef"}, "perf_hint_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.perf_hint_log", "name": "perf_hint_log", "type": "logging.Logger"}}, "pexpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.pexpr", "name": "pexpr", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["expr", "simplify", "p"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.common.sympy", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool"], "bound_args": ["torch._inductor.codegen.common.PythonPrinter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prefix_is_reduction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.prefix_is_reduction", "kind": "Gdef"}, "prefix_str": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.prefix_str", "kind": "Gdef"}, "prologue_preserves_zero_mask": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.analyze_preserves_zero_mask.prologue_preserves_zero_mask", "kind": "Gdef"}, "schedule_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.simd.schedule_log", "name": "schedule_log", "type": "logging.Logger"}}, "scheduler": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler", "kind": "Gdef"}, "set_kernel_post_grad_provenance_tracing": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.set_kernel_post_grad_provenance_tracing", "kind": "Gdef"}, "symbol_is_type": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.symbol_is_type", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.simd.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.simd.sympy", "source_any": null, "type_of_any": 3}}}, "sympy_index_symbol": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_index_symbol", "kind": "Gdef"}, "sympy_product": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_product", "kind": "Gdef"}, "sympy_subs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_subs", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "unique": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.unique", "kind": "Gdef"}, "yellow_text": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.runtime_utils.yellow_text", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\simd.py"}