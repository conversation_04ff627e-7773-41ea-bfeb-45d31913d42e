{"data_mtime": 1755649448, "dep_lines": [34, 35, 36, 49, 52, 95, 123, 24, 25, 26, 32, 46, 47, 48, 51, 51, 51, 62, 81, 95, 97, 98, 112, 119, 121, 124, 128, 394, 502, 661, 1527, 2160, 20, 21, 23, 28, 33, 51, 116, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 19, 117, 2014, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 139, 16], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 5, 20, 5, 5, 5, 25, 25, 20, 5, 20, 20, 20, 20, 20, 5, 10, 5, 5, 5, 20, 25, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["torch.fx.experimental._backward_state", "torch.fx.experimental.sym_node", "torch.fx.experimental.symbolic_shapes", "torch.utils._sympy.numbers", "torch._inductor.codegen.common", "torch._inductor.runtime.autotune_cache", "torch._inductor.codegen.wrapper", "torch._dynamo.utils", "torch._library.fake_class_registry", "torch._library.utils", "torch._subclasses.fake_tensor", "torch.fx.node", "torch.utils._mode_utils", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.metrics", "torch._inductor.exc", "torch._inductor.lowering", "torch._inductor.runtime", "torch._inductor.sizevars", "torch._inductor.utils", "torch._inductor.virtualized", "torch._higher_order_ops.effects", "torch.fx.graph", "torch._inductor.scheduler", "torch._inductor.codecache", "torch._inductor.extern_node_serializer", "torch._dynamo.source", "torch.utils.flop_counter", "torch._inductor.compiler_bisector", "torch._inductor.compile_fx", "torch._logging", "torch.fx", "torch._decomp", "torch._prims_common", "torch._utils_internal", "torch._inductor", "collections.abc", "__future__", "contextlib", "functools", "itertools", "logging", "operator", "os", "re", "sys", "time", "collections", "typing", "torch", "types", "copy", "builtins", "inspect", "html", "string", "pprint", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "warnings", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._C._VariableFunctions", "torch._guards", "torch._higher_order_ops", "torch._higher_order_ops.triton_kernel_wrap", "torch._inductor.codegen", "torch._library", "torch._logging._internal", "torch._ops", "torch._subclasses", "torch._tensor", "torch.fx.experimental", "torch.fx.graph_module", "torch.fx.interpreter", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "torch.utils", "torch.utils._config_typing", "torch.utils._python_dispatch", "typing_extensions"], "hash": "92355e0fb993af2aa0428a25eca7ed4d60f65671", "id": "torch._inductor.graph", "ignore_all": true, "interface_hash": "0e117e7d3bf117c512ed3307cc426d42bf742046", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\graph.py", "plugin_data": null, "size": 106266, "suppressed": ["torch._inductor.fb.utils", "sympy"], "version_id": "1.15.0"}