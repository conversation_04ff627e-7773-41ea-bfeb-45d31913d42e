{".class": "MypyFile", "_fullname": "torch.utils.tensorboard._pytorch_graph", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CLASSTYPE_KIND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard._pytorch_graph.CLASSTYPE_KIND", "name": "CLASSTYPE_KIND", "type": "builtins.str"}}, "DeviceStepStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.DeviceStepStats", "name": "DeviceStepStats", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard._pytorch_graph.DeviceStepStats", "source_any": null, "type_of_any": 3}}}, "GETATTR_KIND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard._pytorch_graph.GETATTR_KIND", "name": "GETATTR_KIND", "type": "builtins.str"}}, "GraphDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphDef", "name": "GraphDef", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard._pytorch_graph.GraphDef", "source_any": null, "type_of_any": 3}}}, "GraphPy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy", "name": "GraphPy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.tensorboard._pytorch_graph", "mro": ["torch.utils.tensorboard._pytorch_graph.GraphPy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.__init__", "name": "__init__", "type": null}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.append", "name": "append", "type": null}}, "find_common_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.find_common_root", "name": "find_common_root", "type": null}}, "nodes_io": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.nodes_io", "name": "nodes_io", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "nodes_op": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.nodes_op", "name": "nodes_op", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "populate_namespace_from_OP_to_IO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.populate_namespace_from_OP_to_IO", "name": "populate_namespace_from_OP_to_IO", "type": null}}, "printall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.printall", "name": "printall", "type": null}}, "scope_name_appeared": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.scope_name_appeared", "name": "scope_name_appeared", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "shallowest_scope_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.shallowest_scope_name", "name": "shallowest_scope_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "to_proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.to_proto", "name": "to_proto", "type": null}}, "unique_name_to_scoped_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.unique_name_to_scoped_name", "name": "unique_name_to_scoped_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.tensorboard._pytorch_graph.GraphPy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.tensorboard._pytorch_graph.GraphPy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodeBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase", "name": "NodeBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.tensorboard._pytorch_graph", "mro": ["torch.utils.tensorboard._pytorch_graph.NodeBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "debugName", "inputs", "scope", "tensor_size", "op_type", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.__repr__", "name": "__repr__", "type": null}}, "attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.attributes", "name": "attributes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "debugName": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.debugName", "name": "debugName", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.inputs", "name": "inputs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.kind", "name": "kind", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.scope", "name": "scope", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tensor_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.tensor_size", "name": "tensor_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.tensorboard._pytorch_graph.NodeBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.tensorboard._pytorch_graph.NodeBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodePy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.tensorboard._pytorch_graph.NodeBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.tensorboard._pytorch_graph.NodePy", "name": "NodePy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodePy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.tensorboard._pytorch_graph", "mro": ["torch.utils.tensorboard._pytorch_graph.NodePy", "torch.utils.tensorboard._pytorch_graph.NodeBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node_cpp", "valid_methods"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodePy.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.tensorboard._pytorch_graph.NodePy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.tensorboard._pytorch_graph.NodePy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodePyIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.tensorboard._pytorch_graph.NodePy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyIO", "name": "NodePyIO", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyIO", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.tensorboard._pytorch_graph", "mro": ["torch.utils.tensorboard._pytorch_graph.NodePyIO", "torch.utils.tensorboard._pytorch_graph.NodePy", "torch.utils.tensorboard._pytorch_graph.NodeBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node_cpp", "input_or_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyIO.__init__", "name": "__init__", "type": null}}, "input_or_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyIO.input_or_output", "name": "input_or_output", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyIO.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.tensorboard._pytorch_graph.NodePyIO", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodePyOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.tensorboard._pytorch_graph.NodePy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyOP", "name": "NodePyOP", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyOP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.tensorboard._pytorch_graph", "mro": ["torch.utils.tensorboard._pytorch_graph.NodePyOP", "torch.utils.tensorboard._pytorch_graph.NodePy", "torch.utils.tensorboard._pytorch_graph.NodeBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_cpp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyOP.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.tensorboard._pytorch_graph.NodePyOP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.tensorboard._pytorch_graph.NodePyOP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "RunMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.RunMetadata", "name": "RunMetadata", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard._pytorch_graph.RunMetadata", "source_any": null, "type_of_any": 3}}}, "StepStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.StepStats", "name": "StepStats", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard._pytorch_graph.StepStats", "source_any": null, "type_of_any": 3}}}, "VersionDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph.VersionDef", "name": "VersionDef", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard._pytorch_graph.VersionDef", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._pytorch_graph.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._pytorch_graph.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._pytorch_graph.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._pytorch_graph.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._pytorch_graph.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._pytorch_graph.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_node_get": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph._node_get", "name": "_node_get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node", "key"], "arg_types": ["torch._<PERSON><PERSON>", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_node_get", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_model_to_eval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.utils.tensorboard._pytorch_graph._set_model_to_eval", "name": "_set_model_to_eval", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard._pytorch_graph._set_model_to_eval", "name": "_set_model_to_eval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_model_to_eval", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "graph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["model", "args", "verbose", "use_strict_trace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.graph", "name": "graph", "type": null}}, "methods_IO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard._pytorch_graph.methods_IO", "name": "methods_IO", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "methods_OP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard._pytorch_graph.methods_OP", "name": "methods_OP", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "node_proto": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._proto_graph.node_proto", "kind": "Gdef"}, "parse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["graph", "trace", "args", "omit_useless_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._pytorch_graph.parse", "name": "parse", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\_pytorch_graph.py"}