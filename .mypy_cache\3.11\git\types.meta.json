{"data_mtime": 1755656862, "dep_lines": [40, 41, 4, 5, 6, 20, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 10, 10, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["git.objects", "git.repo", "os", "sys", "typing", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc", "git.repo.base"], "hash": "24d51ea5d19db960e7baf8ed5abcd366c8bbfd9f", "id": "git.types", "ignore_all": true, "interface_hash": "56d8fd0a063cc2e1c2a45d7ba02067d6f2eefd68", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\types.py", "plugin_data": null, "size": 10272, "suppressed": [], "version_id": "1.15.0"}