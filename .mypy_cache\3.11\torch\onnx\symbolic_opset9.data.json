{".class": "MypyFile", "_fullname": "torch.onnx.symbolic_opset9", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "GLOBALS": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._globals.GLOBALS", "kind": "Gdef", "module_public": false}, "Number": {".class": "SymbolTableNode", "cross_ref": "torch.types.Number", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "_C": {".class": "SymbolTableNode", "cross_ref": "torch._C", "kind": "Gdef", "module_public": false}, "_C_onnx": {".class": "SymbolTableNode", "cross_ref": "torch._<PERSON>._onnx", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.symbolic_opset9.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__and_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__and_", "name": "__and_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__and_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__and_", "name": "__and_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset9.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__contains_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__contains_", "name": "__contains_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "element"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__contains_", "name": "__contains_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__derive_index": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "index", "start", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__derive_index", "name": "__derive_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "index", "start", "step"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__derive_index", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__derive_index", "name": "__derive_index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset9.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset9.__file__", "name": "__file__", "type": "builtins.str"}}, "__getitem_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__getitem_", "name": "__getitem_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "i"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__getitem_", "name": "__getitem_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__interpolate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "size", "scale_factor", "mode", "align_corners", "recompute_scale_factor", "antialias"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__interpolate", "name": "__interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "size", "scale_factor", "mode", "align_corners", "recompute_scale_factor", "antialias"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__interpolate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__interpolate", "name": "__interpolate", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__is_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__is_", "name": "__is_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__is_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__is_", "name": "__is_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__isnot_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__isnot_", "name": "__isnot_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__isnot_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__isnot_", "name": "__isnot_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__lshift_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__lshift_", "name": "__lshift_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lshift_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__lshift_", "name": "__lshift_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset9.__name__", "name": "__name__", "type": "builtins.str"}}, "__not_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__not_", "name": "__not_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__not_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__not_", "name": "__not_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__or_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__or_", "name": "__or_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__or_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__or_", "name": "__or_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset9.__package__", "name": "__package__", "type": "builtins.str"}}, "__range_length": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "lo", "hi", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__range_length", "name": "__range_length", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "lo", "hi", "step"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__range_length", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__range_length", "name": "__range_length", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__rshift_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__rshift_", "name": "__rshift_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rshift_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__rshift_", "name": "__rshift_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset9.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__xor_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.__xor_", "name": "__xor_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__xor_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.__xor_", "name": "__xor_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_adaptive_pool": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["name", "type", "tuple_fn", "fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._adaptive_pool", "name": "_adaptive_pool", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._adaptive_pool", "name": "_adaptive_pool", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_all": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["g", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._all", "name": "_all", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["g", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._all", "name": "_all", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_any": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["g", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._any", "name": "_any", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["g", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_any", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._any", "name": "_any", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_avg_pool": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "tuple_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._avg_pool", "name": "_avg_pool", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._avg_pool", "name": "_avg_pool", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Bool": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Bool is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Bool", "name": "_cast_<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_<PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Bool", "name": "_cast_<PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Byte": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Byte is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Byte", "name": "_cast_Byte", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_Byte", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Byte", "name": "_cast_Byte", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Char": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Char is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Char", "name": "_cast_Char", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_Char", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Char", "name": "_cast_Char", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Double": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Double is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Double", "name": "_cast_Double", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_Double", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Double", "name": "_cast_Double", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Float": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Float is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Float", "name": "_cast_Float", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_Float", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Float", "name": "_cast_Float", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Half": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Half is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Half", "name": "_cast_Half", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_Half", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Half", "name": "_cast_Half", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Int": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Int is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Int", "name": "_cast_Int", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_Int", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Int", "name": "_cast_Int", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Long": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Long is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Long", "name": "_cast_Long", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_Long", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Long", "name": "_cast_Long", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_cast_Short": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.symbolic_opset9._cast_Short is deprecated: Avoid using this function and create a Cast node instead", "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._cast_Short", "name": "_cast_Short", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "non_blocking"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_Short", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._cast_Short", "name": "_cast_Short", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_constants": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._constants", "kind": "Gdef", "module_public": false}, "_convert_padding_node": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._convert_padding_node", "name": "_convert_padding_node", "type": null}}, "_convolution": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "transposed", "output_padding", "groups", "benchmark", "deterministic", "cudnn_enabled", "allow_tf32"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._convolution", "name": "_convolution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "transposed", "output_padding", "groups", "benchmark", "deterministic", "cudnn_enabled", "allow_tf32"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convolution", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._convolution", "name": "_convolution", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_convolution_mode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._convolution_mode", "name": "_convolution_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "groups"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convolution_mode", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._convolution_mode", "name": "_convolution_mode", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_dim_arange": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "like", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._dim_arange", "name": "_dim_arange", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "like", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dim_arange", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._dim_arange", "name": "_dim_arange", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_div_rounding_mode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "other", "rounding_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._div_rounding_mode", "name": "_div_rounding_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "other", "rounding_mode"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div_rounding_mode", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._div_rounding_mode", "name": "_div_rounding_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, "self", "other", "rounding_mode"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div_rounding_mode", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_euclidean_dist": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._euclidean_dist", "name": "_euclidean_dist", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "x1", "x2"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_euclidean_dist", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_export": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._export", "name": "_export", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_export", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_floor_divide": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._floor_divide", "name": "_floor_divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_floor_divide", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generic_rnn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["g", "variant", "input", "initial_states", "all_weights", "has_biases", "num_layers", "dropout", "train", "bidirectional", "batch_first", "batch_sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._generic_rnn", "name": "_generic_rnn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["g", "variant", "input", "initial_states", "all_weights", "has_biases", "num_layers", "dropout", "train", "bidirectional", "batch_first", "batch_sizes"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generic_rnn", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_gt_impl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._gt_impl", "name": "_gt_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gt_impl", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_interpolate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "dim", "interpolate_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._interpolate", "name": "_interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "dim", "interpolate_mode"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_interpolate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._interpolate", "name": "_interpolate", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_kl_div_log_target_impl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._kl_div_log_target_impl", "name": "_kl_div_log_target_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "target"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_kl_div_log_target_impl", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_kl_div_non_log_target_impl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._kl_div_non_log_target_impl", "name": "_kl_div_non_log_target_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "target"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_kl_div_non_log_target_impl", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_len": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._len", "name": "_len", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_len", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._len", "name": "_len", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._list", "name": "_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_list", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._list", "name": "_list", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_log_softmax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "half_to_float"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._log_softmax", "name": "_log_softmax", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "half_to_float"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_softmax", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._log_softmax", "name": "_log_softmax", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_lstm_full": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "hidden_v", "weight_v", "has_biases", "num_layers", "dropout", "train", "bidirectional", "batch_first"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._lstm_full", "name": "_lstm_full", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "hidden_v", "weight_v", "has_biases", "num_layers", "dropout", "train", "bidirectional", "batch_first"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lstm_full", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._lstm_full", "name": "_lstm_full", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": [null, "input", "hidden_v", "weight_v", "has_biases", "num_layers", "dropout", "train", "bidirectional", "batch_first"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lstm_full", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_lstm_packed": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "batch_sizes", "hidden_v", "weight_v", "has_biases", "num_layers", "dropout", "train", "bidirectional"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._lstm_packed", "name": "_lstm_packed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "batch_sizes", "hidden_v", "weight_v", "has_biases", "num_layers", "dropout", "train", "bidirectional"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lstm_packed", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._lstm_packed", "name": "_lstm_packed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": [null, "input", "batch_sizes", "hidden_v", "weight_v", "has_biases", "num_layers", "dropout", "train", "bidirectional"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lstm_packed", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_lt_impl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._lt_impl", "name": "_lt_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lt_impl", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_max_pool": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "tuple_fn", "ndims", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._max_pool", "name": "_max_pool", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._max_pool", "name": "_max_pool", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_one_hidden_rnn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._one_hidden_rnn", "name": "_one_hidden_rnn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["kind"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_one_hidden_rnn", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._one_hidden_rnn", "name": "_one_hidden_rnn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_onnx_symbolic": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.symbolic_opset9._onnx_symbolic", "name": "_onnx_symbolic", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": {".class": "ExtraAttrs", "attrs": {"__mypy_partial": {".class": "CallableType", "arg_kinds": [0, 5, 1, 1], "arg_names": ["name", "opset", "decorate", "custom"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "onnx_symbolic", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "immutable": [], "mod_name": null}, "type_ref": "functools.partial"}}}, "_pack_padded_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "lengths", "batch_first"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._pack_padded_sequence", "name": "_pack_padded_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "lengths", "batch_first"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pack_padded_sequence", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._pack_padded_sequence", "name": "_pack_padded_sequence", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_pad_circular": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._pad_circular", "name": "_pad_circular", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "pad"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pad_circular", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pad_packed_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "data", "batch_sizes", "batch_first", "padding_value", "total_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._pad_packed_sequence", "name": "_pad_packed_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "data", "batch_sizes", "batch_first", "padding_value", "total_length"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pad_packed_sequence", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._pad_packed_sequence", "name": "_pad_packed_sequence", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_prepare_onnx_paddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["dim", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._prepare_onnx_paddings", "name": "_prepare_onnx_paddings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["dim", "pad"], "arg_types": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_onnx_paddings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reduce_with_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["onnx_op", "name", "allow_multi_dim_support"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._reduce_with_dtype", "name": "_reduce_with_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["onnx_op", "name", "allow_multi_dim_support"], "arg_types": ["builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reduce_with_dtype", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._reduce_with_dtype", "name": "_reduce_with_dtype", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_reshape_from_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._reshape_from_tensor", "name": "_reshape_from_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "shape"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reshape_from_tensor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._reshape_from_tensor", "name": "_reshape_from_tensor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_sample_dirichlet": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "generator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._sample_dirichlet", "name": "_sample_dirichlet", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "generator"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sample_dirichlet", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._sample_dirichlet", "name": "_sample_dirichlet", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_shape_as_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._shape_as_tensor", "name": "_shape_as_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shape_as_tensor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._shape_as_tensor", "name": "_shape_as_tensor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_slice": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "axes", "starts", "ends"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._slice", "name": "_slice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "axes", "starts", "ends"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_slice", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._slice", "name": "_slice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "axes", "starts", "ends"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_slice", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_standard_gamma": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "generator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._standard_gamma", "name": "_standard_gamma", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "generator"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_standard_gamma", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._standard_gamma", "name": "_standard_gamma", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_trunc_divide": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9._trunc_divide", "name": "_trunc_divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_trunc_divide", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._type_utils", "kind": "Gdef", "module_public": false}, "_unique": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "sorted", "return_inverse"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._unique", "name": "_unique", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "sorted", "return_inverse"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unique", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._unique", "name": "_unique", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_unique2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "sorted", "return_inverse", "return_counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._unique2", "name": "_unique2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "sorted", "return_inverse", "return_counts"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unique2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._unique2", "name": "_unique2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_unsupported_dropout": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._unsupported_dropout", "name": "_unsupported_dropout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unsupported_dropout", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._unsupported_dropout", "name": "_unsupported_dropout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_var_mean": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "dim", "correction", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._var_mean", "name": "_var_mean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "dim", "correction", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_var_mean", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._var_mean", "name": "_var_mean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, "input", "dim", "correction", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_var_mean", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_weight_norm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "weight_v", "weight_g", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9._weight_norm", "name": "_weight_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "weight_v", "weight_g", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_weight_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9._weight_norm", "name": "_weight_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "abs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.abs", "name": "abs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "abs", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.abs", "name": "abs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "acos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.acos", "name": "acos", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acos", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.acos", "name": "acos", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "add": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "other", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "other", "alpha"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.add", "name": "add", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "addcmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "tensor1", "tensor2", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.addcmul", "name": "addcmul", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "tensor1", "tensor2", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addcmul", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.addcmul", "name": "addcmul", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "addmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "mat1", "mat2", "beta", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.addmm", "name": "addmm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "mat1", "mat2", "beta", "alpha"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addmm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.addmm", "name": "addmm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "alias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.alias", "name": "alias", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alias", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.alias", "name": "alias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "amax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.amax", "name": "amax", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "amax", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.amax", "name": "amax", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "amin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.amin", "name": "amin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "amin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.amin", "name": "amin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "aminmax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.aminmax", "name": "aminmax", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aminmax", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.aminmax", "name": "aminmax", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "arange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["g", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.arange", "name": "arange", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["g", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arange", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.arange", "name": "arange", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "argmax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.argmax", "name": "argmax", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "torch._C.Value", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "argmax", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.argmax", "name": "argmax", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "argmin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.argmin", "name": "a<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "torch._C.Value", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "a<PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.argmin", "name": "a<PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "as_strided": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "sizes", "strides", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.as_strided", "name": "as_strided", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "sizes", "strides", "offset"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_strided", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.as_strided", "name": "as_strided", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "as_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "data", "dtype", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.as_tensor", "name": "as_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "data", "dtype", "device"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_tensor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.as_tensor", "name": "as_tensor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "asin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.asin", "name": "asin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.asin", "name": "asin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "atan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.atan", "name": "atan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atan", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.atan", "name": "atan", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "atan2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.atan2", "name": "atan2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atan2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.atan2", "name": "atan2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "baddbmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "batch1", "batch2", "beta", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.baddbmm", "name": "baddbmm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "batch1", "batch2", "beta", "alpha"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "baddbmm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.baddbmm", "name": "baddbmm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "batch_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "running_mean", "running_var", "training", "momentum", "eps", "cudnn_enabled"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.batch_norm", "name": "batch_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "running_mean", "running_var", "training", "momentum", "eps", "cudnn_enabled"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.batch_norm", "name": "batch_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "bernoulli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["g", "input", "p", "generator", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.be<PERSON><PERSON>i", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["g", "input", "p", "generator", "out"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.be<PERSON><PERSON>i", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "bitwise_not": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.bitwise_not", "name": "bitwise_not", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_not", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.bitwise_not", "name": "bitwise_not", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "bitwise_or": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.bitwise_or", "name": "bitwise_or", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.bitwise_or", "name": "bitwise_or", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "bmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.bmm", "name": "bmm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bmm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.bmm", "name": "bmm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "broadcast_tensors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.broadcast_tensors", "name": "broadcast_tensors", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_tensors", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.broadcast_tensors", "name": "broadcast_tensors", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "broadcast_to": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.broadcast_to", "name": "broadcast_to", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "size"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_to", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.broadcast_to", "name": "broadcast_to", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "bucketize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["g", "self", "boundaries", "out_int32", "right"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.bucketize", "name": "bucketize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["g", "self", "boundaries", "out_int32", "right"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bucketize", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.bucketize", "name": "bucketize", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_public": false}, "cat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.cat", "name": "cat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cat", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.cat", "name": "cat", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "cdist": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["g", "x1", "x2", "p", "compute_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.cdist", "name": "cdist", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["g", "x1", "x2", "p", "compute_mode"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cdist", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.cdist", "name": "cdist", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "ceil": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.ceil", "name": "ceil", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ceil", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.ceil", "name": "ceil", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "clamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "min", "max"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.clamp", "name": "clamp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "min", "max"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clamp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.clamp", "name": "clamp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "clamp_max": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "max"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.clamp_max", "name": "clamp_max", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "max"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clamp_max", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.clamp_max", "name": "clamp_max", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "clamp_min": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "min"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.clamp_min", "name": "clamp_min", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "min"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clamp_min", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.clamp_min", "name": "clamp_min", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "clone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "unused_memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "unused_memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.clone", "name": "clone", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "constant_pad_nd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "padding", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.constant_pad_nd", "name": "constant_pad_nd", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "padding", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constant_pad_nd", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.constant_pad_nd", "name": "constant_pad_nd", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "contiguous": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.contiguous", "name": "contiguous", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contiguous", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.contiguous", "name": "contiguous", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "conv1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.conv1d", "name": "conv1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "groups"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conv1d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.conv1d", "name": "conv1d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "conv2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.conv2d", "name": "conv2d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "groups"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conv2d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.conv2d", "name": "conv2d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "conv3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.conv3d", "name": "conv3d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "groups"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conv3d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.conv3d", "name": "conv3d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "conv_tbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.conv_tbc", "name": "conv_tbc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "pad"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conv_tbc", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.conv_tbc", "name": "conv_tbc", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "conv_transpose1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "output_padding", "groups", "dilation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.conv_transpose1d", "name": "conv_transpose1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "output_padding", "groups", "dilation"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conv_transpose1d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.conv_transpose1d", "name": "conv_transpose1d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "conv_transpose2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "output_padding", "groups", "dilation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.conv_transpose2d", "name": "conv_transpose2d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "output_padding", "groups", "dilation"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conv_transpose2d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.conv_transpose2d", "name": "conv_transpose2d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "conv_transpose3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "output_padding", "groups", "dilation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.conv_transpose3d", "name": "conv_transpose3d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "output_padding", "groups", "dilation"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conv_transpose3d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.conv_transpose3d", "name": "conv_transpose3d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "convert_element_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["g", "self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.convert_element_type", "name": "convert_element_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["g", "self", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert_element_type", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.convert_element_type", "name": "convert_element_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "convolution": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "transposed", "output_padding", "groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.convolution", "name": "convolution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "stride", "padding", "dilation", "transposed", "output_padding", "groups"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convolution", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.convolution", "name": "convolution", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "cos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.cos", "name": "cos", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cos", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.cos", "name": "cos", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "cosine_similarity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "x1", "x2", "dim", "eps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.cosine_similarity", "name": "cosine_similarity", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "x1", "x2", "dim", "eps"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cosine_similarity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.cosine_similarity", "name": "cosine_similarity", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "cross": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "other", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.cross", "name": "cross", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "other", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cross", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.cross", "name": "cross", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "cumsum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.cumsum", "name": "cumsum", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cumsum", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.cumsum", "name": "cumsum", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_public": false}, "detach": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.detach", "name": "detach", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.detach", "name": "detach", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "dim": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.dim", "name": "dim", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dim", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.dim", "name": "dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "div": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["g", "self", "other", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.div", "name": "div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["g", "self", "other", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "div", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.div", "name": "div", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "dot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.dot", "name": "dot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dot", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.dot", "name": "dot", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "dropout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "p", "train"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.dropout", "name": "dropout", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "p", "train"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dropout", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "elu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "alpha", "scale", "input_scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.elu", "name": "elu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "alpha", "scale", "input_scale"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "elu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.elu", "name": "elu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "embedding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "weight", "indices", "padding_idx", "scale_grad_by_freq", "sparse"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.embedding", "name": "embedding", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "weight", "indices", "padding_idx", "scale_grad_by_freq", "sparse"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "embedding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.embedding", "name": "embedding", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "embedding_bag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "embedding_matrix", "indices", "offsets", "scale_grad_by_freq", "mode", "sparse", "per_sample_weights", "include_last_offset", "padding_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.embedding_bag", "name": "embedding_bag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "embedding_matrix", "indices", "offsets", "scale_grad_by_freq", "mode", "sparse", "per_sample_weights", "include_last_offset", "padding_idx"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "embedding_bag", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.embedding_bag", "name": "embedding_bag", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "empty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["g", "sizes", "dtype", "layout", "device", "pin_memory", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.empty", "name": "empty", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["g", "sizes", "dtype", "layout", "device", "pin_memory", "memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.empty", "name": "empty", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "empty_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["g", "input", "dtype", "layout", "device", "pin_memory", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.empty_like", "name": "empty_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["g", "input", "dtype", "layout", "device", "pin_memory", "memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty_like", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.empty_like", "name": "empty_like", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "eq": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.eq", "name": "eq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eq", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.eq", "name": "eq", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "erf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.erf", "name": "erf", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "erf", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.erf", "name": "erf", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "errors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.errors", "kind": "Gdef", "module_public": false}, "exp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.exp", "name": "exp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.exp", "name": "exp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "expand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "size", "implicit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.expand", "name": "expand", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "size", "implicit"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.expand", "name": "expand", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "expand_as": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.expand_as", "name": "expand_as", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand_as", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.expand_as", "name": "expand_as", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "eye": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["g", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["g", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.eye", "name": "eye", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "fill": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.fill", "name": "fill", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fill", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.fill", "name": "fill", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "flatten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "start_dim", "end_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.flatten", "name": "flatten", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "start_dim", "end_dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flatten", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.flatten", "name": "flatten", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "floor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.floor", "name": "floor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "floor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.floor", "name": "floor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "floor_divide": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.floor_divide", "name": "floor_divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "floor_divide", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.floor_divide", "name": "floor_divide", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "floordiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.floordiv", "name": "floordiv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "floordiv", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.floordiv", "name": "floordiv", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "frobenius_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.frobenius_norm", "name": "frobenius_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frobenius_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.frobenius_norm", "name": "frobenius_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "full": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "sizes", "value", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.full", "name": "full", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "sizes", "value", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.full", "name": "full", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "full_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["g", "input", "fill_value", "dtype", "layout", "device", "pin_memory", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.full_like", "name": "full_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["g", "input", "fill_value", "dtype", "layout", "device", "pin_memory", "memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_like", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.full_like", "name": "full_like", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "gather": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "index", "sparse_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "index", "sparse_grad"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.gather", "name": "gather", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "ge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.ge", "name": "ge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ge", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.ge", "name": "ge", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "gelu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "approximate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.gelu", "name": "gelu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "approximate"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gelu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.gelu", "name": "gelu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "get_pool_ceil_padding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["input", "kernel_size", "stride", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.get_pool_ceil_padding", "name": "get_pool_ceil_padding", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.get_pool_ceil_padding", "name": "get_pool_ceil_padding", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "glu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.glu", "name": "glu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "glu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.glu", "name": "glu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "group_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "num_groups", "weight", "bias", "eps", "cudnn_enabled"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.group_norm", "name": "group_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "num_groups", "weight", "bias", "eps", "cudnn_enabled"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.group_norm", "name": "group_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "gt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.gt", "name": "gt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.gt", "name": "gt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "hann_window": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["g", "window_length", "periodic", "dtype", "layout", "device", "pin_memory", "requires_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.hann_window", "name": "hann_window", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["g", "window_length", "periodic", "dtype", "layout", "device", "pin_memory", "requires_grad"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hann_window", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.hann_window", "name": "hann_window", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "hardshrink": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "lambd"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.hardshrink", "name": "hardshrink", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "lambd"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hardshrink", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.hardshrink", "name": "hardshrink", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "hardsigmoid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.hardsigmoid", "name": "hardsigmoid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hardsigmoid", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.hardsigmoid", "name": "hardsigmoid", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "hardswish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.hardswish", "name": "hardswish", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hardswish", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.hardswish", "name": "hardswish", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "hardtanh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "min_val", "max_val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.hardtanh", "name": "hardtanh", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "min_val", "max_val"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hardtanh", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.hardtanh", "name": "hardtanh", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "index"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.index", "name": "index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index_add": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "index", "other", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.index_add", "name": "index_add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "index", "other", "alpha"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_add", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.index_add", "name": "index_add", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index_copy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.index_copy", "name": "index_copy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "source"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_copy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.index_copy", "name": "index_copy", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index_fill": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.index_fill", "name": "index_fill", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_fill", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.index_fill", "name": "index_fill", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index_put": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "indices_list_value", "values", "accumulate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.index_put", "name": "index_put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "indices_list_value", "values", "accumulate"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_put", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.index_put", "name": "index_put", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index_select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.index_select", "name": "index_select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_select", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.index_select", "name": "index_select", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "instance_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "running_mean", "running_var", "use_input_stats", "momentum", "eps", "cudnn_enabled"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.instance_norm", "name": "instance_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias", "running_mean", "running_var", "use_input_stats", "momentum", "eps", "cudnn_enabled"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "torch.types.Number"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.types.Number"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.instance_norm", "name": "instance_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "is_floating_point": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.is_floating_point", "name": "is_floating_point", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_floating_point", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.is_floating_point", "name": "is_floating_point", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "is_pinned": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.is_pinned", "name": "is_pinned", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "device"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_pinned", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.is_pinned", "name": "is_pinned", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "isnan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.isnan", "name": "isnan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnan", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.isnan", "name": "isnan", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "item": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.item", "name": "item", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "item", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.item", "name": "item", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "jit_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.jit_utils", "kind": "Gdef", "module_public": false}, "kl_div": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "target", "reduction", "log_target"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.kl_div", "name": "kl_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "target", "reduction", "log_target"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kl_div", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.kl_div", "name": "kl_div", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "layer_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "normalized_shape", "weight", "bias", "eps", "cudnn_enable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.layer_norm", "name": "layer_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "normalized_shape", "weight", "bias", "eps", "cudnn_enable"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "torch._C.Value", "torch._C.Value", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "layer_norm", "ret_type": "torch._C.Value", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.layer_norm", "name": "layer_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.le", "name": "le", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "le", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.le", "name": "le", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "leaky_relu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "negative_slope", "inplace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.leaky_relu", "name": "leaky_relu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "negative_slope", "inplace"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leaky_relu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.leaky_relu", "name": "leaky_relu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "lerp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "end", "weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.lerp", "name": "lerp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "end", "weight"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lerp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.lerp", "name": "lerp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "lift": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.lift", "name": "lift", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lift", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.lift", "name": "lift", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "linalg_cross": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "other", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.linalg_cross", "name": "linalg_cross", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "other", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linalg_cross", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.linalg_cross", "name": "linalg_cross", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "linalg_matrix_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "ord", "dim", "keepdim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.linalg_matrix_norm", "name": "linalg_matrix_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "ord", "dim", "keepdim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "torch._C.Value", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linalg_matrix_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.linalg_matrix_norm", "name": "linalg_matrix_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "linalg_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "ord", "dim", "keepdim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.linalg_norm", "name": "linalg_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "ord", "dim", "keepdim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "torch._C.Value", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linalg_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.linalg_norm", "name": "linalg_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "linalg_vector_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "ord", "dim", "keepdim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.linalg_vector_norm", "name": "linalg_vector_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "ord", "dim", "keepdim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linalg_vector_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.linalg_vector_norm", "name": "linalg_vector_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "linear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.linear", "name": "linear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "weight", "bias"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linear", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.linear", "name": "linear", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "linspace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "start", "end", "steps", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.linspace", "name": "linspace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "start", "end", "steps", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linspace", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.linspace", "name": "linspace", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.log", "name": "log", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "log10": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.log10", "name": "log10", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log10", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.log10", "name": "log10", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "log1p": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.log1p", "name": "log1p", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log1p", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.log1p", "name": "log1p", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "log2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.log2", "name": "log2", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.log2", "name": "log2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "log_sigmoid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.log_sigmoid", "name": "log_sigmoid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_sigmoid", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.log_sigmoid", "name": "log_sigmoid", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "log_softmax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.log_softmax", "name": "log_softmax", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "dim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_softmax", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.log_softmax", "name": "log_softmax", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "logical_and": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.logical_and", "name": "logical_and", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_and", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.logical_and", "name": "logical_and", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "logical_not": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.logical_not", "name": "logical_not", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_not", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.logical_not", "name": "logical_not", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "logical_or": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.logical_or", "name": "logical_or", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_or", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.logical_or", "name": "logical_or", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "logical_xor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.logical_xor", "name": "logical_xor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logical_xor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.logical_xor", "name": "logical_xor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "logit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "eps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.logit", "name": "logit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "eps"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logit", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.logit", "name": "logit", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "logsumexp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.logsumexp", "name": "logsumexp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logsumexp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.logsumexp", "name": "logsumexp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "lstm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["g", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.lstm", "name": "lstm", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["g", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.lstm", "name": "lstm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "lstm_cell": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "hidden", "w_ih", "w_hh", "b_ih", "b_hh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.lstm_cell", "name": "lstm_cell", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "hidden", "w_ih", "w_hh", "b_ih", "b_hh"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstm_cell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.lstm_cell", "name": "lstm_cell", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "lt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.lt", "name": "lt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.lt", "name": "lt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "masked_fill": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "mask", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.masked_fill", "name": "masked_fill", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "mask", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "masked_fill", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.masked_fill", "name": "masked_fill", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "masked_fill_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "mask", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.masked_fill_", "name": "masked_fill_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "mask", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "masked_fill_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.masked_fill_", "name": "masked_fill_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "matmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.matmul", "name": "<PERSON><PERSON>l", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON>l", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.matmul", "name": "<PERSON><PERSON>l", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "max": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim_or_y", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.max", "name": "max", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim_or_y", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.max", "name": "max", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "max_pool1d_with_indices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.symbolic_opset9.max_pool1d_with_indices", "name": "max_pool1d_with_indices", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "max_pool2d_with_indices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.symbolic_opset9.max_pool2d_with_indices", "name": "max_pool2d_with_indices", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "max_pool3d_with_indices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.symbolic_opset9.max_pool3d_with_indices", "name": "max_pool3d_with_indices", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "maximum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.maximum", "name": "maximum", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maximum", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.maximum", "name": "maximum", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "meshgrid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "tensor_list", "indexing"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.meshgrid", "name": "meshgrid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "tensor_list", "indexing"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "meshgrid", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.meshgrid", "name": "meshgrid", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "min": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim_or_y", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.min", "name": "min", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim_or_y", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "min", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.min", "name": "min", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "minimum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.minimum", "name": "minimum", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "minimum", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.minimum", "name": "minimum", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "mish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.mish", "name": "mish", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mish", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.mish", "name": "mish", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.mm", "name": "mm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.mm", "name": "mm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "movedim": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "source", "destination"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.movedim", "name": "<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "source", "destination"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.movedim", "name": "<PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "mse_loss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "target", "reduction"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.mse_loss", "name": "mse_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "target", "reduction"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mse_loss", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.mse_loss", "name": "mse_loss", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "mul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.mul", "name": "mul", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mul", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.mul", "name": "mul", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "multinomial": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["g", "input", "num_samples", "replacement", "generator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.multinomial", "name": "multinomial", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["g", "input", "num_samples", "replacement", "generator"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multinomial", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.multinomial", "name": "multinomial", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "mv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "vec"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.mv", "name": "mv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "vec"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mv", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.mv", "name": "mv", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "narrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "dim", "start", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.narrow", "name": "narrow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "dim", "start", "length"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "narrow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.narrow", "name": "narrow", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "native_layer_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "normalized_shape", "weight", "bias", "eps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.native_layer_norm", "name": "native_layer_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "normalized_shape", "weight", "bias", "eps"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "torch._C.Value", "torch._C.Value", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "native_layer_norm", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._C.Value", "torch._C.Value", "torch._C.Value"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.native_layer_norm", "name": "native_layer_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "ne": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.ne", "name": "ne", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ne", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.ne", "name": "ne", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "neg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.neg", "name": "neg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "neg", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.neg", "name": "neg", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "new_empty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "sizes", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.new_empty", "name": "new_empty", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "sizes", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_empty", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.new_empty", "name": "new_empty", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "new_full": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "size", "fill_value", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.new_full", "name": "new_full", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "size", "fill_value", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_full", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.new_full", "name": "new_full", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "new_ones": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "sizes", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.new_ones", "name": "new_ones", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "sizes", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_ones", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.new_ones", "name": "new_ones", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "new_zeros": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "sizes", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.new_zeros", "name": "new_zeros", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "sizes", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_zeros", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.new_zeros", "name": "new_zeros", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "nonzero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.nonzero", "name": "nonzero", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nonzero", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.nonzero", "name": "nonzero", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "nonzero_numpy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "input", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.nonzero_numpy", "name": "nonzero_numpy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "input", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nonzero_numpy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.nonzero_numpy", "name": "nonzero_numpy", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "noop_complex_operators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.noop_complex_operators", "name": "noop_complex_operators", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "noop_complex_operators", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.noop_complex_operators", "name": "noop_complex_operators", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "p", "dim", "keepdim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.norm", "name": "norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "p", "dim", "keepdim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.norm", "name": "norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "numel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.numel", "name": "numel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "numel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.numel", "name": "numel", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "numpy_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.numpy_T", "name": "numpy_T", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "numpy_T", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.numpy_T", "name": "numpy_T", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "one_hot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "num_classes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.one_hot", "name": "one_hot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "num_classes"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "one_hot", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.one_hot", "name": "one_hot", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "ones": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["g", "sizes", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.ones", "name": "ones", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["g", "sizes", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.ones", "name": "ones", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "ones_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["g", "input", "dtype", "layout", "device", "pin_memory", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.ones_like", "name": "ones_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["g", "input", "dtype", "layout", "device", "pin_memory", "memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones_like", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.ones_like", "name": "ones_like", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "onnx_placeholder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.onnx_placeholder", "name": "onnx_placeholder", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "attrs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "onnx_placeholder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.onnx_placeholder", "name": "onnx_placeholder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "pad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "pad", "mode", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.pad", "name": "pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "pad", "mode", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "torch._C.Value", "torch._C.Value", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.pad", "name": "pad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "pairwise_distance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input1", "input2", "p", "eps", "keepdim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.pairwise_distance", "name": "pairwise_distance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input1", "input2", "p", "eps", "keepdim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pairwise_distance", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.pairwise_distance", "name": "pairwise_distance", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "permute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "dims"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.permute", "name": "permute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "dims"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "permute", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.permute", "name": "permute", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "pixel_shuffle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "upscale_factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.pixel_shuffle", "name": "pixel_shuffle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "upscale_factor"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pixel_shuffle", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.pixel_shuffle", "name": "pixel_shuffle", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "pixel_unshuffle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "downscale_factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.pixel_unshuffle", "name": "pixel_unshuffle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "downscale_factor"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pixel_unshuffle", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.pixel_unshuffle", "name": "pixel_unshuffle", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "pow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "exponent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.pow", "name": "pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "exponent"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.pow", "name": "pow", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prelu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prelu", "name": "prelu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "weight"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prelu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prelu", "name": "prelu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_constant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_constant", "name": "prim_constant", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "attrs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_constant", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_constant", "name": "prim_constant", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_constant_chunk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "chunks", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_constant_chunk", "name": "prim_constant_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "chunks", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_constant_chunk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_constant_chunk", "name": "prim_constant_chunk", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_constant_split": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "split_size", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_constant_split", "name": "prim_constant_split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "split_size", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_constant_split", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_constant_split", "name": "prim_constant_split", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_data", "name": "prim_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_data", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_data", "name": "prim_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_device", "name": "prim_device", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_device", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_device", "name": "prim_device", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_dtype", "name": "prim_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_dtype", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_dtype", "name": "prim_dtype", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_if": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_if", "name": "prim_if", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "attrs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_if", "ret_type": {".class": "Instance", "args": ["torch._C.Value"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_if", "name": "prim_if", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_layout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_layout", "name": "prim_layout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_layout", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_layout", "name": "prim_layout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_list_construct": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_list_construct", "name": "prim_list_construct", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_list_construct", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_list_construct", "name": "prim_list_construct", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_list_unpack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_list_unpack", "name": "prim_list_unpack", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_list_unpack", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._C.Value"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_list_unpack", "name": "prim_list_unpack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_loop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_loop", "name": "prim_loop", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "attrs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_loop", "ret_type": {".class": "Instance", "args": ["torch._C.Value"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_loop", "name": "prim_loop", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_max": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_max", "name": "prim_max", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_max", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_max", "name": "prim_max", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_min": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_min", "name": "prim_min", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_min", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_min", "name": "prim_min", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_shape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_shape", "name": "prim_shape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_shape", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_shape", "name": "prim_shape", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_tolist": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim_val", "elem_ty_val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_tolist", "name": "prim_tolist", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "dim_val", "elem_ty_val"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_tolist", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_tolist", "name": "prim_tolist", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_tuple_construct": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_tuple_construct", "name": "prim_tuple_construct", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_tuple_construct", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_tuple_construct", "name": "prim_tuple_construct", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["g", "device_value", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_type", "name": "prim_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["g", "device_value", "args", "kwargs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_type", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_type", "name": "prim_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_unchecked_cast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_unchecked_cast", "name": "prim_unchecked_cast", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_unchecked_cast", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_unchecked_cast", "name": "prim_unchecked_cast", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_uninitialized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.prim_uninitialized", "name": "prim_uninitialized", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["g", "inputs", "kwargs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_uninitialized", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.prim_uninitialized", "name": "prim_uninitialized", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "rand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["g", "shapes", "dtype", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.rand", "name": "rand", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["g", "shapes", "dtype", "options"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rand", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.rand", "name": "rand", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "rand_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["g", "self", "dtype", "layout", "device", "pin_memory", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.rand_like", "name": "rand_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["g", "self", "dtype", "layout", "device", "pin_memory", "memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rand_like", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.rand_like", "name": "rand_like", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "randint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 2], "arg_names": ["g", "low", "high", "shapes", "dtype", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.randint", "name": "ran<PERSON>t", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 2], "arg_names": ["g", "low", "high", "shapes", "dtype", "options"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ran<PERSON>t", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.randint", "name": "ran<PERSON>t", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "randint_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 2], "arg_names": ["g", "self", "low", "high", "dtype", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.randint_like", "name": "randint_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 2], "arg_names": ["g", "self", "low", "high", "dtype", "options"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randint_like", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.randint_like", "name": "randint_like", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "randn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["g", "shapes", "dtype", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.randn", "name": "randn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["g", "shapes", "dtype", "options"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randn", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.randn", "name": "randn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "randn_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["g", "self", "dtype", "layout", "device", "pin_memory", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.randn_like", "name": "randn_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["g", "self", "dtype", "layout", "device", "pin_memory", "memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randn_like", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.randn_like", "name": "randn_like", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "reciprocal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.reciprocal", "name": "reciprocal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reciprocal", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.reciprocal", "name": "reciprocal", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "reflection_pad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.reflection_pad", "name": "reflection_pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "padding"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflection_pad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.reflection_pad", "name": "reflection_pad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "registration": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.registration", "kind": "Gdef", "module_public": false}, "relu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.relu", "name": "relu", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "relu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.relu", "name": "relu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "relu6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.relu6", "name": "relu6", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "relu6", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.relu6", "name": "relu6", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "remainder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.remainder", "name": "remainder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remainder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.remainder", "name": "remainder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "repeat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.repeat", "name": "repeat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "repeats"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repeat", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.repeat", "name": "repeat", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "repeat_interleave": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["g", "self", "repeats", "dim", "output_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.repeat_interleave", "name": "repeat_interleave", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["g", "self", "repeats", "dim", "output_size"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repeat_interleave", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.repeat_interleave", "name": "repeat_interleave", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "replication_pad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.replication_pad", "name": "replication_pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "padding"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replication_pad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.replication_pad", "name": "replication_pad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "reshape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.reshape", "name": "reshape", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "shape"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reshape", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.reshape", "name": "reshape", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "reshape_as": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.reshape_as", "name": "reshape_as", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reshape_as", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.reshape_as", "name": "reshape_as", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "roll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "shifts", "dims"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.roll", "name": "roll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "shifts", "dims"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "roll", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.roll", "name": "roll", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "rrelu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "lower", "upper", "training", "generator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.rrelu", "name": "rrelu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "lower", "upper", "training", "generator"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rrelu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.rrelu", "name": "rrelu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "rsqrt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.rsqrt", "name": "rsqrt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsqrt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.rsqrt", "name": "rsqrt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "rsub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "other", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.rsub", "name": "rsub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "other", "alpha"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsub", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.rsub", "name": "rsub", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "scalar_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["g", "scalar", "dtype", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.scalar_tensor", "name": "scalar_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["g", "scalar", "dtype", "options"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar_tensor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.scalar_tensor", "name": "scalar_tensor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "scatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.scatter", "name": "scatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "src"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scatter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.scatter", "name": "scatter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "scatter_add": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.scatter_add", "name": "scatter_add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "src"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scatter_add", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.scatter_add", "name": "scatter_add", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.select", "name": "select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.select", "name": "select", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "selu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.selu", "name": "selu", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.selu", "name": "selu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "sigmoid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.sigmoid", "name": "sigmoid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sigmoid", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.sigmoid", "name": "sigmoid", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "sign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.sign", "name": "sign", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "silu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.silu", "name": "silu", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "silu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.silu", "name": "silu", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "sin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.sin", "name": "sin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.sin", "name": "sin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "size": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.size", "name": "size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["g", "self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.slice", "name": "slice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["g", "self", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "slice", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.slice", "name": "slice", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "softmax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.softmax", "name": "softmax", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "dim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "softmax", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.softmax", "name": "softmax", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "softplus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "beta", "threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.softplus", "name": "softplus", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "beta", "threshold"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "softplus", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.softplus", "name": "softplus", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "softshrink": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "lambd"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.softshrink", "name": "softshrink", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "lambd"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "softshrink", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.softshrink", "name": "softshrink", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "sort": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "decending", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.sort", "name": "sort", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "decending", "out"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sort", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.sort", "name": "sort", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "split": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_size_or_sizes", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_size_or_sizes", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.split", "name": "split", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "split_with_sizes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_sizes", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.split_with_sizes", "name": "split_with_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_sizes", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_with_sizes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.split_with_sizes", "name": "split_with_sizes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "sqrt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.sqrt", "name": "sqrt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sqrt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.sqrt", "name": "sqrt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "square": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.square", "name": "square", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "square", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.square", "name": "square", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "squeeze": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.squeeze", "name": "squeeze", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "squeeze", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.squeeze", "name": "squeeze", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.stack", "name": "stack", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.stack", "name": "stack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "std": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["g", "input", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.std", "name": "std", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["g", "input", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "std", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.std", "name": "std", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "std_mean": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["g", "input", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.std_mean", "name": "std_mean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["g", "input", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "std_mean", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.std_mean", "name": "std_mean", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "sub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "other", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "other", "alpha"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.sub", "name": "sub", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "symbolic_helper": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_helper", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "t": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.t", "name": "t", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "t", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.t", "name": "t", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "take": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.take", "name": "take", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "index"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "take", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.take", "name": "take", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "tan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.tan", "name": "tan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tan", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.tan", "name": "tan", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "tanh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.tanh", "name": "tanh", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tanh", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.tanh", "name": "tanh", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "tanhshrink": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.tanhshrink", "name": "tanhshrink", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tanhshrink", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.tanhshrink", "name": "tanhshrink", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "tensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["g", "data", "dtype", "device", "requires_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.tensor", "name": "tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["g", "data", "dtype", "device", "requires_grad"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.tensor", "name": "tensor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "threshold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "threshold", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.threshold", "name": "threshold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "threshold", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "threshold", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.threshold", "name": "threshold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "to": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["g", "self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.to", "name": "to", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["g", "self", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.to", "name": "to", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "topk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "k", "dim", "largest", "sorted", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.topk", "name": "topk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "k", "dim", "largest", "sorted", "out"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "topk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.topk", "name": "topk", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "transpose": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim0", "dim1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.transpose", "name": "transpose", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim0", "dim1"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transpose", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.transpose", "name": "transpose", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "true_divide": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.true_divide", "name": "true_divide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "true_divide", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.true_divide", "name": "true_divide", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "type_as": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.type_as", "name": "type_as", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type_as", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.type_as", "name": "type_as", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unbind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.unbind", "name": "unbind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unbind", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.unbind", "name": "unbind", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unfold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "dimension", "size", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.unfold", "name": "unfold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "dimension", "size", "step"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unfold", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.unfold", "name": "unfold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unsafe_chunk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "chunks", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.unsafe_chunk", "name": "unsafe_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "chunks", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsafe_chunk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.unsafe_chunk", "name": "unsafe_chunk", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unsafe_split": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_size_or_sizes", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.unsafe_split", "name": "unsafe_split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_size_or_sizes", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsafe_split", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.unsafe_split", "name": "unsafe_split", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unsafe_split_with_sizes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_sizes", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.unsafe_split_with_sizes", "name": "unsafe_split_with_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_sizes", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsafe_split_with_sizes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.unsafe_split_with_sizes", "name": "unsafe_split_with_sizes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unsqueeze": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.unsqueeze", "name": "unsqueeze", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsqueeze", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.unsqueeze", "name": "unsqueeze", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unsupported_complex_operators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.unsupported_complex_operators", "name": "unsupported_complex_operators", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsupported_complex_operators", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.unsupported_complex_operators", "name": "unsupported_complex_operators", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unused": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["g"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9.unused", "name": "unused", "type": null}}, "var": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["g", "input", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.var", "name": "var", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["g", "input", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "var", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.var", "name": "var", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "var_mean": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["g", "input", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.var_mean", "name": "var_mean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["g", "input", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "var_mean", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.var_mean", "name": "var_mean", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "view": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.view", "name": "view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "size"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "view", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.view", "name": "view", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "view_as": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.view_as", "name": "view_as", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "view_as", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.view_as", "name": "view_as", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}, "where": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["g", "condition", "self", "other", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.where", "name": "where", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["g", "condition", "self", "other", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "where", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.where", "name": "where", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "wrap_logical_op_with_cast_to": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["to_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9.wrap_logical_op_with_cast_to", "name": "wrap_logical_op_with_cast_to", "type": null}}, "wrap_logical_op_with_negation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset9.wrap_logical_op_with_negation", "name": "wrap_logical_op_with_negation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_logical_op_with_negation", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "zero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.zero", "name": "zero", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zero", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.zero", "name": "zero", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "zeros": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["g", "sizes", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.zeros", "name": "zeros", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["g", "sizes", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.zeros", "name": "zeros", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "zeros_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["g", "input", "dtype", "layout", "device", "pin_memory", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset9.zeros_like", "name": "zeros_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["g", "input", "dtype", "layout", "device", "pin_memory", "memory_format"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros_like", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset9.zeros_like", "name": "zeros_like", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\symbolic_opset9.py"}