{"data_mtime": 1755656862, "dep_lines": [30, 15, 26, 29, 31, 8, 12, 13, 21, 27, 8, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 24, 10], "dep_prios": [25, 5, 25, 25, 25, 10, 5, 5, 5, 25, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 25, 20], "dependencies": ["git.objects.submodule.base", "git.objects.util", "git.refs.reference", "git.objects.blob", "git.objects.tree", "os.path", "git.exc", "git.util", "git.types", "git.repo", "os", "typing", "builtins", "_frozen_importlib", "abc", "git.db", "git.diff", "git.objects.commit", "git.objects.tag", "git.refs", "git.refs.symbolic", "git.repo.base"], "hash": "2bb04804aca5189bfc86e97afd8e7618a5940abc", "id": "git.objects.base", "ignore_all": true, "interface_hash": "5e38cb19f0a1e561232be7d2163c199305c627f0", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\base.py", "plugin_data": null, "size": 10285, "suppressed": ["gitdb.typ", "gitdb.base", "gitdb"], "version_id": "1.15.0"}