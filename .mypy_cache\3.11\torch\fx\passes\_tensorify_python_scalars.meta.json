{"data_mtime": 1755649448, "dep_lines": [20, 28, 30, 31, 32, 12, 13, 14, 16, 19, 25, 29, 11, 15, 16, 18, 1, 3, 4, 5, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 7], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 20, 5, 5, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["torch.fx.experimental.symbolic_shapes", "torch.fx.passes.runtime_assert", "torch.utils._sympy.interp", "torch.utils._sympy.reference", "torch.utils._sympy.symbol", "torch._dynamo.exc", "torch._dynamo.symbolic_convert", "torch._dynamo.utils", "torch._subclasses.fake_tensor", "torch.fx._utils", "torch.fx.graph_module", "torch.fx.proxy", "torch.fx", "torch._prims_common", "torch._subclasses", "torch._utils_internal", "__future__", "logging", "os", "typing", "torch", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._logging", "torch._logging._internal", "torch._ops", "torch.fx._compatibility", "torch.fx.experimental", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "torch.utils._python_dispatch"], "hash": "c6725121d88b4ab11572454318e660cc6456cea7", "id": "torch.fx.passes._tensorify_python_scalars", "ignore_all": true, "interface_hash": "a7024f3293f3ef684643a7e9b688ef4e0843cae7", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\fx\\passes\\_tensorify_python_scalars.py", "plugin_data": null, "size": 16466, "suppressed": ["sympy.logic.boolalg", "sympy"], "version_id": "1.15.0"}