{"data_mtime": 1755649448, "dep_lines": [18, 25, 29, 35, 12, 17, 19, 20, 21, 22, 24, 27, 27, 23, 24, 27, 1, 2, 3, 4, 5, 6, 7, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 520, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 20, 20, 10, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 10], "dependencies": ["torch._inductor.runtime.triton_heuristics", "torch.utils._sympy.functions", "torch._inductor.codegen.common", "torch._inductor.codegen.wrapper", "torch._higher_order_ops.triton_kernel_wrap", "torch._inductor.codecache", "torch._inductor.select_algorithm", "torch._inductor.utils", "torch._inductor.virtualized", "torch._library.triton", "torch.utils._pytree", "torch._inductor.config", "torch._inductor.ir", "torch.fx", "torch.utils", "torch._inductor", "dataclasses", "functools", "logging", "operator", "textwrap", "collections", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._C._VariableFunctions", "torch._higher_order_ops", "torch._inductor.graph", "torch._inductor.runtime", "torch._ops", "torch._tensor", "torch.fx._symbolic_trace", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.interpreter", "torch.fx.node", "torch.fx.proxy", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils._ordered_set"], "hash": "cdcc794b7018b441082d31fcf6d45dd280b9ef99", "id": "torch._inductor.codegen.wrapper_fxir", "ignore_all": true, "interface_hash": "9db45fb17fad5db59f6cc689eb418f227d4efa41", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\wrapper_fxir.py", "plugin_data": null, "size": 25306, "suppressed": ["triton.runtime", "sympy"], "version_id": "1.15.0"}