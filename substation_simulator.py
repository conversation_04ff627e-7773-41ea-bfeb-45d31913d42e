import streamlit as st
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import time
from datetime import datetime, timedelta
import random
from sklearn.preprocessing import MinMaxScaler

# Set page config
st.set_page_config(
    page_title="Substation Equipment Predictive Maintenance Simulator",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    .equipment-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        margin: 0.5rem 0;
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #1f77b4;
    }
    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    .alert-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Load the trained LSTM model
@st.cache_resource
def load_lstm_model():
    """Load the trained LSTM model"""
    try:
        # Define the model architecture (same as in lstm_fixed.py)
        class MultiTaskLSTM(nn.Module):
            def __init__(self, input_size, hidden_size=128, num_layers=3, 
                         dropout=0.2, bidirectional=True, n_classes=4):
                super(MultiTaskLSTM, self).__init__()
                
                self.input_size = input_size
                self.hidden_size = hidden_size
                self.num_layers = num_layers
                self.bidirectional = bidirectional
                self.n_classes = n_classes
                
                # LSTM layers with dropout
                self.lstm = nn.LSTM(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    dropout=dropout if num_layers > 1 else 0,
                    bidirectional=bidirectional,
                    batch_first=True
                )
                
                # Calculate LSTM output size
                lstm_output_size = hidden_size * 2 if bidirectional else hidden_size
                
                # Attention mechanism
                self.attention = nn.MultiheadAttention(
                    embed_dim=lstm_output_size,
                    num_heads=8,
                    dropout=dropout,
                    batch_first=True
                )
                
                # Feature extraction layers
                self.feature_layers = nn.Sequential(
                    nn.Linear(lstm_output_size, 256),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(256, 128),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                )
                
                # Regression head for degradation factor prediction
                self.regression_head = nn.Sequential(
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(64, 1),
                    nn.Sigmoid()  # Degradation factor between 0 and 1
                )
                
                # Classification head for health status
                self.classification_head = nn.Sequential(
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(64, n_classes)
                )
                
                # Equipment type embedding (optional)
                self.equipment_embedding = nn.Embedding(3, 16)  # 3 equipment types
                
            def forward(self, x, equipment_type_ids=None):
                # LSTM forward pass
                lstm_out, _ = self.lstm(x)
                
                # Apply attention
                attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
                
                # Use the last timestep output for prediction
                last_output = attn_out[:, -1, :]
                
                # Optional: Add equipment type embedding
                if equipment_type_ids is not None:
                    equipment_emb = self.equipment_embedding(equipment_type_ids)
                    last_output = torch.cat([last_output, equipment_emb], dim=1)
                
                # Feature extraction
                features = self.feature_layers(last_output)
                
                # Multi-task outputs
                degradation_pred = self.regression_head(features)
                health_class_pred = self.classification_head(features)
                
                return degradation_pred.squeeze(), health_class_pred
        
        # Initialize model
        model = MultiTaskLSTM(input_size=29)
        
        # Try to load trained weights
        try:
            checkpoint = torch.load('trained_lstm_model.pth', map_location='cpu', weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            st.success("✅ Loaded trained LSTM model successfully!")
        except FileNotFoundError:
            st.warning("⚠️ No trained model found. Using randomly initialized model for demonstration.")
        
        model.eval()
        return model
    
    except Exception as e:
        st.error(f"❌ Error loading model: {e}")
        return None

# Equipment simulation classes
class EquipmentSimulator:
    """Base class for equipment simulation"""
    
    def __init__(self, equipment_id, equipment_type):
        self.equipment_id = equipment_id
        self.equipment_type = equipment_type
        self.scaler = MinMaxScaler()
        self.feature_names = [
            'load_factor', 'ambient_temp_c', 'hotspot_temp_c', 'insulation_resistance_mohm',
            'power_factor', 'moisture_content_ppm', 'oil_acidity_mg_koh_g', 'degradation_factor',
            'H2_ppm', 'CH4_ppm', 'C2H2_ppm', 'C2H4_ppm', 'C2H6_ppm', 'CO_ppm', 'CO2_ppm',
            'operation_count', 'contact_resistance_microohm', 'opening_time_ms', 'closing_time_ms',
            'sf6_pressure_bar', 'sf6_purity_percent', 'partial_discharge_pc', 'humidity_percent',
            'primary_current_a', 'burden_load_pu', 'winding_temp_c', 'capacitance_pf',
            'ratio_error_percent', 'phase_angle_error_min'
        ]
    
    def generate_normal_data(self, hours=168):
        """Generate normal operating data"""
        timestamps = pd.date_range(start=datetime.now() - timedelta(hours=hours),
                                 periods=hours, freq='h')
        
        # Base normal values
        data = {
            'timestamp': timestamps,
            'equipment_id': self.equipment_id,
            'equipment_type': self.equipment_type,
            'degradation_factor': np.random.uniform(0.1, 0.3, hours),  # Low degradation
        }
        
        # Add equipment-specific features
        data.update(self._generate_equipment_features(hours, degradation_mode='normal'))
        
        return pd.DataFrame(data)
    
    def generate_degrading_data(self, hours=168):
        """Generate degrading equipment data"""
        timestamps = pd.date_range(start=datetime.now() - timedelta(hours=hours),
                                 periods=hours, freq='h')
        
        # Progressive degradation
        degradation_trend = np.linspace(0.3, 0.8, hours) + np.random.normal(0, 0.05, hours)
        degradation_trend = np.clip(degradation_trend, 0, 1)
        
        data = {
            'timestamp': timestamps,
            'equipment_id': self.equipment_id,
            'equipment_type': self.equipment_type,
            'degradation_factor': degradation_trend,
        }
        
        # Add equipment-specific features
        data.update(self._generate_equipment_features(hours, degradation_mode='degrading'))
        
        return pd.DataFrame(data)
    
    def _generate_equipment_features(self, hours, degradation_mode='normal'):
        """Generate equipment-specific features - to be overridden by subclasses"""
        # Default implementation with random values
        features = {}
        for feature in self.feature_names[1:-1]:  # Skip timestamp and degradation_factor
            if degradation_mode == 'normal':
                features[feature] = np.random.uniform(0.2, 0.8, hours)
            else:
                # Degrading equipment shows more extreme values
                features[feature] = np.random.uniform(0.1, 0.9, hours)
        
        return features

class TransformerSimulator(EquipmentSimulator):
    """Power transformer simulator"""
    
    def _generate_equipment_features(self, hours, degradation_mode='normal'):
        base_degradation = 0.2 if degradation_mode == 'normal' else 0.6
        
        # Transformer-specific features
        features = {
            'load_factor': np.random.uniform(0.4, 0.9, hours),
            'ambient_temp_c': 20 + 15 * np.sin(np.linspace(0, 4*np.pi, hours)) + np.random.normal(0, 2, hours),
            'hotspot_temp_c': 60 + 20 * base_degradation + np.random.normal(0, 5, hours),
            'insulation_resistance_mohm': 5000 * (1 - base_degradation) + np.random.normal(0, 500, hours),
            'power_factor': 0.002 + 0.008 * base_degradation + np.random.normal(0, 0.001, hours),
            'moisture_content_ppm': 5 + 15 * base_degradation + np.random.normal(0, 2, hours),
            'oil_acidity_mg_koh_g': 0.01 + 0.1 * base_degradation + np.random.normal(0, 0.01, hours),
        }
        
        # DGA (Dissolved Gas Analysis) - key indicators for transformers
        features.update({
            'H2_ppm': 20 + 100 * base_degradation + np.random.normal(0, 10, hours),
            'CH4_ppm': 10 + 50 * base_degradation + np.random.normal(0, 5, hours),
            'C2H2_ppm': 1 + 10 * base_degradation + np.random.normal(0, 2, hours),
            'C2H4_ppm': 5 + 30 * base_degradation + np.random.normal(0, 5, hours),
            'C2H6_ppm': 3 + 20 * base_degradation + np.random.normal(0, 3, hours),
            'CO_ppm': 200 + 500 * base_degradation + np.random.normal(0, 50, hours),
            'CO2_ppm': 5000 + 3000 * base_degradation + np.random.normal(0, 500, hours),
        })
        
        # Fill non-applicable features with NaN or default values
        non_applicable = ['operation_count', 'contact_resistance_microohm', 'opening_time_ms', 
                         'closing_time_ms', 'sf6_pressure_bar', 'sf6_purity_percent',
                         'primary_current_a', 'burden_load_pu', 'winding_temp_c', 
                         'capacitance_pf', 'ratio_error_percent', 'phase_angle_error_min']
        
        for feature in non_applicable:
            features[feature] = np.full(hours, 0.5)  # Neutral values for non-applicable features
        
        features['partial_discharge_pc'] = 5 + 20 * base_degradation + np.random.normal(0, 3, hours)
        features['humidity_percent'] = 40 + 30 * np.random.random(hours)

        return features

class CircuitBreakerSimulator(EquipmentSimulator):
    """Circuit breaker simulator"""

    def _generate_equipment_features(self, hours, degradation_mode='normal'):
        base_degradation = 0.2 if degradation_mode == 'normal' else 0.6

        # Circuit breaker specific features
        features = {
            'operation_count': 10000 + 5000 * base_degradation + np.random.normal(0, 1000, hours),
            'contact_resistance_microohm': 30 + 50 * base_degradation + np.random.normal(0, 10, hours),
            'opening_time_ms': 45 + 15 * base_degradation + np.random.normal(0, 5, hours),
            'closing_time_ms': 75 + 25 * base_degradation + np.random.normal(0, 8, hours),
            'sf6_pressure_bar': 6.0 - 1.0 * base_degradation + np.random.normal(0, 0.2, hours),
            'sf6_purity_percent': 98 - 5 * base_degradation + np.random.normal(0, 1, hours),
            'partial_discharge_pc': 5 + 20 * base_degradation + np.random.normal(0, 3, hours),
            'humidity_percent': 40 + 30 * np.random.random(hours),
        }

        # Common features
        features.update({
            'ambient_temp_c': 20 + 15 * np.sin(np.linspace(0, 4*np.pi, hours)) + np.random.normal(0, 2, hours),
            'insulation_resistance_mohm': 8000 * (1 - base_degradation) + np.random.normal(0, 800, hours),
            'power_factor': 0.001 + 0.005 * base_degradation + np.random.normal(0, 0.0005, hours),
        })

        # Fill non-applicable features
        non_applicable = ['load_factor', 'hotspot_temp_c', 'moisture_content_ppm', 'oil_acidity_mg_koh_g',
                         'H2_ppm', 'CH4_ppm', 'C2H2_ppm', 'C2H4_ppm', 'C2H6_ppm', 'CO_ppm', 'CO2_ppm',
                         'primary_current_a', 'burden_load_pu', 'winding_temp_c', 'capacitance_pf',
                         'ratio_error_percent', 'phase_angle_error_min']

        for feature in non_applicable:
            features[feature] = np.full(hours, 0.5)

        return features

class CurrentTransformerSimulator(EquipmentSimulator):
    """Current transformer simulator"""

    def _generate_equipment_features(self, hours, degradation_mode='normal'):
        base_degradation = 0.2 if degradation_mode == 'normal' else 0.6

        # CT specific features
        features = {
            'primary_current_a': 400 + 200 * np.random.random(hours),
            'burden_load_pu': 0.3 + 0.4 * np.random.random(hours),
            'winding_temp_c': 30 + 20 * base_degradation + np.random.normal(0, 5, hours),
            'capacitance_pf': 200 + 50 * base_degradation + np.random.normal(0, 20, hours),
            'ratio_error_percent': 0.001 + 0.01 * base_degradation + np.random.normal(0, 0.002, hours),
            'phase_angle_error_min': 0.05 + 0.2 * base_degradation + np.random.normal(0, 0.02, hours),
            'partial_discharge_pc': 3 + 15 * base_degradation + np.random.normal(0, 2, hours),
        }

        # Common features
        features.update({
            'ambient_temp_c': 20 + 15 * np.sin(np.linspace(0, 4*np.pi, hours)) + np.random.normal(0, 2, hours),
            'insulation_resistance_mohm': 6000 * (1 - base_degradation) + np.random.normal(0, 600, hours),
            'power_factor': 0.002 + 0.008 * base_degradation + np.random.normal(0, 0.001, hours),
            'humidity_percent': 40 + 30 * np.random.random(hours),
        })

        # Fill non-applicable features
        non_applicable = ['load_factor', 'hotspot_temp_c', 'moisture_content_ppm', 'oil_acidity_mg_koh_g',
                         'H2_ppm', 'CH4_ppm', 'C2H2_ppm', 'C2H4_ppm', 'C2H6_ppm', 'CO_ppm', 'CO2_ppm',
                         'operation_count', 'contact_resistance_microohm', 'opening_time_ms', 'closing_time_ms',
                         'sf6_pressure_bar', 'sf6_purity_percent']

        for feature in non_applicable:
            features[feature] = np.full(hours, 0.5)

        return features

# Prediction and analysis functions
def prepare_lstm_input(data_df):
    """Prepare data for LSTM input"""
    # Select numeric features (excluding timestamp, equipment_id, equipment_type)
    exclude_cols = ['timestamp', 'equipment_id', 'equipment_type']
    feature_cols = [col for col in data_df.columns if col not in exclude_cols]

    # Extract features
    features = data_df[feature_cols].values

    # Normalize features
    scaler = MinMaxScaler()
    scaled_features = scaler.fit_transform(features)

    # Create sequence (use all data as one sequence)
    if len(scaled_features) >= 168:
        # Use last 168 hours
        sequence = scaled_features[-168:]
    else:
        # Pad with zeros if less than 168 hours
        padding = np.zeros((168 - len(scaled_features), scaled_features.shape[1]))
        sequence = np.vstack([padding, scaled_features])

    # Add batch dimension
    sequence = sequence.reshape(1, 168, -1)

    return torch.FloatTensor(sequence), scaler

def predict_degradation(model, data_df):
    """Predict degradation using the LSTM model"""
    if model is None:
        return None, None

    try:
        # Prepare input
        lstm_input, scaler = prepare_lstm_input(data_df)

        # Make prediction
        with torch.no_grad():
            outputs = model(lstm_input)
            if isinstance(outputs, tuple):
                degradation_pred, health_class_pred = outputs
                # Convert health classification to probabilities
                health_probs = torch.softmax(health_class_pred, dim=1)
                return degradation_pred.item(), health_probs.numpy()[0]
            else:
                return outputs.item(), None

    except Exception as e:
        st.error(f"Prediction error: {e}")
        return None, None

def get_health_status(degradation_factor):
    """Convert degradation factor to health status"""
    if degradation_factor < 0.3:
        return "Healthy", "success"
    elif degradation_factor < 0.6:
        return "Degrading", "warning"
    elif degradation_factor < 0.8:
        return "Critical", "error"
    else:
        return "Failed", "error"

def create_degradation_chart(data_df, predictions=None):
    """Create degradation trend chart"""
    fig = go.Figure()

    # Historical degradation
    fig.add_trace(go.Scatter(
        x=data_df['timestamp'],
        y=data_df['degradation_factor'],
        mode='lines+markers',
        name='Historical Degradation',
        line=dict(color='blue', width=2),
        marker=dict(size=4)
    ))

    # Predicted degradation
    if predictions:
        future_time = data_df['timestamp'].iloc[-1] + timedelta(hours=1)
        fig.add_trace(go.Scatter(
            x=[data_df['timestamp'].iloc[-1], future_time],
            y=[data_df['degradation_factor'].iloc[-1], predictions],
            mode='lines+markers',
            name='LSTM Prediction',
            line=dict(color='red', width=3, dash='dash'),
            marker=dict(size=8, symbol='diamond')
        ))

    # Add health zones
    fig.add_hline(y=0.3, line_dash="dot", line_color="green",
                  annotation_text="Healthy Threshold")
    fig.add_hline(y=0.6, line_dash="dot", line_color="orange",
                  annotation_text="Degrading Threshold")
    fig.add_hline(y=0.8, line_dash="dot", line_color="red",
                  annotation_text="Critical Threshold")

    fig.update_layout(
        title="Equipment Degradation Trend",
        xaxis_title="Time",
        yaxis_title="Degradation Factor",
        yaxis=dict(range=[0, 1]),
        hovermode='x unified',
        template='plotly_white'
    )

    return fig

# Main Streamlit App
def main():
    # Header
    st.markdown('<h1 class="main-header">⚡ Substation Equipment Predictive Maintenance Simulator</h1>',
                unsafe_allow_html=True)

    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem; color: #666;">
        <p>Interactive demonstration of LSTM-based predictive maintenance for substation equipment</p>
        <p>🔧 Power Transformers | ⚡ Circuit Breakers | 📊 Current Transformers</p>
    </div>
    """, unsafe_allow_html=True)

    # Load model
    model = load_lstm_model()

    # Sidebar controls
    st.sidebar.header("🎛️ Simulation Controls")

    # Equipment selection
    equipment_type = st.sidebar.selectbox(
        "Select Equipment Type",
        ["Power Transformer", "Circuit Breaker", "Current Transformer"],
        help="Choose the type of substation equipment to simulate"
    )

    equipment_id = st.sidebar.text_input(
        "Equipment ID",
        value=f"{equipment_type.replace(' ', '').upper()[:2]}001",
        help="Unique identifier for the equipment"
    )

    # Scenario selection
    scenario = st.sidebar.radio(
        "Operating Scenario",
        ["Normal Operation", "Degrading Equipment", "Custom Scenario"],
        help="Select the equipment condition to simulate"
    )

    # Time range
    hours = st.sidebar.slider(
        "Historical Data (Hours)",
        min_value=24, max_value=168, value=168, step=24,
        help="Amount of historical data to generate (168 hours = 1 week)"
    )

    # Generate data button
    if st.sidebar.button("🔄 Generate New Data", type="primary"):
        st.session_state.data_generated = True
        st.session_state.current_scenario = scenario
        st.session_state.current_equipment = equipment_type
        st.session_state.current_hours = hours
        st.session_state.current_id = equipment_id

    # Initialize session state
    if 'data_generated' not in st.session_state:
        st.session_state.data_generated = False

    # Main content area
    if not st.session_state.data_generated:
        # Welcome screen
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.markdown("""
            <div style="text-align: center; padding: 3rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border-radius: 15px; color: white; margin: 2rem 0;">
                <h2>🚀 Welcome to the Simulator</h2>
                <p>Select your equipment type and scenario from the sidebar, then click "Generate New Data" to begin the simulation.</p>
                <p>The LSTM model will analyze the equipment data and predict future degradation trends.</p>
            </div>
            """, unsafe_allow_html=True)

        # Feature overview
        st.markdown("### 🔍 What This Simulator Does")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            <div class="metric-card">
                <h4>📈 Data Generation</h4>
                <p>Creates realistic sensor data for different equipment types with normal or degrading conditions</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div class="metric-card">
                <h4>🤖 LSTM Prediction</h4>
                <p>Uses trained neural network to predict future equipment degradation based on historical patterns</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown("""
            <div class="metric-card">
                <h4>⚠️ Health Assessment</h4>
                <p>Provides real-time health status and maintenance recommendations based on AI predictions</p>
            </div>
            """, unsafe_allow_html=True)

        return

    # Generate equipment data
    simulator_map = {
        "Power Transformer": TransformerSimulator,
        "Circuit Breaker": CircuitBreakerSimulator,
        "Current Transformer": CurrentTransformerSimulator
    }

    simulator = simulator_map[st.session_state.current_equipment](
        st.session_state.current_id,
        st.session_state.current_equipment.lower().replace(' ', '_')
    )

    # Generate data based on scenario
    if st.session_state.current_scenario == "Normal Operation":
        data_df = simulator.generate_normal_data(st.session_state.current_hours)
    elif st.session_state.current_scenario == "Degrading Equipment":
        data_df = simulator.generate_degrading_data(st.session_state.current_hours)
    else:  # Custom scenario
        # For now, use normal operation - could be extended
        data_df = simulator.generate_normal_data(st.session_state.current_hours)

    # Make prediction
    degradation_pred, health_probs = predict_degradation(model, data_df)

    # Display results
    st.markdown("### 📊 Equipment Analysis Dashboard")

    # Key metrics row
    col1, col2, col3, col4 = st.columns(4)

    current_degradation = data_df['degradation_factor'].iloc[-1]
    health_status, status_type = get_health_status(current_degradation)

    with col1:
        st.metric(
            label="Current Degradation",
            value=f"{current_degradation:.1%}",
            delta=f"{(current_degradation - data_df['degradation_factor'].iloc[0]):.1%}"
        )

    with col2:
        if degradation_pred:
            delta_pred = degradation_pred - current_degradation
            st.metric(
                label="LSTM Prediction",
                value=f"{degradation_pred:.1%}",
                delta=f"{delta_pred:.1%}"
            )
        else:
            st.metric(label="LSTM Prediction", value="N/A")

    with col3:
        st.metric(label="Health Status", value=health_status)

    with col4:
        st.metric(label="Equipment Type", value=st.session_state.current_equipment)

    # Health status alert
    if status_type == "success":
        st.markdown(f'<div class="alert-success">✅ Equipment is operating normally</div>',
                   unsafe_allow_html=True)
    elif status_type == "warning":
        st.markdown(f'<div class="alert-warning">⚠️ Equipment shows signs of degradation - monitor closely</div>',
                   unsafe_allow_html=True)
    else:
        st.markdown(f'<div class="alert-danger">🚨 Equipment requires immediate attention!</div>',
                   unsafe_allow_html=True)

    # Charts
    col1, col2 = st.columns([2, 1])

    with col1:
        # Degradation trend chart
        fig = create_degradation_chart(data_df, degradation_pred)
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        # Health probability chart (if available)
        if health_probs is not None:
            health_labels = ['Healthy', 'Degrading', 'Critical', 'Failed']
            fig_pie = px.pie(
                values=health_probs,
                names=health_labels,
                title="Health Classification Probabilities",
                color_discrete_sequence=px.colors.qualitative.Set3
            )
            st.plotly_chart(fig_pie, use_container_width=True)
        else:
            st.info("Health classification not available")

    # Data table
    with st.expander("📋 View Raw Data"):
        st.dataframe(data_df.tail(24), use_container_width=True)  # Show last 24 hours

    # Equipment-specific insights
    st.markdown("### 🔧 Equipment-Specific Analysis")

    if st.session_state.current_equipment == "Power Transformer":
        col1, col2 = st.columns(2)
        with col1:
            fig_temp = px.line(data_df.tail(48), x='timestamp', y='hotspot_temp_c',
                              title='Hotspot Temperature Trend')
            st.plotly_chart(fig_temp, use_container_width=True)
        with col2:
            fig_gas = px.line(data_df.tail(48), x='timestamp', y=['H2_ppm', 'CH4_ppm', 'C2H2_ppm'],
                             title='Key Gas Concentrations')
            st.plotly_chart(fig_gas, use_container_width=True)

    elif st.session_state.current_equipment == "Circuit Breaker":
        col1, col2 = st.columns(2)
        with col1:
            fig_ops = px.line(data_df.tail(48), x='timestamp', y='operation_count',
                             title='Operation Count Trend')
            st.plotly_chart(fig_ops, use_container_width=True)
        with col2:
            fig_timing = px.line(data_df.tail(48), x='timestamp', y=['opening_time_ms', 'closing_time_ms'],
                                title='Operating Times')
            st.plotly_chart(fig_timing, use_container_width=True)

    else:  # Current Transformer
        col1, col2 = st.columns(2)
        with col1:
            fig_current = px.line(data_df.tail(48), x='timestamp', y='primary_current_a',
                                 title='Primary Current')
            st.plotly_chart(fig_current, use_container_width=True)
        with col2:
            fig_error = px.line(data_df.tail(48), x='timestamp', y='ratio_error_percent',
                               title='Ratio Error')
            st.plotly_chart(fig_error, use_container_width=True)

if __name__ == "__main__":
    main()
