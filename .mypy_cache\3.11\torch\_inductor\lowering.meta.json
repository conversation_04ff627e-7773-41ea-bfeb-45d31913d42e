{"data_mtime": 1755649448, "dep_lines": [23, 23, 42, 43, 45, 6584, 23, 25, 26, 27, 28, 44, 48, 48, 48, 48, 49, 66, 80, 84, 6889, 7051, 7112, 7117, 7123, 7128, 15, 18, 23, 24, 25, 29, 48, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 20], "dep_prios": [10, 20, 5, 5, 5, 5, 20, 10, 5, 5, 5, 5, 10, 10, 5, 10, 5, 5, 5, 25, 5, 5, 10, 10, 10, 10, 5, 5, 20, 10, 20, 5, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["torch.ao.quantization.fx._decomposed", "torch.ao.quantization.fx", "torch.fx.experimental.sym_node", "torch.fx.experimental.symbolic_shapes", "torch.utils._sympy.functions", "torch._inductor.codegen.common", "torch.ao.quantization", "torch.utils._pytree", "torch._dynamo.utils", "torch._higher_order_ops.associative_scan", "torch._higher_order_ops.triton_kernel_wrap", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.inductor_prims", "torch._inductor.ir", "torch._inductor.test_operators", "torch._inductor.decomposition", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor.ops_handler", "torch._higher_order_ops.auto_functionalize", "torch._inductor.comm_lowering", "torch._inductor.kernel", "torch._inductor.quantized_lowerings", "torch._inductor.mkldnn_lowerings", "torch._inductor.jagged_lowerings", "collections.abc", "unittest.mock", "torch.ao", "torch.fx", "torch.utils", "torch._prims_common", "torch._inductor", "__future__", "contextlib", "dataclasses", "functools", "itertools", "logging", "math", "operator", "os", "textwrap", "warnings", "collections", "typing", "typing_extensions", "torch", "builtins", "inspect", "html", "sys", "string", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "copy", "_collections_abc", "_frozen_importlib", "abc", "enum", "torch._C", "torch._C._VariableFunctions", "torch._dynamo", "torch._higher_order_ops", "torch._higher_order_ops._invoke_quant", "torch._higher_order_ops.base_hop", "torch._higher_order_ops.foreach_map", "torch._inductor.codegen", "torch._ops", "torch._prims", "torch._prims.rng_prims", "torch._tensor", "torch.fx.experimental", "torch.fx.node", "torch.types", "torch.xpu"], "hash": "0071e5793fbd2b8359a2cfa6848cfbef3eb573b9", "id": "torch._inductor.lowering", "ignore_all": true, "interface_hash": "96c43021d4e793af76227a31365156f27e337e8a", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\lowering.py", "plugin_data": null, "size": 239215, "suppressed": ["sympy"], "version_id": "1.15.0"}