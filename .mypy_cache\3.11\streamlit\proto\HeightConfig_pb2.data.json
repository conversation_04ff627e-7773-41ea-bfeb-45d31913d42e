{".class": "MypyFile", "_fullname": "streamlit.proto.HeightConfig_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.HeightConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "HeightConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig", "name": "HeightConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.HeightConfig_pb2", "mro": ["streamlit.proto.HeightConfig_pb2.HeightConfig", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.HeightConfig_pb2.HeightConfig", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "height_spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height_spec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pixel_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "pixel_height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_content"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_content"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_stretch"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_stretch"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of HeightConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.HeightConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.HeightConfig_pb2.HeightConfig", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "height_spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height_spec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pixel_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "pixel_height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_content"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_content"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_stretch"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_stretch"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Has<PERSON><PERSON> of HeightConfig", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PIXEL_HEIGHT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.PIXEL_HEIGHT_FIELD_NUMBER", "name": "PIXEL_HEIGHT_FIELD_NUMBER", "type": "builtins.int"}}, "USE_CONTENT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.USE_CONTENT_FIELD_NUMBER", "name": "USE_CONTENT_FIELD_NUMBER", "type": "builtins.int"}}, "USE_STRETCH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.USE_STRETCH_FIELD_NUMBER", "name": "USE_STRETCH_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.HeightConfig_pb2.HeightConfig", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "height_spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height_spec"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of HeightConfig", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "use_stretch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_content"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pixel_height"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "use_stretch", "use_content", "pixel_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "use_stretch", "use_content", "pixel_height"], "arg_types": ["streamlit.proto.HeightConfig_pb2.HeightConfig", "builtins.bool", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HeightConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pixel_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.pixel_height", "name": "pixel_height", "type": "builtins.int"}}, "use_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.use_content", "name": "use_content", "type": "builtins.bool"}}, "use_stretch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.use_stretch", "name": "use_stretch", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.HeightConfig_pb2.HeightConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.HeightConfig_pb2.HeightConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.HeightConfig_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___HeightConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.HeightConfig_pb2.global___HeightConfig", "line": 48, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.HeightConfig_pb2.HeightConfig"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.HeightConfig_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.HeightConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\HeightConfig_pb2.pyi"}