{"data_mtime": 1755656862, "dep_lines": [25, 26, 27, 21, 22, 23, 25, 56, 59, 60, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 38, 702, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19], "dep_prios": [10, 5, 5, 5, 5, 5, 20, 5, 20, 25, 5, 10, 5, 10, 10, 10, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.objects.base", "git.objects.tree", "git.objects.util", "git.cmd", "git.diff", "git.util", "git.objects", "git.types", "git.refs", "git.repo", "collections", "datetime", "io", "logging", "os", "re", "subprocess", "sys", "time", "warnings", "typing", "git", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "configparser", "git.config", "git.db", "git.objects.blob", "git.objects.tag", "git.refs.head", "git.refs.reference", "git.refs.symbolic", "git.repo.base", "types", "typing_extensions"], "hash": "d838c5965e5d4e734fd08062e41376f5d8be5016", "id": "git.objects.commit", "ignore_all": true, "interface_hash": "386af16cc612c00b1fb2d79e8a93b46ea9769060", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\commit.py", "plugin_data": null, "size": 30560, "suppressed": ["gitdb"], "version_id": "1.15.0"}