{"data_mtime": 1755649448, "dep_lines": [20, 34, 35, 51, 55, 56, 57, 60, 83, 89, 97, 17, 18, 19, 24, 39, 49, 50, 64, 69, 71, 73, 74, 81, 82, 95, 99, 100, 108, 15, 16, 17, 68, 70, 72, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 20, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch._export.db.logging", "torch._export.passes.collect_tracepoints_pass", "torch._export.passes.lift_constants_pass", "torch._functorch._aot_autograd.input_output_analysis", "torch._functorch._aot_autograd.schemas", "torch._functorch._aot_autograd.subclass_utils", "torch._functorch._aot_autograd.traced_function_transforms", "torch._functorch._aot_autograd.utils", "torch.fx.experimental.proxy_tensor", "torch.fx.experimental.symbolic_shapes", "torch.utils._sympy.value_ranges", "torch.utils._pytree", "torch._dispatch.python", "torch._dynamo.exc", "torch._export.non_strict_utils", "torch._export.utils", "torch._export.verifier", "torch._export.wrappers", "torch._functorch.aot_autograd", "torch._library.fake_class_registry", "torch._subclasses.fake_tensor", "torch.export._unlift", "torch.export.dynamic_shapes", "torch.export.exported_program", "torch.fx._symbolic_trace", "torch.fx.graph", "torch.export._safeguard", "torch.export._wrapper_utils", "torch.export.graph_signature", "torch._dynamo", "torch.fx", "torch.utils", "torch._guards", "torch._logging", "torch._utils_internal", "dataclasses", "functools", "inspect", "logging", "re", "sys", "time", "warnings", "contextlib", "typing", "torch", "builtins", "os", "html", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "copy", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._export", "torch._library", "torch._subclasses", "torch._tensor", "torch.fx.graph_module", "torch.fx.node", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils._python_dispatch"], "hash": "c2f9117a2e6d548fe9d5535d38d580f8dc3ff340", "id": "torch.export._trace", "ignore_all": true, "interface_hash": "7d087ba4353e67fadec11db055baf737a5a05a7e", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\export\\_trace.py", "plugin_data": null, "size": 90570, "suppressed": [], "version_id": "1.15.0"}