{".class": "MypyFile", "_fullname": "torch.distributed.fsdp", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BackwardPrefetch": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.BackwardPrefetch", "kind": "Gdef"}, "CPUOffload": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.CPUOffload", "kind": "Gdef"}, "CPUOffloadPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_api.CPUOffloadPolicy", "kind": "Gdef"}, "FSDPModule": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "kind": "Gdef"}, "FlatParameter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParameter", "kind": "Gdef", "module_public": false}, "FullOptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.FullOptimStateDictConfig", "kind": "Gdef"}, "FullStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.FullStateDictConfig", "kind": "Gdef"}, "FullyShardedDataParallel": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "kind": "Gdef"}, "LocalOptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.LocalOptimStateDictConfig", "kind": "Gdef"}, "LocalStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.LocalStateDictConfig", "kind": "Gdef"}, "MixedPrecision": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.MixedPrecision", "kind": "Gdef"}, "MixedPrecisionPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "kind": "Gdef"}, "OffloadPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", "kind": "Gdef"}, "OptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.OptimStateDictConfig", "kind": "Gdef"}, "OptimStateKeyType": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType", "kind": "Gdef"}, "ShardedOptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardedOptimStateDictConfig", "kind": "Gdef"}, "ShardedStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardedStateDictConfig", "kind": "Gdef"}, "ShardingStrategy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardingStrategy", "kind": "Gdef"}, "StateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictConfig", "kind": "Gdef"}, "StateDictSettings": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictSettings", "kind": "Gdef"}, "StateDictType": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictType", "kind": "Gdef"}, "UnshardHandle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "fully_shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "kind": "Gdef"}, "register_fsdp_forward_method": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fully_shard.register_fsdp_forward_method", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\__init__.py"}