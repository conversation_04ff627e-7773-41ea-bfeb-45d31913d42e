{".class": "MypyFile", "_fullname": "streamlit.proto.PageLink_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageLink_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageLink_pb2.google", "source_any": null, "type_of_any": 3}}}, "PageLink": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageLink_pb2.PageLink", "name": "PageLink", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.PageLink_pb2.PageLink", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageLink_pb2", "mro": ["streamlit.proto.PageLink_pb2.PageLink", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageLink_pb2.PageLink.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PageLink_pb2.PageLink", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_use_container_width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "external"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "external"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "icon"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "icon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "label"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "label"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "page"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "page"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "page_script_hash"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "page_script_hash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_container_width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of PageLink", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageLink_pb2.google", "source_any": null, "type_of_any": 3}}}, "DISABLED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.DISABLED_FIELD_NUMBER", "name": "DISABLED_FIELD_NUMBER", "type": "builtins.int"}}, "EXTERNAL_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.EXTERNAL_FIELD_NUMBER", "name": "EXTERNAL_FIELD_NUMBER", "type": "builtins.int"}}, "HELP_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.HELP_FIELD_NUMBER", "name": "HELP_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageLink_pb2.PageLink.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PageLink_pb2.PageLink", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_use_container_width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_container_width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Has<PERSON><PERSON> of PageLink", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ICON_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.ICON_FIELD_NUMBER", "name": "ICON_FIELD_NUMBER", "type": "builtins.int"}}, "LABEL_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.LABEL_FIELD_NUMBER", "name": "LABEL_FIELD_NUMBER", "type": "builtins.int"}}, "PAGE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.PAGE_FIELD_NUMBER", "name": "PAGE_FIELD_NUMBER", "type": "builtins.int"}}, "PAGE_SCRIPT_HASH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.PAGE_SCRIPT_HASH_FIELD_NUMBER", "name": "PAGE_SCRIPT_HASH_FIELD_NUMBER", "type": "builtins.int"}}, "USE_CONTAINER_WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.USE_CONTAINER_WIDTH_FIELD_NUMBER", "name": "USE_CONTAINER_WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageLink_pb2.PageLink.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.PageLink_pb2.PageLink", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_use_container_width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of PageLink", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "use_container_width"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "page", "label", "icon", "page_script_hash", "help", "use_container_width", "disabled", "external"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageLink_pb2.PageLink.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "page", "label", "icon", "page_script_hash", "help", "use_container_width", "disabled", "external"], "arg_types": ["streamlit.proto.PageLink_pb2.PageLink", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PageLink", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.disabled", "name": "disabled", "type": "builtins.bool"}}, "external": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.external", "name": "external", "type": "builtins.bool"}}, "help": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.help", "name": "help", "type": "builtins.str"}}, "icon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.icon", "name": "icon", "type": "builtins.str"}}, "label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.label", "name": "label", "type": "builtins.str"}}, "page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.page", "name": "page", "type": "builtins.str"}}, "page_script_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.page_script_hash", "name": "page_script_hash", "type": "builtins.str"}}, "use_container_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageLink_pb2.PageLink.use_container_width", "name": "use_container_width", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageLink_pb2.PageLink.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageLink_pb2.PageLink", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageLink_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageLink_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageLink_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageLink_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageLink_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageLink_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___PageLink": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.PageLink_pb2.global___PageLink", "line": 63, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PageLink_pb2.PageLink"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PageLink_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageLink_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\PageLink_pb2.pyi"}