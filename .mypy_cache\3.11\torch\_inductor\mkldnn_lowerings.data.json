{".class": "MypyFile", "_fullname": "torch._inductor.mkldnn_lowerings", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChoiceCaller": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.ChoiceCaller", "kind": "Gdef"}, "CppGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "kind": "Gdef"}, "CppGroupedGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate", "kind": "Gdef"}, "ExternKernelChoice": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.ExternKernelChoice", "kind": "Gdef"}, "OpsValue": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.OpsValue", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TensorBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.mkldnn_lowerings.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.mkldnn_lowerings.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.mkldnn_lowerings.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.mkldnn_lowerings.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.mkldnn_lowerings.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.mkldnn_lowerings.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.add", "kind": "Gdef"}, "add_needs_realized_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.add_needs_realized_inputs", "kind": "Gdef"}, "aten": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.aten", "kind": "Gdef"}, "autotune_select_algorithm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.autotune_select_algorithm", "kind": "Gdef"}, "codegen_int8_gemm_template_compensation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["use_int8_fast_compensation_path", "input", "_weight_compo", "_x_scale", "_x_zp", "_w_scale", "_x_w_scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.mkldnn_lowerings.codegen_int8_gemm_template_compensation", "name": "codegen_int8_gemm_template_compensation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["use_int8_fast_compensation_path", "input", "_weight_compo", "_x_scale", "_x_zp", "_w_scale", "_x_w_scale"], "arg_types": ["builtins.bool", "torch._inductor.virtualized.OpsValue", "torch._inductor.virtualized.OpsValue", {".class": "UnionType", "items": ["torch._inductor.virtualized.OpsValue", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.virtualized.OpsValue", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.virtualized.OpsValue", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.virtualized.OpsValue", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_int8_gemm_template_compensation", "ret_type": "torch._inductor.virtualized.OpsValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "create_epilogue_with_attr": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.create_epilogue_with_attr", "kind": "Gdef"}, "create_int8_compensation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["W_tensor", "packed_weight", "x_scale", "x_zp", "w_scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.mkldnn_lowerings.create_int8_compensation", "name": "create_int8_compensation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["W_tensor", "packed_weight", "x_scale", "x_zp", "w_scale"], "arg_types": ["torch._tensor.Tensor", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_int8_compensation", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "grouped_gemm_lowering": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["x", "w", "b", "attr", "scalars", "algorithm", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.mkldnn_lowerings.grouped_gemm_lowering", "name": "grouped_gemm_lowering", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["x", "w", "b", "attr", "scalars", "algorithm", "layout"], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "grouped_gemm_lowering", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "mm_args": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_args", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.ops", "kind": "Gdef"}, "permute": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.permute", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "register_lowering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.register_lowering", "kind": "Gdef"}, "register_onednn_fusion_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.mkldnn_lowerings.register_onednn_fusion_ops", "name": "register_onednn_fusion_ops", "type": null}}, "to_dtype": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.to_dtype", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "use_aten_gemm_kernels": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_aten_gemm_kernels", "kind": "Gdef"}, "use_cpp_gemm_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_cpp_gemm_template", "kind": "Gdef"}, "view": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.view", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\mkldnn_lowerings.py"}