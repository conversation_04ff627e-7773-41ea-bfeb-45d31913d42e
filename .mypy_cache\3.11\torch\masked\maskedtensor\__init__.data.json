{".class": "MypyFile", "_fullname": "torch.masked.<PERSON><PERSON>or", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MaskedTensor": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.core.MaskedTensor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.masked.maskedtensor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.masked.maskedtensor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.masked.maskedtensor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.masked.maskedtensor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.masked.maskedtensor.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.masked.maskedtensor.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.masked.maskedtensor.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_apply_native_binary": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.binary._apply_native_binary", "kind": "Gdef"}, "_apply_native_unary": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.unary._apply_native_unary", "kind": "Gdef"}, "_apply_pass_through_fn": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.passthrough._apply_pass_through_fn", "kind": "Gdef"}, "_apply_reduction": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.reductions._apply_reduction", "kind": "Gdef"}, "_is_native_binary": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.binary._is_native_binary", "kind": "Gdef"}, "_is_native_unary": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.unary._is_native_unary", "kind": "Gdef"}, "_is_pass_through_fn": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.passthrough._is_pass_through_fn", "kind": "Gdef"}, "_is_reduction": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.reductions._is_reduction", "kind": "Gdef"}, "is_masked_tensor": {".class": "SymbolTableNode", "cross_ref": "torch.masked.maskedtensor.core.is_masked_tensor", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\masked\\maskedtensor\\__init__.py"}