{"data_mtime": 1755649448, "dep_lines": [24, 24, 24, 24, 16, 11, 13, 14, 15, 17, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 21, 19, 28, 1], "dep_prios": [10, 10, 10, 20, 5, 5, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 20, 5, 5, 25, 20], "dependencies": ["torch.onnx._internal.exporter._errors", "torch.onnx._internal.exporter._schemas", "torch.onnx._internal.exporter._tensors", "torch.onnx._internal.exporter", "collections.abc", "__future__", "copy", "inspect", "logging", "typing", "torch", "builtins", "collections", "warnings", "operator", "itertools", "pprint", "math", "string", "torch.distributed._functional_collectives", "types", "os", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "torch.onnx.errors", "typing_extensions", "contextlib"], "hash": "d9159a5cb32bcaa21ca455c5ceeef3bf24a45398", "id": "torch.onnx._internal.exporter._building", "ignore_all": true, "interface_hash": "d618f97bc000b298d5632b1ad70a583cd285d921", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_building.py", "plugin_data": null, "size": 31568, "suppressed": ["onnxscript.ir", "onnxscript", "onnx", "traitlets.utils.warnings"], "version_id": "1.15.0"}