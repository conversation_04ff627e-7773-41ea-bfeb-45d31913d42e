{".class": "MypyFile", "_fullname": "narwhals._constants", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EPOCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.EPOCH", "name": "EPOCH", "type": "datetime.datetime"}}, "EPOCH_YEAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.EPOCH_YEAR", "name": "EPOCH_YEAR", "type": "builtins.int"}}, "MS_PER_MINUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.MS_PER_MINUTE", "name": "MS_PER_MINUTE", "type": "builtins.int"}}, "MS_PER_SECOND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.MS_PER_SECOND", "name": "MS_PER_SECOND", "type": "builtins.int"}}, "NS_PER_MICROSECOND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.NS_PER_MICROSECOND", "name": "NS_PER_MICROSECOND", "type": "builtins.int"}}, "NS_PER_MILLISECOND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.NS_PER_MILLISECOND", "name": "NS_PER_MILLISECOND", "type": "builtins.int"}}, "NS_PER_MINUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.NS_PER_MINUTE", "name": "NS_PER_MINUTE", "type": "builtins.int"}}, "NS_PER_SECOND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.NS_PER_SECOND", "name": "NS_PER_SECOND", "type": "builtins.int"}}, "SECONDS_PER_DAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.SECONDS_PER_DAY", "name": "SECONDS_PER_DAY", "type": "builtins.int"}}, "SECONDS_PER_MINUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.SECONDS_PER_MINUTE", "name": "SECONDS_PER_MINUTE", "type": "builtins.int"}}, "US_PER_MINUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.US_PER_MINUTE", "name": "US_PER_MINUTE", "type": "builtins.int"}}, "US_PER_SECOND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "narwhals._constants.US_PER_SECOND", "name": "US_PER_SECOND", "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dt": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\narwhals\\_constants.py"}