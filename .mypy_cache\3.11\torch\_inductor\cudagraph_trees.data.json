{".class": "MypyFile", "_fullname": "torch._inductor.cudagraph_trees", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"SymInt\" and \"Generator\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.SymInt", "torch._C.Generator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.<subclass of \"SymInt\" and \"Generator\">", "name": "<subclass of \"SymInt\" and \"Generator\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "torch._inductor.cudagraph_trees.<subclass of \"SymInt\" and \"Generator\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.<subclass of \"SymInt\" and \"Generator\">", "torch.SymInt", "torch._C.Generator", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"Tensor\" and \"Generator\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._tensor.Tensor", "torch._C.Generator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.<subclass of \"Tensor\" and \"Generator\">", "name": "<subclass of \"Tensor\" and \"Generator\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "torch._inductor.cudagraph_trees.<subclass of \"Tensor\" and \"Generator\">", "has_param_spec_type": false, "metaclass_type": "torch._C._TensorMeta", "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.<subclass of \"Tensor\" and \"Generator\">", "torch._tensor.Tensor", "torch._<PERSON><PERSON>", "torch._C.Generator", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"int\" and \"Generator\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int", "torch._C.Generator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.<subclass of \"int\" and \"Generator\">", "name": "<subclass of \"int\" and \"Generator\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "torch._inductor.cudagraph_trees.<subclass of \"int\" and \"Generator\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.<subclass of \"int\" and \"Generator\">", "builtins.int", "torch._C.Generator", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib.AbstractContextManager", "kind": "Gdef"}, "AliasesNewOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.cudagraph_trees.OutputAliasInfo"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.AliasesNewOutput", "name": "AliasesNewOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.AliasesNewOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.AliasesNewOutput", "torch._inductor.cudagraph_trees.OutputAliasInfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.AliasesNewOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["torch._inductor.cudagraph_trees.AliasesNewOutput", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AliasesNewOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "torch._inductor.cudagraph_trees.AliasesNewOutput.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.cudagraph_trees.AliasesNewOutput.index", "name": "index", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.AliasesNewOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.AliasesNewOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AliasesPriorGraphOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.cudagraph_trees.OutputAliasInfo"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.AliasesPriorGraphOutput", "name": "AliasesPriorGraphOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.AliasesPriorGraphOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.AliasesPriorGraphOutput", "torch._inductor.cudagraph_trees.OutputAliasInfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.AliasesPriorGraphOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["torch._inductor.cudagraph_trees.AliasesPriorGraphOutput", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AliasesPriorGraphOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "torch._inductor.cudagraph_trees.AliasesPriorGraphOutput.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.cudagraph_trees.AliasesPriorGraphOutput.index", "name": "index", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.AliasesPriorGraphOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.AliasesPriorGraphOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AllocatorState": {".class": "SymbolTableNode", "cross_ref": "torch._C._cuda_CUDAAllocator_AllocatorState", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CUDAGraphNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode", "name": "CUDAGraphNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "wrapped_function", "id", "parent", "inputs", "cuda_graphs_pool", "device_index", "stack_traces", "stream", "mode", "compile_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "wrapped_function", "id", "parent", "inputs", "cuda_graphs_pool", "device_index", "stack_traces", "stream", "mode", "compile_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_utils.WrappedFunction", "torch._inductor.cudagraph_trees.GraphID", {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.StackTraces"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.cuda.streams.Stream", {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CompilationMode", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._guards.CompileId", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_first_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "outputs", "static_input_persistent_storage_ptrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._add_first_outputs", "name": "_add_first_outputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "outputs", "static_input_persistent_storage_ptrs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, {".class": "Instance", "args": ["builtins.int", "torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_first_outputs of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_allocate_and_copy_recording_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._allocate_and_copy_recording_inputs", "name": "_allocate_and_copy_recording_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_allocate_and_copy_recording_inputs of CUDAGraphNode", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_liveness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["indices", "output_refs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._check_liveness", "name": "_check_liveness", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["indices", "output_refs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_liveness of CUDAGraphNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._check_liveness", "name": "_check_liveness", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["indices", "output_refs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_liveness of CUDAGraphNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_copy_inputs_and_remove_from_src": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dsts", "srcs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._copy_inputs_and_remove_from_src", "name": "_copy_inputs_and_remove_from_src", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dsts", "srcs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_inputs_and_remove_from_src of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_different_indices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["prev", "curr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._get_different_indices", "name": "_get_different_indices", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["prev", "curr"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_different_indices of CUDAGraphNode", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._get_different_indices", "name": "_get_different_indices", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["prev", "curr"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_different_indices of CUDAGraphNode", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_liveness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["weakrefs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._get_liveness", "name": "_get_liveness", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["weakrefs"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_liveness of CUDAGraphNode", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._get_liveness", "name": "_get_liveness", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["weakrefs"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_liveness of CUDAGraphNode", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_initialize_cached_tensors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._initialize_cached_tensors", "name": "_initialize_cached_tensors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_initialize_cached_tensors of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_alias_of_live_recorded_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._is_alias_of_live_recorded_tensor", "name": "_is_alias_of_live_recorded_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "t"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_alias_of_live_recorded_tensor of CUDAGraphNode", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_cuda_graph_recorded_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._is_cuda_graph_recorded_tensor", "name": "_is_cuda_graph_recorded_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "t"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_cuda_graph_recorded_tensor of CUDAGraphNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mark_prior_graph_output_as_aliased": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._mark_prior_graph_output_as_aliased", "name": "_mark_prior_graph_output_as_aliased", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mark_prior_graph_output_as_aliased of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._parent", "name": "_parent", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_path_from_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._path_from_root", "name": "_path_from_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_from_root of CUDAGraphNode", "ret_type": {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._path_from_root", "name": "_path_from_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_from_root of CUDAGraphNode", "ret_type": {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_path_to_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._path_to_root", "name": "_path_to_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_to_root of CUDAGraphNode", "ret_type": {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._path_to_root", "name": "_path_to_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_to_root of CUDAGraphNode", "ret_type": {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_reconstruct_from_tensor_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "metadata", "storage"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._reconstruct_from_tensor_metadata", "name": "_reconstruct_from_tensor_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "metadata", "storage"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["torch.storage.UntypedStorage", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reconstruct_from_tensor_metadata of CUDAGraphNode", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._record", "name": "_record", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.ModelType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_record of CUDAGraphNode", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tensor_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "ignore_storage_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._tensor_metadata", "name": "_tensor_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "ignore_storage_offset"], "arg_types": ["torch._tensor.Tensor", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tensor_metadata of CUDAGraphNode", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode._tensor_metadata", "name": "_tensor_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "ignore_storage_offset"], "arg_types": ["torch._tensor.Tensor", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tensor_metadata of CUDAGraphNode", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_child": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "function_id", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.add_child", "name": "add_child", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "function_id", "node"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_utils.FunctionID", "torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_child of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_outputs_are_dead": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.all_outputs_are_dead", "name": "all_outputs_are_dead", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_outputs_are_dead of CUDAGraphNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cached_tensor_outputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.cached_tensor_outputs", "name": "cached_tensor_outputs", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "check_invariants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.check_invariants", "name": "check_invariants", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_invariants of CUDAGraphNode", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._inductor.cudagraph_utils.CheckInvariantStatus", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_static_inputs_are_stable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.check_static_inputs_are_stable", "name": "check_static_inputs_are_stable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_static_inputs_are_stable of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "checkpointed_caching_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.checkpointed_caching_state", "name": "checkpointed_caching_state", "type": {".class": "UnionType", "items": ["torch._C._cuda_CUDAAllocator_AllocatorState", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "children": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.children", "name": "children", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID", {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "clear_path_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.clear_path_state", "name": "clear_path_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_path_state of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_storage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.create_storage", "name": "create_storage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_storage of CUDAGraphNode", "ret_type": "torch.types.Storage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cuda_graphs_pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.cuda_graphs_pool", "name": "cuda_graphs_pool", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "cudagraph_managed_idxs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.cudagraph_managed_idxs", "name": "cudagraph_managed_idxs", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "data_ptrs_dead_since_invocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.data_ptrs_dead_since_invocation", "name": "data_ptrs_dead_since_invocation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_ptrs_dead_since_invocation of CUDAGraphNode", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug_assert_invariants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "expected_liveness", "newly_dead"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.debug_assert_invariants", "name": "debug_assert_invariants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "expected_liveness", "newly_dead"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_assert_invariants of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug_check_invariants_after_invocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.debug_check_invariants_after_invocation", "name": "debug_check_invariants_after_invocation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_check_invariants_after_invocation of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug_check_invariants_before_invocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.debug_check_invariants_before_invocation", "name": "debug_check_invariants_before_invocation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_check_invariants_before_invocation of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "device": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.device", "name": "device", "type": "builtins.int"}}, "expanded_dims": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.expanded_dims", "name": "expanded_dims", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "expected_dead_indices_after_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.expected_dead_indices_after_graph", "name": "expected_dead_indices_after_graph", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "expected_dead_indices_before_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.expected_dead_indices_before_graph", "name": "expected_dead_indices_before_graph", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_output_refcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.get_output_refcount", "name": "get_output_refcount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_output_refcount of CUDAGraphNode", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.graph", "name": "graph", "type": {".class": "UnionType", "items": ["torch.cuda.graphs.CUDAGraph", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.id", "name": "id", "type": "torch._inductor.cudagraph_trees.GraphID"}}, "live_cudagraph_managed_path_refs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.live_cudagraph_managed_path_refs", "name": "live_cudagraph_managed_path_refs", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "live_indices_after_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.live_indices_after_graph", "name": "live_indices_after_graph", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.PathOutputIndex"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "non_managed_static_input_idxs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.non_managed_static_input_idxs", "name": "non_managed_static_input_idxs", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "non_static_input_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.non_static_input_idx", "name": "non_static_input_idx", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "num_descendants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.num_descendants", "name": "num_descendants", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "num_descendants of CUDAGraphNode", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_storage_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.output_storage_alias", "name": "output_storage_alias", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.OutputAliasInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "outputs_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.outputs_metadata", "name": "outputs_metadata", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "outputs_weakrefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.outputs_weakrefs", "name": "outputs_weakrefs", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.parent", "name": "parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parent of CUDAGraphNode", "ret_type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.parent", "name": "parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parent of CUDAGraphNode", "ret_type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path_live_weakrefs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.path_live_weakrefs", "name": "path_live_weakrefs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_live_weakrefs of CUDAGraphNode", "ret_type": {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "path_stacktraces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.path_stacktraces", "name": "path_stacktraces", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.StackTraces"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "path_weakrefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.path_weakrefs", "name": "path_weakrefs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "prepare_alias_info_for_tensor_construction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "out_alias_info", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.prepare_alias_info_for_tensor_construction", "name": "prepare_alias_info_for_tensor_construction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "out_alias_info", "metadata"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.OutputAliasInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_alias_info_for_tensor_construction of CUDAGraphNode", "ret_type": {".class": "UnionType", "items": ["torch.storage.UntypedStorage", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_storages_for_construction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.prepare_storages_for_construction", "name": "prepare_storages_for_construction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_storages_for_construction of CUDAGraphNode", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.storage.UntypedStorage", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preserved_aliased_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.preserved_aliased_inputs", "name": "preserved_aliased_inputs", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "reconstruct_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.reconstruct_outputs", "name": "reconstruct_outputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reconstruct_outputs of CUDAGraphNode", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reconstructed_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.reconstructed_inputs", "name": "reconstructed_inputs", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "recorded_liveness_after_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.recorded_liveness_after_graph", "name": "recorded_liveness_after_graph", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "recorded_liveness_before_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.recorded_liveness_before_graph", "name": "recorded_liveness_before_graph", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "recording_outputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.recording_outputs", "name": "recording_outputs", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "remove_node_cached_tensors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.remove_node_cached_tensors", "name": "remove_node_cached_tensors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_node_cached_tensors of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_path_cached_tensors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.remove_path_cached_tensors", "name": "remove_path_cached_tensors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_path_cached_tensors of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rerecord_if_static_inputs_change": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.rerecord_if_static_inputs_change", "name": "rerecord_if_static_inputs_change", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "builtins.bool"], "uses_pep604_syntax": false}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of CUDAGraphNode", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_first_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.run_first_inputs", "name": "run_first_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_first_inputs of CUDAGraphNode", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.run_graph", "name": "run_graph", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_graph of CUDAGraphNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stack_traces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.stack_traces", "name": "stack_traces", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.StackTraces"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "static_input_data_ptrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.static_input_data_ptrs", "name": "static_input_data_ptrs", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "static_input_idxs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.static_input_idxs", "name": "static_input_idxs", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "static_inputs_stable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.static_inputs_stable", "name": "static_inputs_stable", "type": "builtins.bool"}}, "static_output_tensors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.static_output_tensors", "name": "static_output_tensors", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.stream", "name": "stream", "type": "torch.cuda.streams.Stream"}}, "tensor_weakrefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.tensor_weakrefs", "name": "tensor_weakrefs", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.utils.weak.TensorWeakRef", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "unaliased_in_all_paths": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.unaliased_in_all_paths", "name": "unaliased_in_all_paths", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "wrapped_function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.wrapped_function", "name": "wrapped_function", "type": "torch._inductor.cudagraph_utils.WrappedFunction"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.CUDAGraphNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.CUDAGraphNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUDAGraphTreeManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "name": "CUDAGraphTreeManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device_index"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager._current_node", "name": "_current_node", "type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_get_cuda_graph_recorded_tensor_checker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager._get_cuda_graph_recorded_tensor_checker", "name": "_get_cuda_graph_recorded_tensor_checker", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cuda_graph_recorded_tensor_checker of CUDAGraphTreeManager", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_node_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager._get_node_id", "name": "_get_node_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_node_id of CUDAGraphTreeManager", "ret_type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.GraphID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_inputs", "function_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager._run", "name": "_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_inputs", "function_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.cudagraph_utils.FunctionID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run of CUDAGraphTreeManager", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_non_cudagraph_managed_mutation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "function_id", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager._update_non_cudagraph_managed_mutation", "name": "_update_non_cudagraph_managed_mutation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "function_id", "inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "torch._inductor.cudagraph_utils.FunctionID", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_non_cudagraph_managed_mutation of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "model", "inputs", "static_input_idxs", "stack_traces", "mode", "constants", "placeholders", "mutated_input_idxs", "compile_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.add_function", "name": "add_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "model", "inputs", "static_input_idxs", "stack_traces", "mode", "constants", "placeholders", "mutated_input_idxs", "compile_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.ModelType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.StackTraces"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch._inductor.cudagraph_trees.CompilationMode", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.PlaceholderInfo"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["torch._guards.CompileId", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_function of CUDAGraphTreeManager", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.ModelType"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_checkpoint_execution_state_in_allocator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.apply_checkpoint_execution_state_in_allocator", "name": "apply_checkpoint_execution_state_in_allocator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_checkpoint_execution_state_in_allocator of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_start_new_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.can_start_new_generation", "name": "can_start_new_generation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_start_new_generation of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_warn_on_unable_to_start_executing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.check_warn_on_unable_to_start_executing", "name": "check_warn_on_unable_to_start_executing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "torch._inductor.cudagraph_utils.FunctionID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_warn_on_unable_to_start_executing of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_current_path_state_and_set_to_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.clear_current_path_state_and_set_to_none", "name": "clear_current_path_state_and_set_to_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_current_path_state_and_set_to_none of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.compile_id", "name": "compile_id", "type": {".class": "UnionType", "items": ["torch._guards.CompileId", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cuda_graphs_thread_pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.cuda_graphs_thread_pool", "name": "cuda_graphs_thread_pool", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "current_gen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.current_gen", "name": "current_gen", "type": "builtins.int"}}, "current_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.current_node", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.current_node", "name": "current_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_node of CUDAGraphTreeManager", "ret_type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.current_node", "name": "current_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_node of CUDAGraphTreeManager", "ret_type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.current_node", "name": "current_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_node of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "current_node", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_node of CUDAGraphTreeManager", "ret_type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "dealloc_current_path_weakrefs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.dealloc_current_path_weakrefs", "name": "dealloc_current_path_weakrefs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dealloc_current_path_weakrefs of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug_checkpointing_counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.debug_checkpointing_counter", "name": "debug_checkpointing_counter", "type": "builtins.int"}}, "debug_fail_counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.debug_fail_counter", "name": "debug_fail_counter", "type": "builtins.int"}}, "device_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.device_index", "name": "device_index", "type": "builtins.int"}}, "disable_invalidate_aliases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.disable_invalidate_aliases", "name": "disable_invalidate_aliases", "type": "builtins.bool"}}, "exceed_rerecord_limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node_id", "function_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.exceed_rerecord_limit", "name": "exceed_rerecord_limit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node_id", "function_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.GraphID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch._inductor.cudagraph_utils.FunctionID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exceed_rerecord_limit of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "new_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.execute_node", "name": "execute_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "new_inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "torch._inductor.cudagraph_trees.CUDAGraphNode", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_node of CUDAGraphTreeManager", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_dealloc_msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["stack_trace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.format_dealloc_msg", "name": "format_dealloc_msg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["stack_trace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_dealloc_msg of CUDAGraphTreeManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.format_dealloc_msg", "name": "format_dealloc_msg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["stack_trace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_dealloc_msg of CUDAGraphTreeManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "func_counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.func_counter", "name": "func_counter", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "get_curr_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.get_curr_generation", "name": "get_curr_generation", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_curr_generation of CUDAGraphTreeManager", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.get_curr_generation", "name": "get_curr_generation", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_curr_generation of CUDAGraphTreeManager", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_roots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.get_roots", "name": "get_roots", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_roots of CUDAGraphTreeManager", "ret_type": {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.graph", "name": "graph", "type": {".class": "UnionType", "items": ["torch.cuda.graphs.CUDAGraph", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "graph_counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.graph_counter", "name": "graph_counter", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "id_to_compile_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.id_to_compile_id", "name": "id_to_compile_id", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID", {".class": "UnionType", "items": ["torch._guards.CompileId", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "id_to_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.id_to_mode", "name": "id_to_mode", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID", "torch._inductor.cudagraph_trees.CompilationMode"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ids_to_funcs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.ids_to_funcs", "name": "ids_to_funcs", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID", "torch._inductor.cudagraph_utils.WrappedFunction"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ids_to_stack_traces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.ids_to_stack_traces", "name": "ids_to_stack_traces", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.StackTraces"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "in_new_torch_compile_invocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.in_new_torch_compile_invocation", "name": "in_new_torch_compile_invocation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_new_torch_compile_invocation of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_recording": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.in_recording", "name": "in_recording", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_recording of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.in_recording", "name": "in_recording", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_recording of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "in_warmup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.in_warmup", "name": "in_warmup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_warmup of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.in_warmup", "name": "in_warmup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_warmup of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "live_cudagraph_pool_storages_in_curr_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.live_cudagraph_pool_storages_in_curr_execution", "name": "live_cudagraph_pool_storages_in_curr_execution", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "live_cudagraph_pool_storages_in_curr_execution of CUDAGraphTreeManager", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.mode", "name": "mode", "type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CompilationMode", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "new_func_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.new_func_id", "name": "new_func_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_func_id of CUDAGraphTreeManager", "ret_type": "torch._inductor.cudagraph_utils.FunctionID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new_graph_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.new_graph_id", "name": "new_graph_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_graph_id of CUDAGraphTreeManager", "ret_type": "torch._inductor.cudagraph_trees.GraphID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new_warmup_node_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.new_warmup_node_id", "name": "new_warmup_node_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_warmup_node_id of CUDAGraphTreeManager", "ret_type": "torch._inductor.cudagraph_trees.GraphID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "non_cudagraph_managed_mutation_hint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.non_cudagraph_managed_mutation_hint", "name": "non_cudagraph_managed_mutation_hint", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.GraphID", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "num_rerecord": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.num_rerecord", "name": "num_rerecord", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.GraphID", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "path_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.path_state", "name": "path_state", "type": "torch._inductor.cudagraph_trees.ExecutionState"}}, "record_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_inputs", "function_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.record_function", "name": "record_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_inputs", "function_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.cudagraph_utils.FunctionID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record_function of CUDAGraphTreeManager", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "roots": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.roots", "name": "roots", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID", {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.CUDAGraphNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_inputs", "function_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_inputs", "function_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.cudagraph_utils.FunctionID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of CUDAGraphTreeManager", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_eager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_inputs", "function_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.run_eager", "name": "run_eager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_inputs", "function_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.cudagraph_utils.FunctionID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_eager of CUDAGraphTreeManager", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "running_forwards_with_pending_backwards": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.running_forwards_with_pending_backwards", "name": "running_forwards_with_pending_backwards", "type": "builtins.bool"}}, "set_to_running_backward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.set_to_running_backward", "name": "set_to_running_backward", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_to_running_backward of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shutdown of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.stream", "name": "stream", "type": "torch.cuda.streams.Stream"}}, "try_end_curr_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.try_end_curr_execution", "name": "try_end_curr_execution", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_end_curr_execution of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "try_end_curr_recording": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.try_end_curr_recording", "name": "try_end_curr_recording", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "torch._inductor.cudagraph_utils.FunctionID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_end_curr_recording of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "try_end_curr_warmup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.try_end_curr_warmup", "name": "try_end_curr_warmup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function_id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "torch._inductor.cudagraph_utils.FunctionID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_end_curr_warmup of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.update_generation", "name": "update_generation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_generation of CUDAGraphTreeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "user_invoked_mark_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.user_invoked_mark_step", "name": "user_invoked_mark_step", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_invoked_mark_step of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.user_invoked_mark_step", "name": "user_invoked_mark_step", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_invoked_mark_step of CUDAGraphTreeManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "warmed_up_functions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.warmed_up_functions", "name": "warmed_up_functions", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "warmup_node_counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.warmup_node_counter", "name": "warmup_node_counter", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "warned_functions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.warned_functions", "name": "warned_functions", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "warned_mutation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.warned_mutation", "name": "warned_mutation", "type": {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.FunctionID"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUDAWarmupNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode", "name": "CUDAWarmupNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.CUDAWarmupNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "wrapped_function", "parent", "cuda_graphs_pool", "existing_cuda_graph", "device_index", "stack_traces", "stream", "already_warm", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "wrapped_function", "parent", "cuda_graphs_pool", "existing_cuda_graph", "device_index", "stack_traces", "stream", "already_warm", "id"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAWarmupNode", "torch._inductor.cudagraph_utils.WrappedFunction", {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["torch.cuda.graphs.CUDAGraph", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.StackTraces"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.cuda.streams.Stream", "builtins.bool", "torch._inductor.cudagraph_trees.GraphID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDAWarmupNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_cuda_graph_recorded_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode._is_cuda_graph_recorded_tensor", "name": "_is_cuda_graph_recorded_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "t"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAWarmupNode", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_cuda_graph_recorded_tensor of CUDAWarmupNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_path_from_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode._path_from_root", "name": "_path_from_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAWarmupNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_from_root of CUDAWarmupNode", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode._path_from_root", "name": "_path_from_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAWarmupNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_from_root of CUDAWarmupNode", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "all_outputs_are_dead": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.all_outputs_are_dead", "name": "all_outputs_are_dead", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAWarmupNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_outputs_are_dead of CUDAWarmupNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "already_warm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.already_warm", "name": "already_warm", "type": "builtins.bool"}}, "cuda_graphs_pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.cuda_graphs_pool", "name": "cuda_graphs_pool", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "device_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.device_index", "name": "device_index", "type": "builtins.int"}}, "existing_cuda_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.existing_cuda_graph", "name": "existing_cuda_graph", "type": {".class": "UnionType", "items": ["torch.cuda.graphs.CUDAGraph", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "has_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.has_run", "name": "has_run", "type": "builtins.bool"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.id", "name": "id", "type": "torch._inductor.cudagraph_trees.GraphID"}}, "outputs_weakrefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.outputs_weakrefs", "name": "outputs_weakrefs", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.parent", "name": "parent", "type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphNode", "torch._inductor.cudagraph_trees.CUDAWarmupNode", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "path_live_weakrefs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.path_live_weakrefs", "name": "path_live_weakrefs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAWarmupNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_live_weakrefs of CUDAWarmupNode", "ret_type": {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_inputs"], "arg_types": ["torch._inductor.cudagraph_trees.CUDAWarmupNode", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of CUDAWarmupNode", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stack_traces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.stack_traces", "name": "stack_traces", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.StackTraces"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.stream", "name": "stream", "type": "torch.cuda.streams.Stream"}}, "tensor_weakrefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.tensor_weakrefs", "name": "tensor_weakrefs", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.utils.weak.TensorWeakRef", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "wrapped_function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.wrapped_function", "name": "wrapped_function", "type": "torch._inductor.cudagraph_utils.WrappedFunction"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.CUDAWarmupNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.CUDAWarmupNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CallbackTrigger": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.callback.CallbackTrigger", "kind": "Gdef"}, "CheckInvariantStatus": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.CheckInvariantStatus", "kind": "Gdef"}, "CompilationMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.CompilationMode", "name": "CompilationMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch._inductor.cudagraph_trees.CompilationMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.CompilationMode", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BACKWARD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.CompilationMode.BACKWARD", "name": "BACKWARD", "type": "enum.auto"}}, "FORWARD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.CompilationMode.FORWARD", "name": "FORWARD", "type": "enum.auto"}}, "INFERENCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.CompilationMode.INFERENCE", "name": "INFERENCE", "type": "enum.auto"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.CompilationMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.CompilationMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompileId": {".class": "SymbolTableNode", "cross_ref": "torch._guards.CompileId", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "ExecutionState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.ExecutionState", "name": "ExecutionState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch._inductor.cudagraph_trees.ExecutionState", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.ExecutionState", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "EXECUTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.ExecutionState.EXECUTION", "name": "EXECUTION", "type": "enum.auto"}}, "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.ExecutionState.NONE", "name": "NONE", "type": "enum.auto"}}, "RECORDING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.ExecutionState.RECORDING", "name": "RECORDING", "type": "enum.auto"}}, "WARMUP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.ExecutionState.WARMUP", "name": "WARMUP", "type": "enum.auto"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.ExecutionState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.ExecutionState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionID": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.FunctionID", "kind": "Gdef"}, "GenerationTracker": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.mutation_guard.GenerationTracker", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "GraphID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.GraphID", "name": "GraphID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.GraphID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 125, "name": "id", "type": "builtins.int"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.GraphID", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.cudagraph_trees.GraphID.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.GraphID.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id"], "arg_types": ["torch._inductor.cudagraph_trees.GraphID", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GraphID", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.cudagraph_trees.GraphID.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "id"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.GraphID.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["id"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of GraphID", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.cudagraph_trees.GraphID.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["id"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of GraphID", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.cudagraph_trees.GraphID.id", "name": "id", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.GraphID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.GraphID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InputList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.InputList", "line": 753, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "InputType": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.InputType", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LevelList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.LevelList", "line": 755, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "MarkStepBox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.MarkStepBox", "name": "MarkStepBox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.MarkStepBox", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.MarkStepBox", "builtins.object"], "names": {".class": "SymbolTable", "mark_step_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.MarkStepBox.mark_step_counter", "name": "mark_step_counter", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.MarkStepBox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.MarkStepBox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelType": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.ModelType", "kind": "Gdef"}, "NBytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.NBytes", "line": 97, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "OutputAliasInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.OutputAliasInfo", "name": "OutputAliasInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.OutputAliasInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.OutputAliasInfo", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.OutputAliasInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.OutputAliasInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OutputList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.OutputList", "line": 754, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "OutputType": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.OutputType", "kind": "Gdef"}, "PathLiveness": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.PathLiveness", "line": 603, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "PathOutputIndex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.PathOutputIndex", "line": 600, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "PlaceholderInfo": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.PlaceholderInfo", "kind": "Gdef"}, "S": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.S", "name": "S", "upper_bound": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper", "values": [], "variance": 0}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "StackTraces": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.StackTraces", "line": 605, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "StorageDataPtr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.StorageDataPtr", "line": 96, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "StorageWeakRef": {".class": "SymbolTableNode", "cross_ref": "torch.multiprocessing.reductions.StorageWeakRef", "kind": "Gdef"}, "StorageWeakRefPointer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefPointer", "line": 95, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "StorageWeakRefWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper", "name": "StorageWeakRefWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of StorageWeakRefWrapper", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "inp", "extra_ref_check"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "inp", "extra_ref_check"], "arg_types": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "UnionType", "items": ["torch._tensor.Tensor", "torch.storage.UntypedStorage"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StorageWeakRefWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of StorageWeakRefWrapper", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_data_ptr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper._data_ptr", "name": "_data_ptr", "type": "builtins.int"}}, "data_ptr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.data_ptr", "name": "data_ptr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_ptr of StorageWeakRefWrapper", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.expired", "name": "expired", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expired of StorageWeakRefWrapper", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extra_ref_check": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.extra_ref_check", "name": "extra_ref_check", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "from_weakref_and_data_ptr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "cdata", "data_ptr", "extra_ref_check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.from_weakref_and_data_ptr", "name": "from_weakref_and_data_ptr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "cdata", "data_ptr", "extra_ref_check"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_weakref_and_data_ptr of StorageWeakRefWrapper", "ret_type": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.from_weakref_and_data_ptr", "name": "from_weakref_and_data_ptr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "cdata", "data_ptr", "extra_ref_check"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_weakref_and_data_ptr of StorageWeakRefWrapper", "ret_type": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ref": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.ref", "name": "ref", "type": "torch.multiprocessing.reductions.StorageWeakRef"}}, "remove_extra_reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.remove_extra_reference", "name": "remove_extra_reference", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_extra_reference of StorageWeakRefWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "storage_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.storage_ref", "name": "storage_ref", "type": {".class": "UnionType", "items": ["torch.multiprocessing.reductions.StorageWeakRef", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "swap_weakref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cdata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.swap_weakref", "name": "swap_weakref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cdata"], "arg_types": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swap_weakref of StorageWeakRefWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.StorageWeakRefWrapper", "values": [], "variance": 0}, "slots": ["_data_ptr", "extra_ref_check", "ref"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "TensorWeakRef": {".class": "SymbolTableNode", "cross_ref": "torch.utils.weak.TensorWeakRef", "kind": "Gdef"}, "TreeManagerContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer", "name": "TreeManagerContainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees.TreeManagerContainer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device_index"], "arg_types": ["torch._inductor.cudagraph_trees.TreeManagerContainer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TreeManagerContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_finalize_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer._finalize_tensor", "name": "_finalize_tensor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.TreeManagerContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finalize_tensor of TreeManagerContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_finalize_tree_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer._finalize_tree_manager", "name": "_finalize_tree_manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.TreeManagerContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finalize_tree_manager of TreeManagerContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_strong_reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.add_strong_reference", "name": "add_strong_reference", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": ["torch._inductor.cudagraph_trees.TreeManagerContainer", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_strong_reference of TreeManagerContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "device_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.device_index", "name": "device_index", "type": "builtins.int"}}, "finalize_cudagraphify_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.finalize_cudagraphify_fn", "name": "finalize_cudagraphify_fn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.TreeManagerContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize_cudagraphify_fn of TreeManagerContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tree_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.get_tree_manager", "name": "get_tree_manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.cudagraph_trees.TreeManagerContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tree_manager of TreeManagerContainer", "ret_type": "torch._inductor.cudagraph_trees.CUDAGraphTreeManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.graph", "name": "graph", "type": {".class": "UnionType", "items": ["torch.cuda.graphs.CUDAGraph", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "live_cudagraphify_fns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.live_cudagraphify_fns", "name": "live_cudagraphify_fns", "type": "builtins.int"}}, "live_storages_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.live_storages_count", "name": "live_storages_count", "type": "builtins.int"}}, "lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.lock", "name": "lock", "type": "_thread.LockType"}}, "tree_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.tree_manager", "name": "tree_manager", "type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees.TreeManagerContainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees.TreeManagerContainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UnaliasedStorage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.UnaliasedStorage", "name": "UnaliasedStorage", "type": "torch._inductor.cudagraph_trees._UnaliasedStorage"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UntypedStorage": {".class": "SymbolTableNode", "cross_ref": "torch.storage.UntypedStorage", "kind": "Gdef"}, "WrappedFunction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.WrappedFunction", "kind": "Gdef"}, "_UnaliasedStorage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.cudagraph_trees.OutputAliasInfo"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.cudagraph_trees._UnaliasedStorage", "name": "_UnaliasedStorage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees._UnaliasedStorage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.cudagraph_trees", "mro": ["torch._inductor.cudagraph_trees._UnaliasedStorage", "torch._inductor.cudagraph_trees.OutputAliasInfo", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.cudagraph_trees._UnaliasedStorage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.cudagraph_trees._UnaliasedStorage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.cudagraph_trees.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.cudagraph_trees.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.cudagraph_trees.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.cudagraph_trees.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.cudagraph_trees.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.cudagraph_trees.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_bool": {".class": "SymbolTableNode", "cross_ref": "builtins.bool", "kind": "Gdef"}, "_set_cached_tensors_enabled": {".class": "SymbolTableNode", "cross_ref": "torch._C._set_cached_tensors_enabled", "kind": "Gdef"}, "_use_cuda_memory_pool_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["device", "mem_pool", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.cudagraph_trees._use_cuda_memory_pool_manager", "name": "_use_cuda_memory_pool_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["device", "mem_pool", "stream"], "arg_types": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "torch.cuda.streams.Stream"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_use_cuda_memory_pool_manager", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees._use_cuda_memory_pool_manager", "name": "_use_cuda_memory_pool_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["device", "mem_pool", "stream"], "arg_types": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "torch.cuda.streams.Stream"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_use_cuda_memory_pool_manager", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "align_inputs_from_check_idxs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.align_inputs_from_check_idxs", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "auto": {".class": "SymbolTableNode", "cross_ref": "enum.auto", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "check_for_mutation": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.check_for_mutation", "kind": "Gdef"}, "check_memory_pool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["device", "pool_id", "live_storages_ptrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.check_memory_pool", "name": "check_memory_pool", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["device", "pool_id", "live_storages_ptrs"], "arg_types": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_memory_pool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_cublas_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.cudagraph_trees.clear_cublas_manager", "name": "clear_cublas_manager", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_cublas_manager", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.clear_cublas_manager", "name": "clear_cublas_manager", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_cublas_manager", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "clear_cublass_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.clear_cublass_cache", "name": "clear_cublass_cache", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_cublass_cache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "copy_misaligned_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.copy_misaligned_inputs", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "cudagraphify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 3, 3, 3, 5, 5, 5, 5, 5], "arg_names": ["model", "inputs", "static_input_idxs", "device_index", "is_backward", "is_inference", "stack_traces", "constants", "placeholders", "mutated_input_idxs", "compile_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.cudagraphify", "name": "cudagraphify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 3, 3, 5, 5, 5, 5, 5], "arg_names": ["model", "inputs", "static_input_idxs", "device_index", "is_backward", "is_inference", "stack_traces", "constants", "placeholders", "mutated_input_idxs", "compile_id"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.ModelType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_trees.StackTraces"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["torch._inductor.cudagraph_utils.PlaceholderInfo"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["torch._guards.CompileId", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cudagraphify", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.ModelType"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.OutputType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cudagraphify_impl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["model", "inputs", "static_input_idxs", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.cudagraphify_impl", "name": "cudagraphify_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["model", "inputs", "static_input_idxs", "args", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.ModelType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.InputType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cudagraphify_impl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.cudagraph_utils.ModelType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "disable_conv_cache_emptying": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.cudagraph_trees.disable_conv_cache_emptying", "name": "disable_conv_cache_emptying", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_conv_cache_emptying", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.disable_conv_cache_emptying", "name": "disable_conv_cache_emptying", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_conv_cache_emptying", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dynamo_timed": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.dynamo_timed", "kind": "Gdef"}, "dynamo_timed_cudagraph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "compile_id", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.cudagraph_trees.dynamo_timed_cudagraph", "name": "dynamo_timed_cudagraph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "compile_id", "mode"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["torch._guards.CompileId", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CompilationMode", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dynamo_timed_cudagraph", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.dynamo_timed_cudagraph", "name": "dynamo_timed_cudagraph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "compile_id", "mode"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["torch._guards.CompileId", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CompilationMode", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dynamo_timed_cudagraph", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enable_history_recording": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.cudagraph_trees.enable_history_recording", "name": "enable_history_recording", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_history_recording", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.cudagraph_trees.enable_history_recording", "name": "enable_history_recording", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_history_recording", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "format_tb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["frames"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.format_tb", "name": "format_tb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["frames"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_tb", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "gc": {".class": "SymbolTableNode", "cross_ref": "gc", "kind": "Gdef"}, "get_block_addrs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["pool_id", "live_only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.get_block_addrs", "name": "get_block_addrs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pool_id", "live_only"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_block_addrs", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_container": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["device_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.get_container", "name": "get_container", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["device_index"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_container", "ret_type": "torch._inductor.cudagraph_trees.TreeManagerContainer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cudagraph_segments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pool_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.get_cudagraph_segments", "name": "get_cudagraph_segments", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pool_id"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cudagraph_segments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_expanded_dims": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.get_expanded_dims", "kind": "Gdef"}, "get_history_recording": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.get_history_recording", "name": "get_history_recording", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_history_recording", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_input_idxs_to_check": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.compile_fx.get_input_idxs_to_check", "kind": "Gdef"}, "get_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["device_index", "create_if_none_exists"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.get_manager", "name": "get_manager", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["device_index", "create_if_none_exists"], "arg_types": ["builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_manager", "ret_type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.CUDAGraphTreeManager", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_obj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["local", "attr_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.get_obj", "name": "get_obj", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["local", "attr_name"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_obj", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index_expanded_dims": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.index_expanded_dims", "kind": "Gdef"}, "is_cudagraph_capture_sizes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["int_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.is_cudagraph_capture_sizes", "name": "is_cudagraph_capture_sizes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["int_key"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_cudagraph_capture_sizes", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_live": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["weak_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.is_live", "name": "is_live", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["weak_ref"], "arg_types": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_live", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "local": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.local", "name": "local", "type": "_thread._local"}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.cudagraph_trees.log", "name": "log", "type": "logging.Logger"}}, "log_cudagraph_skip_and_bump_counter": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.log_cudagraph_skip_and_bump_counter", "kind": "Gdef"}, "log_data_ptr_mismatch": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.log_data_ptr_mismatch", "kind": "Gdef"}, "map_to_ref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.map_to_ref", "name": "map_to_ref", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map_to_ref", "ret_type": {".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mark_step_begin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.mark_step_begin", "name": "mark_step_begin", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mark_step_begin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_deref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["weak_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.maybe_deref", "name": "maybe_deref", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["weak_ref"], "arg_types": [{".class": "UnionType", "items": ["torch._inductor.cudagraph_trees.StorageWeakRefWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maybe_deref", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_warning_due_to_dynamic_shape": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cudagraph_utils.maybe_warning_due_to_dynamic_shape", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "preserve_rng_state": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.preserve_rng_state", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "remove_unaligned_input_idxs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.remove_unaligned_input_idxs", "kind": "Gdef"}, "reset_cudagraph_trees": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.cudagraph_trees.reset_cudagraph_trees", "name": "reset_cudagraph_trees", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_cudagraph_trees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "static_input": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.compile_fx.static_input", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\cudagraph_trees.py"}