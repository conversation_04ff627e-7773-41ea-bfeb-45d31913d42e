{".class": "MypyFile", "_fullname": "torch._inductor.fx_passes.b2b_gemm", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Arg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Arg", "kind": "Gdef"}, "B2B_GEMM_PASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.b2b_gemm.B2B_GEMM_PASS", "name": "B2B_GEMM_PASS", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "CallFunction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.CallFunction", "kind": "Gdef"}, "ComputedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "ExternKernelChoice": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.ExternKernelChoice", "kind": "Gdef"}, "FixedLayout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.FixedLayout", "kind": "Gdef"}, "FlexibleLayout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.FlexibleLayout", "kind": "Gdef"}, "InputBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.In<PERSON>uffer", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Match", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "PatternMatcherPass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.PatternMatcherPass", "kind": "Gdef"}, "StorageBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.StorageBox", "kind": "Gdef"}, "Subgraph": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Subgraph", "kind": "Gdef"}, "SymbolicGridFn": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.SymbolicGridFn", "kind": "Gdef"}, "TensorBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "TritonTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.TritonTemplate", "kind": "Gdef"}, "TritonTemplateCaller": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.TritonTemplateCaller", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.b2b_gemm.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.b2b_gemm.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.b2b_gemm.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.b2b_gemm.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.b2b_gemm.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.b2b_gemm.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "autotune_select_algorithm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.autotune_select_algorithm", "kind": "Gdef"}, "b2b_gemm_configs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.b2b_gemm.b2b_gemm_configs", "name": "b2b_gemm_configs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "b2b_gemm_grid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": ["M", "P", "meta", "cdiv"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.b2b_gemm.b2b_gemm_grid", "name": "b2b_gemm_grid", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.b2b_gemm.b2b_gemm_grid", "name": "b2b_gemm_grid", "type": "torch._inductor.select_algorithm.SymbolicGridFn"}}}, "b2b_gemm_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["match", "mat1", "mat2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.b2b_gemm.b2b_gemm_handler", "name": "b2b_gemm_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["match", "mat1", "mat2"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "b2b_gemm_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.b2b_gemm.b2b_gemm_handler", "name": "b2b_gemm_handler", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "b2b_gemm_handler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "b2b_gemm_left_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.b2b_gemm.b2b_gemm_left_template", "name": "b2b_gemm_left_template", "type": "torch._inductor.select_algorithm.TritonTemplate"}}, "b2b_gemm_right_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.b2b_gemm.b2b_gemm_right_template", "name": "b2b_gemm_right_template", "type": "torch._inductor.select_algorithm.TritonTemplate"}}, "build_subgraph_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["args", "subgraph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.b2b_gemm.build_subgraph_buffer", "name": "build_subgraph_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["args", "subgraph"], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Subgraph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_subgraph_buffer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ceildiv": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.ceildiv", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "create_placeholder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "dtype", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.b2b_gemm.create_placeholder", "name": "create_placeholder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "dtype", "device"], "arg_types": ["builtins.str", "torch._C.dtype", "torch._C.device"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_placeholder", "ret_type": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "is_b2b_gemm_good_on": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["is_left_assoc", "A_node", "B_node", "C_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.b2b_gemm.is_b2b_gemm_good_on", "name": "is_b2b_gemm_good_on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["is_left_assoc", "A_node", "B_node", "C_node"], "arg_types": ["builtins.bool", "torch.fx.node.Node", "torch.fx.node.Node", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_b2b_gemm_good_on", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_ratio_left": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["M", "N", "O", "P", "m", "n", "o", "p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.b2b_gemm.load_ratio_left", "name": "load_ratio_left", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["M", "N", "O", "P", "m", "n", "o", "p"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_ratio_left", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_ratio_right": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["M", "N", "O", "P", "m", "n", "o", "p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.b2b_gemm.load_ratio_right", "name": "load_ratio_right", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["M", "N", "O", "P", "m", "n", "o", "p"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_ratio_right", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lowerings": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.lowerings", "kind": "Gdef"}, "register_graph_pattern": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.register_graph_pattern", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tree_map": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree.tree_map", "kind": "Gdef"}, "tuned_b2b_gemm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": ["is_left_assoc", "subgraph", "A", "B", "C", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.b2b_gemm.tuned_b2b_gemm", "name": "tuned_b2b_gemm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": ["is_left_assoc", "subgraph", "A", "B", "C", "layout"], "arg_types": ["builtins.bool", "torch._inductor.ir.Subgraph", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuned_b2b_gemm", "ret_type": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unoptimized_b2b_gemm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 3], "arg_names": ["is_left_assoc", "subgraph", "A", "B", "C", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.b2b_gemm.unoptimized_b2b_gemm", "name": "unoptimized_b2b_gemm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 3], "arg_names": ["is_left_assoc", "subgraph", "A", "B", "C", "out"], "arg_types": ["builtins.bool", "torch._inductor.ir.Subgraph", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unoptimized_b2b_gemm", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unoptimized_choice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.b2b_gemm.unoptimized_choice", "name": "unoptimized_choice", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\b2b_gemm.py"}