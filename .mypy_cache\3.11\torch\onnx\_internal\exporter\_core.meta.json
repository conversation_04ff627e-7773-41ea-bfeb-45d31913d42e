{"data_mtime": 1755649448, "dep_lines": [27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 26, 27, 25, 163, 15, 24, 25, 49, 163, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 23, 47, 49, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19, 21, 18], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 10, 20, 5, 10, 20, 25, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 20, 25, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 5, 5], "dependencies": ["torch.onnx._internal.exporter._analysis", "torch.onnx._internal.exporter._building", "torch.onnx._internal.exporter._capture_strategies", "torch.onnx._internal.exporter._constants", "torch.onnx._internal.exporter._dispatching", "torch.onnx._internal.exporter._errors", "torch.onnx._internal.exporter._flags", "torch.onnx._internal.exporter._fx_passes", "torch.onnx._internal.exporter._ir_passes", "torch.onnx._internal.exporter._onnx_program", "torch.onnx._internal.exporter._registration", "torch.onnx._internal.exporter._reporting", "torch.onnx._internal.exporter._tensors", "torch.onnx._internal.exporter._type_casting", "torch.onnx._internal.exporter._verification", "torch.onnx._internal._lazy_import", "torch.onnx._internal.exporter", "torch.export.graph_signature", "torch._subclasses.fake_tensor", "collections.abc", "torch.fx", "torch.export", "numpy.typing", "torch._subclasses", "__future__", "ctypes", "datetime", "inspect", "itertools", "logging", "operator", "pathlib", "textwrap", "traceback", "typing", "torch", "os", "numpy", "builtins", "html", "sys", "string", "pprint", "types", "math", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "numpy._typing", "numpy._typing._dtype_like", "torch._C", "torch._ops", "torch._tensor", "torch.export.exported_program", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.node", "torch.jit", "torch.jit._script", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "a306a9e69c50902dcfc2a2bdba78d0851c36e040", "id": "torch.onnx._internal.exporter._core", "ignore_all": true, "interface_hash": "e40c8d7a959d565f3d9a1391905aac777e335dbf", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_core.py", "plugin_data": null, "size": 68964, "suppressed": ["onnxscript.evaluator", "onnxscript.ir", "onnxscript"], "version_id": "1.15.0"}