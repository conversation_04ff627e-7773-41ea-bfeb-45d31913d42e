{".class": "MypyFile", "_fullname": "torch.distributed.fsdp._unshard_param_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FLAT_PARAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._unshard_param_utils.FLAT_PARAM", "name": "FLAT_PARAM", "type": "builtins.str"}}, "FlatParamHandle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParamHandle", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "HandleTrainingState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.HandleTrainingState", "kind": "Gdef"}, "TrainingState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.TrainingState", "kind": "Gdef"}, "_FSDPState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._FSDPState", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._unshard_param_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._unshard_param_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._unshard_param_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._unshard_param_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._unshard_param_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._unshard_param_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_deregister_flat_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._unshard_param_utils._deregister_flat_param", "name": "_deregister_flat_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "module"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deregister_flat_param", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deregister_orig_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._unshard_param_utils._deregister_orig_params", "name": "_deregister_orig_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "module"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deregister_orig_params", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_module_fsdp_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._get_module_fsdp_state", "kind": "Gdef"}, "_has_fsdp_params": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._has_fsdp_params", "kind": "Gdef"}, "_lazy_init": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._lazy_init", "kind": "Gdef"}, "_module_handle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._module_handle", "kind": "Gdef"}, "_p_assert": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._p_assert", "kind": "Gdef"}, "_register_flat_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._unshard_param_utils._register_flat_param", "name": "_register_flat_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "module"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_flat_param", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register_orig_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._unshard_param_utils._register_orig_params", "name": "_register_orig_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "module"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_orig_params", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reset_flat_param_grad_info_if_needed": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._reset_flat_param_grad_info_if_needed", "kind": "Gdef"}, "_reshard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._reshard", "kind": "Gdef"}, "_reshard_grads": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._reshard_grads", "kind": "Gdef"}, "_unflatten_as_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._unshard_param_utils._unflatten_as_params", "name": "_unflatten_as_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "module"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unflatten_as_params", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._unshard_param_utils._unflatten_as_params", "name": "_unflatten_as_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "module"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unflatten_as_params", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_unshard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._unshard", "kind": "Gdef"}, "_unshard_fsdp_state_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._unshard_param_utils._unshard_fsdp_state_params", "name": "_unshard_fsdp_state_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unshard_fsdp_state_params", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._unshard_param_utils._unshard_fsdp_state_params", "name": "_unshard_fsdp_state_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unshard_fsdp_state_params", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_unshard_grads": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._unshard_grads", "kind": "Gdef"}, "_unshard_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "recurse", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._unshard_param_utils._unshard_params", "name": "_unshard_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "recurse", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.nn.modules.module.Module", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unshard_params", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._unshard_param_utils._unshard_params", "name": "_unshard_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "recurse", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.nn.modules.module.Module", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unshard_params", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_unshard_params_for_summon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._unshard_param_utils._unshard_params_for_summon", "name": "_unshard_params_for_summon", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unshard_params_for_summon", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._unshard_param_utils._unshard_params_for_summon", "name": "_unshard_params_for_summon", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["module", "state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unshard_params_for_summon", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_validate_unshard_params_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._unshard_param_utils._validate_unshard_params_args", "name": "_validate_unshard_params_args", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_unshard_params_args", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_writeback_to_local_shard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["handle", "writeback_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._unshard_param_utils._writeback_to_local_shard", "name": "_writeback_to_local_shard", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["handle", "writeback_grad"], "arg_types": ["torch.distributed.fsdp._flat_param.FlatParamHandle", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_writeback_to_local_shard", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._unshard_param_utils._writeback_to_local_shard", "name": "_writeback_to_local_shard", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["handle", "writeback_grad"], "arg_types": ["torch.distributed.fsdp._flat_param.FlatParamHandle", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_writeback_to_local_shard", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "traversal_utils": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._traversal_utils", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_unshard_param_utils.py"}