{"data_mtime": 1755649448, "dep_lines": [8, 8, 8, 8, 2, 4, 5, 6, 12, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14], "dep_prios": [10, 10, 10, 20, 5, 10, 10, 5, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 25], "dependencies": ["torch.onnx._internal.exporter._analysis", "torch.onnx._internal.exporter._registration", "torch.onnx._internal.exporter._verification", "torch.onnx._internal.exporter", "__future__", "dataclasses", "re", "typing", "os", "torch", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch.export", "torch.export.exported_program"], "hash": "0d28a31709e26c880faef0854b3a92ca52db185c", "id": "torch.onnx._internal.exporter._reporting", "ignore_all": true, "interface_hash": "a43a1a45360d2dd3f145df1adf4397ca46d03630", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_reporting.py", "plugin_data": null, "size": 7603, "suppressed": ["onnxscript"], "version_id": "1.15.0"}