{".class": "MypyFile", "_fullname": "torch.onnx._internal.fx.registration", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ONNXFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.registration.ONNXFunction", "name": "ONNXFunction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "onnx_function", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.registration.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.registration.onnxscript", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "op_full_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "is_custom", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "is_complex", "type": "builtins.bool"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch.onnx._internal.fx.registration", "mro": ["torch.onnx._internal.fx.registration.ONNXFunction", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "onnx_function", "op_full_name", "is_custom", "is_complex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "onnx_function", "op_full_name", "is_custom", "is_complex"], "arg_types": ["torch.onnx._internal.fx.registration.ONNXFunction", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.registration.onnxscript", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ONNXFunction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "onnx_function"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "op_full_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_custom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_complex"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["onnx_function", "op_full_name", "is_custom", "is_complex"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["onnx_function", "op_full_name", "is_custom", "is_complex"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.registration.onnxscript", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ONNXFunction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["onnx_function", "op_full_name", "is_custom", "is_complex"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.registration.onnxscript", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ONNXFunction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "is_complex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.is_complex", "name": "is_complex", "type": "builtins.bool"}}, "is_custom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.is_custom", "name": "is_custom", "type": "builtins.bool"}}, "onnx_function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.onnx_function", "name": "onnx_function", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.registration.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.registration.onnxscript", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}}}, "op_full_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.op_full_name", "name": "op_full_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.registration.ONNXFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.registration.ONNXFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OpName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.registration.OpName", "name": "OpName", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.registration.OpName", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 41, "name": "namespace", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "op_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "overload", "type": "builtins.str"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch.onnx._internal.fx.registration", "mro": ["torch.onnx._internal.fx.registration.OpName", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.OpName.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "namespace", "op_name", "overload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.registration.OpName.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "namespace", "op_name", "overload"], "arg_types": ["torch.onnx._internal.fx.registration.OpName", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OpName", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.onnx._internal.fx.registration.OpName.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "namespace"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "op_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overload"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["namespace", "op_name", "overload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.onnx._internal.fx.registration.OpName.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["namespace", "op_name", "overload"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.OpName.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["namespace", "op_name", "overload"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "from_builtin_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "builtin_function"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.registration.OpName.from_builtin_function", "name": "from_builtin_function", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "builtin_function"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.registration.OpName"}, "types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_builtin_function of OpName", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.registration.OpName.from_builtin_function", "name": "from_builtin_function", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "builtin_function"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.registration.OpName"}, "types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_builtin_function of OpName", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_name_parts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "namespace", "op_name", "overload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.registration.OpName.from_name_parts", "name": "from_name_parts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "namespace", "op_name", "overload"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.registration.OpName"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_name_parts of OpName", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.registration.OpName.from_name_parts", "name": "from_name_parts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "namespace", "op_name", "overload"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.registration.OpName"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_name_parts of OpName", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_op_overload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "op_overload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.registration.OpName.from_op_overload", "name": "from_op_overload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "op_overload"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.registration.OpName"}, {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_op_overload of OpName", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.registration.OpName.from_op_overload", "name": "from_op_overload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "op_overload"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.registration.OpName"}, {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_op_overload of OpName", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_qualified_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "qualified_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.registration.OpName.from_qualified_name", "name": "from_qualified_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "qualified_name"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.registration.OpName"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_qualified_name of OpName", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.registration.OpName.from_qualified_name", "name": "from_qualified_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "qualified_name"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.registration.OpName"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_qualified_name of OpName", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.OpName.namespace", "name": "namespace", "type": "builtins.str"}}, "op_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.OpName.op_name", "name": "op_name", "type": "builtins.str"}}, "overload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.onnx._internal.fx.registration.OpName.overload", "name": "overload", "type": "builtins.str"}}, "qualified_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.registration.OpName.qualified_name", "name": "qualified_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.registration.OpName"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qualified_name of OpName", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.registration.OpName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.registration.OpName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.registration.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.registration.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.registration.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.registration.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.registration.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.registration.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "onnxscript": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.registration.onnxscript", "name": "onnxscript", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.registration.onnxscript", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\registration.py"}