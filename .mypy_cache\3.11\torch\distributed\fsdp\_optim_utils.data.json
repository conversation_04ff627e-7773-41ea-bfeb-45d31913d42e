{".class": "MypyFile", "_fullname": "torch.distributed.fsdp._optim_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.DTensor", "kind": "Gdef"}, "ExitStack": {".class": "SymbolTableNode", "cross_ref": "contextlib.ExitStack", "kind": "Gdef"}, "FSDPParamInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo", "name": "FSDPParamInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "state", "type": "torch.distributed.fsdp._common_utils._FSDPState"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "handle", "type": "torch.distributed.fsdp._flat_param.FlatParamHandle"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "param_indices", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 58, "name": "param_requires_grad", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.distributed.fsdp._optim_utils", "mro": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "state", "handle", "param_indices", "param_requires_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "state", "handle", "param_indices", "param_requires_grad"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", "torch.distributed.fsdp._common_utils._FSDPState", "torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FSDPParamInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "handle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "param_indices"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "param_requires_grad"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["state", "handle", "param_indices", "param_requires_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["state", "handle", "param_indices", "param_requires_grad"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FSDPParamInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["state", "handle", "param_indices", "param_requires_grad"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FSDPParamInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "handle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.handle", "name": "handle", "type": "torch.distributed.fsdp._flat_param.FlatParamHandle"}}, "param_indices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.param_indices", "name": "param_indices", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "param_requires_grad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.param_requires_grad", "name": "param_requires_grad", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.state", "name": "state", "type": "torch.distributed.fsdp._common_utils._FSDPState"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils.FSDPParamInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp._optim_utils.FSDPParamInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlatParamHandle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParamHandle", "kind": "Gdef"}, "FlatParameter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParameter", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Replicate": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Replicate", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "ShardedTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.api.ShardedTensor", "kind": "Gdef"}, "ShardingStrategy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardingStrategy", "kind": "Gdef"}, "SimpleProfiler": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._debug_utils.SimpleProfiler", "kind": "Gdef"}, "StateDictSettings": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictSettings", "kind": "Gdef"}, "StateDictType": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictType", "kind": "Gdef"}, "StateInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._optim_utils.StateInfo", "name": "StateInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1285, "name": "tensors", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1286, "name": "scalar_tensors", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1287, "name": "non_tensors", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.distributed.fsdp._optim_utils", "mro": ["torch.distributed.fsdp._optim_utils.StateInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tensors", "scalar_tensors", "non_tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tensors", "scalar_tensors", "non_tensors"], "arg_types": ["torch.distributed.fsdp._optim_utils.StateInfo", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StateInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tensors"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scalar_tensors"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "non_tensors"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["tensors", "scalar_tensors", "non_tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["tensors", "scalar_tensors", "non_tensors"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StateInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["tensors", "scalar_tensors", "non_tensors"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StateInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "non_tensors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.non_tensors", "name": "non_tensors", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "scalar_tensors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.scalar_tensors", "name": "scalar_tensors", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tensors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.tensors", "name": "tensors", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils.StateInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp._optim_utils.StateInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_ConsolidatedOptimState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState", "name": "_ConsolidatedOptimState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 88, "name": "tensor_state", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 89, "name": "zero_dim_tensor_state", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 90, "name": "non_tensor_state", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.distributed.fsdp._optim_utils", "mro": ["torch.distributed.fsdp._optim_utils._ConsolidatedOptimState", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "tensor_state", "zero_dim_tensor_state", "non_tensor_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "tensor_state", "zero_dim_tensor_state", "non_tensor_state"], "arg_types": ["torch.distributed.fsdp._optim_utils._ConsolidatedOptimState", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ConsolidatedOptimState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tensor_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero_dim_tensor_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "non_tensor_state"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["tensor_state", "zero_dim_tensor_state", "non_tensor_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["tensor_state", "zero_dim_tensor_state", "non_tensor_state"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _ConsolidatedOptimState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["tensor_state", "zero_dim_tensor_state", "non_tensor_state"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _ConsolidatedOptimState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "non_tensor_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.non_tensor_state", "name": "non_tensor_state", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tensor_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.tensor_state", "name": "tensor_state", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "zero_dim_tensor_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.zero_dim_tensor_state", "name": "zero_dim_tensor_state", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FSDPState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._FSDPState", "kind": "Gdef"}, "_OptimStateKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey", "name": "_OptimStateKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["unflat_param_names", "is_fsdp_managed"]}}, "module_name": "torch.distributed.fsdp._optim_utils", "mro": ["torch.distributed.fsdp._optim_utils._OptimStateKey", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "unflat_param_names"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_fsdp_managed"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "unflat_param_names", "is_fsdp_managed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "unflat_param_names", "is_fsdp_managed"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _OptimStateKey", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _OptimStateKey", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _OptimStateKey", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _OptimStateKey", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "unflat_param_names", "is_fsdp_managed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "unflat_param_names", "is_fsdp_managed"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _OptimStateKey", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._OptimStateKey._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey._source", "name": "_source", "type": "builtins.str"}}, "is_fsdp_managed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey.is_fsdp_managed", "name": "is_fsdp_managed", "type": "builtins.bool"}}, "is_fsdp_managed-redefinition": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey.is_fsdp_managed", "kind": "<PERSON><PERSON><PERSON>"}, "unflat_param_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey.unflat_param_names", "name": "unflat_param_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "unflat_param_names-redefinition": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey.unflat_param_names", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._OptimStateKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": "torch.distributed.fsdp._optim_utils._OptimStateKey"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "_PosDimTensorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo", "name": "_PosDimTensorInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["shape", "dtype"]}}, "module_name": "torch.distributed.fsdp._optim_utils", "mro": ["torch.distributed.fsdp._optim_utils._PosDimTensorInfo", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "shape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dtype"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "shape", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "shape", "dtype"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "torch._<PERSON><PERSON>", "torch._C.dtype"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _PosDimTensorInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _PosDimTensorInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _PosDimTensorInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _PosDimTensorInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "shape", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "shape", "dtype"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "torch._<PERSON><PERSON>", "torch._C.dtype"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _PosDimTensorInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo._source", "name": "_source", "type": "builtins.str"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.dtype", "name": "dtype", "type": "torch._C.dtype"}}, "dtype-redefinition": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.dtype", "kind": "<PERSON><PERSON><PERSON>"}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.shape", "name": "shape", "type": "torch._<PERSON><PERSON>"}}, "shape-redefinition": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.shape", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": "torch.distributed.fsdp._optim_utils._PosDimTensorInfo"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._C.dtype"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._optim_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_allgather_orig_param_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["fsdp_param_info", "gathered_state_info", "input_states", "shard_state", "to_save", "cpu_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._allgather_orig_param_states", "name": "_allgather_orig_param_states", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["fsdp_param_info", "gathered_state_info", "input_states", "shard_state", "to_save", "cpu_offload"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch.distributed.fsdp._optim_utils.StateInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_allgather_orig_param_states", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_allgather_state_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fsdp_state", "input_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._allgather_state_info", "name": "_allgather_state_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fsdp_state", "input_states"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_allgather_state_info", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch.distributed.fsdp._optim_utils.StateInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_to_modules": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._apply_to_modules", "kind": "Gdef"}, "_broadcast_processed_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_state", "optim_state", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._broadcast_processed_state", "name": "_broadcast_processed_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_state", "optim_state", "group"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_broadcast_processed_state", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_broadcast_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_state", "state", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._broadcast_state", "name": "_broadcast_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_state", "state", "group"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_broadcast_state", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_missing_keys_on_rank": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["r0_optim_state_keys", "optim_state_key_to_param_key", "param_key_to_param", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._check_missing_keys_on_rank", "name": "_check_missing_keys_on_rank", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["r0_optim_state_keys", "optim_state_key_to_param_key", "param_key_to_param", "group"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey"}, {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}, "torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_missing_keys_on_rank", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_communicate_optim_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fsdp_param_info", "flat_param_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._communicate_optim_state", "name": "_communicate_optim_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fsdp_param_info", "flat_param_state"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_communicate_optim_state", "ret_type": "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_all_state_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["fsdp_param_info", "gathered_state_info", "input_states", "output_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._convert_all_state_info", "name": "_convert_all_state_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["fsdp_param_info", "gathered_state_info", "input_states", "output_states"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch.distributed.fsdp._optim_utils.StateInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_all_state_info", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_state_with_flat_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["all_optim_state_keys", "optim_state_key_to_param_key", "fqn_to_fsdp_param_info", "optim_state_dict", "to_save", "shard_state", "cpu_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._convert_state_with_flat_params", "name": "_convert_state_with_flat_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["all_optim_state_keys", "optim_state_key_to_param_key", "fqn_to_fsdp_param_info", "optim_state_dict", "to_save", "shard_state", "cpu_offload"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch.distributed.fsdp._optim_utils.FSDPParamInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_state_with_flat_params", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_state_with_orig_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["all_optim_state_keys", "optim_state_key_to_param_key", "fqn_to_fsdp_param_info", "optim_state_dict", "to_save", "shard_state", "cpu_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._convert_state_with_orig_params", "name": "_convert_state_with_orig_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["all_optim_state_keys", "optim_state_key_to_param_key", "fqn_to_fsdp_param_info", "optim_state_dict", "to_save", "shard_state", "cpu_offload"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch.distributed.fsdp._optim_utils.FSDPParamInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_state_with_orig_params", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ext_chunk_dtensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fsdp_extensions._ext_chunk_dtensor", "kind": "Gdef"}, "_ext_chunk_tensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fsdp_extensions._ext_chunk_tensor", "kind": "Gdef"}, "_flatten_non_tensor_optim_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state_name", "non_tensors", "unflat_param_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._flatten_non_tensor_optim_state", "name": "_flatten_non_tensor_optim_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["state_name", "non_tensors", "unflat_param_names"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flatten_non_tensor_optim_state", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flatten_optim_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_param_info", "unflat_osd_state", "unflat_param_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._flatten_optim_state", "name": "_flatten_optim_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_param_info", "unflat_osd_state", "unflat_param_names"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flatten_optim_state", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flatten_optim_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["optim_state_dict", "model", "use_orig_params", "optim", "rank0_only", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._flatten_optim_state_dict", "name": "_flatten_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["optim_state_dict", "model", "use_orig_params", "optim", "rank0_only", "group"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", "builtins.bool", {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flatten_optim_state_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flatten_tensor_optim_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["state_name", "pos_dim_tensors", "unflat_param_names", "unflat_param_shapes", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._flatten_tensor_optim_state", "name": "_flatten_tensor_optim_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["state_name", "pos_dim_tensors", "unflat_param_names", "unflat_param_shapes", "handle"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "torch.distributed.fsdp._flat_param.FlatParamHandle"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flatten_tensor_optim_state", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flatten_zero_dim_tensor_optim_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state_name", "zero_dim_tensors", "unflat_param_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._flatten_zero_dim_tensor_optim_state", "name": "_flatten_zero_dim_tensor_optim_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["state_name", "zero_dim_tensors", "unflat_param_names"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flatten_zero_dim_tensor_optim_state", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_gather_all_orig_param_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["fsdp_param_info", "input_states", "shard_state", "to_save", "cpu_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._gather_all_orig_param_state", "name": "_gather_all_orig_param_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["fsdp_param_info", "input_states", "shard_state", "to_save", "cpu_offload"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gather_all_orig_param_state", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_gather_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._state_dict_utils._gather_state_dict", "kind": "Gdef"}, "_get_flat_param_to_fqn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._get_flat_param_to_fqn", "name": "_get_flat_param_to_fqn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_flat_param_to_fqn", "ret_type": {".class": "Instance", "args": ["torch.distributed.fsdp._flat_param.FlatParameter", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_fqn_to_fsdp_param_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._get_fqn_to_fsdp_param_info", "name": "_get_fqn_to_fsdp_param_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_fqn_to_fsdp_param_info", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch.distributed.fsdp._optim_utils.FSDPParamInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_module_fsdp_state_if_fully_sharded_module": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._get_module_fsdp_state_if_fully_sharded_module", "kind": "Gdef"}, "_get_param_id_to_param_from_optim_input": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["model", "optim_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._get_param_id_to_param_from_optim_input", "name": "_get_param_id_to_param_from_optim_input", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model", "optim_input"], "arg_types": ["torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_param_id_to_param_from_optim_input", "ret_type": {".class": "Instance", "args": ["builtins.int", "torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_param_key_to_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["optim", "model", "is_named_optimizer", "param_to_fqns", "flat_param_to_fqn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._get_param_key_to_param", "name": "_get_param_key_to_param", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["optim", "model", "is_named_optimizer", "param_to_fqns", "flat_param_to_fqn"], "arg_types": ["torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.distributed.fsdp._flat_param.FlatParameter", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_param_key_to_param", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_param_to_fqns": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._get_param_to_fqns", "kind": "Gdef"}, "_get_param_to_param_id_from_optim_input": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["model", "optim_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._get_param_to_param_id_from_optim_input", "name": "_get_param_to_param_id_from_optim_input", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model", "optim_input"], "arg_types": ["torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_param_to_param_id_from_optim_input", "ret_type": {".class": "Instance", "args": ["torch.nn.parameter.Parameter", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_param_to_param_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["optim", "model", "is_named_optimizer", "param_to_fqns", "flat_param_to_fqn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._get_param_to_param_key", "name": "_get_param_to_param_key", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["optim", "model", "is_named_optimizer", "param_to_fqns", "flat_param_to_fqn"], "arg_types": ["torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.distributed.fsdp._flat_param.FlatParameter", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_param_to_param_key", "ret_type": {".class": "Instance", "args": ["torch.nn.parameter.Parameter", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_pg_default_device": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.distributed_c10d._get_pg_default_device", "kind": "Gdef"}, "_is_named_optimizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["optim_state_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._is_named_optimizer", "name": "_is_named_optimizer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["optim_state_dict"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_named_optimizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_zero_dim_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._is_zero_dim_tensor", "name": "_is_zero_dim_tensor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_zero_dim_tensor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lazy_init": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._lazy_init", "kind": "Gdef"}, "_map_param_key_to_optim_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["optim_state_dict", "group", "param_key_to_param", "param_to_fqns", "fqn_to_fsdp_param_info", "merge_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._map_param_key_to_optim_keys", "name": "_map_param_key_to_optim_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["optim_state_dict", "group", "param_key_to_param", "param_to_fqns", "fqn_to_fsdp_param_info", "merge_keys"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch.distributed.fsdp._optim_utils.FSDPParamInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_map_param_key_to_optim_keys", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._optim_utils._OptimStateKey"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_module_handle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._module_handle", "kind": "Gdef"}, "_named_parameters_with_duplicates": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._named_parameters_with_duplicates", "kind": "Gdef"}, "_optim_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "optim_input", "rank0_only", "shard_state", "group", "using_optim_input", "use_orig_params", "cpu_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._optim_utils._optim_state_dict", "name": "_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "optim_input", "rank0_only", "shard_state", "group", "using_optim_input", "use_orig_params", "cpu_offload"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_optim_state_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._optim_utils._optim_state_dict", "name": "_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "optim_input", "rank0_only", "shard_state", "group", "using_optim_input", "use_orig_params", "cpu_offload"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_optim_state_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_rekey_sharded_optim_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["sharded_osd", "model", "optim", "optim_input", "using_optim_input", "is_named_optimizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._rekey_sharded_optim_state_dict", "name": "_rekey_sharded_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["sharded_osd", "model", "optim", "optim_input", "using_optim_input", "is_named_optimizer"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rekey_sharded_optim_state_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reset_flat_param_grad_info_if_needed": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._reset_flat_param_grad_info_if_needed", "kind": "Gdef"}, "_set_optim_use_dtensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fsdp_state", "state_dict_settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._optim_utils._set_optim_use_dtensor", "name": "_set_optim_use_dtensor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._optim_utils._set_optim_use_dtensor", "name": "_set_optim_use_dtensor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_shard_orig_param_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_param_info", "fqn", "optim_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._shard_orig_param_state", "name": "_shard_orig_param_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_param_info", "fqn", "optim_state"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shard_orig_param_state", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unflatten_communicated_optim_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_param_info", "state", "shard_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._unflatten_communicated_optim_state", "name": "_unflatten_communicated_optim_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fsdp_param_info", "state", "shard_state"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", "torch.distributed.fsdp._optim_utils._ConsolidatedOptimState", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unflatten_communicated_optim_state", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unflatten_optim_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["fsdp_param_info", "flat_param_state", "to_save", "shard_state", "cpu_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._unflatten_optim_state", "name": "_unflatten_optim_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["fsdp_param_info", "flat_param_state", "to_save", "shard_state", "cpu_offload"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unflatten_optim_state", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unflatten_orig_param_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["fsdp_param_info", "output_states", "state_name", "shard_state", "to_save", "cpu_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._unflatten_orig_param_states", "name": "_unflatten_orig_param_states", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["fsdp_param_info", "output_states", "state_name", "shard_state", "to_save", "cpu_offload"], "arg_types": ["torch.distributed.fsdp._optim_utils.FSDPParamInfo", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unflatten_orig_param_states", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unflatten_param_groups": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state_dict", "param_key_to_param", "param_to_fqns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils._unflatten_param_groups", "name": "_unflatten_param_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["state_dict", "param_key_to_param", "param_to_fqns"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unflatten_param_groups", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "clean_tensor_name": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.clean_tensor_name", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._optim_utils.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "no_type_check": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check", "kind": "Gdef"}, "sorted_items": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dictionary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._optim_utils.sorted_items", "name": "sorted_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dictionary"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sorted_items", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "traversal_utils": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._traversal_utils", "kind": "Gdef"}, "tree_map_only": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree.tree_map_only", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_optim_utils.py"}