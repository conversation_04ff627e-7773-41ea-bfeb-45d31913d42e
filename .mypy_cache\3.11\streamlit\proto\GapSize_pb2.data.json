{".class": "MypyFile", "_fullname": "streamlit.proto.GapSize_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GapSize_pb2.google", "source_any": null, "type_of_any": 3}}}, "GAP_UNDEFINED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.GAP_UNDEFINED", "name": "GAP_UNDEFINED", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "GapConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GapSize_pb2.GapConfig", "name": "GapConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.GapSize_pb2.GapConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GapSize_pb2", "mro": ["streamlit.proto.GapSize_pb2.GapConfig", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GapSize_pb2.GapConfig.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.GapSize_pb2.GapConfig", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gap_size"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "gap_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gap_spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "gap_spec"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of GapConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2.GapConfig.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GapSize_pb2.google", "source_any": null, "type_of_any": 3}}}, "GAP_SIZE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2.GapConfig.GAP_SIZE_FIELD_NUMBER", "name": "GAP_SIZE_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GapSize_pb2.GapConfig.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.GapSize_pb2.GapConfig", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gap_size"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "gap_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gap_spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "gap_spec"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Has<PERSON><PERSON> of GapConfig", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GapSize_pb2.GapConfig.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.GapSize_pb2.GapConfig", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gap_spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "gap_spec"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of GapConfig", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gap_size"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "gap_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GapSize_pb2.GapConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "gap_size"], "arg_types": ["streamlit.proto.GapSize_pb2.GapConfig", "streamlit.proto.GapSize_pb2._GapSize.ValueType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GapConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gap_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2.GapConfig.gap_size", "name": "gap_size", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GapSize_pb2.GapConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GapSize_pb2.GapConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GapSize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.GapSize_pb2._GapSize"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GapSize_pb2.GapSize", "name": "GapSize", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.GapSize_pb2.GapSize", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.GapSize_pb2", "mro": ["streamlit.proto.GapSize_pb2.GapSize", "streamlit.proto.GapSize_pb2._GapSize", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GapSize_pb2.GapSize.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GapSize_pb2.GapSize", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LARGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.LARGE", "name": "LARGE", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "MEDIUM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.MEDIUM", "name": "MEDIUM", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.NONE", "name": "NONE", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "SMALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.SMALL", "name": "SMALL", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "_GapSize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GapSize_pb2._GapSize", "name": "_GapSize", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.GapSize_pb2._GapSize", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GapSize_pb2", "mro": ["streamlit.proto.GapSize_pb2._GapSize", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "streamlit.proto.GapSize_pb2._GapSize.V", "line": 36, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GapSize_pb2._GapSize.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.GapSize_pb2._GapSize.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GapSize_pb2", "mro": ["streamlit.proto.GapSize_pb2._GapSize.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GapSize_pb2._GapSize.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.GapSize_pb2._GapSize.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GapSize_pb2._GapSize.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GapSize_pb2._GapSize", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GapSizeEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper", "name": "_GapSizeEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GapSize_pb2", "mro": ["streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GapSize_pb2.google", "source_any": null, "type_of_any": 3}}}, "GAP_UNDEFINED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper.GAP_UNDEFINED", "name": "GAP_UNDEFINED", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "LARGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper.LARGE", "name": "LARGE", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "MEDIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper.MEDIUM", "name": "MEDIUM", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper.NONE", "name": "NONE", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}, "SMALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper.SMALL", "name": "SMALL", "type": "streamlit.proto.GapSize_pb2._GapSize.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GapSize_pb2._GapSizeEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GapSize_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___GapConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.GapSize_pb2.global___GapConfig", "line": 70, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.GapSize_pb2.GapConfig"}}, "global___GapSize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.GapSize_pb2.global___GapSize", "line": 53, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.GapSize_pb2.GapSize"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.GapSize_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GapSize_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\GapSize_pb2.pyi"}