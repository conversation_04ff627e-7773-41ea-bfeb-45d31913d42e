{".class": "MypyFile", "_fullname": "torch._inductor.quantized_lowerings", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CppGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "kind": "Gdef"}, "CppWoqInt4GemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplate", "kind": "Gdef"}, "ExternKernelChoice": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.ExternKernelChoice", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "WeightInt4PackMatmul": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.mkldnn_ir.WeightInt4PackMatmul", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.quantized_lowerings.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.quantized_lowerings.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.quantized_lowerings.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.quantized_lowerings.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.quantized_lowerings.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.quantized_lowerings.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_quantized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.quantized_lowerings._quantized", "name": "_quantized", "type": "torch._ops._OpNamespace"}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.quantized_lowerings.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "aten__weight_int4pack_mm_cpu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.quantized_lowerings.aten__weight_int4pack_mm_cpu", "name": "aten__weight_int4pack_mm_cpu", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "aten__weight_int8pack_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.quantized_lowerings.aten__weight_int8pack_mm", "name": "aten__weight_int8pack_mm", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "autotune_select_algorithm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.autotune_select_algorithm", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "create_epilogue_with_attr": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.create_epilogue_with_attr", "kind": "Gdef"}, "expand": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.expand", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.quantized_lowerings.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "lowering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering", "kind": "Gdef"}, "mm_args": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_args", "kind": "Gdef"}, "quantized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.quantized_lowerings.quantized", "name": "quantized", "type": "torch._ops._OpNamespace"}}, "realize_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.realize_inputs", "kind": "Gdef"}, "register_lowering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.register_lowering", "kind": "Gdef"}, "register_quantized_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.quantized_lowerings.register_quantized_ops", "name": "register_quantized_ops", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_quantized_ops", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_woq_mm_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.quantized_lowerings.register_woq_mm_ops", "name": "register_woq_mm_ops", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_woq_mm_ops", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "use_aten_gemm_kernels": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_aten_gemm_kernels", "kind": "Gdef"}, "use_cpp_gemm_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_cpp_gemm_template", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\quantized_lowerings.py"}