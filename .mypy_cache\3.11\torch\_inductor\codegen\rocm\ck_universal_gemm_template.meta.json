{"data_mtime": 1755649448, "dep_lines": [14, 15, 16, 13, 18, 12, 17, 20, 12, 2, 3, 4, 5, 6, 7, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 915, 918, 9], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 20, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 10], "dependencies": ["torch._inductor.codegen.rocm.ck_template", "torch._inductor.codegen.rocm.compile_command", "torch._inductor.codegen.rocm.rocm_kernel", "torch._inductor.codegen.cpp_utils", "torch._inductor.runtime.runtime_utils", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.utils", "torch._inductor", "copy", "logging", "math", "random", "collections", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "_frozen_importlib", "_typeshed", "abc", "functools", "torch._C", "torch._inductor.codegen.common", "torch._inductor.codegen.rocm.rocm_template", "torch.version"], "hash": "6232faad7eac8017667dd30e2c6823993025e0b9", "id": "torch._inductor.codegen.rocm.ck_universal_gemm_template", "ignore_all": true, "interface_hash": "e16652f5ef4a45c587e8eda2d1fe2bc58ba8a3d1", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\rocm\\ck_universal_gemm_template.py", "plugin_data": null, "size": 40526, "suppressed": ["ck4inductor.batched_universal_gemm.gen_instances", "ck4inductor.universal_gemm.gen_instances", "sympy"], "version_id": "1.15.0"}