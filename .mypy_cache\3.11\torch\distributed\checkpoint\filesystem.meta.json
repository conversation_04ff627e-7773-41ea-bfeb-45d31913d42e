{"data_mtime": 1755649449, "dep_lines": [28, 29, 33, 40, 41, 51, 52, 57, 14, 27, 58, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 17, 19, 20, 23, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed._shard._utils", "torch.distributed.checkpoint._extension", "torch.distributed.checkpoint._hf_utils", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.staging", "torch.distributed.checkpoint.storage", "torch.distributed.checkpoint.utils", "collections.abc", "torch._utils", "torch.futures", "collections", "dataclasses", "io", "json", "operator", "os", "pickle", "queue", "threading", "uuid", "warnings", "abc", "contextlib", "enum", "pathlib", "typing", "typing_extensions", "torch", "builtins", "inspect", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "copy", "_frozen_importlib", "_io", "_pickle", "_typeshed", "functools", "torch._C", "torch._tensor", "torch.cuda", "torch.cuda.streams"], "hash": "c5c747965124c866a4dd099f3811eeb877d89683", "id": "torch.distributed.checkpoint.filesystem", "ignore_all": true, "interface_hash": "a26de769b3acc19b58d17d3c426444cfaa44aea7", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\filesystem.py", "plugin_data": null, "size": 35168, "suppressed": [], "version_id": "1.15.0"}