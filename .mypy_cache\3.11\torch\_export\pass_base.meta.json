{"data_mtime": 1755649449, "dep_lines": [23, 11, 12, 17, 18, 24, 10, 13, 15, 16, 22, 25, 9, 14, 25, 2, 3, 4, 5, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 20, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.fx.passes.infra.pass_base", "torch._export.pass_infra.node_metadata", "torch._export.pass_infra.proxy_value", "torch.fx.experimental.proxy_tensor", "torch.fx.experimental.symbolic_shapes", "torch.fx.passes.shape_prop", "torch._dispatch.python", "torch._higher_order_ops.map", "torch._subclasses.fake_tensor", "torch.fx.traceback", "torch.fx.graph", "torch.utils._pytree", "torch.fx", "torch._subclasses", "torch.utils", "operator", "traceback", "typing", "contextlib", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "copy", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._export.pass_infra", "torch._tensor", "torch.fx._symbolic_trace", "torch.fx.experimental", "torch.fx.graph_module", "torch.fx.interpreter", "torch.fx.node", "torch.fx.passes", "torch.fx.passes.infra", "torch.fx.proxy", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils._python_dispatch"], "hash": "81f765d252d9b49b1b68966f9c824d0c02fd6322", "id": "torch._export.pass_base", "ignore_all": true, "interface_hash": "cc7d73f7fb950c953106d528e2b6b635b7535402", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_export\\pass_base.py", "plugin_data": null, "size": 18746, "suppressed": [], "version_id": "1.15.0"}