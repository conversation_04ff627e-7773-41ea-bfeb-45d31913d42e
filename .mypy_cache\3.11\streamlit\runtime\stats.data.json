{".class": "MypyFile", "_fullname": "streamlit.runtime.stats", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CacheStat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.runtime.stats.CacheStat", "name": "CacheStat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "streamlit.runtime.stats.CacheStat", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["category_name", "cache_name", "byte_length"]}}, "module_name": "streamlit.runtime.stats", "mro": ["streamlit.runtime.stats.CacheStat", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "category_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cache_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "byte_length"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "category_name", "cache_name", "byte_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "streamlit.runtime.stats.CacheStat.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "category_name", "cache_name", "byte_length"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of CacheStat", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.CacheStat._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of CacheStat", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "streamlit.runtime.stats.CacheStat._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of CacheStat", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of CacheStat", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "category_name", "cache_name", "byte_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.CacheStat._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "category_name", "cache_name", "byte_length"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of CacheStat", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat._NT", "id": -1, "name": "_NT", "namespace": "streamlit.runtime.stats.CacheStat._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat._source", "name": "_source", "type": "builtins.str"}}, "byte_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat.byte_length", "name": "byte_length", "type": "builtins.int"}}, "byte_length-redefinition": {".class": "SymbolTableNode", "cross_ref": "streamlit.runtime.stats.CacheStat.byte_length", "kind": "<PERSON><PERSON><PERSON>"}, "cache_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat.cache_name", "name": "cache_name", "type": "builtins.str"}}, "cache_name-redefinition": {".class": "SymbolTableNode", "cross_ref": "streamlit.runtime.stats.CacheStat.cache_name", "kind": "<PERSON><PERSON><PERSON>"}, "category_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "streamlit.runtime.stats.CacheStat.category_name", "name": "category_name", "type": "builtins.str"}}, "category_name-redefinition": {".class": "SymbolTableNode", "cross_ref": "streamlit.runtime.stats.CacheStat.category_name", "kind": "<PERSON><PERSON><PERSON>"}, "marshall_metric_proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metric"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.CacheStat.marshall_metric_proto", "name": "marshall_metric_proto", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "metric"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": "streamlit.runtime.stats.CacheStat"}, "streamlit.proto.openmetrics_data_model_pb2.Metric"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "marshall_metric_proto of CacheStat", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_metric_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.CacheStat.to_metric_str", "name": "to_metric_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": "streamlit.runtime.stats.CacheStat"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_metric_str of CacheStat", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": "streamlit.runtime.stats.CacheStat"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "CacheStatsProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_stats", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.runtime.stats.CacheStatsProvider", "name": "CacheStatsProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "streamlit.runtime.stats.CacheStatsProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "streamlit.runtime.stats", "mro": ["streamlit.runtime.stats.CacheStatsProvider", "builtins.object"], "names": {".class": "SymbolTable", "get_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "streamlit.runtime.stats.CacheStatsProvider.get_stats", "name": "get_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.runtime.stats.CacheStatsProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stats of CacheStatsProvider", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "streamlit.runtime.stats.CacheStat"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "streamlit.runtime.stats.CacheStatsProvider.get_stats", "name": "get_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.runtime.stats.CacheStatsProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stats of CacheStatsProvider", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "streamlit.runtime.stats.CacheStat"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.CacheStatsProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.runtime.stats.CacheStatsProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MetricProto": {".class": "SymbolTableNode", "cross_ref": "streamlit.proto.openmetrics_data_model_pb2.Metric", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "StatsManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.runtime.stats.StatsManager", "name": "StatsManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.StatsManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.runtime.stats", "mro": ["streamlit.runtime.stats.StatsManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.StatsManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.runtime.stats.StatsManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StatsManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cache_stats_providers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "streamlit.runtime.stats.StatsManager._cache_stats_providers", "name": "_cache_stats_providers", "type": {".class": "Instance", "args": ["streamlit.runtime.stats.CacheStatsProvider"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.StatsManager.get_stats", "name": "get_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.runtime.stats.StatsManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stats of StatsManager", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "streamlit.runtime.stats.CacheStat"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_provider": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.StatsManager.register_provider", "name": "register_provider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "provider"], "arg_types": ["streamlit.runtime.stats.StatsManager", "streamlit.runtime.stats.CacheStatsProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_provider of StatsManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.runtime.stats.StatsManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.runtime.stats.StatsManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.runtime.stats.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.runtime.stats.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.runtime.stats.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.runtime.stats.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.runtime.stats.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.runtime.stats.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "group_stats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.runtime.stats.group_stats", "name": "group_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["stats"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "streamlit.runtime.stats.CacheStat"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group_stats", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "streamlit.runtime.stats.CacheStat"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing.runtime_checkable", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\runtime\\stats.py"}