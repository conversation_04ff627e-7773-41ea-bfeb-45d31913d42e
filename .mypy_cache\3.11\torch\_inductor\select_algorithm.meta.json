{"data_mtime": 1755649448, "dep_lines": [2158, 35, 44, 52, 53, 54, 60, 61, 65, 66, 67, 68, 98, 27, 28, 29, 30, 31, 32, 33, 36, 36, 37, 43, 62, 64, 84, 3181, 16, 17, 22, 27, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 20, 21, 26, 96, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2702, 24], "dep_prios": [20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 10, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 25, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 10], "dependencies": ["torch._inductor.codegen.cuda.cuda_kernel", "torch.utils._sympy.functions", "torch._inductor.codegen.common", "torch._inductor.codegen.simd_kernel_features", "torch._inductor.codegen.subgraph", "torch._inductor.codegen.triton", "torch._inductor.codegen.triton_utils", "torch._inductor.codegen.wrapper", "torch._inductor.runtime.benchmarking", "torch._inductor.runtime.hints", "torch._inductor.runtime.triton_compat", "torch._inductor.runtime.triton_heuristics", "torch._inductor.codegen.simd", "torch._inductor.async_compile", "torch._dynamo.device_interface", "torch._dynamo.testing", "torch._dynamo.utils", "torch._inductor.utils", "torch.utils._filelock", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.autotune_process", "torch._inductor.codecache", "torch._inductor.exc", "torch._inductor.ops_handler", "torch._inductor.virtualized", "torch._inductor.lowering", "collections.abc", "concurrent.futures", "unittest.mock", "torch._inductor", "contextlib", "dataclasses", "functools", "inspect", "itertools", "json", "logging", "math", "operator", "os", "re", "sys", "textwrap", "time", "io", "types", "typing", "typing_extensions", "torch", "concurrent", "builtins", "html", "string", "pprint", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "warnings", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._dynamo", "torch._inductor.codegen", "torch._inductor.graph", "torch._inductor.runtime", "torch._inductor.scheduler", "torch._inductor.sizevars", "torch._inductor.tiling_utils", "torch._tensor", "torch.fx", "torch.fx.interpreter", "torch.utils", "torch.utils._sympy"], "hash": "7c360628e09540b399f465001c1105e7e04170a6", "id": "torch._inductor.select_algorithm", "ignore_all": true, "interface_hash": "133c1d5317a80907777612a970397817f422538c", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\select_algorithm.py", "plugin_data": null, "size": 120171, "suppressed": ["triton.runtime.autotuner", "sympy"], "version_id": "1.15.0"}