{"data_mtime": 1755654449, "dep_lines": [2, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn", "torch", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._tensor", "torch.cuda", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.linear", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.nn.parameter", "torch.version", "types", "typing"], "hash": "b163aa2b760b5ae0b39e96294346600c14518a6d", "id": "test_cuda_simple", "ignore_all": false, "interface_hash": "1d26575215435b1ddf47bd2d40dd3175a64586b1", "mtime": 1755654441, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\sub-station-predictive -maint\\test_cuda_simple.py", "plugin_data": null, "size": 1909, "suppressed": [], "version_id": "1.15.0"}