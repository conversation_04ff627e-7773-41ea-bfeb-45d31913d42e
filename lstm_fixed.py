import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
from sklearn.preprocessing import MinMaxScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Check GPU availability with detailed debugging
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    try:
        import torch.version
        cuda_version = torch.version.cuda
        print(f"CUDA version: {cuda_version}")
    except Exception as e:
        print(f"CUDA version: Unable to detect ({e})")
else:
    print("CUDA version: N/A")

if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    print(f"Number of GPUs: {torch.cuda.device_count()}")

    # Force CUDA device
    device = torch.device('cuda:0')
    print(f"Forcing device: {device}")

    # Test CUDA functionality
    try:
        test_tensor = torch.randn(10, 10).to(device)
        print("CUDA test tensor creation: SUCCESS")
    except Exception as e:
        print(f"CUDA test failed: {e}")
        device = torch.device('cpu')
        print("Falling back to CPU")
else:
    device = torch.device('cpu')
    print("CUDA not available, using CPU")

print(f"Final device: {device}")

class SubstationDataset(Dataset):
    """Custom PyTorch Dataset for substation equipment time series data"""
    
    def __init__(self, sequences, targets, equipment_ids=None, equipment_types=None):
        """
        Args:
            sequences: numpy array of shape (n_samples, sequence_length, n_features)
            targets: numpy array of shape (n_samples,) or (n_samples, n_outputs)
            equipment_ids: list of equipment identifiers
            equipment_types: list of equipment types
        """
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
        self.equipment_ids = equipment_ids
        self.equipment_types = equipment_types
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

class MultiTaskLSTM(nn.Module):
    """
    Multi-task LSTM for substation equipment predictive maintenance
    Predicts degradation factor and classifies health status
    """
    
    def __init__(self, input_size, hidden_size=128, num_layers=3, 
                 dropout=0.2, bidirectional=True, n_classes=4):
        super(MultiTaskLSTM, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bidirectional = bidirectional
        self.n_classes = n_classes
        
        # LSTM layers with dropout
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
            batch_first=True
        )
        
        # Calculate LSTM output size
        lstm_output_size = hidden_size * 2 if bidirectional else hidden_size
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=lstm_output_size,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # Feature extraction layers
        self.feature_layers = nn.Sequential(
            nn.Linear(lstm_output_size, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(dropout),
        )
        
        # Regression head for degradation factor prediction
        self.regression_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Degradation factor between 0 and 1
        )
        
        # Classification head for health status
        self.classification_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, n_classes)
        )
        
        # Equipment type embedding (optional)
        self.equipment_embedding = nn.Embedding(3, 16)  # 3 equipment types
        
    def forward(self, x, equipment_type_ids=None):
        # LSTM forward pass
        lstm_out, _ = self.lstm(x)
        
        # Apply attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # Use the last timestep output for prediction
        last_output = attn_out[:, -1, :]
        
        # Optional: Add equipment type embedding
        if equipment_type_ids is not None:
            equipment_emb = self.equipment_embedding(equipment_type_ids)
            last_output = torch.cat([last_output, equipment_emb], dim=1)
        
        # Feature extraction
        features = self.feature_layers(last_output)
        
        # Multi-task outputs
        degradation_pred = self.regression_head(features)
        health_class_pred = self.classification_head(features)
        
        return degradation_pred.squeeze(), health_class_pred

class LSTMTrainer:
    """Trainer class for LSTM models with GPU optimization"""
    
    def __init__(self, model, device=device):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        
    def train_model(self, train_loader, val_loader, num_epochs=100, 
                   learning_rate=0.001, weight_decay=1e-5, patience=15):
        """
        Train the LSTM model with early stopping and learning rate scheduling
        """
        # Initialize optimizer and scheduler
        optimizer = optim.AdamW(self.model.parameters(), 
                               lr=learning_rate, weight_decay=weight_decay)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5)
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        print(f"Training on {self.device}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(num_epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            num_batches = 0
            
            for batch_idx, (data, targets) in enumerate(train_loader):
                data, targets = data.to(self.device), targets.to(self.device)

                optimizer.zero_grad()
                outputs = self.model(data)

                # Handle multi-task model output (tuple) vs single-task model output (tensor)
                if isinstance(outputs, tuple):
                    # Multi-task model: use only the regression output (degradation prediction)
                    degradation_pred, _ = outputs
                    loss = criterion(degradation_pred, targets)
                else:
                    # Single-task model: use output directly
                    loss = criterion(outputs, targets)

                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()
                num_batches += 1
                
                # Print progress every 100 batches
                if batch_idx % 100 == 0:
                    print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, '
                          f'Loss: {loss.item():.6f}')
            
            avg_train_loss = train_loss / num_batches
            self.train_losses.append(avg_train_loss)
            
            # Validation phase
            val_loss = self.evaluate(val_loader, criterion)
            self.val_losses.append(val_loss)
            
            # Learning rate scheduling
            scheduler.step(val_loss)
            
            print(f'Epoch {epoch+1}/{num_epochs}: '
                  f'Train Loss: {avg_train_loss:.6f}, Val Loss: {val_loss:.6f}')
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # Save best model
                torch.save(self.model.state_dict(), 'best_lstm_model.pth')
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                print(f'Early stopping at epoch {epoch+1}')
                break
        
        # Load best model
        self.model.load_state_dict(torch.load('best_lstm_model.pth'))
        print("Training completed!")
        
    def evaluate(self, data_loader, criterion):
        """Evaluate model on validation/test data"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for data, targets in data_loader:
                data, targets = data.to(self.device), targets.to(self.device)
                outputs = self.model(data)
                
                # Handle multi-task model output (tuple) vs single-task model output (tensor)
                if isinstance(outputs, tuple):
                    # Multi-task model: use only the regression output (degradation prediction)
                    degradation_pred, _ = outputs
                    loss = criterion(degradation_pred, targets)
                else:
                    # Single-task model: use output directly
                    loss = criterion(outputs, targets)
                    
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def predict(self, data_loader):
        """Make predictions on new data"""
        self.model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for data, targets in data_loader:
                data = data.to(self.device)
                outputs = self.model(data)
                
                # Handle multi-task model output (tuple) vs single-task model output (tensor)
                if isinstance(outputs, tuple):
                    # Multi-task model: use only the regression output (degradation prediction)
                    degradation_pred, _ = outputs
                    predictions.extend(degradation_pred.cpu().numpy())
                else:
                    # Single-task model: use output directly
                    predictions.extend(outputs.cpu().numpy())
                    
                actuals.extend(targets.numpy())
        
        return np.array(predictions), np.array(actuals)

def prepare_data_loaders(X, y, batch_size=64, train_ratio=0.7, val_ratio=0.15):
    """
    Prepare PyTorch data loaders for training, validation, and testing
    """
    # Create dataset
    dataset = SubstationDataset(X, y)
    
    # Calculate split sizes
    total_size = len(dataset)
    train_size = int(train_ratio * total_size)
    val_size = int(val_ratio * total_size)
    test_size = total_size - train_size - val_size
    
    # Split dataset
    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size])
    
    # Create data loaders with GPU optimization (num_workers=0 for Windows compatibility)
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True, 
        num_workers=0, pin_memory=True if torch.cuda.is_available() else False)
    
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False,
        num_workers=0, pin_memory=True if torch.cuda.is_available() else False)
    
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False,
        num_workers=0, pin_memory=True if torch.cuda.is_available() else False)
    
    return train_loader, val_loader, test_loader

# Example usage and training script
if __name__ == "__main__":
    # Load the synthetic dataset generated from previous step
    print("Loading synthetic dataset...")

    try:
        # Load pre-generated data
        X = np.load('lstm_sequences_X.npy')
        y = np.load('lstm_sequences_y.npy')

        print(f"Loaded data shapes: X={X.shape}, y={y.shape}")
        print(f"Target statistics: min={y.min():.3f}, max={y.max():.3f}, mean={y.mean():.3f}")

    except FileNotFoundError:
        print("Dataset files not found. Please run the dataset generator first.")
        print("Expected files: lstm_sequences_X.npy, lstm_sequences_y.npy")
        exit()

    # Prepare data loaders
    print("Preparing data loaders...")
    train_loader, val_loader, test_loader = prepare_data_loaders(
        X, y, batch_size=128, train_ratio=0.7, val_ratio=0.15)

    # Get dataset sizes for logging
    train_size = len(train_loader.dataset.indices)  # type: ignore
    val_size = len(val_loader.dataset.indices)  # type: ignore
    test_size = len(test_loader.dataset.indices)  # type: ignore
    print(f"Data splits: Train={train_size}, Val={val_size}, Test={test_size}")

    # Initialize model
    input_size = X.shape[2]  # Number of features

    # Multi-task LSTM
    model = MultiTaskLSTM(
        input_size=input_size,
        hidden_size=128,
        num_layers=3,
        dropout=0.2,
        bidirectional=True
    )

    print(f"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters")

    # Initialize trainer
    trainer = LSTMTrainer(model, device)

    # Train the model
    print("\nStarting training...")
    trainer.train_model(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=10,  # Reduced for testing
        learning_rate=0.001,
        weight_decay=1e-5,
        patience=5  # Reduced for testing
    )

    print("Training completed successfully!")
