{".class": "MypyFile", "_fullname": "torch._inductor.kernel.mm_common", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Layout", "kind": "Gdef"}, "PythonWrapperCodegen": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SymbolicGridFn": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.SymbolicGridFn", "kind": "Gdef"}, "TMA_DESCRIPTOR_SIZE": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.TMA_DESCRIPTOR_SIZE", "kind": "Gdef"}, "TensorBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "_IntLike": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir._IntLike", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_common.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_common.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_common.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_common.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_common.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_common.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_is_static_problem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common._is_static_problem", "name": "_is_static_problem", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["layout"], "arg_types": ["torch._inductor.ir.Layout"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_static_problem", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "acc_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.acc_type", "name": "acc_type", "type": null}}, "addmm_epilogue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dtype", "alpha", "beta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.addmm_epilogue", "name": "addmm_epilogue", "type": null}}, "check_supported_striding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mat_a", "mat_b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.check_supported_striding", "name": "check_supported_striding", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mat_a", "mat_b"], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_supported_striding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_num_sms": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_num_sms", "kind": "Gdef"}, "inductor_config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "is_batch_stride_largest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mat1", "mat2", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.is_batch_stride_largest", "name": "is_batch_stride_largest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mat1", "mat2", "layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_batch_stride_largest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm_common.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mm_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5], "arg_names": ["mat1", "mat2", "others", "layout", "out_dtype", "use_4x2_dim", "mat2_transposed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.mm_args", "name": "mm_args", "type": null}}, "mm_config_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["device", "exclude_condition", "dtype_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.mm_config_kwargs", "name": "mm_config_kwargs", "type": null}}, "mm_grid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": ["m", "n", "meta", "cdiv"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm_common.mm_grid", "name": "mm_grid", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm_common.mm_grid", "name": "mm_grid", "type": "torch._inductor.select_algorithm.SymbolicGridFn"}}}, "mm_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["config", "sym_m", "sym_n", "sym_k", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.mm_options", "name": "mm_options", "type": null}}, "persistent_grouped_mm_grid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm_common.persistent_grouped_mm_grid", "name": "persistent_grouped_mm_grid", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm_common.persistent_grouped_mm_grid", "name": "persistent_grouped_mm_grid", "type": "torch._inductor.select_algorithm.SymbolicGridFn"}}}, "persistent_mm_grid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3], "arg_names": ["M", "N", "meta", "cdiv", "min"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm_common.persistent_mm_grid", "name": "persistent_mm_grid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3], "arg_names": ["M", "N", "meta", "cdiv", "min"], "arg_types": ["builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_mm_grid", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm_common.persistent_mm_grid", "name": "persistent_mm_grid", "type": "torch._inductor.select_algorithm.SymbolicGridFn"}}}, "persistent_mm_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mat1", "mat2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.persistent_mm_options", "name": "persistent_mm_options", "type": null}}, "realize_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.realize_inputs", "kind": "Gdef"}, "scale_mm_epilogue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.scale_mm_epilogue", "name": "scale_mm_epilogue", "type": null}}, "scaled_mm_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["config", "sym_m", "sym_n", "sym_k", "layout", "scale_a", "scale_b", "use_fast_accum", "device_tma"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.scaled_mm_options", "name": "scaled_mm_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["config", "sym_m", "sym_n", "sym_k", "layout", "scale_a", "scale_b", "use_fast_accum", "device_tma"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": "torch._inductor.kernel.mm_common.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.kernel.mm_common.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.kernel.mm_common.sympy", "source_any": null, "type_of_any": 3}, "torch._inductor.ir.Layout", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scaled_mm_options", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm_common.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.kernel.mm_common.sympy", "source_any": null, "type_of_any": 3}}}, "sympy_product": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_product", "kind": "Gdef"}, "tma_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_common.tma_options", "name": "tma_options", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tma_options", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\kernel\\mm_common.py"}