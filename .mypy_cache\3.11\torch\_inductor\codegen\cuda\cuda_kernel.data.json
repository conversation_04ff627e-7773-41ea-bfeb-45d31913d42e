{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cuda.cuda_kernel", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgInfo": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_template.ArgInfo", "kind": "Gdef"}, "BaseSchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseSchedulerNode", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "CSEVariable": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.CSEVariable", "kind": "Gdef"}, "CUDABenchmarkRequest": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.autotune_process.CUDABenchmarkRequest", "kind": "Gdef"}, "CUDAKernel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["torch._inductor.codegen.common.CSEVariable"], "extra_attrs": null, "type_ref": "torch._inductor.codegen.common.Kernel"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", "name": "CUDAKernel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cuda.cuda_kernel", "mro": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", "torch._inductor.codegen.common.Kernel", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDAKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_layout_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "symbol", "node", "attr", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.add_layout_arg", "name": "add_layout_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "symbol", "node", "attr", "dim"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutSymbols"}, "torch._inductor.ir.IRNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_layout_arg of CUDAKernel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_layout_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "attr", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.find_layout_arg", "name": "find_layout_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "attr", "dim"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", "torch._inductor.ir.IRNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_layout_arg of CUDAKernel", "ret_type": {".class": "UnionType", "items": ["torch._inductor.codegen.cuda.cuda_kernel.LayoutArg", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_ld_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.find_ld_idx", "name": "find_ld_idx", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_ld_idx of CUDAKernel", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.find_ld_idx", "name": "find_ld_idx", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_ld_idx of CUDAKernel", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "attr", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.find_symbol", "name": "find_symbol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "attr", "dim"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", "torch._inductor.ir.IRNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_symbol of CUDAKernel", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_dynamic_shape_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.get_dynamic_shape_args", "name": "get_dynamic_shape_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dynamic_shape_args of CUDAKernel", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cuda_kernel.Expr", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_layout_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.get_layout_args", "name": "get_layout_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_layout_args of CUDAKernel", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cuda_kernel.Expr", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_layout_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.init_layout_args", "name": "init_layout_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_layout_args of CUDAKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layout_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.layout_args", "name": "layout_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["torch._inductor.codegen.cuda.cuda_kernel.LayoutArg"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "named_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.named_nodes", "name": "named_nodes", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "overrides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.overrides", "name": "overrides", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["torch._inductor.codegen.common.OpOverrides"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._inductor.codegen.common.OpOverrides", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.size_args", "name": "size_args", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cuda_kernel.Expr", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUDATemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "kind": "Gdef"}, "CUDATemplateBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.CUDATemplateBuffer", "kind": "Gdef"}, "CUDATemplateCaller": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.ir.ChoiceCaller"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller", "name": "CUDATemplateCaller", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cuda.cuda_kernel", "mro": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller", "torch._inductor.ir.ChoiceCaller", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "category", "input_nodes", "layout", "make_kernel_render", "bmreq", "supports_epilogue_fusion", "template", "info_kwargs", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "category", "input_nodes", "layout", "make_kernel_render", "bmreq", "supports_epilogue_fusion", "template", "info_kwargs", "description"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller", "builtins.str", "builtins.str", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._inductor.ir.CUDATemplateBuffer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.scheduler.BaseSchedulerNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "functools.partial"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch._inductor.autotune_process.CUDABenchmarkRequest", "builtins.bool", "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDATemplateCaller", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of CUDATemplateCaller", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "benchmark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3], "arg_names": ["self", "args", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.benchmark", "name": "benchmark", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3], "arg_names": ["self", "args", "out"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "benchmark of CUDATemplateCaller", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bmreq": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.bmreq", "name": "bmreq", "type": "torch._inductor.autotune_process.CUDABenchmarkRequest"}}, "call_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.call_name", "name": "call_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_name of CUDATemplateCaller", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "category": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.category", "name": "category", "type": "builtins.str"}}, "hash_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.hash_key", "name": "hash_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hash_key of CUDATemplateCaller", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.info_dict", "name": "info_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info_dict of CUDATemplateCaller", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.info_kwargs", "name": "info_kwargs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "kernel_hash_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.kernel_hash_key", "name": "kernel_hash_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kernel_hash_key of CUDATemplateCaller", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_kernel_render": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.make_kernel_render", "name": "make_kernel_render", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._inductor.ir.CUDATemplateBuffer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.scheduler.BaseSchedulerNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "functools.partial"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.output_node", "name": "output_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output_node of CUDATemplateCaller", "ret_type": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "precompile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.precompile", "name": "precompile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "precompile of CUDATemplateCaller", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_epilogue_fusion": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.supports_epilogue_fusion", "name": "supports_epilogue_fusion", "type": "builtins.bool"}}, "template": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.template", "name": "template", "type": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUDATemplateKernel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "name": "CUDATemplateKernel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cuda.cuda_kernel", "mro": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.codegen.cuda.cuda_kernel.CUDAKernel", "torch._inductor.codegen.common.Kernel", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "_EXTRA_CPP_ARGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel._EXTRA_CPP_ARGS", "name": "_EXTRA_CPP_ARGS", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kernel_name", "runtime_arg_info", "runtime_arg_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kernel_name", "runtime_arg_info", "runtime_arg_values"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "builtins.str", {".class": "Instance", "args": ["torch._inductor.codegen.cuda.cuda_template.ArgInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDATemplateKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_stride": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "default_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.batch_stride", "name": "batch_stride", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "default_value"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_stride of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.call_kernel", "name": "call_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "node"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "builtins.str", "torch._inductor.ir.CUDATemplateBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_kernel of CUDATemplateKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_not_null": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.check_not_null", "name": "check_not_null", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_not_null of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cutlass_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "default_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.cutlass_dtype", "name": "cutlass_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "default_dtype"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cutlass_dtype of CUDATemplateKernel", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "def_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "inputs", "outputs", "names_str", "input_reorder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.def_kernel", "name": "def_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "inputs", "outputs", "names_str", "input_reorder"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "def_kernel of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.dtype", "name": "dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype of CUDATemplateKernel", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.get_signature", "name": "get_signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_signature of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "index", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "index", "mode"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cuda_kernel.Expr", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of CUDATemplateKernel", "ret_type": "torch._inductor.codegen.common.CSEVariable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_valid_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.max_valid_index", "name": "max_valid_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "default"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max_valid_index of CUDATemplateKernel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.offset", "name": "offset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "offset of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ptr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.ptr", "name": "ptr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ptr of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "row_or_column_stride": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "default_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.row_or_column_stride", "name": "row_or_column_stride", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "default_value"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "row_or_column_stride of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runtime_arg_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.runtime_arg_info", "name": "runtime_arg_info", "type": {".class": "Instance", "args": ["torch._inductor.codegen.cuda.cuda_template.ArgInfo"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "runtime_arg_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.runtime_arg_values", "name": "runtime_arg_values", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "signature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.signature", "name": "signature", "type": "builtins.str"}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "node", "start_index", "end_index", "default_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "node", "start_index", "end_index", "default_value"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "store": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "index", "value", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.store", "name": "store", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "index", "value", "mode"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cuda_kernel.Expr", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "store of CUDATemplateKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stride": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "index", "default_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.stride", "name": "stride", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "index", "default_value"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "torch._inductor.ir.IRNode", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stride of CUDATemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ChoiceCaller": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.ChoiceCaller", "kind": "Gdef"}, "CppPrinter": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.CppPrinter", "kind": "Gdef"}, "CppWrapperCpu": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "kind": "Gdef"}, "DTYPE_TO_CPP": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.DTYPE_TO_CPP", "kind": "Gdef"}, "DTYPE_TO_CUTLASS_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_utils.DTYPE_TO_CUTLASS_TYPE", "kind": "Gdef"}, "Expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.Expr", "name": "Expr", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cuda_kernel.Expr", "source_any": null, "type_of_any": 3}}}, "IRNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.IRNode", "kind": "Gdef"}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "Kernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.Kernel", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Layout", "kind": "Gdef"}, "LayoutArg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg", "name": "LayoutArg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "node", "type": "torch._inductor.ir.IRNode"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 66, "name": "symbol", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutSymbols"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 67, "name": "attr", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 68, "name": "dim", "type": "builtins.int"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.cuda.cuda_kernel", "mro": ["torch._inductor.codegen.cuda.cuda_kernel.LayoutArg", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "symbol", "attr", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "symbol", "attr", "dim"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.LayoutArg", "torch._inductor.ir.IRNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutSymbols"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LayoutArg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "symbol"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dim"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["node", "symbol", "attr", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["node", "symbol", "attr", "dim"], "arg_types": ["torch._inductor.ir.IRNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutSymbols"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LayoutArg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["node", "symbol", "attr", "dim"], "arg_types": ["torch._inductor.ir.IRNode", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutSymbols"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LayoutArg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.attr", "name": "attr", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs"}}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.dim", "name": "dim", "type": "builtins.int"}}, "matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "attr", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "attr", "dim"], "arg_types": ["torch._inductor.codegen.cuda.cuda_kernel.LayoutArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of LayoutArg", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.node", "name": "node", "type": "torch._inductor.ir.IRNode"}}, "symbol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.symbol", "name": "symbol", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutSymbols"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.cuda_kernel.LayoutArg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "OpOverrides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.OpOverrides", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "Placeholder": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.Placeholder", "kind": "Gdef"}, "PrimitiveInfoType": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.PrimitiveInfoType", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TensorBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "ValidLayoutAttrs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutAttrs", "line": 60, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stride"}], "uses_pep604_syntax": false}}}, "ValidLayoutSymbols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.cuda_kernel.ValidLayoutSymbols", "line": 59, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "M"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "N"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "K"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "B"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lda"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ldb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ldc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ldd"}], "uses_pep604_syntax": false}}}, "ValueRanges": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.value_ranges.ValueRanges", "kind": "Gdef"}, "WorkspaceArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceArg", "kind": "Gdef"}, "WorkspaceZeroMode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceZeroMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_normalize_idx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["index", "total_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_kernel._normalize_idx", "name": "_normalize_idx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["index", "total_length"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_idx", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cexpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.cexpr", "name": "cexpr", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["expr", "simplify", "p"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["torch._inductor.codegen.cpp_utils.CppPrinter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "do_bench_using_profiling": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.do_bench_using_profiling", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "symbols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_kernel.symbols", "name": "symbols", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cuda_kernel.symbols", "source_any": null, "type_of_any": 3}}}, "sympy_product": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_product", "kind": "Gdef"}, "torch_dtype": {".class": "SymbolTableNode", "cross_ref": "torch._C.dtype", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cuda_kernel.py"}