{"data_mtime": 1755649449, "dep_lines": [10, 9, 9, 2, 3, 4, 5, 6, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 20, 10, 10, 10, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 20, 20], "dependencies": ["torch.distributed.checkpoint.logging_handlers", "torch.distributed.c10d_logger", "torch.distributed", "functools", "logging", "time", "typing", "typing_extensions", "uuid", "torch", "builtins", "collections", "warnings", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "torch.nn", "multiprocessing.reduction", "dataclasses", "sys", "json", "traceback", "re", "html", "_frozen_importlib", "abc", "contextlib"], "hash": "815c2be0912e12e4d21fddfd3946f524af4ae55f", "id": "torch.distributed.checkpoint.logger", "ignore_all": true, "interface_hash": "3e3e4e3cf84c7e98e30448dad118b200d34f1929", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\logger.py", "plugin_data": null, "size": 3667, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}