{"data_mtime": 1755649448, "dep_lines": [22, 25, 26, 44, 60, 61, 62, 63, 1651, 21, 23, 24, 33, 34, 34, 34, 35, 36, 37, 43, 46, 59, 20, 34, 73, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 10, 20, 25, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["torch.fx.experimental.symbolic_shapes", "torch.utils._sympy.functions", "torch.utils._sympy.symbol", "torch._inductor.runtime.runtime_utils", "torch._inductor.codegen.block_analysis", "torch._inductor.codegen.common", "torch._inductor.codegen.multi_kernel", "torch._inductor.codegen.simd_kernel_features", "torch._inductor.codegen.triton_combo_kernel", "torch._inductor.tiling_utils", "torch.fx.immutable_collections", "torch.utils._ordered_set", "torch._dynamo.utils", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.scheduler", "torch._inductor.analyze_preserves_zero_mask", "torch._inductor.codecache", "torch._inductor.dependencies", "torch._inductor.optimize_indexing", "torch._inductor.utils", "torch._inductor.virtualized", "torch._logging", "torch._inductor", "collections.abc", "__future__", "collections", "contextlib", "dataclasses", "functools", "itertools", "logging", "math", "operator", "textwrap", "typing", "typing_extensions", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._inductor.codegen.wrapper", "torch._inductor.graph", "torch._inductor.ops_handler", "torch._inductor.sizevars", "torch._logging._internal", "torch.fx", "torch.fx.interpreter", "torch.fx.node", "torch.utils", "torch.utils._sympy", "torch.utils._sympy.printers"], "hash": "c59942b6c4b6ea1fcc527c53c7e5807dd804b4e2", "id": "torch._inductor.codegen.simd", "ignore_all": true, "interface_hash": "48f78ff1adab8ce5a014d293fd0c0b787a667ebc", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\simd.py", "plugin_data": null, "size": 98123, "suppressed": ["sympy"], "version_id": "1.15.0"}