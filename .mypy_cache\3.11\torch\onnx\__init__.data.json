{".class": "MypyFile", "_fullname": "torch.onnx", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_public": false}, "ExportOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx.ExportOptions", "name": "ExportOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": "class torch.onnx.ExportOptions is deprecated: torch.onnx.dynamo_export is deprecated since 2.7.0. Please use torch.onnx.export(..., dynamo=True) instead.", "flags": [], "fullname": "torch.onnx.ExportOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx", "mro": ["torch.onnx.ExportOptions", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "dynamic_shapes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.ExportOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "dynamic_shapes"], "arg_types": ["torch.onnx.ExportOptions", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExportOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dynamic_shapes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.onnx.ExportOptions.dynamic_shapes", "name": "dynamic_shapes", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx.ExportOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx.ExportOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JitScalarType": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._type_utils.JitScalarType", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "ONNXProgram": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._onnx_program.ONNXProgram", "kind": "Gdef"}, "OnnxExporterError": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.errors.OnnxExporterError", "kind": "Gdef"}, "OperatorExportTypes": {".class": "SymbolTableNode", "cross_ref": "torch._C._onnx.OperatorExportTypes", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TensorProtoDataType": {".class": "SymbolTableNode", "cross_ref": "torch._C._onnx.TensorProtoDataType", "kind": "Gdef"}, "TrainingMode": {".class": "SymbolTableNode", "cross_ref": "torch._C._onnx.TrainingMode", "kind": "Gdef"}, "_C_onnx": {".class": "SymbolTableNode", "cross_ref": "torch._<PERSON>._onnx", "kind": "Gdef", "module_public": false}, "_OrtBackend": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.onnxruntime.OrtBackend", "kind": "Gdef", "module_public": false}, "_OrtBackendOptions": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.onnxruntime.OrtBackendOptions", "kind": "Gdef", "module_public": false}, "_OrtExecutionProvider": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.onnxruntime.OrtExecutionProvider", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_run_symbolic_function": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.utils._run_symbolic_function", "kind": "Gdef", "module_public": false}, "_run_symbolic_method": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.utils._run_symbolic_method", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_public": false}, "dynamo_export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": [null, "model_args", "export_options", "model_kwargs"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.dynamo_export is deprecated: torch.onnx.dynamo_export is deprecated since 2.7.0. Please use torch.onnx.export(..., dynamo=True) instead.", "flags": ["is_decorated"], "fullname": "torch.onnx.dynamo_export", "name": "dynamo_export", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": [null, "model_args", "export_options", "model_kwargs"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch.export.exported_program.ExportedProgram"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch.onnx.ExportOptions", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dynamo_export", "ret_type": "torch.onnx._internal.exporter._onnx_program.ONNXProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.dynamo_export", "name": "dynamo_export", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": [null, "model_args", "export_options", "model_kwargs"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch.export.exported_program.ExportedProgram"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch.onnx.ExportOptions", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dynamo_export", "ret_type": "torch.onnx._internal.exporter._onnx_program.ONNXProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enable_fake_mode": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal._exporter_legacy.enable_fake_mode", "kind": "Gdef"}, "errors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.errors", "kind": "Gdef"}, "export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model", "args", "f", "kwargs", "export_params", "verbose", "input_names", "output_names", "opset_version", "dynamic_axes", "keep_initializers_as_inputs", "dynamo", "external_data", "dynamic_shapes", "custom_translation_table", "report", "optimize", "verify", "profile", "dump_exported_program", "artifacts_dir", "fallback", "training", "operator_export_type", "do_constant_folding", "custom_opsets", "export_modules_as_functions", "autograd_inlining"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.export", "name": "export", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model", "args", "f", "kwargs", "export_params", "verbose", "input_names", "output_names", "opset_version", "dynamic_axes", "keep_initializers_as_inputs", "dynamo", "external_data", "dynamic_shapes", "custom_translation_table", "report", "optimize", "verify", "profile", "dump_exported_program", "artifacts_dir", "fallback", "training", "operator_export_type", "do_constant_folding", "custom_opsets", "export_modules_as_functions", "autograd_inlining"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", "torch.export.exported_program.ExportedProgram", "torch.jit._script.ScriptModule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.bool", "torch._C._onnx.TrainingMode", "torch._C._onnx.OperatorExportTypes", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": [{".class": "TypeType", "item": "torch.nn.modules.module.Module"}], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export", "ret_type": {".class": "UnionType", "items": ["torch.onnx._internal.exporter._onnx_program.ONNXProgram", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_in_onnx_export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.is_in_onnx_export", "name": "is_in_onnx_export", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_in_onnx_export", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_onnxrt_backend_supported": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.onnxruntime.is_onnxrt_backend_supported", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.ops", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "producer_name": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx.producer_name", "name": "producer_name", "type": "builtins.str"}}, "producer_version": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.producer_version", "name": "producer_version", "type": "builtins.str"}}, "register_custom_op_symbolic": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.utils.register_custom_op_symbolic", "kind": "Gdef"}, "select_model_mode_for_export": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.utils.select_model_mode_for_export", "kind": "Gdef"}, "symbolic_caffe2": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_caffe2", "kind": "Gdef"}, "symbolic_helper": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_helper", "kind": "Gdef"}, "symbolic_opset10": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset10", "kind": "Gdef"}, "symbolic_opset11": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset11", "kind": "Gdef"}, "symbolic_opset12": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset12", "kind": "Gdef"}, "symbolic_opset13": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset13", "kind": "Gdef"}, "symbolic_opset14": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset14", "kind": "Gdef"}, "symbolic_opset15": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset15", "kind": "Gdef"}, "symbolic_opset16": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset16", "kind": "Gdef"}, "symbolic_opset17": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset17", "kind": "Gdef"}, "symbolic_opset18": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset18", "kind": "Gdef"}, "symbolic_opset19": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset19", "kind": "Gdef"}, "symbolic_opset20": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset20", "kind": "Gdef"}, "symbolic_opset7": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset7", "kind": "Gdef"}, "symbolic_opset8": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset8", "kind": "Gdef"}, "symbolic_opset9": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset9", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "unregister_custom_op_symbolic": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.utils.unregister_custom_op_symbolic", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.utils", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\__init__.py"}