{"data_mtime": 1755649448, "dep_lines": [15, 45, 11, 12, 13, 15, 18, 27, 33, 34, 44, 11, 16, 17, 46, 47, 51, 6, 10, 14, 2, 3, 4, 5, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 55], "dep_prios": [10, 5, 10, 10, 10, 20, 5, 5, 5, 5, 5, 20, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch.distributed.algorithms._comm_hooks.default_hooks", "torch.distributed.tensor.parallel.fsdp", "torch.distributed.fsdp._exec_order_utils", "torch.distributed.fsdp._traversal_utils", "torch.distributed.fsdp.fully_sharded_data_parallel", "torch.distributed.algorithms._comm_hooks", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp._flat_param", "torch.distributed.fsdp._limiter_utils", "torch.distributed.fsdp.api", "torch.distributed.fsdp.wrap", "torch.distributed.fsdp", "torch.distributed.device_mesh", "torch.distributed.distributed_c10d", "torch.distributed.utils", "torch.utils._python_dispatch", "torch.utils.hooks", "collections.abc", "torch.distributed", "torch.nn", "collections", "itertools", "os", "warnings", "typing", "torch", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.distributed.algorithms", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "typing_extensions"], "hash": "28dcd43068acbe35705998cebb1110715915520a", "id": "torch.distributed.fsdp._init_utils", "ignore_all": true, "interface_hash": "80c6a8fbc07966d63ed46b216b1957ec6ef63a3f", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_init_utils.py", "plugin_data": null, "size": 47409, "suppressed": ["torchdistx"], "version_id": "1.15.0"}