{"data_mtime": 1755649448, "dep_lines": [17, 9, 10, 15, 16, 18, 19, 21, 22, 23, 4, 9, 2, 3, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.fx.experimental.proxy_tensor", "torch.utils._pytree", "torch._export.non_strict_utils", "torch._export.utils", "torch.export.unflatten", "torch.fx.graph", "torch.fx.traceback", "torch.export._remove_effect_tokens_pass", "torch.export._tree_utils", "torch.export.exported_program", "collections.abc", "torch.utils", "copy", "warnings", "itertools", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "contextlib", "_frozen_importlib", "abc", "torch._C", "torch._dynamo", "torch._dynamo.decorators", "torch.export.graph_signature", "torch.fx", "torch.fx.graph_module", "torch.fx.node", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "6691969d71c35e32aeed7b462ac52c3d3010b191", "id": "torch.export._unlift", "ignore_all": true, "interface_hash": "c6ba508782d0fca07af16efacad17355c77f3df3", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\export\\_unlift.py", "plugin_data": null, "size": 18009, "suppressed": [], "version_id": "1.15.0"}