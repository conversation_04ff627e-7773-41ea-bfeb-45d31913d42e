{".class": "MypyFile", "_fullname": "curses", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ACS_BBSS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_BBSS", "kind": "Gdef"}, "ACS_BLOCK": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_BLOCK", "kind": "Gdef"}, "ACS_BOARD": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_BOARD", "kind": "Gdef"}, "ACS_BSBS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_BSBS", "kind": "Gdef"}, "ACS_BSSB": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_BSSB", "kind": "Gdef"}, "ACS_BSSS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_BSSS", "kind": "Gdef"}, "ACS_BTEE": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_BTEE", "kind": "Gdef"}, "ACS_BULLET": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_BULLET", "kind": "Gdef"}, "ACS_CKBOARD": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_CKBOARD", "kind": "Gdef"}, "ACS_DARROW": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_DARROW", "kind": "Gdef"}, "ACS_DEGREE": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_DEGREE", "kind": "Gdef"}, "ACS_DIAMOND": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_DIAMOND", "kind": "Gdef"}, "ACS_GEQUAL": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_GEQUAL", "kind": "Gdef"}, "ACS_HLINE": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_HLINE", "kind": "Gdef"}, "ACS_LANTERN": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_LANTERN", "kind": "Gdef"}, "ACS_LARROW": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_LARROW", "kind": "Gdef"}, "ACS_LEQUAL": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_LEQUAL", "kind": "Gdef"}, "ACS_LLCORNER": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_LLCORNER", "kind": "Gdef"}, "ACS_LRCORNER": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_LRCORNER", "kind": "Gdef"}, "ACS_LTEE": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_LTEE", "kind": "Gdef"}, "ACS_NEQUAL": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_NEQUAL", "kind": "Gdef"}, "ACS_PI": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_PI", "kind": "Gdef"}, "ACS_PLMINUS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_PLMINUS", "kind": "Gdef"}, "ACS_PLUS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_PLUS", "kind": "Gdef"}, "ACS_RARROW": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_RARROW", "kind": "Gdef"}, "ACS_RTEE": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_RTEE", "kind": "Gdef"}, "ACS_S1": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_S1", "kind": "Gdef"}, "ACS_S3": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_S3", "kind": "Gdef"}, "ACS_S7": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_S7", "kind": "Gdef"}, "ACS_S9": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_S9", "kind": "Gdef"}, "ACS_SBBS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_SBBS", "kind": "Gdef"}, "ACS_SBSB": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_SBSB", "kind": "Gdef"}, "ACS_SBSS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_SBSS", "kind": "Gdef"}, "ACS_SSBB": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_SSBB", "kind": "Gdef"}, "ACS_SSBS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_SSBS", "kind": "Gdef"}, "ACS_SSSB": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_SSSB", "kind": "Gdef"}, "ACS_SSSS": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_SSSS", "kind": "Gdef"}, "ACS_STERLING": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_STERLING", "kind": "Gdef"}, "ACS_TTEE": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_TTEE", "kind": "Gdef"}, "ACS_UARROW": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_UARROW", "kind": "Gdef"}, "ACS_ULCORNER": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_ULCORNER", "kind": "Gdef"}, "ACS_URCORNER": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_URCORNER", "kind": "Gdef"}, "ACS_VLINE": {".class": "SymbolTableNode", "cross_ref": "_curses.ACS_VLINE", "kind": "Gdef"}, "ALL_MOUSE_EVENTS": {".class": "SymbolTableNode", "cross_ref": "_curses.ALL_MOUSE_EVENTS", "kind": "Gdef"}, "A_ALTCHARSET": {".class": "SymbolTableNode", "cross_ref": "_curses.A_ALTCHARSET", "kind": "Gdef"}, "A_ATTRIBUTES": {".class": "SymbolTableNode", "cross_ref": "_curses.A_ATTRIBUTES", "kind": "Gdef"}, "A_BLINK": {".class": "SymbolTableNode", "cross_ref": "_curses.A_BLINK", "kind": "Gdef"}, "A_BOLD": {".class": "SymbolTableNode", "cross_ref": "_curses.A_BOLD", "kind": "Gdef"}, "A_CHARTEXT": {".class": "SymbolTableNode", "cross_ref": "_curses.A_CHARTEXT", "kind": "Gdef"}, "A_COLOR": {".class": "SymbolTableNode", "cross_ref": "_curses.A_COLOR", "kind": "Gdef"}, "A_DIM": {".class": "SymbolTableNode", "cross_ref": "_curses.A_DIM", "kind": "Gdef"}, "A_HORIZONTAL": {".class": "SymbolTableNode", "cross_ref": "_curses.A_HORIZONTAL", "kind": "Gdef"}, "A_INVIS": {".class": "SymbolTableNode", "cross_ref": "_curses.A_INVIS", "kind": "Gdef"}, "A_ITALIC": {".class": "SymbolTableNode", "cross_ref": "_curses.A_ITALIC", "kind": "Gdef"}, "A_LEFT": {".class": "SymbolTableNode", "cross_ref": "_curses.A_LEFT", "kind": "Gdef"}, "A_LOW": {".class": "SymbolTableNode", "cross_ref": "_curses.A_LOW", "kind": "Gdef"}, "A_NORMAL": {".class": "SymbolTableNode", "cross_ref": "_curses.A_NORMAL", "kind": "Gdef"}, "A_PROTECT": {".class": "SymbolTableNode", "cross_ref": "_curses.A_PROTECT", "kind": "Gdef"}, "A_REVERSE": {".class": "SymbolTableNode", "cross_ref": "_curses.A_REVERSE", "kind": "Gdef"}, "A_RIGHT": {".class": "SymbolTableNode", "cross_ref": "_curses.A_RIGHT", "kind": "Gdef"}, "A_STANDOUT": {".class": "SymbolTableNode", "cross_ref": "_curses.A_STANDOUT", "kind": "Gdef"}, "A_TOP": {".class": "SymbolTableNode", "cross_ref": "_curses.A_TOP", "kind": "Gdef"}, "A_UNDERLINE": {".class": "SymbolTableNode", "cross_ref": "_curses.A_UNDERLINE", "kind": "Gdef"}, "A_VERTICAL": {".class": "SymbolTableNode", "cross_ref": "_curses.A_VERTICAL", "kind": "Gdef"}, "BUTTON1_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON1_CLICKED", "kind": "Gdef"}, "BUTTON1_DOUBLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON1_DOUBLE_CLICKED", "kind": "Gdef"}, "BUTTON1_PRESSED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON1_PRESSED", "kind": "Gdef"}, "BUTTON1_RELEASED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON1_RELEASED", "kind": "Gdef"}, "BUTTON1_TRIPLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON1_TRIPLE_CLICKED", "kind": "Gdef"}, "BUTTON2_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON2_CLICKED", "kind": "Gdef"}, "BUTTON2_DOUBLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON2_DOUBLE_CLICKED", "kind": "Gdef"}, "BUTTON2_PRESSED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON2_PRESSED", "kind": "Gdef"}, "BUTTON2_RELEASED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON2_RELEASED", "kind": "Gdef"}, "BUTTON2_TRIPLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON2_TRIPLE_CLICKED", "kind": "Gdef"}, "BUTTON3_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON3_CLICKED", "kind": "Gdef"}, "BUTTON3_DOUBLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON3_DOUBLE_CLICKED", "kind": "Gdef"}, "BUTTON3_PRESSED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON3_PRESSED", "kind": "Gdef"}, "BUTTON3_RELEASED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON3_RELEASED", "kind": "Gdef"}, "BUTTON3_TRIPLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON3_TRIPLE_CLICKED", "kind": "Gdef"}, "BUTTON4_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON4_CLICKED", "kind": "Gdef"}, "BUTTON4_DOUBLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON4_DOUBLE_CLICKED", "kind": "Gdef"}, "BUTTON4_PRESSED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON4_PRESSED", "kind": "Gdef"}, "BUTTON4_RELEASED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON4_RELEASED", "kind": "Gdef"}, "BUTTON4_TRIPLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON4_TRIPLE_CLICKED", "kind": "Gdef"}, "BUTTON5_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON5_CLICKED", "kind": "Gdef"}, "BUTTON5_DOUBLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON5_DOUBLE_CLICKED", "kind": "Gdef"}, "BUTTON5_PRESSED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON5_PRESSED", "kind": "Gdef"}, "BUTTON5_RELEASED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON5_RELEASED", "kind": "Gdef"}, "BUTTON5_TRIPLE_CLICKED": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON5_TRIPLE_CLICKED", "kind": "Gdef"}, "BUTTON_ALT": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON_ALT", "kind": "Gdef"}, "BUTTON_CTRL": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON_CTRL", "kind": "Gdef"}, "BUTTON_SHIFT": {".class": "SymbolTableNode", "cross_ref": "_curses.BUTTON_SHIFT", "kind": "Gdef"}, "COLORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.COLORS", "name": "COLORS", "type": "builtins.int"}}, "COLOR_BLACK": {".class": "SymbolTableNode", "cross_ref": "_curses.COLOR_BLACK", "kind": "Gdef"}, "COLOR_BLUE": {".class": "SymbolTableNode", "cross_ref": "_curses.COLOR_BLUE", "kind": "Gdef"}, "COLOR_CYAN": {".class": "SymbolTableNode", "cross_ref": "_curses.COLOR_CYAN", "kind": "Gdef"}, "COLOR_GREEN": {".class": "SymbolTableNode", "cross_ref": "_curses.<PERSON>L<PERSON>_<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "COLOR_MAGENTA": {".class": "SymbolTableNode", "cross_ref": "_curses.COLOR_MAGENTA", "kind": "Gdef"}, "COLOR_PAIRS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.COLOR_PAIRS", "name": "COLOR_PAIRS", "type": "builtins.int"}}, "COLOR_RED": {".class": "SymbolTableNode", "cross_ref": "_curses.COLOR_RED", "kind": "Gdef"}, "COLOR_WHITE": {".class": "SymbolTableNode", "cross_ref": "_curses.COLOR_WHITE", "kind": "Gdef"}, "COLOR_YELLOW": {".class": "SymbolTableNode", "cross_ref": "_curses.COLOR_YELLOW", "kind": "Gdef"}, "COLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.COLS", "name": "COLS", "type": "builtins.int"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Concatenate": {".class": "SymbolTableNode", "cross_ref": "typing.Concatenate", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ERR": {".class": "SymbolTableNode", "cross_ref": "_curses.ERR", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "KEY_A1": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_A1", "kind": "Gdef"}, "KEY_A3": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_A3", "kind": "Gdef"}, "KEY_B2": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_B2", "kind": "Gdef"}, "KEY_BACKSPACE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_BACKSPACE", "kind": "Gdef"}, "KEY_BEG": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_BEG", "kind": "Gdef"}, "KEY_BREAK": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_BREAK", "kind": "Gdef"}, "KEY_BTAB": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_BTAB", "kind": "Gdef"}, "KEY_C1": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_C1", "kind": "Gdef"}, "KEY_C3": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_C3", "kind": "Gdef"}, "KEY_CANCEL": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_CANCEL", "kind": "Gdef"}, "KEY_CATAB": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_CATAB", "kind": "Gdef"}, "KEY_CLEAR": {".class": "SymbolTableNode", "cross_ref": "_curses.KE<PERSON>_CLEAR", "kind": "Gdef"}, "KEY_CLOSE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_CLOSE", "kind": "Gdef"}, "KEY_COMMAND": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_COMMAND", "kind": "Gdef"}, "KEY_COPY": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_COPY", "kind": "Gdef"}, "KEY_CREATE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_CREATE", "kind": "Gdef"}, "KEY_CTAB": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_CTAB", "kind": "Gdef"}, "KEY_DC": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_DC", "kind": "Gdef"}, "KEY_DL": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_DL", "kind": "Gdef"}, "KEY_DOWN": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_DOWN", "kind": "Gdef"}, "KEY_EIC": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_EIC", "kind": "Gdef"}, "KEY_END": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_END", "kind": "Gdef"}, "KEY_ENTER": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_ENTER", "kind": "Gdef"}, "KEY_EOL": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_EOL", "kind": "Gdef"}, "KEY_EOS": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_EOS", "kind": "Gdef"}, "KEY_EXIT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_EXIT", "kind": "Gdef"}, "KEY_F0": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F0", "kind": "Gdef"}, "KEY_F1": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F1", "kind": "Gdef"}, "KEY_F10": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F10", "kind": "Gdef"}, "KEY_F11": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F11", "kind": "Gdef"}, "KEY_F12": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F12", "kind": "Gdef"}, "KEY_F13": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F13", "kind": "Gdef"}, "KEY_F14": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F14", "kind": "Gdef"}, "KEY_F15": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F15", "kind": "Gdef"}, "KEY_F16": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F16", "kind": "Gdef"}, "KEY_F17": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F17", "kind": "Gdef"}, "KEY_F18": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F18", "kind": "Gdef"}, "KEY_F19": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F19", "kind": "Gdef"}, "KEY_F2": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F2", "kind": "Gdef"}, "KEY_F20": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F20", "kind": "Gdef"}, "KEY_F21": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F21", "kind": "Gdef"}, "KEY_F22": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F22", "kind": "Gdef"}, "KEY_F23": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F23", "kind": "Gdef"}, "KEY_F24": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F24", "kind": "Gdef"}, "KEY_F25": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F25", "kind": "Gdef"}, "KEY_F26": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F26", "kind": "Gdef"}, "KEY_F27": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F27", "kind": "Gdef"}, "KEY_F28": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F28", "kind": "Gdef"}, "KEY_F29": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F29", "kind": "Gdef"}, "KEY_F3": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F3", "kind": "Gdef"}, "KEY_F30": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F30", "kind": "Gdef"}, "KEY_F31": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F31", "kind": "Gdef"}, "KEY_F32": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F32", "kind": "Gdef"}, "KEY_F33": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F33", "kind": "Gdef"}, "KEY_F34": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F34", "kind": "Gdef"}, "KEY_F35": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F35", "kind": "Gdef"}, "KEY_F36": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F36", "kind": "Gdef"}, "KEY_F37": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F37", "kind": "Gdef"}, "KEY_F38": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F38", "kind": "Gdef"}, "KEY_F39": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F39", "kind": "Gdef"}, "KEY_F4": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F4", "kind": "Gdef"}, "KEY_F40": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F40", "kind": "Gdef"}, "KEY_F41": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F41", "kind": "Gdef"}, "KEY_F42": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F42", "kind": "Gdef"}, "KEY_F43": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F43", "kind": "Gdef"}, "KEY_F44": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F44", "kind": "Gdef"}, "KEY_F45": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F45", "kind": "Gdef"}, "KEY_F46": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F46", "kind": "Gdef"}, "KEY_F47": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F47", "kind": "Gdef"}, "KEY_F48": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F48", "kind": "Gdef"}, "KEY_F49": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F49", "kind": "Gdef"}, "KEY_F5": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F5", "kind": "Gdef"}, "KEY_F50": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F50", "kind": "Gdef"}, "KEY_F51": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F51", "kind": "Gdef"}, "KEY_F52": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F52", "kind": "Gdef"}, "KEY_F53": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F53", "kind": "Gdef"}, "KEY_F54": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F54", "kind": "Gdef"}, "KEY_F55": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F55", "kind": "Gdef"}, "KEY_F56": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F56", "kind": "Gdef"}, "KEY_F57": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F57", "kind": "Gdef"}, "KEY_F58": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F58", "kind": "Gdef"}, "KEY_F59": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F59", "kind": "Gdef"}, "KEY_F6": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F6", "kind": "Gdef"}, "KEY_F60": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F60", "kind": "Gdef"}, "KEY_F61": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F61", "kind": "Gdef"}, "KEY_F62": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F62", "kind": "Gdef"}, "KEY_F63": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F63", "kind": "Gdef"}, "KEY_F7": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F7", "kind": "Gdef"}, "KEY_F8": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F8", "kind": "Gdef"}, "KEY_F9": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_F9", "kind": "Gdef"}, "KEY_FIND": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_FIND", "kind": "Gdef"}, "KEY_HELP": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_HELP", "kind": "Gdef"}, "KEY_HOME": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_HOME", "kind": "Gdef"}, "KEY_IC": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_IC", "kind": "Gdef"}, "KEY_IL": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_IL", "kind": "Gdef"}, "KEY_LEFT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_LEFT", "kind": "Gdef"}, "KEY_LL": {".class": "SymbolTableNode", "cross_ref": "_curses.<PERSON><PERSON><PERSON>_<PERSON>", "kind": "Gdef"}, "KEY_MARK": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_MARK", "kind": "Gdef"}, "KEY_MAX": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_MAX", "kind": "Gdef"}, "KEY_MESSAGE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_MESSAGE", "kind": "Gdef"}, "KEY_MIN": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_MIN", "kind": "Gdef"}, "KEY_MOUSE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_MOUSE", "kind": "Gdef"}, "KEY_MOVE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_MOVE", "kind": "Gdef"}, "KEY_NEXT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_NEXT", "kind": "Gdef"}, "KEY_NPAGE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_NPAGE", "kind": "Gdef"}, "KEY_OPEN": {".class": "SymbolTableNode", "cross_ref": "_curses.<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "KEY_OPTIONS": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_OPTIONS", "kind": "Gdef"}, "KEY_PPAGE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_PPAGE", "kind": "Gdef"}, "KEY_PREVIOUS": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_PREVIOUS", "kind": "Gdef"}, "KEY_PRINT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_PRINT", "kind": "Gdef"}, "KEY_REDO": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_REDO", "kind": "Gdef"}, "KEY_REFERENCE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_REFERENCE", "kind": "Gdef"}, "KEY_REFRESH": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_REFRESH", "kind": "Gdef"}, "KEY_REPLACE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_REPLACE", "kind": "Gdef"}, "KEY_RESET": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_RESET", "kind": "Gdef"}, "KEY_RESIZE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_RESIZE", "kind": "Gdef"}, "KEY_RESTART": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_RESTART", "kind": "Gdef"}, "KEY_RESUME": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_RESUME", "kind": "Gdef"}, "KEY_RIGHT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_RIGHT", "kind": "Gdef"}, "KEY_SAVE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SAVE", "kind": "Gdef"}, "KEY_SBEG": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SBEG", "kind": "Gdef"}, "KEY_SCANCEL": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SCANCEL", "kind": "Gdef"}, "KEY_SCOMMAND": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SCOMMAND", "kind": "Gdef"}, "KEY_SCOPY": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SCOPY", "kind": "Gdef"}, "KEY_SCREATE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SCREATE", "kind": "Gdef"}, "KEY_SDC": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SDC", "kind": "Gdef"}, "KEY_SDL": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SDL", "kind": "Gdef"}, "KEY_SELECT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SELECT", "kind": "Gdef"}, "KEY_SEND": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SEND", "kind": "Gdef"}, "KEY_SEOL": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SEOL", "kind": "Gdef"}, "KEY_SEXIT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SEXIT", "kind": "Gdef"}, "KEY_SF": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SF", "kind": "Gdef"}, "KEY_SFIND": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SFIND", "kind": "Gdef"}, "KEY_SHELP": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SHELP", "kind": "Gdef"}, "KEY_SHOME": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SHOME", "kind": "Gdef"}, "KEY_SIC": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SIC", "kind": "Gdef"}, "KEY_SLEFT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SLEFT", "kind": "Gdef"}, "KEY_SMESSAGE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SMESSAGE", "kind": "Gdef"}, "KEY_SMOVE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SMOVE", "kind": "Gdef"}, "KEY_SNEXT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SNEXT", "kind": "Gdef"}, "KEY_SOPTIONS": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SOPTIONS", "kind": "Gdef"}, "KEY_SPREVIOUS": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SPREVIOUS", "kind": "Gdef"}, "KEY_SPRINT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SPRINT", "kind": "Gdef"}, "KEY_SR": {".class": "SymbolTableNode", "cross_ref": "_curses.<PERSON><PERSON><PERSON>_<PERSON>", "kind": "Gdef"}, "KEY_SREDO": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SREDO", "kind": "Gdef"}, "KEY_SREPLACE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SREPLACE", "kind": "Gdef"}, "KEY_SRESET": {".class": "SymbolTableNode", "cross_ref": "_curses.<PERSON>E<PERSON>_SRESET", "kind": "Gdef"}, "KEY_SRIGHT": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SRIGHT", "kind": "Gdef"}, "KEY_SRSUME": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SRSUME", "kind": "Gdef"}, "KEY_SSAVE": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SSAVE", "kind": "Gdef"}, "KEY_SSUSPEND": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SSUSPEND", "kind": "Gdef"}, "KEY_STAB": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_STAB", "kind": "Gdef"}, "KEY_SUNDO": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SUNDO", "kind": "Gdef"}, "KEY_SUSPEND": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_SUSPEND", "kind": "Gdef"}, "KEY_UNDO": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_UNDO", "kind": "Gdef"}, "KEY_UP": {".class": "SymbolTableNode", "cross_ref": "_curses.KEY_UP", "kind": "Gdef"}, "LINES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.LINES", "name": "LINES", "type": "builtins.int"}}, "OK": {".class": "SymbolTableNode", "cross_ref": "_curses.OK", "kind": "Gdef"}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "REPORT_MOUSE_POSITION": {".class": "SymbolTableNode", "cross_ref": "_curses.REPORT_MOUSE_POSITION", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CursesWindow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "curses._CursesWindow", "line": 29, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "_curses.window"}}, "_P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "curses._P", "name": "_P", "upper_bound": "builtins.object", "variance": 0}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "curses._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "curses.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_ncurses_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_typeshed.structseq"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "curses._ncurses_version", "name": "_ncurses_version", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "curses._ncurses_version", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "curses", "mro": ["curses._ncurses_version", "_typeshed.structseq", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "curses._ncurses_version.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "major"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "minor"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "patch"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "major": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "curses._ncurses_version.major", "name": "major", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "curses._ncurses_version"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "major of _ncurses_version", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "curses._ncurses_version.major", "name": "major", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "curses._ncurses_version"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "major of _ncurses_version", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "minor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "curses._ncurses_version.minor", "name": "minor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "curses._ncurses_version"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "minor of _ncurses_version", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "curses._ncurses_version.minor", "name": "minor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "curses._ncurses_version"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "minor of _ncurses_version", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "curses._ncurses_version.patch", "name": "patch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "curses._ncurses_version"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch of _ncurses_version", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "curses._ncurses_version.patch", "name": "patch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "curses._ncurses_version"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch of _ncurses_version", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "curses._ncurses_version.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "curses._ncurses_version"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "baudrate": {".class": "SymbolTableNode", "cross_ref": "_curses.baudrate", "kind": "Gdef"}, "beep": {".class": "SymbolTableNode", "cross_ref": "_curses.beep", "kind": "Gdef"}, "can_change_color": {".class": "SymbolTableNode", "cross_ref": "_curses.can_change_color", "kind": "Gdef"}, "cbreak": {".class": "SymbolTableNode", "cross_ref": "_curses.cbreak", "kind": "Gdef"}, "color_content": {".class": "SymbolTableNode", "cross_ref": "_curses.color_content", "kind": "Gdef"}, "color_pair": {".class": "SymbolTableNode", "cross_ref": "_curses.color_pair", "kind": "Gdef"}, "curs_set": {".class": "SymbolTableNode", "cross_ref": "_curses.curs_set", "kind": "Gdef"}, "def_prog_mode": {".class": "SymbolTableNode", "cross_ref": "_curses.def_prog_mode", "kind": "Gdef"}, "def_shell_mode": {".class": "SymbolTableNode", "cross_ref": "_curses.def_shell_mode", "kind": "Gdef"}, "delay_output": {".class": "SymbolTableNode", "cross_ref": "_curses.delay_output", "kind": "Gdef"}, "doupdate": {".class": "SymbolTableNode", "cross_ref": "_curses.doupdate", "kind": "Gdef"}, "echo": {".class": "SymbolTableNode", "cross_ref": "_curses.echo", "kind": "Gdef"}, "endwin": {".class": "SymbolTableNode", "cross_ref": "_curses.endwin", "kind": "Gdef"}, "erasechar": {".class": "SymbolTableNode", "cross_ref": "_curses.erasechar", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "cross_ref": "_curses.error", "kind": "Gdef"}, "filter": {".class": "SymbolTableNode", "cross_ref": "_curses.filter", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "flash": {".class": "SymbolTableNode", "cross_ref": "_curses.flash", "kind": "Gdef"}, "flushinp": {".class": "SymbolTableNode", "cross_ref": "_curses.flushinp", "kind": "Gdef"}, "get_escdelay": {".class": "SymbolTableNode", "cross_ref": "_curses.get_escdelay", "kind": "Gdef"}, "get_tabsize": {".class": "SymbolTableNode", "cross_ref": "_curses.get_tabsize", "kind": "Gdef"}, "getmouse": {".class": "SymbolTableNode", "cross_ref": "_curses.getmouse", "kind": "Gdef"}, "getsyx": {".class": "SymbolTableNode", "cross_ref": "_curses.getsyx", "kind": "Gdef"}, "getwin": {".class": "SymbolTableNode", "cross_ref": "_curses.getwin", "kind": "Gdef"}, "halfdelay": {".class": "SymbolTableNode", "cross_ref": "_curses.half<PERSON>y", "kind": "Gdef"}, "has_colors": {".class": "SymbolTableNode", "cross_ref": "_curses.has_colors", "kind": "Gdef"}, "has_extended_color_support": {".class": "SymbolTableNode", "cross_ref": "_curses.has_extended_color_support", "kind": "Gdef"}, "has_ic": {".class": "SymbolTableNode", "cross_ref": "_curses.has_ic", "kind": "Gdef"}, "has_il": {".class": "SymbolTableNode", "cross_ref": "_curses.has_il", "kind": "Gdef"}, "has_key": {".class": "SymbolTableNode", "cross_ref": "_curses.has_key", "kind": "Gdef"}, "init_color": {".class": "SymbolTableNode", "cross_ref": "_curses.init_color", "kind": "Gdef"}, "init_pair": {".class": "SymbolTableNode", "cross_ref": "_curses.init_pair", "kind": "Gdef"}, "initscr": {".class": "SymbolTableNode", "cross_ref": "_curses.initscr", "kind": "Gdef"}, "intrflush": {".class": "SymbolTableNode", "cross_ref": "_curses.intrflush", "kind": "Gdef"}, "is_term_resized": {".class": "SymbolTableNode", "cross_ref": "_curses.is_term_resized", "kind": "Gdef"}, "isendwin": {".class": "SymbolTableNode", "cross_ref": "_curses.isendwin", "kind": "Gdef"}, "keyname": {".class": "SymbolTableNode", "cross_ref": "_curses.keyname", "kind": "Gdef"}, "killchar": {".class": "SymbolTableNode", "cross_ref": "_curses.killchar", "kind": "Gdef"}, "longname": {".class": "SymbolTableNode", "cross_ref": "_curses.longname", "kind": "Gdef"}, "meta": {".class": "SymbolTableNode", "cross_ref": "_curses.meta", "kind": "Gdef"}, "mouseinterval": {".class": "SymbolTableNode", "cross_ref": "_curses.mouseinterval", "kind": "Gdef"}, "mousemask": {".class": "SymbolTableNode", "cross_ref": "_curses.mousemask", "kind": "Gdef"}, "napms": {".class": "SymbolTableNode", "cross_ref": "_curses.napms", "kind": "Gdef"}, "ncurses_version": {".class": "SymbolTableNode", "cross_ref": "_curses.ncurses_version", "kind": "Gdef"}, "newpad": {".class": "SymbolTableNode", "cross_ref": "_curses.newpad", "kind": "Gdef"}, "newwin": {".class": "SymbolTableNode", "cross_ref": "_curses.newwin", "kind": "Gdef"}, "nl": {".class": "SymbolTableNode", "cross_ref": "_curses.nl", "kind": "Gdef"}, "nocbreak": {".class": "SymbolTableNode", "cross_ref": "_curses.nocbreak", "kind": "Gdef"}, "noecho": {".class": "SymbolTableNode", "cross_ref": "_curses.noecho", "kind": "Gdef"}, "nonl": {".class": "SymbolTableNode", "cross_ref": "_curses.nonl", "kind": "Gdef"}, "noqiflush": {".class": "SymbolTableNode", "cross_ref": "_curses.noqiflush", "kind": "Gdef"}, "noraw": {".class": "SymbolTableNode", "cross_ref": "_curses.noraw", "kind": "Gdef"}, "pair_content": {".class": "SymbolTableNode", "cross_ref": "_curses.pair_content", "kind": "Gdef"}, "pair_number": {".class": "SymbolTableNode", "cross_ref": "_curses.pair_number", "kind": "Gdef"}, "putp": {".class": "SymbolTableNode", "cross_ref": "_curses.putp", "kind": "Gdef"}, "qiflush": {".class": "SymbolTableNode", "cross_ref": "_curses.qiflush", "kind": "Gdef"}, "raw": {".class": "SymbolTableNode", "cross_ref": "_curses.raw", "kind": "Gdef"}, "reset_prog_mode": {".class": "SymbolTableNode", "cross_ref": "_curses.reset_prog_mode", "kind": "Gdef"}, "reset_shell_mode": {".class": "SymbolTableNode", "cross_ref": "_curses.reset_shell_mode", "kind": "Gdef"}, "resetty": {".class": "SymbolTableNode", "cross_ref": "_curses.resetty", "kind": "Gdef"}, "resize_term": {".class": "SymbolTableNode", "cross_ref": "_curses.resize_term", "kind": "Gdef"}, "resizeterm": {".class": "SymbolTableNode", "cross_ref": "_curses.resizeterm", "kind": "Gdef"}, "savetty": {".class": "SymbolTableNode", "cross_ref": "_curses.savetty", "kind": "Gdef"}, "set_escdelay": {".class": "SymbolTableNode", "cross_ref": "_curses.set_escdelay", "kind": "Gdef"}, "set_tabsize": {".class": "SymbolTableNode", "cross_ref": "_curses.set_tabsize", "kind": "Gdef"}, "setsyx": {".class": "SymbolTableNode", "cross_ref": "_curses.setsyx", "kind": "Gdef"}, "setupterm": {".class": "SymbolTableNode", "cross_ref": "_curses.setupterm", "kind": "Gdef"}, "start_color": {".class": "SymbolTableNode", "cross_ref": "_curses.start_color", "kind": "Gdef"}, "structseq": {".class": "SymbolTableNode", "cross_ref": "_typeshed.structseq", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "termattrs": {".class": "SymbolTableNode", "cross_ref": "_curses.termattrs", "kind": "Gdef"}, "termname": {".class": "SymbolTableNode", "cross_ref": "_curses.termname", "kind": "Gdef"}, "tigetflag": {".class": "SymbolTableNode", "cross_ref": "_curses.tigetflag", "kind": "Gdef"}, "tigetnum": {".class": "SymbolTableNode", "cross_ref": "_curses.tigetnum", "kind": "Gdef"}, "tigetstr": {".class": "SymbolTableNode", "cross_ref": "_curses.tigetstr", "kind": "Gdef"}, "tparm": {".class": "SymbolTableNode", "cross_ref": "_curses.tparm", "kind": "Gdef"}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typeahead": {".class": "SymbolTableNode", "cross_ref": "_curses.typeahead", "kind": "Gdef"}, "unctrl": {".class": "SymbolTableNode", "cross_ref": "_curses.unctrl", "kind": "Gdef"}, "unget_wch": {".class": "SymbolTableNode", "cross_ref": "_curses.unget_wch", "kind": "Gdef"}, "ungetch": {".class": "SymbolTableNode", "cross_ref": "_curses.ungetch", "kind": "Gdef"}, "ungetmouse": {".class": "SymbolTableNode", "cross_ref": "_curses.ungetmouse", "kind": "Gdef"}, "update_lines_cols": {".class": "SymbolTableNode", "cross_ref": "_curses.update_lines_cols", "kind": "Gdef"}, "use_default_colors": {".class": "SymbolTableNode", "cross_ref": "_curses.use_default_colors", "kind": "Gdef"}, "use_env": {".class": "SymbolTableNode", "cross_ref": "_curses.use_env", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "_curses.version", "kind": "Gdef"}, "window": {".class": "SymbolTableNode", "cross_ref": "_curses.window", "kind": "Gdef"}, "wrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": [null, "arg", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "curses.wrapper", "name": "wrapper", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "arg", "kwds"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": ["_curses.window", {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "curses._P", "id": -1, "name": "_P", "namespace": "curses.wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "curses._P", "id": -1, "name": "_P", "namespace": "curses.wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "curses._T", "id": -2, "name": "_T", "namespace": "curses.wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "curses._P", "id": -1, "name": "_P", "namespace": "curses.wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "curses._P", "id": -1, "name": "_P", "namespace": "curses.wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrapper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "curses._T", "id": -2, "name": "_T", "namespace": "curses.wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "curses._P", "id": -1, "name": "_P", "namespace": "curses.wrapper", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "curses._T", "id": -2, "name": "_T", "namespace": "curses.wrapper", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\curses\\__init__.pyi"}