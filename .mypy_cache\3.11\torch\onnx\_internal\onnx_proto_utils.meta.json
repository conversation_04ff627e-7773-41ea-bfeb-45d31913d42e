{"data_mtime": 1755649448, "dep_lines": [15, 15, 12, 14, 15, 12, 13, 14, 20, 4, 6, 7, 8, 9, 11, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 20, 10, 20, 25, 5, 10, 10, 10, 5, 10, 25, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 20, 20], "dependencies": ["torch.onnx._internal.jit_utils", "torch.onnx._internal.registration", "torch.jit._trace", "torch.onnx.errors", "torch.onnx._internal", "torch.jit", "torch.serialization", "torch.onnx", "collections.abc", "__future__", "glob", "os", "shutil", "typing", "torch", "io", "builtins", "collections", "warnings", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "_frozen_importlib", "_io", "abc", "contextlib"], "hash": "8938b6da654d3194ef3289556ef84d0acb2b8bc2", "id": "torch.onnx._internal.onnx_proto_utils", "ignore_all": true, "interface_hash": "87c6a3f1193053b6683e204dde3b11b60e1d103d", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\onnx_proto_utils.py", "plugin_data": null, "size": 9449, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}