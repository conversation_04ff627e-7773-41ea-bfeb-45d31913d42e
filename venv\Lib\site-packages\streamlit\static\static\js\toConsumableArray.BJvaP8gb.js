import{r as a,E as m,_ as d}from"./index.DKN5MVff.js";import{e as f,f as b}from"./possibleConstructorReturn.Bd4ImlQ9.js";var p=a.forwardRef(function(r,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return a.createElement(m,d({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},r,{ref:t}),a.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),a.createElement("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"}))});p.displayName="Close";function h(r){if(Array.isArray(r))return r}function A(r,t){var e=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(e!=null){var i,u,s,o,l=[],n=!0,c=!1;try{if(s=(e=e.call(r)).next,t===0){if(Object(e)!==e)return;n=!1}else for(;!(n=(i=s.call(e)).done)&&(l.push(i.value),l.length!==t);n=!0);}catch(y){c=!0,u=y}finally{try{if(!n&&e.return!=null&&(o=e.return(),Object(o)!==o))return}finally{if(c)throw u}}return l}}function _(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function T(r,t){return h(r)||A(r,t)||f(r,t)||_()}function v(r){if(Array.isArray(r))return b(r)}function w(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function E(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function x(r){return v(r)||w(r)||f(r)||E()}export{p as C,T as _,x as a};
