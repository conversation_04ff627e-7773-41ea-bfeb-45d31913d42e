{".class": "MypyFile", "_fullname": "torch._inductor.fx_passes.micro_pipeline_tp", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CallFunction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.CallFunction", "kind": "Gdef"}, "Ignored": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Ignored", "kind": "Gdef"}, "KeywordArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.KeywordArg", "kind": "Gdef"}, "ListOf": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.ListOf", "kind": "Gdef"}, "MULTIPLE": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.MULTIPLE", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Match", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "PatternExpr": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.PatternExpr", "kind": "Gdef"}, "PatternMatcherPass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.PatternMatcherPass", "kind": "Gdef"}, "_AllGatherMatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch", "name": "_AllGatherMatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 70, "name": "match", "type": "torch._inductor.pattern_matcher.Match"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 71, "name": "shard_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 72, "name": "ag_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "res_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "gather_dim", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "group_name", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.fx_passes.micro_pipeline_tp", "mro": ["torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "match", "shard_node", "ag_node", "res_node", "gather_dim", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "match", "shard_node", "ag_node", "res_node", "gather_dim", "group_name"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch", "torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node", "torch.fx.node.Node", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AllGatherMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "match"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shard_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ag_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "res_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gather_dim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "group_name"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["match", "shard_node", "ag_node", "res_node", "gather_dim", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["match", "shard_node", "ag_node", "res_node", "gather_dim", "group_name"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node", "torch.fx.node.Node", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _AllGatherMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["match", "shard_node", "ag_node", "res_node", "gather_dim", "group_name"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node", "torch.fx.node.Node", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _AllGatherMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "ag_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.ag_node", "name": "ag_node", "type": "torch.fx.node.Node"}}, "erase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.erase", "name": "erase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "erase of _AllGatherMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gather_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.gather_dim", "name": "gather_dim", "type": "builtins.int"}}, "group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.group_name", "name": "group_name", "type": "builtins.str"}}, "match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.match", "name": "match", "type": "torch._inductor.pattern_matcher.Match"}}, "replace_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.replace_with", "name": "replace_with", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_node"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_with of _AllGatherMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "res_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.res_node", "name": "res_node", "type": "torch.fx.node.Node"}}, "shard_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.shard_node", "name": "shard_node", "type": "torch.fx.node.Node"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Matmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "name": "_<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 373, "name": "nodes", "type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 374, "name": "arg_ancestor_nodes", "type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 375, "name": "A_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 376, "name": "B_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 377, "name": "pre_mm_reshape", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 378, "name": "post_mm_reshape", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.fx_passes.micro_pipeline_tp", "mro": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "builtins.object"], "names": {".class": "SymbolTable", "A_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.A_node", "name": "A_node", "type": "torch.fx.node.Node"}}, "B_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.B_node", "name": "B_node", "type": "torch.fx.node.Node"}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _<PERSON><PERSON>l", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nodes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "A_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "B_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pre_mm_reshape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "post_mm_reshape"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of _<PERSON><PERSON>l", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["nodes", "arg_ancestor_nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["nodes", "arg_ancestor_nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape"], "arg_types": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["nodes", "arg_ancestor_nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape"], "arg_types": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.__post_init__", "name": "__post_init__", "type": null}}, "arg_ancestor_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.arg_ancestor_nodes", "name": "arg_ancestor_nodes", "type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "erase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.erase", "name": "erase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "erase of _<PERSON><PERSON>l", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "match"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.from_match", "name": "from_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "match"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_match of _<PERSON><PERSON>l", "ret_type": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.from_match", "name": "from_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "match"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_match of _<PERSON><PERSON>l", "ret_type": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.nodes", "name": "nodes", "type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "post_mm_reshape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.post_mm_reshape", "name": "post_mm_reshape", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pre_mm_reshape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.pre_mm_reshape", "name": "pre_mm_reshape", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "replace_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.replace_with", "name": "replace_with", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_node"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_with of _<PERSON><PERSON>l", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ReduceScatterMatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch", "name": "_ReduceScatterMatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 205, "name": "match", "type": "torch._inductor.pattern_matcher.Match"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 206, "name": "input_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 207, "name": "reduce_scatter_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 208, "name": "wait_tensor_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 209, "name": "reduce_op", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 210, "name": "scatter_dim", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 211, "name": "group_name", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.fx_passes.micro_pipeline_tp", "mro": ["torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "match", "input_node", "reduce_scatter_node", "wait_tensor_node", "reduce_op", "scatter_dim", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "match", "input_node", "reduce_scatter_node", "wait_tensor_node", "reduce_op", "scatter_dim", "group_name"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch", "torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node", "torch.fx.node.Node", "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ReduceScatterMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "match"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "input_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reduce_scatter_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wait_tensor_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reduce_op"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scatter_dim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "group_name"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["match", "input_node", "reduce_scatter_node", "wait_tensor_node", "reduce_op", "scatter_dim", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["match", "input_node", "reduce_scatter_node", "wait_tensor_node", "reduce_op", "scatter_dim", "group_name"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node", "torch.fx.node.Node", "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _ReduceScatterMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["match", "input_node", "reduce_scatter_node", "wait_tensor_node", "reduce_op", "scatter_dim", "group_name"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node", "torch.fx.node.Node", "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _ReduceScatterMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_update_save_for_backward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch._update_save_for_backward", "name": "_update_save_for_backward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_node"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_save_for_backward of _ReduceScatterMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "erase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.erase", "name": "erase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "erase of _ReduceScatterMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.group_name", "name": "group_name", "type": "builtins.str"}}, "input_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.input_node", "name": "input_node", "type": "torch.fx.node.Node"}}, "match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.match", "name": "match", "type": "torch._inductor.pattern_matcher.Match"}}, "reduce_op": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.reduce_op", "name": "reduce_op", "type": "builtins.str"}}, "reduce_scatter_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.reduce_scatter_node", "name": "reduce_scatter_node", "type": "torch.fx.node.Node"}}, "replace_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.replace_with", "name": "replace_with", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_node"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_with of _ReduceScatterMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scatter_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.scatter_dim", "name": "scatter_dim", "type": "builtins.int"}}, "wait_tensor_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.wait_tensor_node", "name": "wait_tensor_node", "type": "torch.fx.node.Node"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ScaledMatmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul", "name": "_ScaledMatmul", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 373, "name": "nodes", "type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 374, "name": "arg_ancestor_nodes", "type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 375, "name": "A_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 376, "name": "B_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 457, "name": "pre_mm_reshape", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 458, "name": "post_mm_reshape", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 451, "name": "A_scale_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 452, "name": "B_scale_node", "type": "torch.fx.node.Node"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 453, "name": "bias_node", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 454, "name": "result_scale_node", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 455, "name": "out_dtype", "type": {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 456, "name": "use_fast_accum", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.fx_passes.micro_pipeline_tp", "mro": ["torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul", "torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "builtins.object"], "names": {".class": "SymbolTable", "A_scale_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.A_scale_node", "name": "A_scale_node", "type": "torch.fx.node.Node"}}, "B_scale_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.B_scale_node", "name": "B_scale_node", "type": "torch.fx.node.Node"}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape", "A_scale_node", "B_scale_node", "bias_node", "result_scale_node", "out_dtype", "use_fast_accum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape", "A_scale_node", "B_scale_node", "bias_node", "result_scale_node", "out_dtype", "use_fast_accum"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ScaledMatmul", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nodes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "A_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "B_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pre_mm_reshape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "post_mm_reshape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "A_scale_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "B_scale_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bias_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "result_scale_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out_dtype"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_fast_accum"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of _ScaledMatmul", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["nodes", "arg_ancestor_nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape", "A_scale_node", "B_scale_node", "bias_node", "result_scale_node", "out_dtype", "use_fast_accum"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["nodes", "arg_ancestor_nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape", "A_scale_node", "B_scale_node", "bias_node", "result_scale_node", "out_dtype", "use_fast_accum"], "arg_types": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _ScaledMatmul", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["nodes", "arg_ancestor_nodes", "A_node", "B_node", "pre_mm_reshape", "post_mm_reshape", "A_scale_node", "B_scale_node", "bias_node", "result_scale_node", "out_dtype", "use_fast_accum"], "arg_types": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.fx.node.Node", "torch.fx.node.Node", {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _ScaledMatmul", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.__post_init__", "name": "__post_init__", "type": null}}, "bias_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.bias_node", "name": "bias_node", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "from_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "match"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.from_match", "name": "from_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "match"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_match of _ScaledMatmul", "ret_type": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.from_match", "name": "from_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "match"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_match of _ScaledMatmul", "ret_type": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "out_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.out_dtype", "name": "out_dtype", "type": {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "post_mm_reshape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.post_mm_reshape", "name": "post_mm_reshape", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pre_mm_reshape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.pre_mm_reshape", "name": "pre_mm_reshape", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "result_scale_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.result_scale_node", "name": "result_scale_node", "type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "use_fast_accum": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.use_fast_accum", "name": "use_fast_accum", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.micro_pipeline_tp._ScaledMatmul", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_compute_mm_arithmetic_intensity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["M", "N", "K"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._compute_mm_arithmetic_intensity", "name": "_compute_mm_arithmetic_intensity", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["M", "N", "K"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_mm_arithmetic_intensity", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_filter_nodes_by_target": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["nodes", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._filter_nodes_by_target", "name": "_filter_nodes_by_target", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["nodes", "target"], "arg_types": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_nodes_by_target", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_ancestors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._find_ancestors", "name": "_find_ancestors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_ancestors", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_consumer_matmuls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._find_consumer_matmuls", "name": "_find_consumer_matmuls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_consumer_matmuls", "ret_type": {".class": "Instance", "args": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_producer_matmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._find_producer_matmul", "name": "_find_producer_matmul", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_producer_matmul", "ret_type": {".class": "UnionType", "items": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_reshape_mm_reshape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._find_reshape_mm_reshape", "name": "_find_reshape_mm_reshape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_reshape_mm_reshape", "ret_type": {".class": "Instance", "args": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_collective_to_overlappable_nodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._get_collective_to_overlappable_nodes", "name": "_get_collective_to_overlappable_nodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_collective_to_overlappable_nodes", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_node_to_ancestors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._get_node_to_ancestors", "name": "_get_node_to_ancestors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_node_to_ancestors", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._get_tensor", "name": "_get_tensor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_tensor", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_unexposed_collectives": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._get_unexposed_collectives", "name": "_get_unexposed_collectives", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_unexposed_collectives", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_insert_fused_all_gather_matmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["graph", "<PERSON><PERSON>ls", "shard_node", "gather_dim", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._insert_fused_all_gather_matmul", "name": "_insert_fused_all_gather_matmul", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["graph", "<PERSON><PERSON>ls", "shard_node", "gather_dim", "group_name"], "arg_types": ["torch.fx.graph.Graph", {".class": "Instance", "args": ["torch._inductor.fx_passes.micro_pipeline_tp._Matmul"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.fx.node.Node", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_insert_fused_all_gather_matmul", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_insert_fused_matmul_reduce_scatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["graph", "<PERSON><PERSON>l", "reduce_op", "orig_scatter_dim", "group_name", "scatter_dim_after_reshape", "output_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._insert_fused_matmul_reduce_scatter", "name": "_insert_fused_matmul_reduce_scatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["graph", "<PERSON><PERSON>l", "reduce_op", "orig_scatter_dim", "group_name", "scatter_dim_after_reshape", "output_shape"], "arg_types": ["torch.fx.graph.Graph", "torch._inductor.fx_passes.micro_pipeline_tp._Matmul", "builtins.str", "builtins.int", "builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_insert_fused_matmul_reduce_scatter", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_backward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._is_backward", "name": "_is_backward", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_backward", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scatter_dim_after_reshape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["reshape_node", "orig_scatter_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp._scatter_dim_after_reshape", "name": "_scatter_dim_after_reshape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["reshape_node", "orig_scatter_dim"], "arg_types": ["torch.fx.node.Node", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scatter_dim_after_reshape", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "find_all_gather_patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.find_all_gather_patterns", "name": "find_all_gather_patterns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_all_gather_patterns", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_reduce_scatter_patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.find_reduce_scatter_patterns", "name": "find_reduce_scatter_patterns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_reduce_scatter_patterns", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse_all_gather_matmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["all_gather"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.fuse_all_gather_matmul", "name": "fuse_all_gather_matmul", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["all_gather"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._AllGatherMatch"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse_all_gather_matmul", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse_matmul_reduce_scatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["reduce_scatter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.fuse_matmul_reduce_scatter", "name": "fuse_matmul_reduce_scatter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["reduce_scatter"], "arg_types": ["torch._inductor.fx_passes.micro_pipeline_tp._ReduceScatterMatch"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse_matmul_reduce_scatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inductor_prims": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.inductor_prims", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "micro_pipeline_tp_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.micro_pipeline_tp_pass", "name": "micro_pipeline_tp_pass", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "micro_pipeline_tp_pass", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.micro_pipeline_tp.patterns", "name": "patterns", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "prod": {".class": "SymbolTableNode", "cross_ref": "math.prod", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\micro_pipeline_tp.py"}