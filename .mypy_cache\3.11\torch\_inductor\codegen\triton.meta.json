{"data_mtime": 1755649448, "dep_lines": [28, 31, 32, 37, 38, 39, 45, 65, 66, 83, 91, 98, 108, 4326, 23, 24, 25, 27, 29, 33, 33, 33, 34, 35, 36, 37, 46, 47, 63, 64, 105, 14, 22, 23, 26, 33, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 21, 102, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19, 18], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 10, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 20, 5, 5, 5, 5, 20, 5, 10, 20, 5, 20, 5, 10, 10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["torch.utils._sympy.functions", "torch.utils._sympy.symbol", "torch.utils._sympy.value_ranges", "torch._inductor.runtime.triton_heuristics", "torch._inductor.runtime.benchmarking", "torch._inductor.runtime.hints", "torch._inductor.runtime.runtime_utils", "torch._inductor.codegen.block_analysis", "torch._inductor.codegen.common", "torch._inductor.codegen.simd", "torch._inductor.codegen.triton_utils", "torch._inductor.codegen.wrapper", "torch._inductor.codegen.simd_kernel_features", "torch._inductor.codegen.triton_split_scan", "torch.utils._pytree", "torch._dynamo.device_interface", "torch._dynamo.utils", "torch.utils._ordered_set", "torch.utils._triton", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.metrics", "torch._inductor.async_compile", "torch._inductor.codecache", "torch._inductor.ops_handler", "torch._inductor.runtime", "torch._inductor.scheduler", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor.wrapper_benchmark", "torch._inductor.dtype_propagation", "collections.abc", "torch._logging", "torch.utils", "torch._prims_common", "torch._inductor", "__future__", "collections", "contextlib", "dataclasses", "functools", "itertools", "logging", "math", "operator", "os", "textwrap", "typing", "torch", "types", "builtins", "inspect", "html", "sys", "string", "pprint", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._C._VariableFunctions", "torch._dynamo", "torch._inductor.graph", "torch._inductor.sizevars", "torch._logging._internal", "torch.fx", "torch.fx.interpreter", "torch.utils._sympy", "torch.utils._sympy.printers", "torch.version", "typing_extensions"], "hash": "afa9deff2cc351342b6215d910d5101ed92dbc72", "id": "torch._inductor.codegen.triton", "ignore_all": true, "interface_hash": "9892dfc4e5f2693c1da8fbe334a1f78b34c8b5b2", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\triton.py", "plugin_data": null, "size": 181775, "suppressed": ["sympy.printing.precedence", "sympy"], "version_id": "1.15.0"}