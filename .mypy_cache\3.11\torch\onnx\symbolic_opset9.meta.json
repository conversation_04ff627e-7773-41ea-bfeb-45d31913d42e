{"data_mtime": 1755649449, "dep_lines": [22, 29, 29, 21, 22, 27, 27, 27, 27, 28, 29, 21, 22, 23, 33, 35, 10, 12, 13, 14, 15, 16, 17, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 10, 10, 10, 10, 5, 20, 10, 20, 10, 25, 25, 5, 10, 10, 10, 10, 10, 5, 5, 10, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30], "dependencies": ["torch.nn.modules.utils", "torch.onnx._internal.jit_utils", "torch.onnx._internal.registration", "torch._<PERSON>._onnx", "torch.nn.modules", "torch.onnx._constants", "torch.onnx._type_utils", "torch.onnx.errors", "torch.onnx.symbolic_helper", "torch.onnx._globals", "torch.onnx._internal", "torch._C", "torch.nn", "torch.onnx", "collections.abc", "torch.types", "__future__", "builtins", "functools", "math", "sys", "warnings", "typing", "typing_extensions", "torch", "os", "inspect", "html", "string", "operator", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "contextlib", "copy", "_frozen_importlib", "abc"], "hash": "3789714a20cdef42a68b3c0f6b10cd9c42bd4ff3", "id": "torch.onnx.symbolic_opset9", "ignore_all": true, "interface_hash": "bad130df9ccf1763f0c4c20a56d56fe6e4a6e1a4", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\symbolic_opset9.py", "plugin_data": null, "size": 231898, "suppressed": [], "version_id": "1.15.0"}