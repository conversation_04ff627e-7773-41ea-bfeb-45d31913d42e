{"data_mtime": 1755649450, "dep_lines": [13, 14, 15, 19, 20, 21, 31, 40, 49, 52, 50, 52, 4, 5, 6, 7, 8, 9, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 20, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed._shard._utils", "torch.distributed.checkpoint._dedup_save_plans", "torch.distributed.checkpoint._nested_dict", "torch.distributed.checkpoint._sharded_tensor_utils", "torch.distributed.checkpoint._traverse", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.planner_helpers", "torch.distributed.checkpoint.utils", "torch.distributed.checkpoint._version", "torch.distributed.tensor", "torch.distributed.checkpoint", "dataclasses", "io", "logging", "operator", "collections", "functools", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "torch.distributed", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "copy", "_collections_abc", "_frozen_importlib", "_io", "abc", "torch._C", "torch._tensor"], "hash": "d3bdf19488adcfa41f14b990dd6e6d55cd076bb2", "id": "torch.distributed.checkpoint.default_planner", "ignore_all": true, "interface_hash": "ebebf47b11ae6cba23f4333d88c964792d5446e3", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\default_planner.py", "plugin_data": null, "size": 26881, "suppressed": [], "version_id": "1.15.0"}