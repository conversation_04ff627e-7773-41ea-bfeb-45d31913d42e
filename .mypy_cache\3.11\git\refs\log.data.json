{".class": "MypyFile", "_fullname": "git.refs.log", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Actor": {".class": "SymbolTableNode", "cross_ref": "git.util.Actor", "kind": "Gdef", "module_public": false}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef", "module_public": false}, "GitConfigParser": {".class": "SymbolTableNode", "cross_ref": "git.config.GitConfigParser", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "LockFile": {".class": "SymbolTableNode", "cross_ref": "git.util.LockFile", "kind": "Gdef", "module_public": false}, "LockedFD": {".class": "SymbolTableNode", "cross_ref": "git.util.LockedFD", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "RefLog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "git.objects.util.Serializable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.refs.log.RefLog", "name": "RefLog", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLog", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.refs.log", "mro": ["git.refs.log.RefLog", "builtins.list", "typing.MutableSequence", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "git.objects.util.Serializable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLog.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "filepath"], "arg_types": ["git.refs.log.RefLog", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RefLog", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "git.refs.log.RefLog.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of RefLog", "ret_type": "git.refs.log.RefLog", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.refs.log.RefLog.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_deserialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLog._deserialize", "name": "_deserialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["git.refs.log.RefLog", "_io.BytesIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deserialize of RefLog", "ret_type": "git.refs.log.RefLog", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.refs.log.RefLog._path", "name": "_path", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_read_from_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLog._read_from_file", "name": "_read_from_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.refs.log.RefLog"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_from_file of RefLog", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLog._serialize", "name": "_serialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["git.refs.log.RefLog", "_io.BytesIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serialize of RefLog", "ret_type": "git.refs.log.RefLog", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "config_reader", "filepath", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "message", "write"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.log.RefLog.append_entry", "name": "append_entry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "config_reader", "filepath", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "message", "write"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "UnionType", "items": ["git.util.Actor", "git.config.GitConfigParser", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "git.config.SectionConstraint"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_entry of RefLog", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLog.append_entry", "name": "append_entry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "config_reader", "filepath", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "message", "write"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "UnionType", "items": ["git.util.Actor", "git.config.GitConfigParser", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "git.config.SectionConstraint"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_entry of RefLog", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "entry_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "filepath", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.log.RefLog.entry_at", "name": "entry_at", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "filepath", "index"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entry_at of RefLog", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLog.entry_at", "name": "entry_at", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "filepath", "index"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entry_at of RefLog", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.log.RefLog.from_file", "name": "from_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_file of RefLog", "ret_type": "git.refs.log.RefLog", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLog.from_file", "name": "from_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_file of RefLog", "ret_type": "git.refs.log.RefLog", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "iter_entries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.log.RefLog.iter_entries", "name": "iter_entries", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "stream"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "UnionType", "items": ["builtins.str", "_io.BytesIO", "mmap.mmap"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_entries of RefLog", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLog.iter_entries", "name": "iter_entries", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "stream"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, {".class": "UnionType", "items": ["builtins.str", "_io.BytesIO", "mmap.mmap"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_entries of RefLog", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.log.RefLog.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "ref"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, "git.refs.symbolic.SymbolicReference"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of RefLog", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLog.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "ref"], "arg_types": [{".class": "TypeType", "item": "git.refs.log.RefLog"}, "git.refs.symbolic.SymbolicReference"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of RefLog", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLog.to_file", "name": "to_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "arg_types": ["git.refs.log.RefLog", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_file of RefLog", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLog.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.refs.log.RefLog"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of <PERSON>f<PERSON><PERSON>", "ret_type": "git.refs.log.RefLog", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.refs.log.RefLog.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.refs.log.RefLog", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RefLogEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.refs.log.RefLogEntry", "name": "RefLogEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLogEntry", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.refs.log", "mro": ["git.refs.log.RefLogEntry", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLogEntry.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of RefLogEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.refs.log.RefLogEntry.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_re_hexsha_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.refs.log.RefLogEntry._re_hexsha_only", "name": "_re_hexsha_only", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "actor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.refs.log.RefLogEntry.actor", "name": "actor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actor of RefLogEntry", "ret_type": "git.util.Actor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLogEntry.actor", "name": "actor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actor of RefLogEntry", "ret_type": "git.util.Actor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refs.log.RefLogEntry.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of RefLogEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.log.RefLogEntry.from_line", "name": "from_line", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "line"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_line of RefLogEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLogEntry.from_line", "name": "from_line", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "line"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_line of RefLogEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.refs.log.RefLogEntry.message", "name": "message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "message of RefLogEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLogEntry.message", "name": "message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "message of RefLogEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "<PERSON><PERSON><PERSON><PERSON>", "newhexsha", "actor", "time", "tz_offset", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.log.RefLogEntry.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "<PERSON><PERSON><PERSON><PERSON>", "newhexsha", "actor", "time", "tz_offset", "message"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}}, "builtins.str", "builtins.str", "git.util.Actor", "builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of RefLogEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLogEntry.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "<PERSON><PERSON><PERSON><PERSON>", "newhexsha", "actor", "time", "tz_offset", "message"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}}, "builtins.str", "builtins.str", "git.util.Actor", "builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of RefLogEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.refs.log.RefLogEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "newhexsha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.refs.log.RefLogEntry.newhexsha", "name": "newhexsha", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "newhexsha of RefLogEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLogEntry.newhexsha", "name": "newhexsha", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "newhexsha of RefLogEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "oldhexsha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.refs.log.RefLogEntry.oldhexsha", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "oldhexsha of RefLogEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLogEntry.oldhexsha", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "oldhexsha of RefLogEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.refs.log.RefLogEntry.time", "name": "time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "time of RefLogEntry", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.refs.log.RefLogEntry.time", "name": "time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "time of RefLogEntry", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.refs.log.RefLogEntry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": "git.refs.log.RefLogEntry"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "git.util.Actor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "SectionConstraint": {".class": "SymbolTableNode", "cross_ref": "git.config.SectionConstraint", "kind": "Gdef", "module_public": false}, "Serializable": {".class": "SymbolTableNode", "cross_ref": "git.objects.util.Serializable", "kind": "Gdef", "module_public": false}, "SymbolicReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.symbolic.SymbolicReference", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.refs.log.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.log.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.log.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.log.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.log.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.log.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.log.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "altz_to_utctz_str": {".class": "SymbolTableNode", "cross_ref": "git.objects.util.altz_to_utctz_str", "kind": "Gdef", "module_public": false}, "assure_directory_exists": {".class": "SymbolTableNode", "cross_ref": "git.util.assure_directory_exists", "kind": "Gdef", "module_public": false}, "bin_to_hex": {".class": "SymbolTableNode", "cross_ref": "git.util.bin_to_hex", "kind": "Gdef", "module_public": false}, "defenc": {".class": "SymbolTableNode", "cross_ref": "git.compat.defenc", "kind": "Gdef", "module_public": false}, "file_contents_ro_filepath": {".class": "SymbolTableNode", "cross_ref": "git.util.file_contents_ro_filepath", "kind": "Gdef", "module_public": false}, "mmap": {".class": "SymbolTableNode", "cross_ref": "mmap.mmap", "kind": "Gdef", "module_public": false}, "osp": {".class": "SymbolTableNode", "cross_ref": "os.path", "kind": "Gdef", "module_public": false}, "parse_date": {".class": "SymbolTableNode", "cross_ref": "git.objects.util.parse_date", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "to_native_path": {".class": "SymbolTableNode", "cross_ref": "git.util.to_native_path", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\refs\\log.py"}