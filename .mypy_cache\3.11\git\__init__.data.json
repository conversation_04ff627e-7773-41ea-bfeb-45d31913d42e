{".class": "MypyFile", "_fullname": "git", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Actor": {".class": "SymbolTableNode", "cross_ref": "git.util.Actor", "kind": "Gdef"}, "AmbiguousObjectName": {".class": "SymbolTableNode", "cross_ref": "git.exc.AmbiguousObjectName", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BadName": {".class": "SymbolTableNode", "cross_ref": "git.exc.BadName", "kind": "Gdef"}, "BadObject": {".class": "SymbolTableNode", "cross_ref": "git.exc.BadObject", "kind": "Gdef"}, "BadObjectType": {".class": "SymbolTableNode", "cross_ref": "git.exc.BadObjectType", "kind": "Gdef"}, "BaseIndexEntry": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntry", "kind": "Gdef"}, "Blob": {".class": "SymbolTableNode", "cross_ref": "git.objects.blob.Blob", "kind": "Gdef"}, "BlobFilter": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BlobFilter", "kind": "Gdef"}, "BlockingLockFile": {".class": "SymbolTableNode", "cross_ref": "git.util.BlockingLockFile", "kind": "Gdef"}, "CacheError": {".class": "SymbolTableNode", "cross_ref": "git.exc.Cache<PERSON>rror", "kind": "Gdef"}, "CheckoutError": {".class": "SymbolTableNode", "cross_ref": "git.exc.CheckoutError", "kind": "Gdef"}, "CommandError": {".class": "SymbolTableNode", "cross_ref": "git.exc.CommandError", "kind": "Gdef"}, "Commit": {".class": "SymbolTableNode", "cross_ref": "git.objects.commit.Commit", "kind": "Gdef"}, "Diff": {".class": "SymbolTableNode", "cross_ref": "git.diff.Diff", "kind": "Gdef"}, "DiffConstants": {".class": "SymbolTableNode", "cross_ref": "git.diff.DiffConstants", "kind": "Gdef"}, "DiffIndex": {".class": "SymbolTableNode", "cross_ref": "git.diff.DiffIndex", "kind": "Gdef"}, "Diffable": {".class": "SymbolTableNode", "cross_ref": "git.diff.Diffable", "kind": "Gdef"}, "FetchInfo": {".class": "SymbolTableNode", "cross_ref": "git.remote.FetchInfo", "kind": "Gdef"}, "GIT_OK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.GIT_OK", "name": "GIT_OK", "type": {".class": "NoneType"}}}, "Git": {".class": "SymbolTableNode", "cross_ref": "git.cmd.Git", "kind": "Gdef"}, "GitCmdObjectDB": {".class": "SymbolTableNode", "cross_ref": "git.db.GitCmdObjectDB", "kind": "Gdef"}, "GitCommandError": {".class": "SymbolTableNode", "cross_ref": "git.exc.GitCommandError", "kind": "Gdef"}, "GitCommandNotFound": {".class": "SymbolTableNode", "cross_ref": "git.exc.GitCommandNotFound", "kind": "Gdef"}, "GitConfigParser": {".class": "SymbolTableNode", "cross_ref": "git.config.GitConfigParser", "kind": "Gdef"}, "GitDB": {".class": "SymbolTableNode", "cross_ref": "git.db.GitDB", "kind": "Gdef"}, "GitError": {".class": "SymbolTableNode", "cross_ref": "git.exc.GitError", "kind": "Gdef"}, "HEAD": {".class": "SymbolTableNode", "cross_ref": "git.refs.head.HEAD", "kind": "Gdef"}, "Head": {".class": "SymbolTableNode", "cross_ref": "git.refs.head.Head", "kind": "Gdef"}, "HookExecutionError": {".class": "SymbolTableNode", "cross_ref": "git.exc.HookExecutionError", "kind": "Gdef"}, "INDEX": {".class": "SymbolTableNode", "cross_ref": "git.diff.INDEX", "kind": "Gdef"}, "IndexEntry": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.IndexEntry", "kind": "Gdef"}, "IndexFile": {".class": "SymbolTableNode", "cross_ref": "git.index.base.IndexFile", "kind": "Gdef"}, "IndexObject": {".class": "SymbolTableNode", "cross_ref": "git.objects.base.IndexObject", "kind": "Gdef"}, "InvalidDBRoot": {".class": "SymbolTableNode", "cross_ref": "git.exc.InvalidDBRoot", "kind": "Gdef"}, "InvalidGitRepositoryError": {".class": "SymbolTableNode", "cross_ref": "git.exc.InvalidGitRepositoryError", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LockFile": {".class": "SymbolTableNode", "cross_ref": "git.util.LockFile", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_public": false}, "NULL_TREE": {".class": "SymbolTableNode", "cross_ref": "git.diff.NULL_TREE", "kind": "Gdef"}, "NoSuchPathError": {".class": "SymbolTableNode", "cross_ref": "git.exc.NoSuchPathError", "kind": "Gdef"}, "ODBError": {".class": "SymbolTableNode", "cross_ref": "git.exc.ODBError", "kind": "Gdef"}, "Object": {".class": "SymbolTableNode", "cross_ref": "git.objects.base.Object", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ParseError": {".class": "SymbolTableNode", "cross_ref": "git.exc.ParseError", "kind": "Gdef"}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef"}, "PushInfo": {".class": "SymbolTableNode", "cross_ref": "git.remote.PushInfo", "kind": "Gdef"}, "RefLog": {".class": "SymbolTableNode", "cross_ref": "git.refs.log.RefLog", "kind": "Gdef"}, "RefLogEntry": {".class": "SymbolTableNode", "cross_ref": "git.refs.log.RefLogEntry", "kind": "Gdef"}, "Reference": {".class": "SymbolTableNode", "cross_ref": "git.refs.reference.Reference", "kind": "Gdef"}, "Remote": {".class": "SymbolTableNode", "cross_ref": "git.remote.Remote", "kind": "Gdef"}, "RemoteProgress": {".class": "SymbolTableNode", "cross_ref": "git.util.RemoteProgress", "kind": "Gdef"}, "RemoteReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.remote.RemoteReference", "kind": "Gdef"}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef"}, "RepositoryDirtyError": {".class": "SymbolTableNode", "cross_ref": "git.exc.RepositoryDirtyError", "kind": "Gdef"}, "RootModule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.root.RootModule", "kind": "Gdef"}, "RootUpdateProgress": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.root.RootUpdateProgress", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "StageType": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.StageType", "kind": "Gdef"}, "Stats": {".class": "SymbolTableNode", "cross_ref": "git.util.Stats", "kind": "Gdef"}, "Submodule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.Submodule", "kind": "Gdef"}, "SymbolicReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.symbolic.SymbolicReference", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tag": {".class": "SymbolTableNode", "cross_ref": "git.refs.tag.Tag", "kind": "Gdef"}, "TagObject": {".class": "SymbolTableNode", "cross_ref": "git.objects.tag.TagObject", "kind": "Gdef"}, "TagReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.tag.TagReference", "kind": "Gdef"}, "Tree": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.Tree", "kind": "Gdef"}, "TreeModifier": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.TreeModifier", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnmergedEntriesError": {".class": "SymbolTableNode", "cross_ref": "git.exc.UnmergedEntriesError", "kind": "Gdef"}, "UnsafeOptionError": {".class": "SymbolTableNode", "cross_ref": "git.exc.UnsafeOptionError", "kind": "Gdef"}, "UnsafeProtocolError": {".class": "SymbolTableNode", "cross_ref": "git.exc.UnsafeProtocolError", "kind": "Gdef"}, "UnsupportedOperation": {".class": "SymbolTableNode", "cross_ref": "git.exc.UnsupportedOperation", "kind": "Gdef"}, "UpdateProgress": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.UpdateProgress", "kind": "Gdef"}, "WorkTreeRepositoryUnsupported": {".class": "SymbolTableNode", "cross_ref": "git.exc.WorkTreeRepositoryUnsupported", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.__version__", "name": "__version__", "type": "builtins.str"}}, "_exc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git._exc", "name": "_exc", "type": {".class": "DeletedType", "source": "_exc"}}}, "_getattr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git._getattr", "name": "_getattr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getattr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_warned_import": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["message", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git._warned_import", "name": "_warned_import", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["message", "fullname"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warned_import", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "refresh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.refresh", "name": "refresh", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refresh", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_password_if_present": {".class": "SymbolTableNode", "cross_ref": "git.util.remove_password_if_present", "kind": "Gdef"}, "rmtree": {".class": "SymbolTableNode", "cross_ref": "git.util.rmtree", "kind": "Gdef"}, "safe_decode": {".class": "SymbolTableNode", "cross_ref": "git.compat.safe_decode", "kind": "Gdef"}, "to_hex_sha": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.to_hex_sha", "name": "to_hex_sha", "type": {".class": "AnyType", "missing_import_name": "git.to_hex_sha", "source_any": null, "type_of_any": 3}}}, "util": {".class": "SymbolTableNode", "cross_ref": "git.index.util", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\__init__.py"}