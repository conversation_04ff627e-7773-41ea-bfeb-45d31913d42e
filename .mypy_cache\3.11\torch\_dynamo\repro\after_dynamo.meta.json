{"data_mtime": 1755649448, "dep_lines": [34, 52, 35, 55, 33, 53, 55, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 20, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["torch._dynamo.backends.registry", "torch.fx.experimental.symbolic_shapes", "torch._dynamo.debug_utils", "torch._dynamo.config", "torch.fx", "torch.hub", "torch._dynamo", "<PERSON><PERSON><PERSON><PERSON>", "copy", "functools", "logging", "os", "shutil", "sys", "textwrap", "importlib", "typing", "torch", "builtins", "collections", "warnings", "operator", "itertools", "pprint", "math", "inspect", "string", "torch.distributed._functional_collectives", "types", "torch.nn", "multiprocessing.reduction", "dataclasses", "json", "traceback", "re", "html", "_frozen_importlib", "abc", "torch._dynamo.backends", "torch.fx.graph_module", "torch.nn.modules", "torch.nn.modules.module", "contextlib"], "hash": "45c4f9f048195c2776941c2988bde7d650705637", "id": "torch._dynamo.repro.after_dynamo", "ignore_all": true, "interface_hash": "54a8176dc1ad654ab531a3e8ee0925f67aec36ff", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_dynamo\\repro\\after_dynamo.py", "plugin_data": null, "size": 21179, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}