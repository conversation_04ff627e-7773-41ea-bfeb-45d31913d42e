{"data_mtime": 1755656859, "dep_lines": [10, 3, 5, 6, 7, 12, 1, 1, 1, 1], "dep_prios": [25, 5, 10, 10, 5, 25, 5, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "datetime", "re", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "5051d2b3057f60c4b9f354e244fefac5d7fd3e16", "id": "narwhals._duration", "ignore_all": true, "interface_hash": "cd680264b634071456bd60ca1db057966662c65c", "mtime": 1755656304, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\narwhals\\_duration.py", "plugin_data": null, "size": 3139, "suppressed": [], "version_id": "1.15.0"}