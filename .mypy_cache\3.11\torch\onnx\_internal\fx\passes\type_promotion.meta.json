{"data_mtime": 1755649448, "dep_lines": [24, 24, 22, 23, 24, 12, 15, 17, 21, 21, 21, 23, 25, 25, 32, 12, 13, 14, 16, 16, 25, 29, 32, 3, 5, 6, 7, 8, 9, 11, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 10, 10, 10, 10, 10, 10, 20, 10, 10, 25, 20, 10, 10, 5, 10, 20, 25, 25, 5, 10, 10, 10, 10, 5, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.fx._pass", "torch.onnx._internal.fx.type_utils", "torch._refs.nn.functional", "torch.fx.experimental.proxy_tensor", "torch.onnx._internal.fx", "torch._dispatch.python", "torch.fx.traceback", "torch._prims_common.wrappers", "torch._refs.linalg", "torch._refs.nn", "torch._refs.special", "torch.fx.experimental", "torch.utils._python_dispatch", "torch.utils._pytree", "torch._subclasses.fake_tensor", "torch._dispatch", "torch._ops", "torch.fx", "torch._prims_common", "torch._refs", "torch.utils", "collections.abc", "torch._subclasses", "__future__", "abc", "dataclasses", "inspect", "logging", "typing", "torch", "types", "builtins", "os", "html", "sys", "string", "operator", "pprint", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_collections_abc", "_frozen_importlib", "enum", "torch._C", "torch._tensor", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.interpreter", "torch.fx.node", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.types"], "hash": "19636fe0a47d7ae285be25447e18b1e2bdb847f8", "id": "torch.onnx._internal.fx.passes.type_promotion", "ignore_all": true, "interface_hash": "12591b973d00506ecd9733d66305b2f154aceeac", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\passes\\type_promotion.py", "plugin_data": null, "size": 66247, "suppressed": [], "version_id": "1.15.0"}