{".class": "MypyFile", "_fullname": "torch.fx.passes.infra.partitioner", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CapabilityBasedPartitioner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", "name": "CapabilityBasedPartitioner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.fx.passes.infra.partitioner", "mro": ["torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "graph_module", "operator_support", "allows_single_node_partition", "non_compute_ops", "allowed_single_node_partition_ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "graph_module", "operator_support", "allows_single_node_partition", "non_compute_ops", "allowed_single_node_partition_ops"], "arg_types": ["torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", "torch.fx.graph_module.GraphModule", "torch.fx.passes.operator_support.OperatorSupportBase", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CapabilityBasedPartitioner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_node_supported": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner._is_node_supported", "name": "_is_node_supported", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_node_supported of CapabilityBasedPartitioner", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allowed_single_node_partition_ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.allowed_single_node_partition_ops", "name": "allowed_single_node_partition_ops", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "allows_single_node_partition": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.allows_single_node_partition", "name": "allows_single_node_partition", "type": "builtins.bool"}}, "dependency_viewer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.dependency_viewer", "name": "dependency_viewer", "type": "torch.fx.passes.infra.partitioner._DependencyViewer"}}, "fuse_partitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "partitions", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.fuse_partitions", "name": "fuse_partitions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "partitions", "prefix"], "arg_types": ["torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", {".class": "Instance", "args": ["torch.fx.passes.infra.partitioner.Partition"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse_partitions of CapabilityBasedPartitioner", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph_module": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.graph_module", "name": "graph_module", "type": "torch.fx.graph_module.GraphModule"}}, "non_compute_ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.non_compute_ops", "name": "non_compute_ops", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "operator_support": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.operator_support", "name": "operator_support", "type": "torch.fx.passes.operator_support.OperatorSupportBase"}}, "partition_and_fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.partition_and_fuse", "name": "partition_and_fuse", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "prefix"], "arg_types": ["torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partition_and_fuse of CapabilityBasedPartitioner", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "propose_partitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.propose_partitions", "name": "propose_partitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "propose_partitions of CapabilityBasedPartitioner", "ret_type": {".class": "Instance", "args": ["torch.fx.passes.infra.partitioner.Partition"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_bookend_non_compute_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "partitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.remove_bookend_non_compute_ops", "name": "remove_bookend_non_compute_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "partitions"], "arg_types": ["torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", {".class": "Instance", "args": ["torch.fx.passes.infra.partitioner.Partition"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_bookend_non_compute_ops of CapabilityBasedPartitioner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.fx.passes.infra.partitioner.CapabilityBasedPartitioner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GraphModule": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph_module.GraphModule", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Node": {".class": "SymbolTableNode", "cross_ref": "torch.fx.node.Node", "kind": "Gdef"}, "OperatorSupportBase": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.operator_support.OperatorSupportBase", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Partition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.fx.passes.infra.partitioner.Partition", "name": "Partition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.Partition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.fx.passes.infra.partitioner", "mro": ["torch.fx.passes.infra.partitioner.Partition", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "id", "nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.Partition.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "id", "nodes"], "arg_types": ["torch.fx.passes.infra.partitioner.Partition", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Partition", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.Partition.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.fx.passes.infra.partitioner.Partition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Partition", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.Partition.add_node", "name": "add_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.fx.passes.infra.partitioner.Partition", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_node of Partition", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner.Partition.id", "name": "id", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner.Partition.nodes", "name": "nodes", "type": {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "remove_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.Partition.remove_node", "name": "remove_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.fx.passes.infra.partitioner.Partition", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_node of Partition", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner.Partition.size", "name": "size", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.fx.passes.infra.partitioner.Partition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.fx.passes.infra.partitioner.Partition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "_DependencyViewer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.fx.passes.infra.partitioner._DependencyViewer", "name": "_DependencyViewer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner._DependencyViewer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.fx.passes.infra.partitioner", "mro": ["torch.fx.passes.infra.partitioner._DependencyViewer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "graph_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner._DependencyViewer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "graph_module"], "arg_types": ["torch.fx.passes.infra.partitioner._DependencyViewer", "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _DependencyViewer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "downstreams": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.infra.partitioner._DependencyViewer.downstreams", "name": "downstreams", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "downstreams_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.infra.partitioner._DependencyViewer.downstreams_of", "name": "downstreams_of", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.fx.passes.infra.partitioner._DependencyViewer", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "downstreams_of of _DependencyViewer", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.fx.passes.infra.partitioner._DependencyViewer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.fx.passes.infra.partitioner._DependencyViewer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.infra.partitioner.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.infra.partitioner.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.infra.partitioner.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.infra.partitioner.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.infra.partitioner.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.infra.partitioner.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_qualified_name": {".class": "SymbolTableNode", "cross_ref": "torch.fx.node._get_qualified_name", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "fuse_by_partitions": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.utils.fuser_utils.fuse_by_partitions", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.fx.passes.infra.partitioner.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\fx\\passes\\infra\\partitioner.py"}