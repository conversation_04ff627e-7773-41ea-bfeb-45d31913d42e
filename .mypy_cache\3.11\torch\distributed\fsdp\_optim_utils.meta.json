{"data_mtime": 1755649448, "dep_lines": [14, 18, 27, 28, 29, 33, 37, 47, 14, 16, 17, 42, 43, 6, 13, 15, 2, 3, 4, 5, 7, 8, 9, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 25, 20, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.fsdp._traversal_utils", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp._debug_utils", "torch.distributed.fsdp._flat_param", "torch.distributed.fsdp._fsdp_extensions", "torch.distributed.fsdp._runtime_utils", "torch.distributed.fsdp.api", "torch.distributed._shard.sharded_tensor", "torch.distributed.fsdp", "torch.distributed._state_dict_utils", "torch.distributed.distributed_c10d", "torch.distributed.tensor", "torch.utils._pytree", "collections.abc", "torch.distributed", "torch.nn", "copy", "functools", "logging", "warnings", "contextlib", "dataclasses", "itertools", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.distributed._composable_state", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "torch.optim", "torch.optim.optimizer", "torch.utils", "torch.utils._contextlib"], "hash": "53367c9e9bcc1c28e72d5034123de834d2e5cfeb", "id": "torch.distributed.fsdp._optim_utils", "ignore_all": true, "interface_hash": "4272fcc2e0e25b6da4cdecb8768d54df9e9586c5", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_optim_utils.py", "plugin_data": null, "size": 88692, "suppressed": [], "version_id": "1.15.0"}