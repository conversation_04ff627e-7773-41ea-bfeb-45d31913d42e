{".class": "MypyFile", "_fullname": "torch.onnx._internal.fx.type_utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.fx.type_utils.Argument", "line": 184, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.slice"}, "builtins.range", "torch.fx.node.Node", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.BaseArgumentTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "BaseArgumentTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.fx.type_utils.BaseArgumentTypes", "line": 168, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.float", "builtins.bool", "builtins.complex", "torch._C.dtype", "torch._tensor.Tensor", "torch._C.device", "torch._C.memory_format", "torch._C.layout", {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, "torch.SymInt", "torch.SymFloat", "torch.SymBool"], "uses_pep604_syntax": false}}}, "META_VALUE_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.fx.type_utils.META_VALUE_TYPE", "line": 166, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["torch._subclasses.fake_tensor.FakeTensor", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.SYM_VALUE_TYPE"}, "builtins.int", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "SYM_VALUE_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.fx.type_utils.SYM_VALUE_TYPE", "line": 165, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["torch.SymInt", "torch.SymFloat", "torch.SymBool"], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TensorLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["dtype", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.type_utils.TensorLike", "name": "TensorLike", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "torch.onnx._internal.fx.type_utils.TensorLike", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.type_utils", "mro": ["torch.onnx._internal.fx.type_utils.TensorLike", "builtins.object"], "names": {".class": "SymbolTable", "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "torch.onnx._internal.fx.type_utils.TensorLike.dtype", "name": "dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.type_utils.TensorLike"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype of TensorLike", "ret_type": {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.type_utils.TensorLike.dtype", "name": "dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.type_utils.TensorLike"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype of TensorLike", "ret_type": {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.type_utils.TensorLike.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.type_utils.TensorLike", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_COMPLEX_TO_FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.type_utils._COMPLEX_TO_FLOAT", "name": "_COMPLEX_TO_FLOAT", "type": {".class": "Instance", "args": ["torch._C.dtype", "torch._C.dtype"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_OPTIONAL_ONNX_DTYPE_STR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.type_utils._OPTIONAL_ONNX_DTYPE_STR", "name": "_OPTIONAL_ONNX_DTYPE_STR", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_PYTHON_TYPE_TO_TORCH_DTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.type_utils._PYTHON_TYPE_TO_TORCH_DTYPE", "name": "_PYTHON_TYPE_TO_TORCH_DTYPE", "type": {".class": "Instance", "args": ["builtins.type", "torch._C.dtype"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SCALAR_TYPE_TO_TORCH_DTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.type_utils._SCALAR_TYPE_TO_TORCH_DTYPE", "name": "_SCALAR_TYPE_TO_TORCH_DTYPE", "type": {".class": "Instance", "args": ["builtins.type", "torch._C.dtype"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SYM_TYPE_TO_TORCH_DTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.type_utils._SYM_TYPE_TO_TORCH_DTYPE", "name": "_SYM_TYPE_TO_TORCH_DTYPE", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["torch.SymBool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch._C.dtype"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_TORCH_DTYPE_TO_ABBREVIATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.type_utils._TORCH_DTYPE_TO_ABBREVIATION", "name": "_TORCH_DTYPE_TO_ABBREVIATION", "type": {".class": "Instance", "args": ["torch._C.dtype", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_TORCH_DTYPE_TO_COMPATIBLE_ONNX_TYPE_STRINGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.type_utils._TORCH_DTYPE_TO_COMPATIBLE_ONNX_TYPE_STRINGS", "name": "_TORCH_DTYPE_TO_COMPATIBLE_ONNX_TYPE_STRINGS", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._C.dtype", "builtins.type"], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.type_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.type_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.type_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.type_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.type_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.type_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "fake_tensor": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor", "kind": "Gdef"}, "from_complex_to_float": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.from_complex_to_float", "name": "from_complex_to_float", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": ["torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_complex_to_float", "ret_type": "torch._C.dtype", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_python_type_to_onnx_attribute_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["dtype", "is_sequence"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.from_python_type_to_onnx_attribute_type", "name": "from_python_type_to_onnx_attribute_type", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["dtype", "is_sequence"], "arg_types": ["builtins.type", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_python_type_to_onnx_attribute_type", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.type_utils.onnx", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_scalar_type_to_torch_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["scalar_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.from_scalar_type_to_torch_dtype", "name": "from_scalar_type_to_torch_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["scalar_type"], "arg_types": ["builtins.type"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_scalar_type_to_torch_dtype", "ret_type": {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_sym_value_to_torch_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sym_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.from_sym_value_to_torch_dtype", "name": "from_sym_value_to_torch_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sym_value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.SYM_VALUE_TYPE"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_sym_value_to_torch_dtype", "ret_type": "torch._C.dtype", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_torch_dtype_to_abbr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.from_torch_dtype_to_abbr", "name": "from_torch_dtype_to_abbr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_torch_dtype_to_abbr", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_torch_dtype_to_onnx_dtype_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.from_torch_dtype_to_onnx_dtype_str", "name": "from_torch_dtype_to_onnx_dtype_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": ["torch._C.dtype", "builtins.type"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_torch_dtype_to_onnx_dtype_str", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_optional_onnx_dtype_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["onnx_type_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.is_optional_onnx_dtype_str", "name": "is_optional_onnx_dtype_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["onnx_type_str"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_optional_onnx_dtype_str", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_torch_complex_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tensor_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.is_torch_complex_dtype", "name": "is_torch_complex_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tensor_dtype"], "arg_types": ["torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_torch_complex_dtype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_torch_symbolic_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.type_utils.is_torch_symbolic_type", "name": "is_torch_symbolic_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_torch_symbolic_type", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "onnx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.type_utils.onnx", "name": "onnx", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.type_utils.onnx", "source_any": null, "type_of_any": 3}}}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.runtime_checkable", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\type_utils.py"}