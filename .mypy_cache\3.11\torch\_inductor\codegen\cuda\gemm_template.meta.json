{"data_mtime": 1755649448, "dep_lines": [1404, 1499, 13, 32, 33, 34, 35, 36, 1499, 31, 32, 12, 14, 15, 16, 18, 19, 30, 12, 18, 2, 3, 4, 5, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 626, 937, 626], "dep_prios": [20, 20, 5, 5, 5, 5, 5, 5, 20, 5, 20, 10, 5, 5, 5, 5, 5, 5, 20, 20, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20], "dependencies": ["torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions", "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions", "torch._inductor.codegen.cuda.cutlass_cache", "torch._inductor.codegen.cuda.cutlass_utils", "torch._inductor.codegen.cuda.cuda_kernel", "torch._inductor.codegen.cuda.cuda_template", "torch._inductor.codegen.cuda.cutlass_presets", "torch._inductor.codegen.cuda.cutlass_python_evt", "torch._inductor.codegen.cuda.cutlass_lib_extensions", "torch._inductor.codegen.common", "torch._inductor.codegen.cuda", "torch.utils._pytree", "torch._inductor.scheduler", "torch._inductor.select_algorithm", "torch._inductor.utils", "torch._inductor.ir", "torch._inductor.config", "torch._inductor.virtualized", "torch.utils", "torch._inductor", "copy", "enum", "functools", "logging", "re", "time", "abc", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "warnings", "contextlib", "_collections_abc", "_frozen_importlib", "_typeshed", "torch._C", "torch._inductor.graph", "torch._tensor", "torch.fx", "torch.fx.interpreter", "torch.utils._ordered_set", "typing_extensions"], "hash": "ff4fa0a3af33f2ad93e81d286f3f06a5fdea9e5a", "id": "torch._inductor.codegen.cuda.gemm_template", "ignore_all": true, "interface_hash": "008f0845eab89dbbcb6c1ac304ecbe665785c33e", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\gemm_template.py", "plugin_data": null, "size": 76765, "suppressed": ["cutlass_library.library", "cutlass_library.gemm_operation", "cutlass_library"], "version_id": "1.15.0"}