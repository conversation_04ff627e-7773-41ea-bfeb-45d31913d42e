{"data_mtime": 1755656862, "dep_lines": [72, 24, 25, 32, 68, 71, 6, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 43, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.repo.base", "git.compat", "git.exc", "git.util", "git.types", "git.diff", "__future__", "contextlib", "io", "itertools", "logging", "os", "re", "signal", "subprocess", "sys", "textwrap", "threading", "warnings", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "git.repo", "types", "typing_extensions"], "hash": "764533afd2ee918079136ae3b022ff7679302354", "id": "git.cmd", "ignore_all": true, "interface_hash": "dc4756c10db0a8b41e580df4204ed91208b8ecba", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\cmd.py", "plugin_data": null, "size": 67472, "suppressed": [], "version_id": "1.15.0"}