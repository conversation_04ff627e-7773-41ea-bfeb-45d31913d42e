{".class": "MypyFile", "_fullname": "streamlit.proto.GraphVizChart_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GraphVizChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "GraphVizChart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart", "name": "GraphVizChart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GraphVizChart_pb2", "mro": ["streamlit.proto.GraphVizChart_pb2.GraphVizChart", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.GraphVizChart_pb2.GraphVizChart", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "element_id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "element_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "engine"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "engine"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "spec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_container_width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of GraphVizChart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GraphVizChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "ELEMENT_ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.ELEMENT_ID_FIELD_NUMBER", "name": "ELEMENT_ID_FIELD_NUMBER", "type": "builtins.int"}}, "ENGINE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.ENGINE_FIELD_NUMBER", "name": "ENGINE_FIELD_NUMBER", "type": "builtins.int"}}, "SPEC_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.SPEC_FIELD_NUMBER", "name": "SPEC_FIELD_NUMBER", "type": "builtins.int"}}, "USE_CONTAINER_WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.USE_CONTAINER_WIDTH_FIELD_NUMBER", "name": "USE_CONTAINER_WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "spec", "use_container_width", "element_id", "engine"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "spec", "use_container_width", "element_id", "engine"], "arg_types": ["streamlit.proto.GraphVizChart_pb2.GraphVizChart", "builtins.str", "builtins.bool", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GraphVizChart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "element_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.element_id", "name": "element_id", "type": "builtins.str"}}, "engine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.engine", "name": "engine", "type": "builtins.str"}}, "spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.spec", "name": "spec", "type": "builtins.str"}}, "use_container_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.use_container_width", "name": "use_container_width", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GraphVizChart_pb2.GraphVizChart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GraphVizChart_pb2.GraphVizChart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GraphVizChart_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___GraphVizChart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.GraphVizChart_pb2.global___GraphVizChart", "line": 53, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.GraphVizChart_pb2.GraphVizChart"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.GraphVizChart_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GraphVizChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\GraphVizChart_pb2.pyi"}