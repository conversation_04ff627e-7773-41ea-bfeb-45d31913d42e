{".class": "MypyFile", "_fullname": "git.refs.tag", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AnyGitObject": {".class": "SymbolTableNode", "cross_ref": "git.types.AnyGitObject", "kind": "Gdef", "module_public": false}, "Commit": {".class": "SymbolTableNode", "cross_ref": "git.objects.commit.Commit", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "Reference": {".class": "SymbolTableNode", "cross_ref": "git.refs.reference.Reference", "kind": "Gdef", "module_public": false}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef", "module_public": false}, "SymbolicReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.symbolic.SymbolicReference", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.refs.tag.Tag", "line": 155, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "git.refs.tag.TagReference"}}, "TagObject": {".class": "SymbolTableNode", "cross_ref": "git.objects.tag.TagObject", "kind": "Gdef", "module_public": false}, "TagReference": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.refs.reference.Reference"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.refs.tag.TagReference", "name": "TagReference", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "git.refs.tag.TagReference", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.refs.tag", "mro": ["git.refs.tag.TagReference", "git.refs.reference.Reference", "git.refs.symbolic.SymbolicReference", "git.util.IterableObj", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.refs.tag.TagReference.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_common_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.refs.tag.TagReference._common_default", "name": "_common_default", "type": "builtins.str"}}, "_common_path_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.refs.tag.TagReference._common_path_default", "name": "_common_path_default", "type": "builtins.str"}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.refs.tag.TagReference.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of TagReference", "ret_type": "git.objects.commit.Commit", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.refs.tag.TagReference.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of TagReference", "ret_type": "git.objects.commit.Commit", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["cls", "repo", "path", "reference", "logmsg", "force", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.tag.TagReference.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["cls", "repo", "path", "reference", "logmsg", "force", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.refs.tag.TagReference"}, "git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "UnionType", "items": ["builtins.str", "git.refs.symbolic.SymbolicReference"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TagReference", "ret_type": "git.refs.tag.TagReference", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.tag.TagReference.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["cls", "repo", "path", "reference", "logmsg", "force", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.refs.tag.TagReference"}, "git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "UnionType", "items": ["builtins.str", "git.refs.symbolic.SymbolicReference"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TagReference", "ret_type": "git.refs.tag.TagReference", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["cls", "repo", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.refs.tag.TagReference.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["cls", "repo", "tags"], "arg_types": [{".class": "TypeType", "item": "git.refs.tag.TagReference"}, "git.repo.base.Repo", "git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of TagReference", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.refs.tag.TagReference.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["cls", "repo", "tags"], "arg_types": [{".class": "TypeType", "item": "git.refs.tag.TagReference"}, "git.repo.base.Repo", "git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of TagReference", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.refs.tag.TagReference.object", "name": "object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object of TagReference", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.refs.tag.TagReference.object", "name": "object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object of TagReference", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.refs.tag.TagReference.tag", "name": "tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag of TagReference", "ret_type": {".class": "UnionType", "items": ["git.objects.tag.TagObject", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.refs.tag.TagReference.tag", "name": "tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag of TagReference", "ret_type": {".class": "UnionType", "items": ["git.objects.tag.TagObject", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.refs.tag.TagReference.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.refs.tag.TagReference", "values": [], "variance": 0}, "slots": ["path", "repo"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.refs.tag.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.tag.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.tag.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.tag.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.tag.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.tag.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.refs.tag.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\refs\\tag.py"}