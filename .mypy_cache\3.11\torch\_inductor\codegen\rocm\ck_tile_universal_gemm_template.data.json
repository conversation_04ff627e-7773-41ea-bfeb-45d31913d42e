{".class": "MypyFile", "_fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgInfo": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.rocm_template.ArgInfo", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "CKTileGemmOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation", "name": "CKTileGemmOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "layout_a", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 40, "name": "layout_b", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 41, "name": "layout_c", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "datatype_a", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "datatype_b", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "datatype_c", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "tile_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "tile_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "tile_k", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "warp_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "warp_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "warp_k", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "warp_tile_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "warp_tile_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "warp_tile_k", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 59, "name": "m_is_padded", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 60, "name": "n_is_padded", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 61, "name": "k_is_padded", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "pipeline", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "scheduler", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "epilogue", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template", "mro": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "layout_a", "layout_b", "layout_c", "datatype_a", "datatype_b", "datatype_c", "tile_m", "tile_n", "tile_k", "warp_m", "warp_n", "warp_k", "warp_tile_m", "warp_tile_n", "warp_tile_k", "m_is_padded", "n_is_padded", "k_is_padded", "pipeline", "scheduler", "epilogue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "layout_a", "layout_b", "layout_c", "datatype_a", "datatype_b", "datatype_c", "tile_m", "tile_n", "tile_k", "warp_m", "warp_n", "warp_k", "warp_tile_m", "warp_tile_n", "warp_tile_k", "m_is_padded", "n_is_padded", "k_is_padded", "pipeline", "scheduler", "epilogue"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CKTileGemmOperation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "layout_a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "layout_b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "layout_c"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datatype_a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datatype_b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datatype_c"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tile_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tile_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tile_k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warp_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warp_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warp_k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warp_tile_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warp_tile_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warp_tile_k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m_is_padded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n_is_padded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "k_is_padded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pipeline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scheduler"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "epilogue"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["layout_a", "layout_b", "layout_c", "datatype_a", "datatype_b", "datatype_c", "tile_m", "tile_n", "tile_k", "warp_m", "warp_n", "warp_k", "warp_tile_m", "warp_tile_n", "warp_tile_k", "m_is_padded", "n_is_padded", "k_is_padded", "pipeline", "scheduler", "epilogue"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["layout_a", "layout_b", "layout_c", "datatype_a", "datatype_b", "datatype_c", "tile_m", "tile_n", "tile_k", "warp_m", "warp_n", "warp_k", "warp_tile_m", "warp_tile_n", "warp_tile_k", "m_is_padded", "n_is_padded", "k_is_padded", "pipeline", "scheduler", "epilogue"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CKTileGemmOperation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["layout_a", "layout_b", "layout_c", "datatype_a", "datatype_b", "datatype_c", "tile_m", "tile_n", "tile_k", "warp_m", "warp_n", "warp_k", "warp_tile_m", "warp_tile_n", "warp_tile_k", "m_is_padded", "n_is_padded", "k_is_padded", "pipeline", "scheduler", "epilogue"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CKTileGemmOperation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "datatype_a": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.datatype_a", "name": "datatype_a", "type": "builtins.str"}}, "datatype_b": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.datatype_b", "name": "datatype_b", "type": "builtins.str"}}, "datatype_c": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.datatype_c", "name": "datatype_c", "type": "builtins.str"}}, "dict_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.dict_items", "name": "dict_items", "type": null}}, "dtype_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.dtype_repr", "name": "dtype_repr", "type": null}}, "epilogue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.epilogue", "name": "epilogue", "type": "builtins.str"}}, "k_is_padded": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.k_is_padded", "name": "k_is_padded", "type": "builtins.str"}}, "layout_a": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.layout_a", "name": "layout_a", "type": "builtins.str"}}, "layout_b": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.layout_b", "name": "layout_b", "type": "builtins.str"}}, "layout_c": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.layout_c", "name": "layout_c", "type": "builtins.str"}}, "layout_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.layout_repr", "name": "layout_repr", "type": null}}, "m_is_padded": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.m_is_padded", "name": "m_is_padded", "type": "builtins.str"}}, "n_is_padded": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.n_is_padded", "name": "n_is_padded", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.name", "name": "name", "type": null}}, "pipeline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.pipeline", "name": "pipeline", "type": "builtins.str"}}, "scheduler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.scheduler", "name": "scheduler", "type": "builtins.str"}}, "tile_k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.tile_k", "name": "tile_k", "type": "builtins.int"}}, "tile_m": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.tile_m", "name": "tile_m", "type": "builtins.int"}}, "tile_n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.tile_n", "name": "tile_n", "type": "builtins.int"}}, "tile_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.tile_sizes", "name": "tile_sizes", "type": null}}, "warp_k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.warp_k", "name": "warp_k", "type": "builtins.int"}}, "warp_m": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.warp_m", "name": "warp_m", "type": "builtins.int"}}, "warp_n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.warp_n", "name": "warp_n", "type": "builtins.int"}}, "warp_tile_k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.warp_tile_k", "name": "warp_tile_k", "type": "builtins.int"}}, "warp_tile_m": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.warp_tile_m", "name": "warp_tile_m", "type": "builtins.int"}}, "warp_tile_n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.warp_tile_n", "name": "warp_tile_n", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CKTileGemmTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.rocm.ck_tile_template.CKTileTemplate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "name": "CKTileGemmTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template", "mro": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_template.CKTileTemplate", "torch._inductor.codegen.rocm.rocm_template.ROCmTemplate", "torch._inductor.codegen.common.KernelTemplate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_nodes", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_nodes", "layout"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CKTileGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["choices", "layout", "input_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.add_choices", "name": "add_choices", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.add_choices", "name": "add_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["choices", "layout", "input_nodes"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_choices of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "check_alignments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.check_alignments", "name": "check_alignments", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_alignments of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_block_tile_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.check_block_tile_size", "name": "check_block_tile_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_block_tile_size of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_block_tiles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.check_block_tiles", "name": "check_block_tiles", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_block_tiles of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_dtypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.check_dtypes", "name": "check_dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_dtypes of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_layouts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.check_layouts", "name": "check_layouts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_layouts of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_warp_tiles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.check_warp_tiles", "name": "check_warp_tiles", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_warp_tiles of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "emit_ck_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.emit_ck_instance", "name": "emit_ck_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emit_ck_instance of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.filter_op", "name": "filter_op", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_op of CKTileGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gemm_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.gemm_template", "name": "gemm_template", "type": "builtins.str"}}, "gen_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.gen_ops", "name": "gen_ops", "type": null}}, "get_gemm_problem_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.get_gemm_problem_size", "name": "get_gemm_problem_size", "type": null}}, "get_runtime_arg_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.get_runtime_arg_info", "name": "get_runtime_arg_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_runtime_arg_info of CKTileGemmTemplate", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.rocm.rocm_template.ArgInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_runtime_arg_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.get_runtime_arg_values", "name": "get_runtime_arg_values", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_runtime_arg_values of CKTileGemmTemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "globals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.globals", "name": "globals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "globals of CKTileGemmTemplate", "ret_type": "torch._inductor.utils.IndentedBuffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.header", "name": "header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header of CKTileGemmTemplate", "ret_type": "torch._inductor.utils.IndentedBuffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "k_batch_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.k_batch_choices", "name": "k_batch_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "k_batch_choices of CKTileGemmTemplate", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "kernel", "op", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "kernel", "op", "kwargs"], "arg_types": ["torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmOperation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of CKTileGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.size_args", "name": "size_args", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CKTileTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.ck_tile_template.CKTileTemplate", "kind": "Gdef"}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Layout", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "ROCmTemplateKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asdict": {".class": "SymbolTableNode", "cross_ref": "dataclasses.asdict", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "is_static_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.is_static_int", "name": "is_static_int", "type": null}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.ops", "name": "ops", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.ops", "name": "ops", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "torch_layout_to_ck_layout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["torch_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.torch_layout_to_ck_layout", "name": "torch_layout_to_ck_layout", "type": null}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\rocm\\ck_tile_universal_gemm_template.py"}