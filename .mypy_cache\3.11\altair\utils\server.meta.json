{"data_mtime": 1755656860, "dep_lines": [14, 8, 9, 10, 11, 12, 13, 14, 15, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 20, 5, 5, 30, 30, 30], "dependencies": ["http.server", "itertools", "random", "socket", "sys", "threading", "webbrowser", "http", "io", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "f232a79e81dea3c4e7aea5e29916826e381b5b50", "id": "altair.utils.server", "ignore_all": true, "interface_hash": "8fd125dee55ef7276eaa383d0f06e02e4836230e", "mtime": 1755656335, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\utils\\server.py", "plugin_data": null, "size": 4100, "suppressed": [], "version_id": "1.15.0"}