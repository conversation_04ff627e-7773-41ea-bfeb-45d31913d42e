{".class": "MypyFile", "_fullname": "torch.fx.passes._tensorify_python_scalars", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BooleanAtom": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.fx.passes._tensorify_python_scalars.BooleanAtom", "name": "BooleanAtom", "type": {".class": "AnyType", "missing_import_name": "torch.fx.passes._tensorify_python_scalars.BooleanAtom", "source_any": null, "type_of_any": 3}}}, "FakeTensor": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor.FakeTensor", "kind": "Gdef", "module_public": false}, "GraphModule": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph_module.GraphModule", "kind": "Gdef", "module_public": false}, "Integer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.fx.passes._tensorify_python_scalars.Integer", "name": "Integer", "type": {".class": "AnyType", "missing_import_name": "torch.fx.passes._tensorify_python_scalars.Integer", "source_any": null, "type_of_any": 3}}}, "MetaProxy": {".class": "SymbolTableNode", "cross_ref": "torch.fx.proxy.MetaProxy", "kind": "Gdef", "module_public": false}, "Number": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.fx.passes._tensorify_python_scalars.Number", "name": "Number", "type": {".class": "AnyType", "missing_import_name": "torch.fx.passes._tensorify_python_scalars.Number", "source_any": null, "type_of_any": 3}}}, "SUPPORTED_OPS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.fx.passes._tensorify_python_scalars.SUPPORTED_OPS", "name": "SUPPORTED_OPS", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ShapeEnv": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.ShapeEnv", "kind": "Gdef", "module_public": false}, "SymT": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.SymT", "kind": "Gdef", "module_public": false}, "Symbol": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.fx.passes._tensorify_python_scalars.Symbol", "name": "Symbol", "type": {".class": "AnyType", "missing_import_name": "torch.fx.passes._tensorify_python_scalars.Symbol", "source_any": null, "type_of_any": 3}}}, "TensorReferenceAnalysis": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.reference.TensorReferenceAnalysis", "kind": "Gdef", "module_public": false}, "TensorifyScalarRestartAnalysis": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.exc.TensorifyScalarRestartAnalysis", "kind": "Gdef", "module_public": false}, "TensorifyState": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.symbolic_convert.TensorifyState", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.fx.passes._tensorify_python_scalars.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes._tensorify_python_scalars.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes._tensorify_python_scalars.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes._tensorify_python_scalars.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes._tensorify_python_scalars.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes._tensorify_python_scalars.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes._tensorify_python_scalars.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_sym_val": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.runtime_assert._get_sym_val", "kind": "Gdef", "module_public": false}, "_run_sympy_handler": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.interp._run_sympy_handler", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "fake_tensor": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor", "kind": "Gdef", "module_public": false}, "fx": {".class": "SymbolTableNode", "cross_ref": "torch.fx", "kind": "Gdef", "module_public": false}, "get_computation_dtype": {".class": "SymbolTableNode", "cross_ref": "torch._prims_common.get_computation_dtype", "kind": "Gdef", "module_public": false}, "get_metrics_context": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.get_metrics_context", "kind": "Gdef", "module_public": false}, "graph_code_log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.fx.passes._tensorify_python_scalars.graph_code_log", "name": "graph_code_log", "type": "logging.Logger"}}, "guard_scalar": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.guard_scalar", "kind": "Gdef", "module_public": false}, "has_free_symbols": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.has_free_symbols", "kind": "Gdef", "module_public": false}, "justknobs_check": {".class": "SymbolTableNode", "cross_ref": "torch._utils_internal.justknobs_check", "kind": "Gdef", "module_public": false}, "lazy_format_graph_code": {".class": "SymbolTableNode", "cross_ref": "torch.fx._utils.lazy_format_graph_code", "kind": "Gdef", "module_public": false}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.fx.passes._tensorify_python_scalars.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "symbol_is_type": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.symbol_is_type", "kind": "Gdef", "module_public": false}, "sympy_interp": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.interp.sympy_interp", "kind": "Gdef", "module_public": false}, "tensorify_python_scalars": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "shape_env", "fake_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.fx.passes._tensorify_python_scalars.tensorify_python_scalars", "name": "tensorify_python_scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["gm", "shape_env", "fake_mode"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.fx.experimental.symbolic_shapes.ShapeEnv", "torch._subclasses.fake_tensor.FakeTensorMode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorify_python_scalars", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.fx.passes._tensorify_python_scalars.tensorify_python_scalars", "name": "tensorify_python_scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["gm", "shape_env", "fake_mode"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.fx.experimental.symbolic_shapes.ShapeEnv", "torch._subclasses.fake_tensor.FakeTensorMode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorify_python_scalars", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\fx\\passes\\_tensorify_python_scalars.py"}