{".class": "MypyFile", "_fullname": "streamlit.proto.ChatInput_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ChatInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput", "name": "ChatInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput", "builtins.object"], "names": {".class": "SymbolTable", "ACCEPT_FILE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.ACCEPT_FILE_FIELD_NUMBER", "name": "ACCEPT_FILE_FIELD_NUMBER", "type": "builtins.int"}}, "AcceptFile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.AcceptFile", "name": "AcceptFile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.AcceptFile", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput.AcceptFile", "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.AcceptFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.ChatInput_pb2.ChatInput.AcceptFile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BOTTOM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.BOTTOM", "name": "BOTTOM", "type": "streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.ChatInput_pb2.ChatInput", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "accept_file"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "accept_file"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "file_type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "file_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_chars"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "max_chars"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_upload_size_mb"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "max_upload_size_mb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "placeholder"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "placeholder"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "position"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "position"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "set_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "set_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of ChatInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DEFAULT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.DEFAULT_FIELD_NUMBER", "name": "DEFAULT_FIELD_NUMBER", "type": "builtins.int"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.ChatInput_pb2.google", "source_any": null, "type_of_any": 3}}}, "DISABLED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.DISABLED_FIELD_NUMBER", "name": "DISABLED_FIELD_NUMBER", "type": "builtins.int"}}, "FILE_TYPE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.FILE_TYPE_FIELD_NUMBER", "name": "FILE_TYPE_FIELD_NUMBER", "type": "builtins.int"}}, "ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.ID_FIELD_NUMBER", "name": "ID_FIELD_NUMBER", "type": "builtins.int"}}, "MAX_CHARS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.MAX_CHARS_FIELD_NUMBER", "name": "MAX_CHARS_FIELD_NUMBER", "type": "builtins.int"}}, "MAX_UPLOAD_SIZE_MB_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.MAX_UPLOAD_SIZE_MB_FIELD_NUMBER", "name": "MAX_UPLOAD_SIZE_MB_FIELD_NUMBER", "type": "builtins.int"}}, "MULTIPLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.MULTIPLE", "name": "MULTIPLE", "type": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType"}}, "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.NONE", "name": "NONE", "type": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType"}}, "PLACEHOLDER_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.PLACEHOLDER_FIELD_NUMBER", "name": "PLACEHOLDER_FIELD_NUMBER", "type": "builtins.int"}}, "POSITION_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.POSITION_FIELD_NUMBER", "name": "POSITION_FIELD_NUMBER", "type": "builtins.int"}}, "Position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.ChatInput_pb2.ChatInput._Position"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.Position", "name": "Position", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.Position", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput.Position", "streamlit.proto.ChatInput_pb2.ChatInput._Position", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.Position.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.ChatInput_pb2.ChatInput.Position", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SET_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.SET_VALUE_FIELD_NUMBER", "name": "SET_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "SINGLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.SINGLE", "name": "SINGLE", "type": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType"}}, "VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.VALUE_FIELD_NUMBER", "name": "VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "_AcceptFile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile", "name": "_AcceptFile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.V", "line": 55, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AcceptFileEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper", "name": "_AcceptFileEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.ChatInput_pb2.google", "source_any": null, "type_of_any": 3}}}, "MULTIPLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper.MULTIPLE", "name": "MULTIPLE", "type": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType"}}, "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper.NONE", "name": "NONE", "type": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType"}}, "SINGLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper.SINGLE", "name": "SINGLE", "type": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFileEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._Position", "name": "_Position", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._Position", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput._Position", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._Position.V", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._Position.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.ChatInput_pb2.ChatInput._Position", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PositionEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper", "name": "_PositionEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.ChatInput_pb2", "mro": ["streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "BOTTOM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper.BOTTOM", "name": "BOTTOM", "type": "streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.ChatInput_pb2.google", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.ChatInput_pb2.ChatInput._PositionEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "id", "placeholder", "max_chars", "disabled", "value", "set_value", "default", "position", "accept_file", "file_type", "max_upload_size_mb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "id", "placeholder", "max_chars", "disabled", "value", "set_value", "default", "position", "accept_file", "file_type", "max_upload_size_mb"], "arg_types": ["streamlit.proto.ChatInput_pb2.ChatInput", "builtins.str", "builtins.str", "builtins.int", "builtins.bool", "builtins.str", "builtins.bool", "builtins.str", "streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType", "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChatInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.accept_file", "name": "accept_file", "type": "streamlit.proto.ChatInput_pb2.ChatInput._AcceptFile.ValueType"}}, "default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.default", "name": "default", "type": "builtins.str"}}, "disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.disabled", "name": "disabled", "type": "builtins.bool"}}, "file_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.file_type", "name": "file_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.ChatInput_pb2.ChatInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file_type of ChatInput", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.ChatInput_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.file_type", "name": "file_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.ChatInput_pb2.ChatInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file_type of ChatInput", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.ChatInput_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.id", "name": "id", "type": "builtins.str"}}, "max_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.max_chars", "name": "max_chars", "type": "builtins.int"}}, "max_upload_size_mb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.max_upload_size_mb", "name": "max_upload_size_mb", "type": "builtins.int"}}, "placeholder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.placeholder", "name": "placeholder", "type": "builtins.str"}}, "position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.position", "name": "position", "type": "streamlit.proto.ChatInput_pb2.ChatInput._Position.ValueType"}}, "set_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.set_value", "name": "set_value", "type": "builtins.bool"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.value", "name": "value", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.ChatInput_pb2.ChatInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.ChatInput_pb2.ChatInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.ChatInput_pb2.google", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.ChatInput_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___ChatInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.ChatInput_pb2.global___ChatInput", "line": 111, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.ChatInput_pb2.ChatInput"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.ChatInput_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.ChatInput_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\ChatInput_pb2.pyi"}