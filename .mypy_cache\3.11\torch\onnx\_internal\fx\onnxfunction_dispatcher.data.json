{".class": "MypyFile", "_fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "OnnxFunctionDispatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "name": "OnnxFunction<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.onnxfunction_dispatcher", "mro": ["torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "onnx_registry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "onnx_registry"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "torch.onnx._internal._exporter_legacy.OnnxRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnnxFunctionDispatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_filter_or_keep_complex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "default_and_custom_functions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher._filter_or_keep_complex", "name": "_filter_or_keep_complex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "default_and_custom_functions"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["torch.onnx._internal.fx.registration.ONNXFunction"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_or_keep_complex of OnnxFunctionDispatcher", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.fx.registration.ONNXFunction"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_the_perfect_or_nearest_match_onnxfunction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "default_and_custom_functions", "onnx_args", "onnx_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher._find_the_perfect_or_nearest_match_onnxfunction", "name": "_find_the_perfect_or_nearest_match_onnxfunction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "default_and_custom_functions", "onnx_args", "onnx_kwargs"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "torch.fx.node.Node", {".class": "Instance", "args": ["torch.onnx._internal.fx.registration.ONNXFunction"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.onnx._internal.fx.type_utils.TensorLike", "builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.complex", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_the_perfect_or_nearest_match_onnxfunction of OnnxFunctionDispatcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_aten_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher._get_aten_name", "name": "_get_aten_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_aten_name of OnnxFunctionDispatcher", "ret_type": "torch.onnx._internal.fx.registration.OpName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "onnx_args", "onnx_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher.dispatch", "name": "dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "onnx_args", "onnx_kwargs"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "torch.fx.node.Node", {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.onnx._internal.fx.type_utils.TensorLike", "builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.complex", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dispatch of OnnxFunctionDispatcher", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_function_overloads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher.get_function_overloads", "name": "get_function_overloads", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_function_overloads of OnnxFunctionDispatcher", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.fx.registration.ONNXFunction"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "onnx_registry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher.onnx_registry", "name": "onnx_registry", "type": "torch.onnx._internal._exporter_legacy.OnnxRegistry"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnnxRegistry": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal._exporter_legacy.OnnxRegistry", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "_OnnxSchemaChecker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", "name": "_OnnxSchemaChecker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.onnxfunction_dispatcher", "mro": ["torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "onnxfunction"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "onnxfunction"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _OnnxSchemaChecker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_onnx_attribute_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "attribute_name", "attribute", "is_sequence"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker._match_onnx_attribute_type", "name": "_match_onnx_attribute_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "attribute_name", "attribute", "is_sequence"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match_onnx_attribute_type of _OnnxSchemaChecker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_matching_score": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker._matching_score", "name": "_matching_score", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_record_matching_score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker._record_matching_score", "name": "_record_matching_score", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "attributes"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.onnx._internal.fx.type_utils.TensorLike", "builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.complex", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_record_matching_score of _OnnxSchemaChecker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_separate_input_attributes_from_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "param_schemas", "args", "kwargs", "fill_defaults"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker._separate_input_attributes_from_arguments", "name": "_separate_input_attributes_from_arguments", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "param_schemas", "args", "kwargs", "fill_defaults"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.onnx._internal.fx.type_utils.TensorLike", "builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.complex", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_separate_input_attributes_from_arguments of _OnnxSchemaChecker", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.attributes", "name": "attributes", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "match_score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.match_score", "name": "match_score", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_score of _OnnxSchemaChecker", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.match_score", "name": "match_score", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_score of _OnnxSchemaChecker", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "onnxfunction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.onnxfunction", "name": "onnxfunction", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}}}, "op_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.op_schema", "name": "op_schema", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "param_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.param_schema", "name": "param_schema", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "perfect_match_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.perfect_match_inputs", "name": "perfect_match_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.onnx._internal.fx.type_utils.TensorLike", "builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.complex", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "perfect_match_inputs of _OnnxSchemaChecker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type_constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.type_constraints", "name": "type_constraints", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.onnxfunction_dispatcher._OnnxSchemaChecker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_find_onnx_data_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["torch_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._find_onnx_data_type", "name": "_find_onnx_data_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["torch_input"], "arg_types": [{".class": "UnionType", "items": ["torch.onnx._internal.fx.type_utils.TensorLike", "builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.complex", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_onnx_data_type", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_arg_with_complex_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher._is_arg_with_complex_dtype", "name": "_is_arg_with_complex_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_arg_with_complex_dtype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "fx_type_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.type_utils", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "onnxscript": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "name": "onnxscript", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript", "source_any": null, "type_of_any": 3}}}, "onnxscript_graph_building": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript_graph_building", "name": "onnxscript_graph_building", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.onnxfunction_dispatcher.onnxscript_graph_building", "source_any": null, "type_of_any": 3}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "registration": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.registration", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\onnxfunction_dispatcher.py"}