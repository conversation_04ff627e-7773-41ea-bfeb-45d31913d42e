{"data_mtime": 1755649448, "dep_lines": [19, 20, 22, 27, 28, 29, 30, 313, 393, 16, 17, 21, 24, 24, 25, 26, 41, 16, 17, 18, 39, 2, 4, 5, 6, 7, 8, 9, 10, 11, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 13], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 20, 20, 10, 10, 5, 10, 10, 5, 5, 20, 20, 20, 10, 25, 5, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["torch._inductor.runtime.runtime_utils", "torch.fx.experimental.symbolic_shapes", "torch.utils._sympy.symbol", "torch._inductor.codegen.aoti_hipify_utils", "torch._inductor.codegen.common", "torch._inductor.codegen.cpp_utils", "torch._inductor.codegen.wrapper", "torch.utils._sympy.solve", "torch.utils._sympy.value_ranges", "torch._higher_order_ops.torchbind", "torch._inductor.async_compile", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor.graph", "torch._higher_order_ops", "torch._inductor", "torch._ops", "collections.abc", "__future__", "ctypes", "functools", "math", "os", "sys", "textwrap", "itertools", "typing", "torch", "builtins", "inspect", "html", "string", "operator", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch.fx", "torch.fx.interpreter", "torch.utils"], "hash": "27c44a1b16e616838ae9fe6e7dad36687c40d240", "id": "torch._inductor.codegen.cpp_wrapper_cpu", "ignore_all": true, "interface_hash": "8b25734546760e73eb671e8d939789aef78f6c86", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_wrapper_cpu.py", "plugin_data": null, "size": 122134, "suppressed": ["sympy"], "version_id": "1.15.0"}