{"data_mtime": 1755649449, "dep_lines": [56, 55, 57, 52, 63, 64, 65, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 74, 52, 99, 2, 48, 49, 51, 98, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 25, 5, 5, 5, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.exporter._onnx_program", "torch.onnx._internal._exporter_legacy", "torch.onnx._internal.onnxruntime", "torch._<PERSON>._onnx", "torch.onnx._type_utils", "torch.onnx.errors", "torch.onnx.utils", "torch.onnx.ops", "torch.onnx.symbolic_caffe2", "torch.onnx.symbolic_helper", "torch.onnx.symbolic_opset7", "torch.onnx.symbolic_opset8", "torch.onnx.symbolic_opset9", "torch.onnx.symbolic_opset10", "torch.onnx.symbolic_opset11", "torch.onnx.symbolic_opset12", "torch.onnx.symbolic_opset13", "torch.onnx.symbolic_opset14", "torch.onnx.symbolic_opset15", "torch.onnx.symbolic_opset16", "torch.onnx.symbolic_opset17", "torch.onnx.symbolic_opset18", "torch.onnx.symbolic_opset19", "torch.onnx.symbolic_opset20", "torch._C", "collections.abc", "__future__", "typing", "typing_extensions", "torch", "os", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "enum", "torch.export", "torch.export.exported_program", "torch.jit", "torch.jit._script", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.onnx._internal", "torch.onnx._internal.exporter"], "hash": "eee700cc521c737ee07d9f96dafecd999b601227", "id": "torch.onnx", "ignore_all": true, "interface_hash": "6ebbfd5b7f5484eab880c8a188bf7c7f4bd52694", "mtime": 1755648857, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\__init__.py", "plugin_data": null, "size": 22300, "suppressed": [], "version_id": "1.15.0"}