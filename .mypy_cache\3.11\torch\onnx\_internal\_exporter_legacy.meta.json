{"data_mtime": 1755649448, "dep_lines": [26, 27, 27, 27, 257, 257, 24, 25, 26, 27, 24, 44, 23, 39, 44, 2, 13, 14, 15, 16, 17, 18, 19, 20, 22, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 41, 42], "dep_prios": [10, 10, 10, 10, 20, 20, 10, 5, 20, 20, 20, 25, 10, 25, 25, 5, 10, 10, 10, 10, 10, 5, 5, 5, 10, 25, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25], "dependencies": ["torch.onnx._internal.exporter._constants", "torch.onnx._internal.fx.decomposition_table", "torch.onnx._internal.fx.patcher", "torch.onnx._internal.fx.registration", "torch.onnx._internal.fx.dynamo_graph_extractor", "torch.onnx._internal.fx.onnxfunction_dispatcher", "torch.onnx._internal.io_adapter", "torch.onnx._internal._lazy_import", "torch.onnx._internal.exporter", "torch.onnx._internal.fx", "torch.onnx._internal", "torch._subclasses.fake_tensor", "torch._ops", "collections.abc", "torch._subclasses", "__future__", "abc", "contextlib", "dataclasses", "logging", "warnings", "collections", "typing", "typing_extensions", "torch", "io", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "copy", "_frozen_importlib", "_io", "_typeshed", "torch.fx", "torch.fx.graph_module", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "torch.utils._python_dispatch"], "hash": "a8c0ae948ab0556f394c341d8b1401f3f6ae88bd", "id": "torch.onnx._internal._exporter_legacy", "ignore_all": true, "interface_hash": "a5427a0183595b0f6125e3d8bd7b563c39d50fee", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\_exporter_legacy.py", "plugin_data": null, "size": 20089, "suppressed": ["onnxruntime", "onnxscript"], "version_id": "1.15.0"}