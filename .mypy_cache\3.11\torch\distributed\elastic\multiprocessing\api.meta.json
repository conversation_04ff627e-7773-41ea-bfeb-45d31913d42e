{"data_mtime": 1755649449, "dep_lines": [30, 31, 35, 39, 25, 29, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.elastic.multiprocessing.errors", "torch.distributed.elastic.multiprocessing.redirects", "torch.distributed.elastic.multiprocessing.subprocess_handler", "torch.distributed.elastic.multiprocessing.tail_log", "multiprocessing.synchronize", "torch.multiprocessing", "abc", "logging", "os", "re", "shutil", "signal", "subprocess", "sys", "tempfile", "threading", "time", "contextlib", "dataclasses", "enum", "multiprocessing", "types", "typing", "torch", "builtins", "inspect", "html", "string", "operator", "pprint", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "warnings", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "multiprocessing.context", "multiprocessing.queues", "torch.distributed.elastic.multiprocessing.subprocess_handler.handlers", "torch.distributed.elastic.multiprocessing.subprocess_handler.subprocess_handler", "torch.multiprocessing.spawn", "typing_extensions"], "hash": "09f946df8cd4c9a2798354859d45da52520c3874", "id": "torch.distributed.elastic.multiprocessing.api", "ignore_all": true, "interface_hash": "436de00c8c665470004f357f5d2939e154b44aaa", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\elastic\\multiprocessing\\api.py", "plugin_data": null, "size": 34668, "suppressed": [], "version_id": "1.15.0"}