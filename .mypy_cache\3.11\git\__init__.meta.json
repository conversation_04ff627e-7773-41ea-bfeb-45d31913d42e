{"data_mtime": 1755656862, "dep_lines": [165, 100, 124, 127, 128, 129, 142, 153, 161, 162, 163, 164, 165, 176, 91, 94, 96, 1, 1, 1, 1, 98], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 10, 5, 30, 30, 30, 5], "dependencies": ["git.index.util", "git.exc", "git.types", "git.compat", "git.config", "git.objects", "git.refs", "git.diff", "git.db", "git.cmd", "git.repo", "git.remote", "git.index", "git.util", "typing", "types", "warnings", "builtins", "_frozen_importlib", "abc", "os"], "hash": "f2c1afbd44c99fd69162f11913c86f39a4af01c9", "id": "git", "ignore_all": true, "interface_hash": "078b0fa468633ebd3ca942133ef1f5bb7e9d4265", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\__init__.py", "plugin_data": null, "size": 8899, "suppressed": ["gitdb.util"], "version_id": "1.15.0"}