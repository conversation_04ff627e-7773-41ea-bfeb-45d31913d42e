{".class": "MypyFile", "_fullname": "streamlit.proto.DownloadButton_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DownloadButton_pb2.google", "source_any": null, "type_of_any": 3}}}, "DownloadButton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton", "name": "DownloadButton", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.DownloadButton_pb2", "mro": ["streamlit.proto.DownloadButton_pb2.DownloadButton", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.DownloadButton_pb2.DownloadButton", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "form_id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "form_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "icon"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "icon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore_rerun"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "ignore_rerun"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "label"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "label"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_container_width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of DownloadButton", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DEFAULT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.DEFAULT_FIELD_NUMBER", "name": "DEFAULT_FIELD_NUMBER", "type": "builtins.int"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DownloadButton_pb2.google", "source_any": null, "type_of_any": 3}}}, "DISABLED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.DISABLED_FIELD_NUMBER", "name": "DISABLED_FIELD_NUMBER", "type": "builtins.int"}}, "FORM_ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.FORM_ID_FIELD_NUMBER", "name": "FORM_ID_FIELD_NUMBER", "type": "builtins.int"}}, "HELP_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.HELP_FIELD_NUMBER", "name": "HELP_FIELD_NUMBER", "type": "builtins.int"}}, "ICON_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.ICON_FIELD_NUMBER", "name": "ICON_FIELD_NUMBER", "type": "builtins.int"}}, "ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.ID_FIELD_NUMBER", "name": "ID_FIELD_NUMBER", "type": "builtins.int"}}, "IGNORE_RERUN_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.IGNORE_RERUN_FIELD_NUMBER", "name": "IGNORE_RERUN_FIELD_NUMBER", "type": "builtins.int"}}, "LABEL_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.LABEL_FIELD_NUMBER", "name": "LABEL_FIELD_NUMBER", "type": "builtins.int"}}, "TYPE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.TYPE_FIELD_NUMBER", "name": "TYPE_FIELD_NUMBER", "type": "builtins.int"}}, "URL_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.URL_FIELD_NUMBER", "name": "URL_FIELD_NUMBER", "type": "builtins.int"}}, "USE_CONTAINER_WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.USE_CONTAINER_WIDTH_FIELD_NUMBER", "name": "USE_CONTAINER_WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "id", "label", "default", "help", "form_id", "url", "disabled", "use_container_width", "type", "icon", "ignore_rerun"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "id", "label", "default", "help", "form_id", "url", "disabled", "use_container_width", "type", "icon", "ignore_rerun"], "arg_types": ["streamlit.proto.DownloadButton_pb2.DownloadButton", "builtins.str", "builtins.str", "builtins.bool", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DownloadButton", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.default", "name": "default", "type": "builtins.bool"}}, "disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.disabled", "name": "disabled", "type": "builtins.bool"}}, "form_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.form_id", "name": "form_id", "type": "builtins.str"}}, "help": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.help", "name": "help", "type": "builtins.str"}}, "icon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.icon", "name": "icon", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.id", "name": "id", "type": "builtins.str"}}, "ignore_rerun": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.ignore_rerun", "name": "ignore_rerun", "type": "builtins.bool"}}, "label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.label", "name": "label", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.type", "name": "type", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.url", "name": "url", "type": "builtins.str"}}, "use_container_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.use_container_width", "name": "use_container_width", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.DownloadButton_pb2.DownloadButton.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.DownloadButton_pb2.DownloadButton", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DownloadButton_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___DownloadButton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.DownloadButton_pb2.global___DownloadButton", "line": 70, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.DownloadButton_pb2.DownloadButton"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.DownloadButton_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DownloadButton_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\DownloadButton_pb2.pyi"}