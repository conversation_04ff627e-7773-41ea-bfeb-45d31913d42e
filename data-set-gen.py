import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import random
from sklearn.preprocessing import MinMaxScaler
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class SubstationEquipmentSimulator:
    """
    Synthetic dataset generator for substation equipment predictive maintenance.
    Phase 1: Power Transformers, HV Circuit Breakers, Current Transformers
    """
    
    def __init__(self, random_seed=42):
        np.random.seed(random_seed)
        random.seed(random_seed)
        self.scaler = MinMaxScaler()
        
    def generate_time_series(self, start_date='2020-01-01', days=365*3, freq='H'):
        """Generate base time series for simulation"""
        dates = pd.date_range(start=start_date, periods=days*24 if freq=='H' else days, freq=freq)
        return dates
    
    def add_seasonal_patterns(self, base_signal, dates, amplitude=0.3):
        """Add realistic seasonal variations"""
        day_of_year = dates.dayofyear
        seasonal = amplitude * np.sin(2 * np.pi * day_of_year / 365.25)
        daily = 0.1 * amplitude * np.sin(2 * np.pi * dates.hour / 24)
        return base_signal + seasonal + daily
    
    def add_noise(self, signal, noise_level=0.05):
        """Add realistic measurement noise"""
        noise = np.random.normal(0, noise_level * np.std(signal), len(signal))
        return signal + noise
    
    def generate_degradation_curve(self, length, failure_type='exponential', 
                                 initial_value=1.0, final_value=0.1, failure_point=None):
        """Generate realistic equipment degradation patterns"""
        if failure_point is None:
            failure_point = length
            
        x = np.linspace(0, 1, failure_point)
        
        if failure_type == 'exponential':
            # Exponential decay (common for insulation)
            decay_rate = -np.log(final_value / initial_value)
            curve = initial_value * np.exp(-decay_rate * x)
        elif failure_type == 'linear':
            # Linear degradation
            curve = initial_value - (initial_value - final_value) * x
        elif failure_type == 'bathtub':
            # Bathtub curve (early failure + wear out)
            early_phase = 0.2
            stable_phase = 0.6
            wearout_phase = 0.2
            
            curve = np.ones(len(x)) * initial_value
            early_end = int(early_phase * len(x))
            wearout_start = int((early_phase + stable_phase) * len(x))
            
            # Early failures
            curve[:early_end] *= np.exp(-2 * x[:early_end])
            # Wear out phase
            if wearout_start < len(x):
                wearout_x = x[wearout_start:]
                curve[wearout_start:] *= np.exp(-3 * (wearout_x - x[wearout_start]))
                
        # Extend to full length if needed
        if length > failure_point:
            # Equipment failed, maintain final value with noise
            remaining = np.ones(length - failure_point) * final_value
            curve = np.concatenate([curve, remaining])
        else:
            curve = curve[:length]
            
        return curve

class PowerTransformerSimulator(SubstationEquipmentSimulator):
    """Simulate power transformer degradation and monitoring data"""
    
    def simulate_transformer(self, equipment_id, dates, health_status='healthy', 
                           failure_time=None):
        """
        Simulate power transformer with DGA, thermal, and electrical parameters
        health_status: 'healthy', 'degrading', 'critical', 'failed'
        """
        n_points = len(dates)
        
        # Base operating conditions
        load_factor = self.add_seasonal_patterns(
            np.random.uniform(0.4, 0.8, n_points), dates, 0.2)
        load_factor = np.clip(load_factor, 0.1, 1.0)
        
        ambient_temp = self.add_seasonal_patterns(
            np.ones(n_points) * 25, dates, 15)  # 10-40°C range
        
        # Hotspot temperature (function of load and ambient)
        hotspot_temp = ambient_temp + 30 + 40 * load_factor**2
        
        # Health-based degradation
        if health_status == 'healthy':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'linear', 1.0, 0.95, n_points)
        elif health_status == 'degrading':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 1.0, 0.7, failure_time or n_points)
        elif health_status == 'critical':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 0.8, 0.3, failure_time or int(n_points*0.8))
        else:  # failed
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 0.5, 0.1, failure_time or int(n_points*0.5))
        
        # DGA Gas concentrations (ppm) - key indicators
        # Healthy baseline concentrations
        h2_base = 50 * (1/degradation_factor)  # Hydrogen
        ch4_base = 30 * (1/degradation_factor)  # Methane
        c2h2_base = 2 * (1/degradation_factor)**2  # Acetylene (critical)
        c2h4_base = 20 * (1/degradation_factor)**1.5  # Ethylene
        c2h6_base = 10 * (1/degradation_factor)  # Ethane
        co_base = 400 * (1/degradation_factor)  # Carbon monoxide
        co2_base = 8000 * (1/degradation_factor)  # Carbon dioxide
        
        # Add temperature dependency
        temp_factor = np.exp((hotspot_temp - 80) / 50)  # Arrhenius-like
        
        # DGA concentrations with noise
        dga_data = {
            'H2_ppm': self.add_noise(h2_base * temp_factor, 0.1),
            'CH4_ppm': self.add_noise(ch4_base * temp_factor, 0.1),
            'C2H2_ppm': self.add_noise(c2h2_base * temp_factor, 0.15),
            'C2H4_ppm': self.add_noise(c2h4_base * temp_factor, 0.1),
            'C2H6_ppm': self.add_noise(c2h6_base * temp_factor, 0.1),
            'CO_ppm': self.add_noise(co_base * temp_factor, 0.05),
            'CO2_ppm': self.add_noise(co2_base * temp_factor, 0.05)
        }
        
        # Electrical parameters
        insulation_resistance = self.add_noise(
            degradation_factor * 1000, 0.1)  # MOhm
        power_factor = self.add_noise(
            0.005 + 0.02 * (1 - degradation_factor), 0.1)  # tan delta
        
        # Physical measurements
        moisture_content = self.add_noise(
            8 + 15 * (1 - degradation_factor), 0.1)  # ppm
        oil_acidity = self.add_noise(
            0.01 + 0.1 * (1 - degradation_factor), 0.1)  # mg KOH/g
        
        # Compile data
        data = {
            'timestamp': dates,
            'equipment_id': equipment_id,
            'equipment_type': 'power_transformer',
            'load_factor': load_factor,
            'ambient_temp_c': ambient_temp,
            'hotspot_temp_c': hotspot_temp,
            'insulation_resistance_mohm': insulation_resistance,
            'power_factor': power_factor,
            'moisture_content_ppm': moisture_content,
            'oil_acidity_mg_koh_g': oil_acidity,
            'health_status': health_status,
            'degradation_factor': degradation_factor,
            **dga_data
        }
        
        return pd.DataFrame(data)

class CircuitBreakerSimulator(SubstationEquipmentSimulator):
    """Simulate HV circuit breaker degradation and monitoring data"""
    
    def simulate_breaker(self, equipment_id, dates, health_status='healthy',
                        failure_time=None):
        """
        Simulate HV circuit breaker mechanical and electrical parameters
        """
        n_points = len(dates)
        
        # Operation count (cumulative)
        base_operations_per_day = np.random.uniform(0.5, 2.0)
        daily_operations = np.random.poisson(base_operations_per_day, n_points//24)
        operation_count = np.repeat(daily_operations, 24)[:n_points].cumsum()
        
        # Health-based degradation
        if health_status == 'healthy':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'linear', 1.0, 0.9, n_points)
        elif health_status == 'degrading':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 1.0, 0.6, failure_time or n_points)
        elif health_status == 'critical':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 0.7, 0.2, failure_time or int(n_points*0.7))
        else:  # failed
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 0.4, 0.05, failure_time or int(n_points*0.4))
        
        # Contact resistance (increases with wear)
        base_resistance = 50  # micro-ohms
        contact_resistance = self.add_noise(
            base_resistance * (1 + 2 * (1 - degradation_factor)), 0.1)
        
        # Operating times (increase with mechanical wear)
        base_open_time = 50  # milliseconds
        base_close_time = 80  # milliseconds
        
        opening_time = self.add_noise(
            base_open_time * (1 + 0.5 * (1 - degradation_factor)), 0.05)
        closing_time = self.add_noise(
            base_close_time * (1 + 0.5 * (1 - degradation_factor)), 0.05)
        
        # SF6 gas parameters (for SF6 breakers)
        sf6_pressure = self.add_noise(
            6.0 * degradation_factor, 0.02)  # bar
        sf6_purity = self.add_noise(
            99.0 * degradation_factor, 0.01)  # %
        
        # Environmental factors
        ambient_temp = self.add_seasonal_patterns(
            np.ones(n_points) * 25, dates, 15)
        humidity = self.add_seasonal_patterns(
            np.ones(n_points) * 60, dates, 20)
        
        # Partial discharge (increases with insulation degradation)
        partial_discharge = self.add_noise(
            10 + 100 * (1 - degradation_factor)**2, 0.2)  # pC
        
        data = {
            'timestamp': dates,
            'equipment_id': equipment_id,
            'equipment_type': 'circuit_breaker',
            'operation_count': operation_count,
            'contact_resistance_microohm': contact_resistance,
            'opening_time_ms': opening_time,
            'closing_time_ms': closing_time,
            'sf6_pressure_bar': sf6_pressure,
            'sf6_purity_percent': sf6_purity,
            'partial_discharge_pc': partial_discharge,
            'ambient_temp_c': ambient_temp,
            'humidity_percent': humidity,
            'health_status': health_status,
            'degradation_factor': degradation_factor
        }
        
        return pd.DataFrame(data)

class CurrentTransformerSimulator(SubstationEquipmentSimulator):
    """Simulate current transformer degradation and monitoring data"""
    
    def simulate_ct(self, equipment_id, dates, health_status='healthy',
                   failure_time=None):
        """
        Simulate current transformer insulation and accuracy parameters
        """
        n_points = len(dates)
        
        # Load conditions
        primary_current = self.add_seasonal_patterns(
            np.random.uniform(200, 800, n_points), dates, 0.3)  # Amperes
        burden_load = self.add_seasonal_patterns(
            np.random.uniform(0.2, 0.8, n_points), dates, 0.2)  # Per unit
        
        # Health-based degradation
        if health_status == 'healthy':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'linear', 1.0, 0.92, n_points)
        elif health_status == 'degrading':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 1.0, 0.65, failure_time or n_points)
        elif health_status == 'critical':
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 0.75, 0.25, failure_time or int(n_points*0.75))
        else:  # failed
            degradation_factor = self.generate_degradation_curve(
                n_points, 'exponential', 0.5, 0.1, failure_time or int(n_points*0.5))
        
        # Insulation parameters
        insulation_resistance = self.add_noise(
            degradation_factor * 5000, 0.1)  # MOhm
        power_factor = self.add_noise(
            0.002 + 0.015 * (1 - degradation_factor), 0.1)  # tan delta
        capacitance = self.add_noise(
            200 + 50 * (1 - degradation_factor), 0.05)  # pF
        
        # Accuracy parameters
        ratio_error = self.add_noise(
            0.1 * (1 - degradation_factor), 0.1)  # %
        phase_angle_error = self.add_noise(
            2 * (1 - degradation_factor), 0.1)  # minutes
        
        # Environmental factors
        ambient_temp = self.add_seasonal_patterns(
            np.ones(n_points) * 25, dates, 15)
        
        # Temperature rise due to loading
        temp_rise = 10 + 20 * (primary_current / 1000)**2 * burden_load
        winding_temp = ambient_temp + temp_rise
        
        # Partial discharge (critical parameter)
        partial_discharge = self.add_noise(
            5 + 50 * (1 - degradation_factor)**2, 0.2)  # pC
        
        data = {
            'timestamp': dates,
            'equipment_id': equipment_id,
            'equipment_type': 'current_transformer',
            'primary_current_a': primary_current,
            'burden_load_pu': burden_load,
            'winding_temp_c': winding_temp,
            'insulation_resistance_mohm': insulation_resistance,
            'power_factor': power_factor,
            'capacitance_pf': capacitance,
            'ratio_error_percent': ratio_error,
            'phase_angle_error_min': phase_angle_error,
            'partial_discharge_pc': partial_discharge,
            'ambient_temp_c': ambient_temp,
            'health_status': health_status,
            'degradation_factor': degradation_factor
        }
        
        return pd.DataFrame(data)

class DatasetGenerator:
    """Main class to generate complete synthetic dataset"""
    
    def __init__(self, random_seed=42):
        self.transformer_sim = PowerTransformerSimulator(random_seed)
        self.breaker_sim = CircuitBreakerSimulator(random_seed)
        self.ct_sim = CurrentTransformerSimulator(random_seed)
        
    def generate_complete_dataset(self, 
                                n_transformers=5, 
                                n_breakers=8, 
                                n_cts=12,
                                duration_days=365*3,
                                start_date='2020-01-01'):
        """
        Generate complete Phase 1 dataset with multiple equipment instances
        """
        all_data = []
        dates = pd.date_range(start=start_date, periods=duration_days*24, freq='H')
        
        # Health status distribution (realistic)
        health_distribution = {
            'healthy': 0.6,
            'degrading': 0.25,
            'critical': 0.1,
            'failed': 0.05
        }
        
        def get_random_health():
            rand = np.random.random()
            cumsum = 0
            for status, prob in health_distribution.items():
                cumsum += prob
                if rand <= cumsum:
                    return status
            return 'healthy'
        
        # Generate transformers
        print("Generating power transformer data...")
        for i in range(n_transformers):
            health = get_random_health()
            failure_time = None
            if health in ['critical', 'failed']:
                failure_time = np.random.randint(len(dates)//3, len(dates))
            
            transformer_data = self.transformer_sim.simulate_transformer(
                f'T{i+1:03d}', dates, health, failure_time)
            all_data.append(transformer_data)
        
        # Generate circuit breakers
        print("Generating circuit breaker data...")
        for i in range(n_breakers):
            health = get_random_health()
            failure_time = None
            if health in ['critical', 'failed']:
                failure_time = np.random.randint(len(dates)//3, len(dates))
            
            breaker_data = self.breaker_sim.simulate_breaker(
                f'CB{i+1:03d}', dates, health, failure_time)
            all_data.append(breaker_data)
        
        # Generate current transformers
        print("Generating current transformer data...")
        for i in range(n_cts):
            health = get_random_health()
            failure_time = None
            if health in ['critical', 'failed']:
                failure_time = np.random.randint(len(dates)//3, len(dates))
            
            ct_data = self.ct_sim.simulate_ct(
                f'CT{i+1:03d}', dates, health, failure_time)
            all_data.append(ct_data)
        
        # Combine all data
        complete_dataset = pd.concat(all_data, ignore_index=True)
        
        return complete_dataset
    
    def prepare_lstm_data(self, dataset, sequence_length=168,
                         target_column='degradation_factor'):
        """
        Prepare data for LSTM training with sliding windows
        sequence_length: hours of history to use (168 = 1 week)
        """
        # Select only numeric features for LSTM
        exclude_cols = ['timestamp', 'equipment_id', 'equipment_type', 'health_status']
        numeric_cols = dataset.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]

        print(f"Using {len(feature_cols)} numeric features for LSTM")

        # Clean the dataset first
        dataset_clean = dataset.copy()

        # Fill NaN values with appropriate strategies
        for col in feature_cols:
            if dataset_clean[col].isnull().any():
                median_val = dataset_clean[col].median()
                dataset_clean[col].fillna(median_val, inplace=True)
                print(f"Filled {dataset_clean[col].isnull().sum()} NaN values in {col} with median: {median_val:.3f}")

        # Prepare sequences for each equipment
        X_sequences = []
        y_sequences = []
        equipment_ids = []

        for equipment_id in dataset_clean['equipment_id'].unique():
            equipment_data = dataset_clean[dataset_clean['equipment_id'] == equipment_id].copy()
            equipment_data = equipment_data.sort_values('timestamp')

            # Extract numeric features and ensure they are float
            feature_data = equipment_data[feature_cols].astype(np.float64).values

            # Check for any remaining NaN or inf values
            if np.isnan(feature_data).any() or np.isinf(feature_data).any():
                print(f"Warning: Found NaN/Inf in equipment {equipment_id}, skipping...")
                continue

            # Scale features with robust scaling
            scaler = MinMaxScaler(feature_range=(0, 1))
            try:
                scaled_features = scaler.fit_transform(feature_data)
            except Exception as e:
                print(f"Scaling failed for equipment {equipment_id}: {e}")
                continue

            # Validate scaled features
            if np.isnan(scaled_features).any() or np.isinf(scaled_features).any():
                print(f"Warning: Scaling produced NaN/Inf for equipment {equipment_id}, skipping...")
                continue

            # Create sequences
            target_idx = feature_cols.index(target_column)
            for i in range(sequence_length, len(scaled_features)):
                X_sequences.append(scaled_features[i-sequence_length:i])
                y_sequences.append(scaled_features[i, target_idx])
                equipment_ids.append(equipment_id)

        X = np.array(X_sequences, dtype=np.float32)
        y = np.array(y_sequences, dtype=np.float32)

        # Final validation
        if np.isnan(X).any() or np.isnan(y).any():
            raise ValueError("Generated data still contains NaN values!")

        if np.isinf(X).any() or np.isinf(y).any():
            raise ValueError("Generated data contains infinite values!")

        print(f"Successfully generated {len(X)} sequences")
        print(f"X range: [{X.min():.6f}, {X.max():.6f}]")
        print(f"y range: [{y.min():.6f}, {y.max():.6f}]")

        return X, y, equipment_ids, feature_cols

    def validate_dataset(self, dataset):
        """
        Comprehensive validation of the generated dataset
        """
        print("\n" + "="*60)
        print("DATASET VALIDATION REPORT")
        print("="*60)

        # Basic dataset info
        print(f"Dataset shape: {dataset.shape}")
        print(f"Memory usage: {dataset.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        print(f"Date range: {dataset['timestamp'].min()} to {dataset['timestamp'].max()}")

        # Equipment distribution
        print(f"\nEquipment distribution:")
        equipment_counts = dataset['equipment_type'].value_counts()
        for eq_type, count in equipment_counts.items():
            print(f"  {eq_type}: {count:,} records")

        unique_equipment = dataset['equipment_id'].nunique()
        print(f"Total unique equipment: {unique_equipment}")

        # Health status distribution
        print(f"\nHealth status distribution:")
        health_counts = dataset['health_status'].value_counts()
        for status, count in health_counts.items():
            print(f"  {status}: {count:,} records ({count/len(dataset)*100:.1f}%)")

        # Data quality checks
        print(f"\nDATA QUALITY CHECKS:")
        print("-" * 30)

        # Check for missing values
        missing_data = dataset.isnull().sum()
        total_missing = missing_data.sum()
        print(f"Total missing values: {total_missing:,}")

        if total_missing > 0:
            print("Missing values by column:")
            for col, missing_count in missing_data.items():
                if missing_count > 0:
                    pct = (missing_count / len(dataset)) * 100
                    print(f"  {col}: {missing_count:,} ({pct:.1f}%)")

        # Check for duplicate records
        duplicates = dataset.duplicated().sum()
        print(f"Duplicate records: {duplicates:,}")

        # Check numeric columns for anomalies
        numeric_cols = dataset.select_dtypes(include=[np.number]).columns
        print(f"\nNUMERIC COLUMN ANALYSIS:")
        print("-" * 30)

        anomaly_count = 0
        for col in numeric_cols:
            if col in ['timestamp']:  # Skip timestamp-like columns
                continue

            col_data = dataset[col]

            # Check for infinite values
            inf_count = np.isinf(col_data).sum()
            if inf_count > 0:
                print(f"  {col}: {inf_count} infinite values")
                anomaly_count += inf_count

            # Check for extreme outliers (beyond 5 standard deviations)
            if col_data.std() > 0:  # Avoid division by zero
                z_scores = np.abs((col_data - col_data.mean()) / col_data.std())
                extreme_outliers = (z_scores > 5).sum()
                if extreme_outliers > 0:
                    print(f"  {col}: {extreme_outliers} extreme outliers (>5σ)")

            # Check for negative values where they shouldn't be
            negative_cols = ['insulation_resistance_mohm', 'moisture_content_ppm',
                           'operation_count', 'sf6_pressure_bar', 'sf6_purity_percent']
            if col in negative_cols:
                negative_count = (col_data < 0).sum()
                if negative_count > 0:
                    print(f"  {col}: {negative_count} negative values (unexpected)")
                    anomaly_count += negative_count

        # Check degradation factor specifically
        if 'degradation_factor' in dataset.columns:
            deg_factor = dataset['degradation_factor']
            out_of_range = ((deg_factor < 0) | (deg_factor > 1)).sum()
            if out_of_range > 0:
                print(f"  degradation_factor: {out_of_range} values outside [0,1] range")
                anomaly_count += out_of_range

        # Time series continuity check
        print(f"\nTIME SERIES VALIDATION:")
        print("-" * 30)

        time_issues = 0
        for equipment_id in dataset['equipment_id'].unique()[:5]:  # Check first 5 equipment
            eq_data = dataset[dataset['equipment_id'] == equipment_id].sort_values('timestamp')
            time_diffs = eq_data['timestamp'].diff().dt.total_seconds() / 3600  # Hours

            # Check for missing time points (gaps > 2 hours)
            large_gaps = (time_diffs > 2).sum()
            if large_gaps > 0:
                print(f"  {equipment_id}: {large_gaps} time gaps > 2 hours")
                time_issues += large_gaps

        if time_issues == 0:
            print("  Time series continuity: OK (checked sample)")

        # Summary
        print(f"\nVALIDATION SUMMARY:")
        print("-" * 30)

        total_issues = total_missing + duplicates + anomaly_count + time_issues

        if total_issues == 0:
            print("✅ Dataset validation PASSED - No issues found")
            return True
        else:
            print(f"⚠️  Dataset validation found {total_issues} issues:")
            if total_missing > 0:
                print(f"   - {total_missing:,} missing values")
            if duplicates > 0:
                print(f"   - {duplicates:,} duplicate records")
            if anomaly_count > 0:
                print(f"   - {anomaly_count:,} data anomalies")
            if time_issues > 0:
                print(f"   - {time_issues:,} time series issues")

            print("\n⚠️  Consider cleaning the data before using for ML training")
            return False

    def validate_lstm_data(self, X, y, feature_cols):
        """
        Validate the prepared LSTM training data
        """
        print("\n" + "="*60)
        print("LSTM DATA VALIDATION REPORT")
        print("="*60)

        print(f"Input sequences shape: {X.shape}")
        print(f"Target values shape: {y.shape}")
        print(f"Number of features: {len(feature_cols)}")
        print(f"Sequence length: {X.shape[1]} time steps")
        print(f"Total samples: {X.shape[0]:,}")

        # Data type and range checks
        print(f"\nDATA QUALITY:")
        print("-" * 20)
        print(f"X data type: {X.dtype}")
        print(f"y data type: {y.dtype}")
        print(f"X range: [{X.min():.6f}, {X.max():.6f}]")
        print(f"y range: [{y.min():.6f}, {y.max():.6f}]")

        # Check for problematic values
        issues = []

        if np.isnan(X).any():
            nan_count = np.isnan(X).sum()
            issues.append(f"X contains {nan_count:,} NaN values")

        if np.isnan(y).any():
            nan_count = np.isnan(y).sum()
            issues.append(f"y contains {nan_count:,} NaN values")

        if np.isinf(X).any():
            inf_count = np.isinf(X).sum()
            issues.append(f"X contains {inf_count:,} infinite values")

        if np.isinf(y).any():
            inf_count = np.isinf(y).sum()
            issues.append(f"y contains {inf_count:,} infinite values")

        # Check if data is properly normalized
        if X.min() < -0.1 or X.max() > 1.1:
            issues.append("X values appear to be outside expected normalized range [0,1]")

        if y.min() < -0.1 or y.max() > 1.1:
            issues.append("y values appear to be outside expected normalized range [0,1]")

        # Check for constant features
        constant_features = []
        for i, feature in enumerate(feature_cols):
            feature_data = X[:, :, i]
            if np.std(feature_data) < 1e-8:
                constant_features.append(feature)

        if constant_features:
            issues.append(f"Constant features detected: {constant_features}")

        # Memory usage estimate
        memory_mb = (X.nbytes + y.nbytes) / 1024**2
        print(f"Memory usage: {memory_mb:.2f} MB")

        # Validation result
        if not issues:
            print("\n✅ LSTM data validation PASSED - Ready for training")
            return True
        else:
            print(f"\n⚠️  LSTM data validation found {len(issues)} issues:")
            for issue in issues:
                print(f"   - {issue}")
            print("\n❌ Data may cause training problems - consider fixing issues")
            return False

# Example usage and data generation
if __name__ == "__main__":
    # Generate the dataset
    generator = DatasetGenerator(random_seed=42)
    
    print("Generating Phase 1 Substation Equipment Dataset...")
    dataset = generator.generate_complete_dataset(
        n_transformers=3,    # Start smaller for testing
        n_breakers=5,
        n_cts=8,
        duration_days=365*2,  # 2 years of data
        start_date='2020-01-01'
    )
    
    print(f"Dataset generated with {len(dataset)} records")
    print(f"Equipment types: {dataset['equipment_type'].value_counts()}")
    print(f"Health status distribution: {dataset['health_status'].value_counts()}")

    # Validate the generated dataset
    print("\nValidating generated dataset...")
    dataset_valid = generator.validate_dataset(dataset)

    if not dataset_valid:
        print("\n⚠️  Dataset validation failed. Consider reviewing the data generation process.")
        print("Proceeding with LSTM data preparation anyway...")

    # Prepare LSTM training data
    print("\nPreparing LSTM training sequences...")
    X, y, equipment_ids, feature_names = generator.prepare_lstm_data(
        dataset, sequence_length=168)  # 1 week sequences

    print(f"LSTM input shape: {X.shape}")
    print(f"LSTM target shape: {y.shape}")
    print(f"Features used: {len(feature_names)}")

    # Validate LSTM data
    print("\nValidating LSTM training data...")
    lstm_valid = generator.validate_lstm_data(X, y, feature_names)

    if not lstm_valid:
        print("\n❌ LSTM data validation failed. Training may encounter issues.")
        print("Consider fixing the data issues before training.")
    else:
        print("\n✅ All validations passed. Data is ready for training!")

    # Save dataset
    dataset.to_csv('substation_equipment_dataset.csv', index=False)
    np.save('lstm_sequences_X.npy', X)
    np.save('lstm_sequences_y.npy', y)

    print("\nDataset saved to:")
    print("- substation_equipment_dataset.csv (full dataset)")
    print("- lstm_sequences_X.npy (LSTM input sequences)")
    print("- lstm_sequences_y.npy (LSTM target values)")

    # Display sample data
    print("\nSample data preview:")
    print(dataset.head())
    print("\nDataset info:")
    print(dataset.info())