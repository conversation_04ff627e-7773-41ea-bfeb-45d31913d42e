{"data_mtime": 1755649448, "dep_lines": [25, 26, 2, 3, 4, 5, 7, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 19, 20, 22, 23, 21, 12, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 20, 5, 5, 5, 5, 5, 5, 5, 20], "dependencies": ["torch.utils.tensorboard._convert_np", "torch.utils.tensorboard._utils", "json", "logging", "os", "struct", "typing", "torch", "numpy", "builtins", "collections", "warnings", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "traceback", "re", "html", "_frozen_importlib", "abc", "torch._C", "torch._tensor", "contextlib"], "hash": "f1739d58a70cf78615d8dc980807e0823af67824", "id": "torch.utils.tensorboard.summary", "ignore_all": true, "interface_hash": "573c73c9e616daf397385eb783aee50cb479fc3c", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\summary.py", "plugin_data": null, "size": 35444, "suppressed": ["tensorboard.compat.proto.summary_pb2", "tensorboard.compat.proto.tensor_pb2", "tensorboard.compat.proto.tensor_shape_pb2", "tensorboard.plugins.pr_curve.plugin_data_pb2", "tensorboard.plugins.text.plugin_data_pb2", "tensorboard.plugins.custom_scalar", "google.protobuf", "traitlets.utils.warnings"], "version_id": "1.15.0"}