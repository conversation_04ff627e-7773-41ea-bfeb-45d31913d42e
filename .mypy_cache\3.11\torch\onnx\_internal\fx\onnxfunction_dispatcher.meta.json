{"data_mtime": 1755649448, "dep_lines": [17, 17, 17, 28, 15, 16, 21, 7, 9, 10, 11, 12, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24, 23, 629], "dep_prios": [10, 10, 20, 25, 10, 10, 25, 5, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 25, 25, 20], "dependencies": ["torch.onnx._internal.fx.registration", "torch.onnx._internal.fx.type_utils", "torch.onnx._internal.fx", "torch.onnx._internal._exporter_legacy", "torch._ops", "torch.fx", "collections.abc", "__future__", "logging", "operator", "types", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._tensor", "torch.fx.node"], "hash": "c2713f45902cf6f488dfe16273a5bacb7405bf4d", "id": "torch.onnx._internal.fx.onnxfunction_dispatcher", "ignore_all": true, "interface_hash": "b8184bbc54338a8f82a0f0d472ca452ed8bee283", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\onnxfunction_dispatcher.py", "plugin_data": null, "size": 31138, "suppressed": ["onnxscript.function_libs.torch_lib", "onnxscript", "onnx"], "version_id": "1.15.0"}