import{r as n,s as p,L as x,bj as h,ap as y,aT as C,j as i,bk as b}from"./index.DKN5MVff.js";const f=n.createContext(null);f.displayName="ElementFullscreenContext";const w=p("div",{target:"e1q5ojhd0"})(({theme:e,isExpanded:t})=>({width:"100%",...t?{position:"fixed",top:0,left:0,bottom:0,right:0,background:e.colors.bgColor,zIndex:e.zIndices.fullscreenWrapper,padding:e.spacing.md,paddingTop:e.sizes.fullScreenHeaderHeight,overflow:"auto",display:"flex",alignItems:"center",justifyContent:"center"}:{}})),F=()=>{const{setFullScreen:e}=n.useContext(x),[t,c]=n.useState(!1),{fullHeight:s,fullWidth:u}=h(),l=n.useCallback(a=>{c(a),e(a)},[e]),d=n.useCallback(()=>{document.body.style.overflow="hidden",l(!0)},[l]),o=n.useCallback(()=>{document.body.style.overflow="unset",l(!1)},[l]),r=n.useCallback(a=>{a.keyCode===27&&t&&o()},[o,t]);return n.useEffect(()=>(document.addEventListener("keydown",r,!1),()=>{document.removeEventListener("keydown",r,!1)}),[r]),n.useMemo(()=>({expanded:t,zoomIn:d,zoomOut:o,fullHeight:s,fullWidth:u}),[t,d,o,s,u])},g=({children:e,height:t})=>{const c=y(),{expanded:s,fullHeight:u,fullWidth:l,zoomIn:d,zoomOut:o}=F(),[r,a]=C(),m=n.useMemo(()=>({width:s?l:r,height:s?u:t,expanded:s,expand:d,collapse:o}),[s,u,l,t,r,d,o]);return i(f.Provider,{value:m,children:i(w,{ref:a,isExpanded:s,"data-testid":"stFullScreenFrame",theme:c,children:e})})};function S(e){const t=c=>i(g,{children:i(e,{...c})});return t.displayName=`withFullScreenWrapper(${e.displayName||e.name})`,b(t,e)}export{f as E,S as w};
