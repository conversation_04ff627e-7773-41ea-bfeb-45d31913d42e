{".class": "MypyFile", "_fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Arg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Arg", "kind": "Gdef"}, "CallFunction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.CallFunction", "kind": "Gdef"}, "MAX_OTHER_DIMENSION_DECOMPOSITION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.MAX_OTHER_DIMENSION_DECOMPOSITION", "name": "MAX_OTHER_DIMENSION_DECOMPOSITION", "type": "builtins.int"}}, "MIN_FIRST_DIMENSION_DECOMPOSITION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.MIN_FIRST_DIMENSION_DECOMPOSITION", "name": "MIN_FIRST_DIMENSION_DECOMPOSITION", "type": "builtins.int"}}, "Match": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Match", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "check_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.check_device", "name": "check_device", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "device"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_device", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "construct_pattern_matcher_pass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.split_cat.construct_pattern_matcher_pass", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "decompose_addmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["match", "mat1", "mat2", "mat3"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.decompose_addmm", "name": "decompose_addmm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["match", "mat1", "mat2", "mat3"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decompose_addmm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.decompose_addmm", "name": "decompose_addmm", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "decompose_addmm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "decompose_bmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["match", "mat1", "mat2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.decompose_bmm", "name": "decompose_bmm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["match", "mat1", "mat2"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decompose_bmm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.decompose_bmm", "name": "decompose_bmm", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "decompose_bmm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "decompose_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["match", "mat1", "mat2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.decompose_mm", "name": "decompose_mm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["match", "mat1", "mat2"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decompose_mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.decompose_mm", "name": "decompose_mm", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "decompose_mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_node_meta_valid": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.is_node_meta_valid", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "max_other_dimension_decomposition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.max_other_dimension_decomposition", "name": "max_other_dimension_decomposition", "type": "builtins.int"}}, "min_first_dimension_decomposition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.min_first_dimension_decomposition", "name": "min_first_dimension_decomposition", "type": "builtins.int"}}, "print_decompose_pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["match", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.print_decompose_pattern", "name": "print_decompose_pattern", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["match", "inputs"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_decompose_pattern", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "realize_inputs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.realize_inputs", "name": "realize_inputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["inputs"], "arg_types": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "realize_inputs", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_graph_pattern": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.register_graph_pattern", "kind": "Gdef"}, "should_decompose_bmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mat1", "mat2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.should_decompose_bmm", "name": "should_decompose_bmm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mat1", "mat2"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_decompose_bmm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_decompose_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mat1", "mat2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.decompose_mem_bound_mm.should_decompose_mm", "name": "should_decompose_mm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mat1", "mat2"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_decompose_mm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "statically_known_true": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.statically_known_true", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\decompose_mem_bound_mm.py"}