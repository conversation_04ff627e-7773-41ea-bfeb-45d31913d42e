{"data_mtime": 1755649449, "dep_lines": [19, 19, 9, 10, 10, 10, 10, 10, 10, 10, 19, 9, 10, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 20, 20, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 20, 20], "dependencies": ["torch.onnx._internal.jit_utils", "torch.onnx._internal.registration", "torch._<PERSON>._onnx", "torch.onnx._constants", "torch.onnx._type_utils", "torch.onnx.errors", "torch.onnx.symbolic_helper", "torch.onnx.symbolic_opset11", "torch.onnx.symbolic_opset9", "torch.onnx.utils", "torch.onnx._internal", "torch._C", "torch.onnx", "functools", "torch", "builtins", "collections", "warnings", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "sys", "json", "traceback", "re", "html", "typing", "_frozen_importlib", "abc", "contextlib"], "hash": "47c9b51d6af4ca22310c6878d9e4228070d33cc9", "id": "torch.onnx.symbolic_opset13", "ignore_all": true, "interface_hash": "925c2201539ecb56a0aae8441a628c67cc3850bb", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\symbolic_opset13.py", "plugin_data": null, "size": 42367, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}