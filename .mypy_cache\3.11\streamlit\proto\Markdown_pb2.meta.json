{"data_mtime": 1755656858, "dep_lines": [20, 24, 25, 1, 1, 1, 1, 22, 21, 22, 23, 21, 21], "dep_prios": [10, 10, 10, 30, 30, 30, 30, 10, 10, 20, 10, 20, 20], "dependencies": ["builtins", "sys", "typing", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "ac4d328bc9691bf32d20ccfc53ec69da54034968", "id": "streamlit.proto.Markdown_pb2", "ignore_all": true, "interface_hash": "6f77fdba4601bf71e846e92f82d3fa061148c7b6", "mtime": 1755656336, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\Markdown_pb2.pyi", "plugin_data": null, "size": 3195, "suppressed": ["google.protobuf.internal.enum_type_wrapper", "google.protobuf.descriptor", "google.protobuf.internal", "google.protobuf.message", "google.protobuf", "google"], "version_id": "1.15.0"}