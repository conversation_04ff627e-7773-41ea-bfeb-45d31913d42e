{"data_mtime": 1755649448, "dep_lines": [12, 2, 3, 4, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 7, 8, 9], "dep_prios": [5, 5, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["torch.utils.tensorboard._proto_graph", "collections", "contextlib", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "copy", "_frozen_importlib", "_typeshed", "abc", "torch._C", "typing_extensions"], "hash": "13a2b52d6b6d30918f97f0a084e968aca1cf24c4", "id": "torch.utils.tensorboard._pytorch_graph", "ignore_all": true, "interface_hash": "b53e539b933a7ae6a38db5b51fd12c3ec3f9e442", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\_pytorch_graph.py", "plugin_data": null, "size": 14068, "suppressed": ["tensorboard.compat.proto.config_pb2", "tensorboard.compat.proto.graph_pb2", "tensorboard.compat.proto.step_stats_pb2", "tensorboard.compat.proto.versions_pb2"], "version_id": "1.15.0"}