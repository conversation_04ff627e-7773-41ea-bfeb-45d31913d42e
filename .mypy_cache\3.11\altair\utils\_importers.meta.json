{"data_mtime": 1755656859, "dep_lines": [3, 6, 1, 4, 9, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 5, 30, 30], "dependencies": ["importlib.metadata", "packaging.version", "__future__", "typing", "types", "builtins", "_frozen_importlib", "abc"], "hash": "5b333c9a892210399e7c7812483ea69cc517aaaa", "id": "altair.utils._importers", "ignore_all": true, "interface_hash": "ef1d32d662550cc49aefe50d4a4bb5d96c77d9eb", "mtime": 1755656335, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\utils\\_importers.py", "plugin_data": null, "size": 4111, "suppressed": [], "version_id": "1.15.0"}