{"data_mtime": 1755656862, "dep_lines": [93, 38, 46, 90, 91, 92, 95, 6, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 47, 68, 1, 1, 1, 1, 1, 1, 1, 1, 1, 54], "dep_prios": [25, 10, 5, 25, 25, 25, 5, 10, 5, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.repo.base", "os.path", "urllib.parse", "git.cmd", "git.config", "git.remote", "git.types", "sys", "abc", "contextlib", "functools", "getpass", "logging", "os", "pathlib", "platform", "re", "shutil", "stat", "subprocess", "time", "warnings", "typing", "builtins", "_frozen_importlib", "_io", "configparser", "enum", "git.repo", "io", "types", "typing_extensions"], "hash": "07abe52d213c8d9c0e6a53e5748c913cca0c5f5a", "id": "git.util", "ignore_all": true, "interface_hash": "1565b8c6d674825262450c34020e45185e1ce67a", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\util.py", "plugin_data": null, "size": 43981, "suppressed": ["gitdb.util"], "version_id": "1.15.0"}