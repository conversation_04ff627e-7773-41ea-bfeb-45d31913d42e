{".class": "MypyFile", "_fullname": "narwhals.exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "ColumnNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["narwhals.exceptions.FormattedKeyError", "narwhals.exceptions.NarwhalsError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.ColumnNotFoundError", "name": "ColumnNotFoundError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.ColumnNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.ColumnNotFoundError", "narwhals.exceptions.FormattedKeyError", "builtins.KeyError", "builtins.LookupError", "narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.ColumnNotFoundError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["narwhals.exceptions.ColumnNotFoundError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnNotFoundError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_available_column_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "available_columns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "narwhals.exceptions.ColumnNotFoundError.from_available_column_names", "name": "from_available_column_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "available_columns"], "arg_types": [{".class": "TypeType", "item": "narwhals.exceptions.ColumnNotFoundError"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_available_column_names of ColumnNotFoundError", "ret_type": "narwhals.exceptions.ColumnNotFoundError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals.exceptions.ColumnNotFoundError.from_available_column_names", "name": "from_available_column_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "available_columns"], "arg_types": [{".class": "TypeType", "item": "narwhals.exceptions.ColumnNotFoundError"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_available_column_names of ColumnNotFoundError", "ret_type": "narwhals.exceptions.ColumnNotFoundError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_missing_and_available_column_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "narwhals.exceptions.ColumnNotFoundError.from_missing_and_available_column_names", "name": "from_missing_and_available_column_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeType", "item": "narwhals.exceptions.ColumnNotFoundError"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_missing_and_available_column_names of ColumnNotFoundError", "ret_type": "narwhals.exceptions.ColumnNotFoundError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals.exceptions.ColumnNotFoundError.from_missing_and_available_column_names", "name": "from_missing_and_available_column_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeType", "item": "narwhals.exceptions.ColumnNotFoundError"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_missing_and_available_column_names of ColumnNotFoundError", "ret_type": "narwhals.exceptions.ColumnNotFoundError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.ColumnNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.ColumnNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComputeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["narwhals.exceptions.NarwhalsError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.ComputeError", "name": "ComputeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.ComputeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.ComputeError", "narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.ComputeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.ComputeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["narwhals.exceptions.NarwhalsError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.DuplicateError", "name": "Du<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.DuplicateError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.DuplicateError", "narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.DuplicateError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.DuplicateError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FormattedKeyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.KeyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.FormattedKeyError", "name": "FormattedKeyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.FormattedKeyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.FormattedKeyError", "builtins.KeyError", "builtins.LookupError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.FormattedKeyError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["narwhals.exceptions.FormattedKeyError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FormattedKeyError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.FormattedKeyError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["narwhals.exceptions.FormattedKeyError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of FormattedKeyError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "narwhals.exceptions.FormattedKeyError.message", "name": "message", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.FormattedKeyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.FormattedKeyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidIntoExprError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.TypeError", "narwhals.exceptions.NarwhalsError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.InvalidIntoExprError", "name": "InvalidIntoExprError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.InvalidIntoExprError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.InvalidIntoExprError", "builtins.TypeError", "narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.InvalidIntoExprError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["narwhals.exceptions.InvalidIntoExprError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidIntoExprError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_invalid_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "invalid_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "narwhals.exceptions.InvalidIntoExprError.from_invalid_type", "name": "from_invalid_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "invalid_type"], "arg_types": ["builtins.type", "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_invalid_type of InvalidIntoExprError", "ret_type": "narwhals.exceptions.InvalidIntoExprError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals.exceptions.InvalidIntoExprError.from_invalid_type", "name": "from_invalid_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "invalid_type"], "arg_types": ["builtins.type", "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_invalid_type of InvalidIntoExprError", "ret_type": "narwhals.exceptions.InvalidIntoExprError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "narwhals.exceptions.InvalidIntoExprError.message", "name": "message", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.InvalidIntoExprError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.InvalidIntoExprError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidOperationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["narwhals.exceptions.NarwhalsError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.InvalidOperationError", "name": "InvalidOperationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.InvalidOperationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.InvalidOperationError", "narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.InvalidOperationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.InvalidOperationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "MultiOutputExpressionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["narwhals.exceptions.NarwhalsError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.MultiOutputExpressionError", "name": "MultiOutputExpressionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.MultiOutputExpressionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.MultiOutputExpressionError", "narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.MultiOutputExpressionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.MultiOutputExpressionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NarwhalsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.NarwhalsError", "name": "NarwhalsError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.NarwhalsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.NarwhalsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.NarwhalsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NarwhalsUnstableWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.UserWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.NarwhalsUnstableWarning", "name": "NarwhalsUnstableWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.NarwhalsUnstableWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.NarwhalsUnstableWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.NarwhalsUnstableWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.NarwhalsUnstableWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PerformanceWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.PerformanceWarning", "name": "PerformanceWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.PerformanceWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.PerformanceWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.PerformanceWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.PerformanceWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShapeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["narwhals.exceptions.NarwhalsError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.ShapeError", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.ShapeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.ShapeError", "narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.ShapeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.ShapeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "UnsupportedDTypeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["narwhals.exceptions.NarwhalsError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals.exceptions.UnsupportedDTypeError", "name": "UnsupportedDTypeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals.exceptions.UnsupportedDTypeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals.exceptions", "mro": ["narwhals.exceptions.UnsupportedDTypeError", "narwhals.exceptions.NarwhalsError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals.exceptions.UnsupportedDTypeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals.exceptions.UnsupportedDTypeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals.exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\narwhals\\exceptions.py"}