{"data_mtime": 1755649448, "dep_lines": [14, 17, 22, 13, 15, 16, 41, 13, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 25, 20, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["torch._export.serde.serialize", "torch.export.pt2_archive._package_weights", "torch.export.pt2_archive.constants", "torch.utils._pytree", "torch.export._tree_utils", "torch.export.exported_program", "torch.utils._ordered_set", "torch.utils", "torch.types", "glob", "io", "json", "logging", "os", "tempfile", "zipfile", "dataclasses", "typing", "typing_extensions", "torch", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._<PERSON><PERSON>_aoti", "torch._tensor"], "hash": "f328edb43cec9d3e709441df85f8f81c3285ae55", "id": "torch.export.pt2_archive._package", "ignore_all": true, "interface_hash": "52b2e08db26ed1ef9c40ff7cca75fcd977fbd238", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\export\\pt2_archive\\_package.py", "plugin_data": null, "size": 25142, "suppressed": [], "version_id": "1.15.0"}