{"data_mtime": 1755649449, "dep_lines": [15, 15, 16, 12, 18, 19, 7, 12, 13, 3, 4, 5, 6, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.nn.jit.instantiator", "torch.distributed.nn.jit", "torch.distributed.rpc.internal", "torch.distributed.rpc", "torch.nn.parameter", "torch.utils.hooks", "collections.abc", "torch.distributed", "torch.nn", "collections", "io", "sys", "types", "typing", "typing_extensions", "torch", "builtins", "os", "inspect", "html", "string", "operator", "pprint", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._C._distributed_rpc", "torch._jit_internal", "torch._tensor", "torch.distributed.remote_device", "torch.distributed.rpc.api", "torch.jit", "torch.jit._script", "torch.nn.modules", "torch.nn.modules.module", "torch.utils"], "hash": "aef053836936c04cc8eb8da84b7cc02bcfe76124", "id": "torch.distributed.nn.api.remote_module", "ignore_all": true, "interface_hash": "1cf69ba20454b74d642b6ee44b63268eeff215a9", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\nn\\api\\remote_module.py", "plugin_data": null, "size": 32040, "suppressed": [], "version_id": "1.15.0"}