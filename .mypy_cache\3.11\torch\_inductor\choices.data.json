{".class": "MypyFile", "_fullname": "torch._inductor.choices", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseConfigHeuristic": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.template_heuristics.BaseConfigHeuristic", "kind": "Gdef"}, "BaseSchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseSchedulerNode", "kind": "Gdef"}, "CPUConfigHeuristic": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.template_heuristics.CPUConfigHeuristic", "kind": "Gdef"}, "CUDAConfigHeuristic": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.template_heuristics.CUDAConfigHeuristic", "kind": "Gdef"}, "DeviceProperties": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.hints.DeviceProperties", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "InductorChoices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.choices.InductorChoices", "name": "InductorChoices", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.choices", "mro": ["torch._inductor.choices.InductorChoices", "builtins.object"], "names": {".class": "SymbolTable", "can_fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.choices.InductorChoices.can_fuse", "name": "can_fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "arg_types": ["torch._inductor.scheduler.Scheduler", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_fuse of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.InductorChoices.can_fuse", "name": "can_fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "arg_types": ["torch._inductor.scheduler.Scheduler", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_fuse of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "can_fuse_horizontal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.choices.InductorChoices.can_fuse_horizontal", "name": "can_fuse_horizontal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "arg_types": ["torch._inductor.scheduler.Scheduler", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_fuse_horizontal of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.InductorChoices.can_fuse_horizontal", "name": "can_fuse_horizontal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "arg_types": ["torch._inductor.scheduler.Scheduler", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_fuse_horizontal of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "can_fuse_vertical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.choices.InductorChoices.can_fuse_vertical", "name": "can_fuse_vertical", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "arg_types": ["torch._inductor.scheduler.Scheduler", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_fuse_vertical of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.InductorChoices.can_fuse_vertical", "name": "can_fuse_vertical", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["scheduler", "node1", "node2", "shared_data_score"], "arg_types": ["torch._inductor.scheduler.Scheduler", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_fuse_vertical of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_base_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_base_mm_configs", "name": "get_base_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_base_mm_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_config_heuristics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_config_heuristics", "name": "get_config_heuristics", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_config_heuristics of InductorChoices", "ret_type": "torch._inductor.template_heuristics.BaseConfigHeuristic", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_conv_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_conv_configs", "name": "get_conv_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_conv_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_extra_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_extra_mm_configs", "name": "get_extra_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_extra_mm_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_attention_bwd_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "head_dim", "dtype", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_flex_attention_bwd_configs", "name": "get_flex_attention_bwd_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "head_dim", "dtype", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", "builtins.int", "torch._C.dtype", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_attention_bwd_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_attention_fwd_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "head_dim", "dtype", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_flex_attention_fwd_configs", "name": "get_flex_attention_fwd_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "head_dim", "dtype", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", "builtins.int", "torch._C.dtype", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_attention_fwd_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_decode_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "head_dim", "dtype", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_flex_decode_configs", "name": "get_flex_decode_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "head_dim", "dtype", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", "builtins.int", "torch._C.dtype", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_decode_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_int8_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_int8_mm_configs", "name": "get_int8_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_int8_mm_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mixed_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_mixed_mm_configs", "name": "get_mixed_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_mm_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mm_plus_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_mm_plus_mm_configs", "name": "get_mm_plus_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mm_plus_mm_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_persistent_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_persistent_mm_configs", "name": "get_persistent_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_persistent_mm_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scaled_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_scaled_mm_configs", "name": "get_scaled_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scaled_mm_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scaled_persistent_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.get_scaled_persistent_mm_configs", "name": "get_scaled_persistent_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device_type"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scaled_persistent_mm_configs of InductorChoices", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reduction_split_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["device", "reduction_numel_hint", "numel_hint", "inner_reduction"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.choices.InductorChoices.reduction_split_factor", "name": "reduction_split_factor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["device", "reduction_numel_hint", "numel_hint", "inner_reduction"], "arg_types": ["torch._C.device", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduction_split_factor of InductorChoices", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.InductorChoices.reduction_split_factor", "name": "reduction_split_factor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["device", "reduction_numel_hint", "numel_hint", "inner_reduction"], "arg_types": ["torch._C.device", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduction_split_factor of InductorChoices", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "score_fusion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["scheduler", "node1", "node2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.choices.InductorChoices.score_fusion", "name": "score_fusion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["scheduler", "node1", "node2"], "arg_types": ["torch._inductor.scheduler.Scheduler", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "score_fusion of InductorChoices", "ret_type": "torch._inductor.choices.Sortable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.InductorChoices.score_fusion", "name": "score_fusion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["scheduler", "node1", "node2"], "arg_types": ["torch._inductor.scheduler.Scheduler", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "score_fusion of InductorChoices", "ret_type": "torch._inductor.choices.Sortable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "should_use_cooperative_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["features"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.choices.InductorChoices.should_use_cooperative_reduction", "name": "should_use_cooperative_reduction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["features"], "arg_types": ["torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_use_cooperative_reduction of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.InductorChoices.should_use_cooperative_reduction", "name": "should_use_cooperative_reduction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["features"], "arg_types": ["torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_use_cooperative_reduction of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "should_use_persistent_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["features", "cooperative_reduction"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.choices.InductorChoices.should_use_persistent_reduction", "name": "should_use_persistent_reduction", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["features", "cooperative_reduction"], "arg_types": ["torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_use_persistent_reduction of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.InductorChoices.should_use_persistent_reduction", "name": "should_use_persistent_reduction", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["features", "cooperative_reduction"], "arg_types": ["torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_use_persistent_reduction of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "triton_kernel_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kernel_cls", "features", "groups", "kernel_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.choices.InductorChoices.triton_kernel_kwargs", "name": "triton_kernel_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kernel_cls", "features", "groups", "kernel_kwargs"], "arg_types": ["torch._inductor.choices.InductorChoices", {".class": "TypeType", "item": "torch._inductor.codegen.triton.TritonKernel"}, "torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.choices.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "triton_kernel_kwargs of InductorChoices", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "want_no_x_dim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["features"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.choices.InductorChoices.want_no_x_dim", "name": "want_no_x_dim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["features"], "arg_types": ["torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "want_no_x_dim of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.InductorChoices.want_no_x_dim", "name": "want_no_x_dim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["features"], "arg_types": ["torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "want_no_x_dim of InductorChoices", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.choices.InductorChoices.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.choices.InductorChoices", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "ROCmConfigHeuristic": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.template_heuristics.ROCmConfigHeuristic", "kind": "Gdef"}, "ReductionHint": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.hints.ReductionHint", "kind": "Gdef"}, "SIMDKernelFeatures": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.simd_kernel_features.SIMDKernelFeatures", "kind": "Gdef"}, "Scheduler": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.Scheduler", "kind": "Gdef"}, "Sortable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__lt__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.choices.Sortable", "name": "Sortable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "torch._inductor.choices.Sortable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._inductor.choices", "mro": ["torch._inductor.choices.Sortable", "builtins.object"], "names": {".class": "SymbolTable", "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "torch._inductor.choices.Sortable.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.choices.Sortable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.choices.Sortable", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.choices.Sortable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.choices.Sortable", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of Sortable", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.choices.Sortable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.choices.Sortable", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.choices.Sortable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.choices.Sortable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TritonConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.TritonConfig", "name": "TritonConfig", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.choices.TritonConfig", "source_any": null, "type_of_any": 3}}}, "TritonKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.triton.TritonKernel", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "WhyNoFuse": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.WhyNoFuse", "kind": "Gdef"}, "XPUConfigHeuristic": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.template_heuristics.XPUConfigHeuristic", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.choices.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.choices.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.choices.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.choices.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.choices.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.choices.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "get_metric_table": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.metrics.get_metric_table", "kind": "Gdef"}, "is_metric_table_enabled": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.metrics.is_metric_table_enabled", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.choices.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.choices.sympy", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "write_text": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.write_text", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\choices.py"}