{"data_mtime": 1755649448, "dep_lines": [57, 58, 59, 60, 69, 81, 83, 84, 85, 92, 118, 55, 82, 83, 89, 91, 93, 102, 118, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 55, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 25, 10, 10, 5, 20, 25, 25, 25, 5, 20, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch._dynamo.callback", "torch._dynamo.mutation_guard", "torch._dynamo.utils", "torch._inductor.compile_fx", "torch._inductor.cudagraph_utils", "torch.multiprocessing.reductions", "torch.utils._pytree", "torch.utils._ordered_set", "torch.utils.weak", "torch._inductor.utils", "torch._inductor.config", "torch.fx", "torch.storage", "torch.utils", "collections.abc", "torch._guards", "torch.types", "torch._C", "torch._inductor", "__future__", "contextlib", "dataclasses", "functools", "gc", "itertools", "operator", "sys", "threading", "traceback", "warnings", "weakref", "collections", "enum", "typing", "torch", "builtins", "os", "inspect", "html", "string", "pprint", "types", "math", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "copy", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "abc", "logging", "torch._dynamo", "torch._dynamo.config", "torch._environment", "torch._inductor.output_code", "torch._logging", "torch._logging._internal", "torch._tensor", "torch._utils_internal", "torch.backends", "torch.backends.cuda", "torch.cuda", "torch.cuda.graphs", "torch.cuda.streams", "torch.multiprocessing", "typing_extensions"], "hash": "6744e65b28e272eaf79099a170cb1bf1b3f9d27c", "id": "torch._inductor.cudagraph_trees", "ignore_all": true, "interface_hash": "37c1b3af339c390ecc1c22c1d7d57a8d0328504d", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\cudagraph_trees.py", "plugin_data": null, "size": 106129, "suppressed": [], "version_id": "1.15.0"}