{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cpp_micro_gemm", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CppMicroBrgemm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm", "name": "CppMicroBrgemm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.object"], "names": {".class": "SymbolTable", "TEMPLATE_ENTRY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm.TEMPLATE_ENTRY", "name": "TEMPLATE_ENTRY", "type": "builtins.str"}}, "codegen_define": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm.codegen_define", "name": "codegen_define", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_define of CppMicroBrgemm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm.codegen_finalize", "name": "codegen_finalize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_finalize of CppMicroBrgemm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_b_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm.get_b_layout", "name": "get_b_layout", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroGemm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "name": "CppMicroGemm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.object"], "names": {".class": "SymbolTable", "ALLOCATE_WEIGHT_BUFFER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.ALLOCATE_WEIGHT_BUFFER", "name": "ALLOCATE_WEIGHT_BUFFER", "type": "builtins.str"}}, "DECLARE_KERNEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.DECLARE_KERNEL", "name": "DECLARE_KERNEL", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "register_blocking", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "register_blocking", "alpha"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CppMicroGemm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.alpha", "name": "alpha", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "codegen_allocate_weight_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "buffer_name", "buffer_dtype", "size_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.codegen_allocate_weight_buffer", "name": "codegen_allocate_weight_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "buffer_name", "buffer_dtype", "size_args"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_allocate_weight_buffer of CppMicroGemm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "kernel", "A", "B", "C", "accum", "prefetch", "kwargs_for_extra_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.codegen_call", "name": "codegen_call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "kernel", "A", "B", "C", "accum", "prefetch", "kwargs_for_extra_args"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_call of CppMicroGemm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_define": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.codegen_define", "name": "codegen_define", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_define of CppMicroGemm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.codegen_finalize", "name": "codegen_finalize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_finalize of CppMicroGemm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.codegen_init", "name": "codegen_init", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_init of CppMicroGemm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.compute_dtype", "name": "compute_dtype", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_b_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.get_b_layout", "name": "get_b_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_b_layout of CppMicroGemm", "ret_type": "torch._inductor.codegen.cpp_micro_gemm.LayoutType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_common_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.get_common_options", "name": "get_common_options", "type": null}}, "get_kernel_declaration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.get_kernel_declaration", "name": "get_kernel_declaration", "type": null}}, "get_kernel_extra_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.get_kernel_extra_args", "name": "get_kernel_extra_args", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kernel_extra_args of CppMicroGemm", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_kernel_extra_args_declare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.get_kernel_extra_args_declare", "name": "get_kernel_extra_args_declare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kernel_extra_args_declare of CppMicroGemm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input2_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.input2_dtype", "name": "input2_dtype", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "input_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.input_dtype", "name": "input_dtype", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_woq_int4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.is_woq_int4", "name": "is_woq_int4", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.output_dtype", "name": "output_dtype", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pack_vnni_B_locally": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.pack_vnni_B_locally", "name": "pack_vnni_B_locally", "type": "builtins.bool"}}, "register_blocking": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.register_blocking", "name": "register_blocking", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "use_local_vnni_blocking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "should_block_weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.use_local_vnni_blocking", "name": "use_local_vnni_blocking", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "should_block_weight"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "use_local_vnni_blocking of CppMicroGemm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroGemmAMX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "name": "CppMicroGemmAMX", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.object"], "names": {".class": "SymbolTable", "TEMPLATE_ENTRY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.TEMPLATE_ENTRY", "name": "TEMPLATE_ENTRY", "type": "builtins.str"}}, "TEMPLATE_KERNEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.TEMPLATE_KERNEL", "name": "TEMPLATE_KERNEL", "type": "builtins.str"}}, "codegen_define": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.codegen_define", "name": "codegen_define", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_define of CppMicroGemmAMX", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.codegen_finalize", "name": "codegen_finalize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_finalize of CppMicroGemmAMX", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.codegen_init", "name": "codegen_init", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_init of CppMicroGemmAMX", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_b_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.get_b_layout", "name": "get_b_layout", "type": null}}, "get_kernel_extra_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.get_kernel_extra_args", "name": "get_kernel_extra_args", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kernel_extra_args of CppMicroGemmAMX", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_kernel_extra_args_declare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.get_kernel_extra_args_declare", "name": "get_kernel_extra_args_declare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kernel_extra_args_declare of CppMicroGemmAMX", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroGemmConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig", "name": "CppMicroGemmConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 228, "name": "input_dtype", "type": "torch._C.dtype"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 229, "name": "input2_dtype", "type": "torch._C.dtype"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 230, "name": "output_dtype", "type": "torch._C.dtype"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 231, "name": "compute_dtype", "type": "torch._C.dtype"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 232, "name": "vec_isa_cls", "type": {".class": "TypeType", "item": "torch._inductor.cpu_vec_isa.VecISA"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 233, "name": "register_blocking", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 234, "name": "extra_check", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "vec_isa_cls", "register_blocking", "extra_check"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "vec_isa_cls", "register_blocking", "extra_check"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig", "torch._C.dtype", "torch._C.dtype", "torch._C.dtype", "torch._C.dtype", {".class": "TypeType", "item": "torch._inductor.cpu_vec_isa.VecISA"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CppMicroGemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "input_dtype"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "input2_dtype"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "output_dtype"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "compute_dtype"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vec_isa_cls"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "register_blocking"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "extra_check"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "vec_isa_cls", "register_blocking", "extra_check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "vec_isa_cls", "register_blocking", "extra_check"], "arg_types": ["torch._C.dtype", "torch._C.dtype", "torch._C.dtype", "torch._C.dtype", {".class": "TypeType", "item": "torch._inductor.cpu_vec_isa.VecISA"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CppMicroGemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "vec_isa_cls", "register_blocking", "extra_check"], "arg_types": ["torch._C.dtype", "torch._C.dtype", "torch._C.dtype", "torch._C.dtype", {".class": "TypeType", "item": "torch._inductor.cpu_vec_isa.VecISA"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CppMicroGemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "compute_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.compute_dtype", "name": "compute_dtype", "type": "torch._C.dtype"}}, "extra_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.extra_check", "name": "extra_check", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "input2_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.input2_dtype", "name": "input2_dtype", "type": "torch._C.dtype"}}, "input_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.input_dtype", "name": "input_dtype", "type": "torch._C.dtype"}}, "output_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.output_dtype", "name": "output_dtype", "type": "torch._C.dtype"}}, "register_blocking": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.register_blocking", "name": "register_blocking", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}}}, "vec_isa_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.vec_isa_cls", "name": "vec_isa_cls", "type": {".class": "TypeType", "item": "torch._inductor.cpu_vec_isa.VecISA"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroGemmFP32Vec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec", "name": "CppMicroGemmFP32Vec", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.object"], "names": {".class": "SymbolTable", "TEMPLATE_ENTRY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec.TEMPLATE_ENTRY", "name": "TEMPLATE_ENTRY", "type": "builtins.str"}}, "TEMPLATE_KERNEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec.TEMPLATE_KERNEL", "name": "TEMPLATE_KERNEL", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "register_blocking", "alpha", "tail_n", "trans_b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "register_blocking", "alpha", "tail_n", "trans_b"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CppMicroGemmFP32Vec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_define": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec.codegen_define", "name": "codegen_define", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_define of CppMicroGemmFP32Vec", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tail_n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec.tail_n", "name": "tail_n", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "trans_b": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec.trans_b", "name": "trans_b", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroGemmRef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef", "name": "CppMicroGemmRef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.object"], "names": {".class": "SymbolTable", "TEMPLATE_ENTRY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef.TEMPLATE_ENTRY", "name": "TEMPLATE_ENTRY", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "alpha"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CppMicroGemmRef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_define": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef.codegen_define", "name": "codegen_define", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernel"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_define of CppMicroGemmRef", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmRef", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroGemmWoQInt4Amx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx", "name": "CppMicroGemmWoQInt4Amx", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.object"], "names": {".class": "SymbolTable", "TEMPLATE_ENTRY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx.TEMPLATE_ENTRY", "name": "TEMPLATE_ENTRY", "type": "builtins.str"}}, "get_kernel_extra_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx.get_kernel_extra_args", "name": "get_kernel_extra_args", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kernel_extra_args of CppMicroGemmWoQInt4Amx", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_kernel_extra_args_declare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx.get_kernel_extra_args_declare", "name": "get_kernel_extra_args_declare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kernel_extra_args_declare of CppMicroGemmWoQInt4Amx", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_woq_int4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx.is_woq_int4", "name": "is_woq_int4", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Amx", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroGemmWoQInt4Avx512": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512", "name": "CppMicroGemmWoQInt4Avx512", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.object"], "names": {".class": "SymbolTable", "TEMPLATE_ENTRY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512.TEMPLATE_ENTRY", "name": "TEMPLATE_ENTRY", "type": "builtins.str"}}, "TEMPLATE_KERNEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512.TEMPLATE_KERNEL", "name": "TEMPLATE_KERNEL", "type": "builtins.str"}}, "get_kernel_extra_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512.get_kernel_extra_args", "name": "get_kernel_extra_args", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kernel_extra_args of CppMicroGemmWoQInt4Avx512", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_kernel_extra_args_declare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512.get_kernel_extra_args_declare", "name": "get_kernel_extra_args_declare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_kernel_extra_args_declare of CppMicroGemmWoQInt4Avx512", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_woq_int4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512.is_woq_int4", "name": "is_woq_int4", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmWoQInt4Avx512", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppTemplateKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel", "kind": "Gdef"}, "DTYPE_TO_CPP": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.DTYPE_TO_CPP", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "GemmBlocking": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking", "kind": "Gdef"}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "KernelTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.KernelTemplate", "kind": "Gdef"}, "LayoutType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_micro_gemm.LayoutType", "name": "LayoutType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.LayoutType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch._inductor.codegen.cpp_micro_gemm", "mro": ["torch._inductor.codegen.cpp_micro_gemm.LayoutType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NORMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.LayoutType.NORMAL", "name": "NORMAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "VNNI2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.LayoutType.VNNI2", "name": "VNNI2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "VNNI4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.LayoutType.VNNI4", "name": "VNNI4", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_micro_gemm.LayoutType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_micro_gemm.LayoutType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "VecAMX": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpu_vec_isa.VecAMX", "kind": "Gdef"}, "VecAVX2": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpu_vec_isa.VecAVX2", "kind": "Gdef"}, "VecAVX512": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpu_vec_isa.VecAVX512", "kind": "Gdef"}, "VecISA": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpu_vec_isa.VecISA", "kind": "Gdef"}, "VecNEON": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpu_vec_isa.VecNEON", "kind": "Gdef"}, "VecSVE256": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpu_vec_isa.VecSVE256", "kind": "Gdef"}, "_IS_WINDOWS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm._IS_WINDOWS", "name": "_IS_WINDOWS", "type": "builtins.bool"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "check_amx_extra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["config", "m", "n", "k", "alpha", "num_threads", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.check_amx_extra", "name": "check_amx_extra", "type": null}}, "check_brgemm_extra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["config", "m", "n", "k", "alpha", "num_threads", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.check_brgemm_extra", "name": "check_brgemm_extra", "type": null}}, "check_int8_woq_small_m_dim": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["config", "m", "n", "k", "alpha", "num_threads", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.check_int8_woq_small_m_dim", "name": "check_int8_woq_small_m_dim", "type": null}}, "check_woq_int4_extra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["config", "m", "n", "k", "alpha", "num_threads", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.check_woq_int4_extra", "name": "check_woq_int4_extra", "type": null}}, "cpp_builder": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpp_builder", "kind": "Gdef"}, "create_micro_gemm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["name", "m", "n", "k", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "alpha", "num_threads", "use_ref", "q_group_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.create_micro_gemm", "name": "create_micro_gemm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["name", "m", "n", "k", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "alpha", "num_threads", "use_ref", "q_group_size"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_micro_gemm", "ret_type": {".class": "UnionType", "items": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "do_not_use_with_small_m_for_int8_woq": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["config", "m", "n", "k", "alpha", "num_threads", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.do_not_use_with_small_m_for_int8_woq", "name": "do_not_use_with_small_m_for_int8_woq", "type": null}}, "generate_gemm_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["vec_isa_cls", "register_blockings", "input_dtype", "input2_dtype", "output_dtype", "compute_dtype", "extra_check"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.generate_gemm_config", "name": "generate_gemm_config", "type": null}}, "get_restrict_keyword": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.get_restrict_keyword", "name": "get_restrict_keyword", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_restrict_keyword", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "is_int8_woq_gemm_small_m_dim_corner_case": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["config", "m", "n", "k"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.is_int8_woq_gemm_small_m_dim_corner_case", "name": "is_int8_woq_gemm_small_m_dim_corner_case", "type": null}}, "micro_gemm_configs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_micro_gemm.micro_gemm_configs", "name": "micro_gemm_configs", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm"}, {".class": "Instance", "args": ["torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmConfig"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "parallel_num_threads": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.parallel_num_threads", "kind": "Gdef"}, "pick_vec_isa": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpu_vec_isa.pick_vec_isa", "kind": "Gdef"}, "register_micro_gemm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["configs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_micro_gemm.register_micro_gemm", "name": "register_micro_gemm", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "value_to_cpp": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.value_to_cpp", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_micro_gemm.py"}