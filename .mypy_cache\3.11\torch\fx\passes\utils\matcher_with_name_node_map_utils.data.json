{".class": "MypyFile", "_fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Graph": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph.Graph", "kind": "Gdef", "module_public": false}, "GraphModule": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph_module.GraphModule", "kind": "Gdef", "module_public": false}, "InternalMatch": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.utils.matcher_utils.InternalMatch", "kind": "Gdef", "module_public": false}, "Node": {".class": "SymbolTableNode", "cross_ref": "torch.fx.node.Node", "kind": "Gdef", "module_public": false}, "SubgraphMatcher": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.utils.matcher_utils.SubgraphMatcher", "kind": "Gdef", "module_public": false}, "SubgraphMatcherWithNameNodeMap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.fx.passes.utils.matcher_utils.SubgraphMatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap", "name": "SubgraphMatcherWithNameNodeMap", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.fx.passes.utils.matcher_with_name_node_map_utils", "mro": ["torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap", "torch.fx.passes.utils.matcher_utils.SubgraphMatcher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "pattern_gm", "match_output", "match_placeholder", "remove_overlapping_matches", "ignore_literals"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "pattern_gm", "match_output", "match_placeholder", "remove_overlapping_matches", "ignore_literals"], "arg_types": ["torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap", "torch.fx.graph_module.GraphModule", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SubgraphMatcherWithNameNodeMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "graph"], "arg_types": ["torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap", "torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of SubgraphMatcherWithNameNodeMap", "ret_type": {".class": "Instance", "args": ["torch.fx.passes.utils.matcher_utils.InternalMatch"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name_node_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap.name_node_map", "name": "name_node_map", "type": {".class": "Instance", "args": ["builtins.str", "torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.fx.passes.utils.matcher_with_name_node_map_utils.SubgraphMatcherWithNameNodeMap", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_split_to_graph_and_name_node_map": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.fx.passes.utils.matcher_with_name_node_map_utils._split_to_graph_and_name_node_map", "name": "_split_to_graph_and_name_node_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_to_graph_and_name_node_map", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["builtins.str", "torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compatibility": {".class": "SymbolTableNode", "cross_ref": "torch.fx._compatibility.compatibility", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\fx\\passes\\utils\\matcher_with_name_node_map_utils.py"}