{".class": "MypyFile", "_fullname": "torch.distributed.elastic.agent.server", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ElasticAgent": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.ElasticAgent", "kind": "Gdef"}, "RunResult": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.RunResult", "kind": "Gdef"}, "SimpleElasticAgent": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.SimpleElasticAgent", "kind": "Gdef"}, "TORCHELASTIC_ENABLE_FILE_TIMER": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.local_elastic_agent.TORCHELASTIC_ENABLE_FILE_TIMER", "kind": "Gdef"}, "TORCHELASTIC_TIMER_FILE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.local_elastic_agent.TORCHELASTIC_TIMER_FILE", "kind": "Gdef"}, "Worker": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.Worker", "kind": "Gdef"}, "WorkerGroup": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.WorkerGroup", "kind": "Gdef"}, "WorkerSpec": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.WorkerSpec", "kind": "Gdef"}, "WorkerState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.WorkerState", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\elastic\\agent\\server\\__init__.py"}