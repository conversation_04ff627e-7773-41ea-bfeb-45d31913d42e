{".class": "MypyFile", "_fullname": "torch.export._trace", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ATenExportArtifact": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.export._trace.ATenExportArtifact", "name": "ATenExportArtifact", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export._trace.ATenExportArtifact", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 140, "name": "gm", "type": "torch.fx.graph_module.GraphModule"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 141, "name": "sig", "type": "torch.export.graph_signature.ExportGraphSignature"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 142, "name": "constants", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.export._trace", "mro": ["torch.export._trace.ATenExportArtifact", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.export._trace.ATenExportArtifact.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gm", "sig", "constants"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace.ATenExportArtifact.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gm", "sig", "constants"], "arg_types": ["torch.export._trace.ATenExportArtifact", "torch.fx.graph_module.GraphModule", "torch.export.graph_signature.ExportGraphSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ATenExportArtifact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.export._trace.ATenExportArtifact.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gm"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sig"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constants"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["gm", "sig", "constants"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.export._trace.ATenExportArtifact.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["gm", "sig", "constants"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.export.graph_signature.ExportGraphSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ATenExportArtifact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.export._trace.ATenExportArtifact.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["gm", "sig", "constants"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.export.graph_signature.ExportGraphSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ATenExportArtifact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "constants": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.export._trace.ATenExportArtifact.constants", "name": "constants", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "gm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.export._trace.ATenExportArtifact.gm", "name": "gm", "type": "torch.fx.graph_module.GraphModule"}}, "sig": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.export._trace.ATenExportArtifact.sig", "name": "sig", "type": "torch.export.graph_signature.ExportGraphSignature"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export._trace.ATenExportArtifact.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export._trace.ATenExportArtifact", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutogradStateOpsFailSafeguard": {".class": "SymbolTableNode", "cross_ref": "torch.export._safeguard.AutogradStateOpsFailSafeguard", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CollectTracepointsPass": {".class": "SymbolTableNode", "cross_ref": "torch._export.passes.collect_tracepoints_pass.CollectTracepointsPass", "kind": "Gdef"}, "ConstantAttrMap": {".class": "SymbolTableNode", "cross_ref": "torch._export.passes.lift_constants_pass.ConstantAttrMap", "kind": "Gdef"}, "ConstraintViolationError": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.ConstraintViolationError", "kind": "Gdef"}, "DEFAULT_EXPORT_DYNAMO_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.export._trace.DEFAULT_EXPORT_DYNAMO_CONFIG", "name": "DEFAULT_EXPORT_DYNAMO_CONFIG", "type": "torch.export._trace.ExportDynamoConfig"}}, "ExportArtifact": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.export._trace.ExportArtifact", "name": "ExportArtifact", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export._trace.ExportArtifact", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 147, "name": "aten", "type": "torch.export._trace.ATenExportArtifact"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 148, "name": "in_spec", "type": "torch.utils._pytree.TreeSpec"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 149, "name": "out_spec", "type": "torch.utils._pytree.TreeSpec"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 150, "name": "fake_mode", "type": "torch._subclasses.fake_tensor.FakeTensorMode"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 151, "name": "module_call_specs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "torch.utils._pytree.TreeSpec"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch.export._trace", "mro": ["torch.export._trace.ExportArtifact", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.export._trace.ExportArtifact.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "aten", "in_spec", "out_spec", "fake_mode", "module_call_specs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace.ExportArtifact.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "aten", "in_spec", "out_spec", "fake_mode", "module_call_specs"], "arg_types": ["torch.export._trace.ExportArtifact", "torch.export._trace.ATenExportArtifact", "torch.utils._pytree.TreeSpec", "torch.utils._pytree.TreeSpec", "torch._subclasses.fake_tensor.FakeTensorMode", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "torch.utils._pytree.TreeSpec"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExportArtifact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.export._trace.ExportArtifact.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "aten"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_spec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out_spec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fake_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "module_call_specs"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["aten", "in_spec", "out_spec", "fake_mode", "module_call_specs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.export._trace.ExportArtifact.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["aten", "in_spec", "out_spec", "fake_mode", "module_call_specs"], "arg_types": ["torch.export._trace.ATenExportArtifact", "torch.utils._pytree.TreeSpec", "torch.utils._pytree.TreeSpec", "torch._subclasses.fake_tensor.FakeTensorMode", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "torch.utils._pytree.TreeSpec"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExportArtifact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.export._trace.ExportArtifact.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["aten", "in_spec", "out_spec", "fake_mode", "module_call_specs"], "arg_types": ["torch.export._trace.ATenExportArtifact", "torch.utils._pytree.TreeSpec", "torch.utils._pytree.TreeSpec", "torch._subclasses.fake_tensor.FakeTensorMode", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "torch.utils._pytree.TreeSpec"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExportArtifact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "aten": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.export._trace.ExportArtifact.aten", "name": "aten", "type": "torch.export._trace.ATenExportArtifact"}}, "fake_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.export._trace.ExportArtifact.fake_mode", "name": "fake_mode", "type": "torch._subclasses.fake_tensor.FakeTensorMode"}}, "in_spec": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.export._trace.ExportArtifact.in_spec", "name": "in_spec", "type": "torch.utils._pytree.TreeSpec"}}, "module_call_specs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.export._trace.ExportArtifact.module_call_specs", "name": "module_call_specs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "torch.utils._pytree.TreeSpec"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "out_spec": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.export._trace.ExportArtifact.out_spec", "name": "out_spec", "type": "torch.utils._pytree.TreeSpec"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export._trace.ExportArtifact.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export._trace.ExportArtifact", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExportDynamoConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.export._trace.ExportDynamoConfig", "name": "ExportDynamoConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export._trace.ExportDynamoConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 120, "name": "allow_rnn", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 121, "name": "reorderable_logging_functions", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 128, "name": "do_not_emit_runtime_asserts", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 129, "name": "specialize_int", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 130, "name": "specialize_float", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 131, "name": "assume_static_by_default", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 132, "name": "automatic_dynamic_shapes", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 133, "name": "capture_dynamic_output_shape_ops", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 134, "name": "capture_scalar_outputs", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 135, "name": "prefer_deferred_runtime_asserts_over_guards", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.export._trace", "mro": ["torch.export._trace.ExportDynamoConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.export._trace.ExportDynamoConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "allow_rnn", "reorderable_logging_functions", "do_not_emit_runtime_asserts", "specialize_int", "specialize_float", "assume_static_by_default", "automatic_dynamic_shapes", "capture_dynamic_output_shape_ops", "capture_scalar_outputs", "prefer_deferred_runtime_asserts_over_guards"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace.ExportDynamoConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "allow_rnn", "reorderable_logging_functions", "do_not_emit_runtime_asserts", "specialize_int", "specialize_float", "assume_static_by_default", "automatic_dynamic_shapes", "capture_dynamic_output_shape_ops", "capture_scalar_outputs", "prefer_deferred_runtime_asserts_over_guards"], "arg_types": ["torch.export._trace.ExportDynamoConfig", "builtins.bool", {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.set"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExportDynamoConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.export._trace.ExportDynamoConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "allow_rnn"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reorderable_logging_functions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "do_not_emit_runtime_asserts"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "specialize_int"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "specialize_float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assume_static_by_default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "automatic_dynamic_shapes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "capture_dynamic_output_shape_ops"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "capture_scalar_outputs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "prefer_deferred_runtime_asserts_over_guards"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["allow_rnn", "reorderable_logging_functions", "do_not_emit_runtime_asserts", "specialize_int", "specialize_float", "assume_static_by_default", "automatic_dynamic_shapes", "capture_dynamic_output_shape_ops", "capture_scalar_outputs", "prefer_deferred_runtime_asserts_over_guards"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.export._trace.ExportDynamoConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["allow_rnn", "reorderable_logging_functions", "do_not_emit_runtime_asserts", "specialize_int", "specialize_float", "assume_static_by_default", "automatic_dynamic_shapes", "capture_dynamic_output_shape_ops", "capture_scalar_outputs", "prefer_deferred_runtime_asserts_over_guards"], "arg_types": ["builtins.bool", {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.set"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExportDynamoConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.export._trace.ExportDynamoConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["allow_rnn", "reorderable_logging_functions", "do_not_emit_runtime_asserts", "specialize_int", "specialize_float", "assume_static_by_default", "automatic_dynamic_shapes", "capture_dynamic_output_shape_ops", "capture_scalar_outputs", "prefer_deferred_runtime_asserts_over_guards"], "arg_types": ["builtins.bool", {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.set"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExportDynamoConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "allow_rnn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.allow_rnn", "name": "allow_rnn", "type": "builtins.bool"}}, "assume_static_by_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.assume_static_by_default", "name": "assume_static_by_default", "type": "builtins.bool"}}, "automatic_dynamic_shapes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.automatic_dynamic_shapes", "name": "automatic_dynamic_shapes", "type": "builtins.bool"}}, "capture_dynamic_output_shape_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.capture_dynamic_output_shape_ops", "name": "capture_dynamic_output_shape_ops", "type": "builtins.bool"}}, "capture_scalar_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.capture_scalar_outputs", "name": "capture_scalar_outputs", "type": "builtins.bool"}}, "do_not_emit_runtime_asserts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.do_not_emit_runtime_asserts", "name": "do_not_emit_runtime_asserts", "type": "builtins.bool"}}, "prefer_deferred_runtime_asserts_over_guards": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.prefer_deferred_runtime_asserts_over_guards", "name": "prefer_deferred_runtime_asserts_over_guards", "type": "builtins.bool"}}, "reorderable_logging_functions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.reorderable_logging_functions", "name": "reorderable_logging_functions", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "specialize_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.specialize_float", "name": "specialize_float", "type": "builtins.bool"}}, "specialize_int": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.export._trace.ExportDynamoConfig.specialize_int", "name": "specialize_int", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export._trace.ExportDynamoConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export._trace.ExportDynamoConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExportGraphSignature": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature.ExportGraphSignature", "kind": "Gdef"}, "ExportedProgram": {".class": "SymbolTableNode", "cross_ref": "torch.export.exported_program.ExportedProgram", "kind": "Gdef"}, "FakeScriptObject": {".class": "SymbolTableNode", "cross_ref": "torch._library.fake_class_registry.FakeScriptObject", "kind": "Gdef"}, "FakeTensorMode": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor.FakeTensorMode", "kind": "Gdef"}, "GraphSignature": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.schemas.GraphSignature", "kind": "Gdef"}, "GuardOnDataDependentSymNode": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.GuardOnDataDependentSymNode", "kind": "Gdef"}, "InputKind": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature.InputKind", "kind": "Gdef"}, "ModuleCallEntry": {".class": "SymbolTableNode", "cross_ref": "torch.export.exported_program.ModuleCallEntry", "kind": "Gdef"}, "ModuleCallSignature": {".class": "SymbolTableNode", "cross_ref": "torch.export.exported_program.ModuleCallSignature", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OutputKind": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature.OutputKind", "kind": "Gdef"}, "PreDispatchTorchFunctionMode": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor.PreDispatchTorchFunctionMode", "kind": "Gdef"}, "ShapeEnv": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.ShapeEnv", "kind": "Gdef"}, "SpecViolationError": {".class": "SymbolTableNode", "cross_ref": "torch._export.verifier.SpecViolationError", "kind": "Gdef"}, "TracingContext": {".class": "SymbolTableNode", "cross_ref": "torch._guards.TracingContext", "kind": "Gdef"}, "TreeSpec": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree.TreeSpec", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UserError": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.exc.UserError", "kind": "Gdef"}, "UserErrorType": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.exc.UserErrorType", "kind": "Gdef"}, "ValueRangeError": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.value_ranges.ValueRangeError", "kind": "Gdef"}, "_ConstantAttributeType": {".class": "SymbolTableNode", "cross_ref": "torch.fx._symbolic_trace._ConstantAttributeType", "kind": "Gdef"}, "_DimHintType": {".class": "SymbolTableNode", "cross_ref": "torch.export.dynamic_shapes._DimHintType", "kind": "Gdef"}, "_EXPORT_FLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.export._trace._EXPORT_FLAGS", "name": "_EXPORT_FLAGS", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_EXPORT_MODULE_HIERARCHY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.export._trace._EXPORT_MODULE_HIERARCHY", "name": "_EXPORT_MODULE_HIERARCHY", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_IntWrapper": {".class": "SymbolTableNode", "cross_ref": "torch.export.dynamic_shapes._IntWrapper", "kind": "Gdef"}, "_NonStrictTorchFunctionHandler": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils._NonStrictTorchFunctionHandler", "kind": "Gdef"}, "_PyTreeCodeGen": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph._PyTreeCodeGen", "kind": "Gdef"}, "_PyTreeInfo": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph._PyTreeInfo", "kind": "Gdef"}, "_WrapperModule": {".class": "SymbolTableNode", "cross_ref": "torch.export._wrapper_utils._WrapperModule", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._trace.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._trace.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._trace.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._trace.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._trace.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export._trace.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_check_dynamic_shapes": {".class": "SymbolTableNode", "cross_ref": "torch.export.dynamic_shapes._check_dynamic_shapes", "kind": "Gdef"}, "_check_input_constraints_pre_hook": {".class": "SymbolTableNode", "cross_ref": "torch.export._unlift._check_input_constraints_pre_hook", "kind": "Gdef"}, "_collect_param_buffer_metadata": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils._collect_param_buffer_metadata", "kind": "Gdef"}, "_combine_args": {".class": "SymbolTableNode", "cross_ref": "torch.export.dynamic_shapes._combine_args", "kind": "Gdef"}, "_compiling_state_context": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils._compiling_state_context", "kind": "Gdef"}, "_convert_to_export_graph_signature": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature._convert_to_export_graph_signature", "kind": "Gdef"}, "_convert_to_positional_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["orig_arg_names", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._convert_to_positional_args", "name": "_convert_to_positional_args", "type": null}}, "_convert_ts_to_export_experimental": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["traced_callable", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._convert_ts_to_export_experimental", "name": "_convert_ts_to_export_experimental", "type": null}}, "_detect_attribute_assignment": {".class": "SymbolTableNode", "cross_ref": "torch._functorch.aot_autograd._detect_attribute_assignment", "kind": "Gdef"}, "_disable_custom_triton_op_functional_decomposition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.export._trace._disable_custom_triton_op_functional_decomposition", "name": "_disable_custom_triton_op_functional_decomposition", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.export._trace._disable_custom_triton_op_functional_decomposition", "name": "_disable_custom_triton_op_functional_decomposition", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_disable_custom_triton_op_functional_decomposition", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_disable_prexisiting_fake_mode": {".class": "SymbolTableNode", "cross_ref": "torch.export.exported_program._disable_prexisiting_fake_mode", "kind": "Gdef"}, "_export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes", "strict", "preserve_module_call_signature", "pre_dispatch", "allow_complex_guards_as_runtime_asserts", "_is_torch_jit_trace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.export._trace._export", "name": "_export", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes", "strict", "preserve_module_call_signature", "pre_dispatch", "allow_complex_guards_as_runtime_asserts", "_is_torch_jit_trace"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_export", "ret_type": "torch.export.exported_program.ExportedProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.export._trace._export", "name": "_export", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_export_for_training": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes", "strict", "preserve_module_call_signature"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.export._trace._export_for_training", "name": "_export_for_training", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes", "strict", "preserve_module_call_signature"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_export_for_training", "ret_type": "torch.export.exported_program.ExportedProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.export._trace._export_for_training", "name": "_export_for_training", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_export_to_aten_ir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["mod", "fake_args", "fake_kwargs", "fake_params_buffers", "constant_attrs", "produce_guards_callback", "transform", "pre_dispatch", "decomp_table", "_check_autograd_state", "_is_torch_jit_trace", "_prettify_placeholder_names", "decompose_custom_triton_ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._export_to_aten_ir", "name": "_export_to_aten_ir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["mod", "fake_args", "fake_kwargs", "fake_params_buffers", "constant_attrs", "produce_guards_callback", "transform", "pre_dispatch", "decomp_table", "_check_autograd_state", "_is_torch_jit_trace", "_prettify_placeholder_names", "decompose_custom_triton_ops"], "arg_types": ["torch.nn.modules.module.Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._export.passes.lift_constants_pass.ConstantAttrMap", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_export_to_aten_ir", "ret_type": "torch.export._trace.ATenExportArtifact", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_export_to_aten_ir_make_fx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["mod", "fake_args", "fake_kwargs", "fake_params_buffers", "constant_attrs", "produce_guards_callback", "transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._export_to_aten_ir_make_fx", "name": "_export_to_aten_ir_make_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["mod", "fake_args", "fake_kwargs", "fake_params_buffers", "constant_attrs", "produce_guards_callback", "transform"], "arg_types": ["torch.nn.modules.module.Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._export.passes.lift_constants_pass.ConstantAttrMap", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_export_to_aten_ir_make_fx", "ret_type": "torch.export._trace.ATenExportArtifact", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_export_to_torch_ir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["f", "args", "kwargs", "dynamic_shapes", "preserve_module_call_signature", "disable_constraint_solver", "allow_complex_guards_as_runtime_asserts", "restore_fqn", "_log_export_usage", "same_signature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._export_to_torch_ir", "name": "_export_to_torch_ir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["f", "args", "kwargs", "dynamic_shapes", "preserve_module_call_signature", "disable_constraint_solver", "allow_complex_guards_as_runtime_asserts", "restore_fqn", "_log_export_usage", "same_signature"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_export_to_torch_ir", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_fake_inputs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._extract_fake_inputs", "name": "_extract_fake_inputs", "type": null}}, "_fakify_module_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils._fakify_module_inputs", "kind": "Gdef"}, "_fakify_params_buffers": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils._fakify_params_buffers", "kind": "Gdef"}, "_fakify_script_objects": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils._fakify_script_objects", "kind": "Gdef"}, "_find_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._find_node", "name": "_find_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["gm", "name"], "arg_types": ["torch.fx.graph_module.GraphModule", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_node", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fixup_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._fixup_key", "name": "_fixup_key", "type": null}}, "_gather_constant_attrs": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils._gather_constant_attrs", "kind": "Gdef"}, "_get_forward_arg_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["mod", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._get_forward_arg_names", "name": "_get_forward_arg_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["mod", "args", "kwargs"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_forward_arg_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_inline_constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fake_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._get_inline_constraints", "name": "_get_inline_constraints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fake_mode"], "arg_types": ["torch._subclasses.fake_tensor.FakeTensorMode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_inline_constraints", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_module_call_graph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["export_artifact", "preserve_module_call_signature", "strict_mode_export", "forward_arg_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._get_module_call_graph", "name": "_get_module_call_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["export_artifact", "preserve_module_call_signature", "strict_mode_export", "forward_arg_names"], "arg_types": ["torch.export._trace.ExportArtifact", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_module_call_graph", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.export.exported_program.ModuleCallEntry"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_module_hierarchy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mod"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._get_module_hierarchy", "name": "_get_module_hierarchy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mod"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_module_hierarchy", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_non_persistent_buffers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mod"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._get_non_persistent_buffers", "name": "_get_non_persistent_buffers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mod"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_non_persistent_buffers", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_original_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mod"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._get_original_state_dict", "name": "_get_original_state_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mod"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_original_state_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_param_buffer_mapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["original_module", "traced_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._get_param_buffer_mapping", "name": "_get_param_buffer_mapping", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["original_module", "traced_module"], "arg_types": ["torch.nn.modules.module.Module", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_param_buffer_mapping", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_range_constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["mod", "export_artifact", "args", "kwargs", "dynamic_shapes", "_is_torch_jit_trace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._get_range_constraints", "name": "_get_range_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["mod", "export_artifact", "args", "kwargs", "dynamic_shapes", "_is_torch_jit_trace"], "arg_types": ["torch.nn.modules.module.Module", "torch.export._trace.ExportArtifact", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_range_constraints", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_graph_input_names": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.input_output_analysis._graph_input_names", "kind": "Gdef"}, "_graph_output_names": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.input_output_analysis._graph_output_names", "kind": "Gdef"}, "_ignore_backend_decomps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.export._trace._ignore_backend_decomps", "name": "_ignore_backend_decomps", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.export._trace._ignore_backend_decomps", "name": "_ignore_backend_decomps", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ignore_backend_decomps", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_log_export_wrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._log_export_wrapper", "name": "_log_export_wrapper", "type": null}}, "_make_module_call_graph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["in_spec", "out_spec", "module_call_signatures", "forward_arg_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._make_module_call_graph", "name": "_make_module_call_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["in_spec", "out_spec", "module_call_signatures", "forward_arg_names"], "arg_types": ["torch.utils._pytree.TreeSpec", "torch.utils._pytree.TreeSpec", {".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ModuleCallSignature"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_module_call_graph", "ret_type": {".class": "Instance", "args": ["torch.export.exported_program.ModuleCallEntry"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_materialize_and_lift_constants": {".class": "SymbolTableNode", "cross_ref": "torch._export.passes.lift_constants_pass._materialize_and_lift_constants", "kind": "Gdef"}, "_move_non_persistent_buffers_to_tensor_constants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["orig_mod", "graph_signature", "constants"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._move_non_persistent_buffers_to_tensor_constants", "name": "_move_non_persistent_buffers_to_tensor_constants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["orig_mod", "graph_signature", "constants"], "arg_types": ["torch.nn.modules.module.Module", "torch.export.graph_signature.ExportGraphSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_move_non_persistent_buffers_to_tensor_constants", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_non_strict_export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes", "preserve_module_call_signature", "orig_in_spec", "allow_complex_guards_as_runtime_asserts", "_is_torch_jit_trace", "_to_aten_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._non_strict_export", "name": "_non_strict_export", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes", "preserve_module_call_signature", "orig_in_spec", "allow_complex_guards_as_runtime_asserts", "_is_torch_jit_trace", "_to_aten_func"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "torch.utils._pytree.TreeSpec", "builtins.bool", "builtins.bool", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_non_strict_export", "ret_type": "torch.export._trace.ExportArtifact", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_nn_module_stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm_torch_level", "root_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._normalize_nn_module_stack", "name": "_normalize_nn_module_stack", "type": null}}, "_override_builtin_ops": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils._override_builtin_ops", "kind": "Gdef"}, "_populate_param_buffer_metadata_to_new_gm": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils._populate_param_buffer_metadata_to_new_gm", "kind": "Gdef"}, "_preserve_requires_grad_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["gm", "sig", "fake_params_buffers", "constants", "flat_fake_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._preserve_requires_grad_pass", "name": "_preserve_requires_grad_pass", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["gm", "sig", "fake_params_buffers", "constants", "flat_fake_args"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.export.graph_signature.ExportGraphSignature", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_preserve_requires_grad_pass", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_dynamic_shapes": {".class": "SymbolTableNode", "cross_ref": "torch.export.dynamic_shapes._process_dynamic_shapes", "kind": "Gdef"}, "_process_export_inputs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._process_export_inputs", "name": "_process_export_inputs", "type": null}}, "_process_jit_trace_inputs_for_export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["example_inputs", "example_kwarg_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._process_jit_trace_inputs_for_export", "name": "_process_jit_trace_inputs_for_export", "type": null}}, "_produce_aten_artifact": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3, 3, 3, 3, 3, 3, 5], "arg_names": ["gm", "mod", "constant_attrs", "graph_signature", "pre_dispatch", "fake_args", "fake_kwargs", "fake_params_buffers", "_prettify_placeholder_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._produce_aten_artifact", "name": "_produce_aten_artifact", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3, 3, 3, 3, 3, 5], "arg_names": ["gm", "mod", "constant_attrs", "graph_signature", "pre_dispatch", "fake_args", "fake_kwargs", "fake_params_buffers", "_prettify_placeholder_names"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_produce_aten_artifact", "ret_type": "torch.export._trace.ATenExportArtifact", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remap_constants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["orig_constant_attrs", "graph_signature", "constants"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._remap_constants", "name": "_remap_constants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["orig_constant_attrs", "graph_signature", "constants"], "arg_types": ["torch._export.passes.lift_constants_pass.ConstantAttrMap", "torch.export.graph_signature.ExportGraphSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remap_constants", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rename_constants_nodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "graph_signature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._rename_constants_nodes", "name": "_rename_constants_nodes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["gm", "graph_signature"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.export.graph_signature.ExportGraphSignature"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rename_constants_nodes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_replace_param_buffer_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["param_buffer_table", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._replace_param_buffer_names", "name": "_replace_param_buffer_names", "type": null}}, "_replace_unbacked_bindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._replace_unbacked_bindings", "name": "_replace_unbacked_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace_unbacked_bindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_restore_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["original_module", "traced_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._restore_state_dict", "name": "_restore_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["original_module", "traced_module"], "arg_types": ["torch.nn.modules.module.Module", "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_restore_state_dict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rewrite_dynamo_tensor_constants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["orig_mod_buffers", "traced_mod_buffers", "graph_signature", "constants"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._rewrite_dynamo_tensor_constants", "name": "_rewrite_dynamo_tensor_constants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["orig_mod_buffers", "traced_mod_buffers", "graph_signature", "constants"], "arg_types": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.export.graph_signature.ExportGraphSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx._symbolic_trace._ConstantAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rewrite_dynamo_tensor_constants", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rewrite_tracepoint_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._rewrite_tracepoint_node", "name": "_rewrite_tracepoint_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rewrite_tracepoint_node", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_strict_export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes", "preserve_module_call_signature", "orig_in_spec", "allow_complex_guards_as_runtime_asserts", "_is_torch_jit_trace", "_to_aten_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._strict_export", "name": "_strict_export", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["mod", "args", "kwargs", "dynamic_shapes", "preserve_module_call_signature", "orig_in_spec", "allow_complex_guards_as_runtime_asserts", "_is_torch_jit_trace", "_to_aten_func"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "torch.utils._pytree.TreeSpec", "builtins.bool", "builtins.bool", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_strict_export", "ret_type": "torch.export._trace.ExportArtifact", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_strip_root": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._strip_root", "name": "_strip_root", "type": null}}, "_temp_disable_texpr_fuser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.export._trace._temp_disable_texpr_fuser", "name": "_temp_disable_texpr_fuser", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.export._trace._temp_disable_texpr_fuser", "name": "_temp_disable_texpr_fuser", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_temp_disable_texpr_fuser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_update_gm_meta_if_possible": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils._update_gm_meta_if_possible", "kind": "Gdef"}, "_verify_nn_module_stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._verify_nn_module_stack", "name": "_verify_nn_module_stack", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph_module"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_nn_module_stack", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_verify_placeholder_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._verify_placeholder_names", "name": "_verify_placeholder_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["gm", "sig"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch.export.graph_signature.ExportGraphSignature"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_placeholder_names", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_verify_stack_trace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace._verify_stack_trace", "name": "_verify_stack_trace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph_module"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_stack_trace", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrap_submodules": {".class": "SymbolTableNode", "cross_ref": "torch._export.wrappers._wrap_submodules", "kind": "Gdef"}, "aot_export_module": {".class": "SymbolTableNode", "cross_ref": "torch._functorch.aot_autograd.aot_export_module", "kind": "Gdef"}, "apply_runtime_assertion_pass": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils.apply_runtime_assertion_pass", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "create_functional_call": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.traced_function_transforms.create_functional_call", "kind": "Gdef"}, "create_tree_flattened_fn": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.utils.create_tree_flattened_fn", "kind": "Gdef"}, "custom_triton_ops_decomposition_disabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace.custom_triton_ops_decomposition_disabled", "name": "custom_triton_ops_decomposition_disabled", "type": null}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "detect_fake_mode": {".class": "SymbolTableNode", "cross_ref": "torch._guards.detect_fake_mode", "kind": "Gdef"}, "detect_shape_env": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace.detect_shape_env", "name": "detect_shape_env", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["inputs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_shape_env", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dtrace_structured": {".class": "SymbolTableNode", "cross_ref": "torch._logging._internal.dtrace_structured", "kind": "Gdef"}, "enable_python_dispatcher": {".class": "SymbolTableNode", "cross_ref": "torch._dispatch.python.enable_python_dispatcher", "kind": "Gdef"}, "exportdb_error_message": {".class": "SymbolTableNode", "cross_ref": "torch._export.db.logging.exportdb_error_message", "kind": "Gdef"}, "free_unbacked_symbols": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.free_unbacked_symbols", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_class_if_classified_error": {".class": "SymbolTableNode", "cross_ref": "torch._export.db.logging.get_class_if_classified_error", "kind": "Gdef"}, "get_ep_stats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace.get_ep_stats", "name": "get_ep_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ep"], "arg_types": ["torch.export.exported_program.ExportedProgram"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ep_stats", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_proxy_slot": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor.get_proxy_slot", "kind": "Gdef"}, "get_subclass_typing_container": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.subclass_utils.get_subclass_typing_container", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.export._trace.log", "name": "log", "type": "logging.Logger"}}, "log_export_usage": {".class": "SymbolTableNode", "cross_ref": "torch._utils_internal.log_export_usage", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_constraints": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils.make_constraints", "kind": "Gdef"}, "make_fake_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils.make_fake_inputs", "kind": "Gdef"}, "make_fx": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor.make_fx", "kind": "Gdef"}, "nullcontext": {".class": "SymbolTableNode", "cross_ref": "contextlib.nullcontext", "kind": "Gdef"}, "patch_forward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["obj", "new_method"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.export._trace.patch_forward", "name": "patch_forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "new_method"], "arg_types": ["torch.nn.modules.module.Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch_forward", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.export._trace.patch_forward", "name": "patch_forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "new_method"], "arg_types": ["torch.nn.modules.module.Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch_forward", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "placeholder_naming_pass": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils.placeholder_naming_pass", "kind": "Gdef"}, "placeholder_prefixes": {".class": "SymbolTableNode", "cross_ref": "torch._export.utils.placeholder_prefixes", "kind": "Gdef"}, "produce_guards_and_solve_constraints": {".class": "SymbolTableNode", "cross_ref": "torch._export.non_strict_utils.produce_guards_and_solve_constraints", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "register_buffer_assignment_hook": {".class": "SymbolTableNode", "cross_ref": "torch._functorch._aot_autograd.utils.register_buffer_assignment_hook", "kind": "Gdef"}, "set_missing_meta_vals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "flat_args", "num_params_buffers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export._trace.set_missing_meta_vals", "name": "set_missing_meta_vals", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tracing": {".class": "SymbolTableNode", "cross_ref": "torch._guards.tracing", "kind": "Gdef"}, "track_tensor_tree": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor.track_tensor_tree", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\export\\_trace.py"}