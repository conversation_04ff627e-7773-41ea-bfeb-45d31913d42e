{".class": "MypyFile", "_fullname": "streamlit.elements.lib.js_number", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "JSNumber": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.elements.lib.js_number.JSNumber", "name": "JSNumber", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.elements.lib.js_number.JSNumber", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.elements.lib.js_number", "mro": ["streamlit.elements.lib.js_number.JSNumber", "builtins.object"], "names": {".class": "SymbolTable", "MAX_SAFE_INTEGER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.js_number.JSNumber.MAX_SAFE_INTEGER", "name": "MAX_SAFE_INTEGER", "type": "builtins.int"}}, "MAX_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.js_number.JSNumber.MAX_VALUE", "name": "MAX_VALUE", "type": "builtins.float"}}, "MIN_NEGATIVE_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.js_number.JSNumber.MIN_NEGATIVE_VALUE", "name": "MIN_NEGATIVE_VALUE", "type": "builtins.float"}}, "MIN_SAFE_INTEGER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.js_number.JSNumber.MIN_SAFE_INTEGER", "name": "MIN_SAFE_INTEGER", "type": "builtins.int"}}, "MIN_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.js_number.JSNumber.MIN_VALUE", "name": "MIN_VALUE", "type": "builtins.float"}}, "validate_float_bounds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "value", "value_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "streamlit.elements.lib.js_number.JSNumber.validate_float_bounds", "name": "validate_float_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "value", "value_name"], "arg_types": [{".class": "TypeType", "item": "streamlit.elements.lib.js_number.JSNumber"}, {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_float_bounds of JSNumber", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "streamlit.elements.lib.js_number.JSNumber.validate_float_bounds", "name": "validate_float_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "value", "value_name"], "arg_types": [{".class": "TypeType", "item": "streamlit.elements.lib.js_number.JSNumber"}, {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_float_bounds of JSNumber", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_int_bounds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "value", "value_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "streamlit.elements.lib.js_number.JSNumber.validate_int_bounds", "name": "validate_int_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "value", "value_name"], "arg_types": [{".class": "TypeType", "item": "streamlit.elements.lib.js_number.JSNumber"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_int_bounds of JSNumber", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "streamlit.elements.lib.js_number.JSNumber.validate_int_bounds", "name": "validate_int_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "value", "value_name"], "arg_types": [{".class": "TypeType", "item": "streamlit.elements.lib.js_number.JSNumber"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_int_bounds of JSNumber", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.elements.lib.js_number.JSNumber.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.elements.lib.js_number.JSNumber", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSNumberBoundsException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.elements.lib.js_number.JSNumberBoundsException", "name": "JSNumberBoundsException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.elements.lib.js_number.JSNumberBoundsException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.elements.lib.js_number", "mro": ["streamlit.elements.lib.js_number.JSNumberBoundsException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.elements.lib.js_number.JSNumberBoundsException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.elements.lib.js_number.JSNumberBoundsException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.js_number.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.js_number.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.js_number.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.js_number.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.js_number.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.js_number.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "numbers": {".class": "SymbolTableNode", "cross_ref": "numbers", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\elements\\lib\\js_number.py"}