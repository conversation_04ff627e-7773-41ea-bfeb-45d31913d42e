{".class": "MypyFile", "_fullname": "streamlit.proto.PageProfile_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageProfile_pb2.Argument", "name": "Argument", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.PageProfile_pb2.Argument", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageProfile_pb2", "mro": ["streamlit.proto.PageProfile_pb2.Argument", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageProfile_pb2.Argument.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PageProfile_pb2.Argument", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "k"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "p"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "p"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "t"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "t"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Argument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}}}, "K_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.K_FIELD_NUMBER", "name": "K_FIELD_NUMBER", "type": "builtins.int"}}, "M_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.M_FIELD_NUMBER", "name": "M_FIELD_NUMBER", "type": "builtins.int"}}, "P_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.P_FIELD_NUMBER", "name": "P_FIELD_NUMBER", "type": "builtins.int"}}, "T_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.T_FIELD_NUMBER", "name": "T_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "k", "t", "m", "p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageProfile_pb2.Argument.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "k", "t", "m", "p"], "arg_types": ["streamlit.proto.PageProfile_pb2.Argument", "builtins.str", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Argument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "k": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.k", "name": "k", "type": "builtins.str"}}, "m": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.m", "name": "m", "type": "builtins.str"}}, "p": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.p", "name": "p", "type": "builtins.int"}}, "t": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Argument.t", "name": "t", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageProfile_pb2.Argument.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageProfile_pb2.Argument", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Command": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageProfile_pb2.Command", "name": "Command", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.PageProfile_pb2.Command", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageProfile_pb2", "mro": ["streamlit.proto.PageProfile_pb2.Command", "builtins.object"], "names": {".class": "SymbolTable", "ARGS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Command.ARGS_FIELD_NUMBER", "name": "ARGS_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageProfile_pb2.Command.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PageProfile_pb2.Command", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "args"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "args"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "time"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Command", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Command.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}}}, "NAME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Command.NAME_FIELD_NUMBER", "name": "NAME_FIELD_NUMBER", "type": "builtins.int"}}, "TIME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Command.TIME_FIELD_NUMBER", "name": "TIME_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "name", "args", "time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageProfile_pb2.Command.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "name", "args", "time"], "arg_types": ["streamlit.proto.PageProfile_pb2.Command", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.PageProfile_pb2.Argument"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Command", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.PageProfile_pb2.Command.args", "name": "args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageProfile_pb2.Command"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "args of Command", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PageProfile_pb2.Command.args", "name": "args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageProfile_pb2.Command"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "args of Command", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Command.name", "name": "name", "type": "builtins.str"}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.Command.time", "name": "time", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageProfile_pb2.Command.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageProfile_pb2.Command", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}}}, "PageProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageProfile_pb2.PageProfile", "name": "PageProfile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageProfile_pb2", "mro": ["streamlit.proto.PageProfile_pb2.PageProfile", "builtins.object"], "names": {".class": "SymbolTable", "ATTRIBUTIONS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.ATTRIBUTIONS_FIELD_NUMBER", "name": "ATTRIBUTIONS_FIELD_NUMBER", "type": "builtins.int"}}, "COMMANDS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.COMMANDS_FIELD_NUMBER", "name": "COMMANDS_FIELD_NUMBER", "type": "builtins.int"}}, "CONFIG_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.CONFIG_FIELD_NUMBER", "name": "CONFIG_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PageProfile_pb2.PageProfile", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "attributions"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "attributions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "commands"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "commands"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exec_time"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "exec_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "headless"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "headless"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_fragment_run"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "is_fragment_run"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "os"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "os"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "prep_time"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "prep_time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timezone"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "timezone"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uncaught_exception"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "uncaught_exception"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of PageProfile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}}}, "EXEC_TIME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.EXEC_TIME_FIELD_NUMBER", "name": "EXEC_TIME_FIELD_NUMBER", "type": "builtins.int"}}, "HEADLESS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.HEADLESS_FIELD_NUMBER", "name": "HEADLESS_FIELD_NUMBER", "type": "builtins.int"}}, "IS_FRAGMENT_RUN_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.IS_FRAGMENT_RUN_FIELD_NUMBER", "name": "IS_FRAGMENT_RUN_FIELD_NUMBER", "type": "builtins.int"}}, "OS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.OS_FIELD_NUMBER", "name": "OS_FIELD_NUMBER", "type": "builtins.int"}}, "PREP_TIME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.PREP_TIME_FIELD_NUMBER", "name": "PREP_TIME_FIELD_NUMBER", "type": "builtins.int"}}, "TIMEZONE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.TIMEZONE_FIELD_NUMBER", "name": "TIMEZONE_FIELD_NUMBER", "type": "builtins.int"}}, "UNCAUGHT_EXCEPTION_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.UNCAUGHT_EXCEPTION_FIELD_NUMBER", "name": "UNCAUGHT_EXCEPTION_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "commands", "exec_time", "prep_time", "config", "uncaught_exception", "attributions", "os", "timezone", "headless", "is_fragment_run"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "commands", "exec_time", "prep_time", "config", "uncaught_exception", "attributions", "os", "timezone", "headless", "is_fragment_run"], "arg_types": ["streamlit.proto.PageProfile_pb2.PageProfile", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.PageProfile_pb2.Command"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PageProfile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attributions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.attributions", "name": "attributions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageProfile_pb2.PageProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributions of PageProfile", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.attributions", "name": "attributions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageProfile_pb2.PageProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributions of PageProfile", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "commands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.commands", "name": "commands", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageProfile_pb2.PageProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commands of PageProfile", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.commands", "name": "commands", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageProfile_pb2.PageProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commands of PageProfile", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageProfile_pb2.PageProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of PageProfile", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageProfile_pb2.PageProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of PageProfile", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exec_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.exec_time", "name": "exec_time", "type": "builtins.int"}}, "headless": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.headless", "name": "headless", "type": "builtins.bool"}}, "is_fragment_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.is_fragment_run", "name": "is_fragment_run", "type": "builtins.bool"}}, "os": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.os", "name": "os", "type": "builtins.str"}}, "prep_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.prep_time", "name": "prep_time", "type": "builtins.int"}}, "timezone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.timezone", "name": "timezone", "type": "builtins.str"}}, "uncaught_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.uncaught_exception", "name": "uncaught_exception", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageProfile_pb2.PageProfile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageProfile_pb2.PageProfile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageProfile_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___Argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.PageProfile_pb2.global___Argument", "line": 105, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PageProfile_pb2.Argument"}}, "global___Command": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.PageProfile_pb2.global___Command", "line": 127, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PageProfile_pb2.Command"}}, "global___PageProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.PageProfile_pb2.global___PageProfile", "line": 72, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PageProfile_pb2.PageProfile"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PageProfile_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageProfile_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\PageProfile_pb2.pyi"}