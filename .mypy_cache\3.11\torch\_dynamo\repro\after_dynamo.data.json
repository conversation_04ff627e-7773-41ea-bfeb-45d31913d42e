{".class": "MypyFile", "_fullname": "torch._dynamo.repro.after_dynamo", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccuracyError": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.AccuracyError", "kind": "Gdef"}, "BUCK_CMD_PREFIX": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.BUCK_CMD_PREFIX", "kind": "Gdef"}, "BuckTargetWriter": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.BuckTargetWriter", "kind": "Gdef"}, "CompiledFn": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.backends.registry.CompiledFn", "kind": "Gdef"}, "InputReader": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.InputReader", "kind": "Gdef"}, "InputWriter": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.InputWriter", "kind": "Gdef"}, "NNModuleToString": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.NNModuleToString", "kind": "Gdef"}, "NopInputReader": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.NopInputReader", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WrapBackendDebug": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug", "name": "WrapBackendDebug", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._dynamo.repro.after_dynamo", "mro": ["torch._dynamo.repro.after_dynamo.WrapBackendDebug", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "gm", "example_inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "unconfigured_compiler_fn", "compiler_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "unconfigured_compiler_fn", "compiler_name"], "arg_types": ["torch._dynamo.repro.after_dynamo.WrapBackendDebug", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WrapBackendDebug", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug.__name__", "name": "__name__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "_compiler_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug._compiler_name", "name": "_compiler_name", "type": "builtins.str"}}, "_torchdynamo_orig_callable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug._torchdynamo_orig_callable", "name": "_torchdynamo_orig_callable", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_compiler_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug.get_compiler_config", "name": "get_compiler_config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._dynamo.repro.after_dynamo.WrapBackendDebug.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._dynamo.repro.after_dynamo.WrapBackendDebug", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_dynamo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_dynamo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_dynamo.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_dynamo.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_dynamo.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_dynamo.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_accuracy_fails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "example_inputs", "compiler_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo._accuracy_fails", "name": "_accuracy_fails", "type": null}}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "backend_accuracy_fails": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.backend_accuracy_fails", "kind": "Gdef"}, "backend_fails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["gm", "example_inputs", "compiler_fn", "orig_failure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.backend_fails", "name": "backend_fails", "type": null}}, "clone_inputs_retaining_gradness": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.clone_inputs_retaining_gradness", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.config", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "dump_backend_repro_as_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["gm", "args", "compiler_name", "check_accuracy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.dump_backend_repro_as_file", "name": "dump_backend_repro_as_file", "type": null}}, "dump_backend_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["gm", "args", "compiler_name", "check_accuracy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.dump_backend_state", "name": "dump_backend_state", "type": null}}, "dump_to_minify_after_dynamo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "args", "compiler_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.dump_to_minify_after_dynamo", "name": "dump_to_minify_after_dynamo", "type": null}}, "dynamo_accuracy_minifier_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "example_inputs", "compiler_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._dynamo.repro.after_dynamo.dynamo_accuracy_minifier_backend", "name": "dynamo_accuracy_minifier_backend", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._dynamo.repro.after_dynamo.dynamo_accuracy_minifier_backend", "name": "dynamo_accuracy_minifier_backend", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "dynamo_minifier_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "example_inputs", "compiler_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._dynamo.repro.after_dynamo.dynamo_minifier_backend", "name": "dynamo_minifier_backend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["gm", "example_inputs", "compiler_name"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._dynamo.backends.registry.CompiledFn"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dynamo_minifier_backend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._dynamo.repro.after_dynamo.dynamo_minifier_backend", "name": "dynamo_minifier_backend", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "extra_imports": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.extra_imports", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "fx": {".class": "SymbolTableNode", "cross_ref": "torch.fx", "kind": "Gdef"}, "fx_placeholder_targets": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.fx_placeholder_targets", "kind": "Gdef"}, "generate_config_string": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.generate_config_string", "kind": "Gdef"}, "generate_dynamo_fx_repro_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5, 5], "arg_names": ["gm", "args", "compiler_name", "check_accuracy", "stable_output", "save_dir", "command"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.generate_dynamo_fx_repro_string", "name": "generate_dynamo_fx_repro_string", "type": null}}, "generate_env_vars_string": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.generate_env_vars_string", "kind": "Gdef"}, "helper_for_dump_minify": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.helper_for_dump_minify", "kind": "Gdef"}, "import_module": {".class": "SymbolTableNode", "cross_ref": "importlib.import_module", "kind": "Gdef"}, "inductor_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._dynamo.repro.after_dynamo.inductor_config", "name": "inductor_config", "type": "types.ModuleType"}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._dynamo.repro.after_dynamo.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "lookup_backend": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.backends.registry.lookup_backend", "kind": "Gdef"}, "minifier_dir": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.minifier_dir", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "register_debug_backend": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.backends.registry.register_debug_backend", "kind": "Gdef"}, "repro_minify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.repro_minify", "name": "repro_minify", "type": null}}, "repro_run": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.repro_run", "name": "repro_run", "type": null}}, "run_fwd_maybe_bwd": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.run_fwd_maybe_bwd", "kind": "Gdef"}, "run_load_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.run_load_args", "name": "run_load_args", "type": null}}, "run_repro": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 4], "arg_names": ["mod", "load_args", "command", "accuracy", "save_dir", "autocast", "backend", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.run_repro", "name": "run_repro", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 4], "arg_names": ["mod", "load_args", "command", "accuracy", "save_dir", "autocast", "backend", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_repro", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "same_two_models": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.same_two_models", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "cross_ref": "torch.hub.tqdm", "kind": "Gdef"}, "use_buck": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._dynamo.repro.after_dynamo.use_buck", "name": "use_buck", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "wrap_backend_debug": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["unconfigured_compiler_fn", "compiler_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_dynamo.wrap_backend_debug", "name": "wrap_backend_debug", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["unconfigured_compiler_fn", "compiler_name"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_backend_debug", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_dynamo\\repro\\after_dynamo.py"}