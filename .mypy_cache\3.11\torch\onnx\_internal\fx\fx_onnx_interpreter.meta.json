{"data_mtime": 1755649448, "dep_lines": [16, 16, 16, 16, 15, 21, 14, 15, 21, 25, 2, 4, 5, 6, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 8], "dep_prios": [10, 10, 10, 20, 10, 10, 10, 20, 20, 25, 5, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["torch.onnx._internal.fx._pass", "torch.onnx._internal.fx.onnxfunction_dispatcher", "torch.onnx._internal.fx.type_utils", "torch.onnx._internal.fx", "torch.onnx._type_utils", "torch.utils._pytree", "torch.fx", "torch.onnx", "torch.utils", "collections.abc", "__future__", "inspect", "operator", "typing", "torch", "builtins", "os", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._ops", "torch._subclasses", "torch._subclasses.fake_tensor", "torch._tensor", "torch.fx.graph_module", "torch.fx.node", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "d7758041f59eee8b8bafdc6e9b0e29908eac1a9e", "id": "torch.onnx._internal.fx.fx_onnx_interpreter", "ignore_all": true, "interface_hash": "9a4a3db5e74c0349cd21f6fd4990160c9168f36b", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\fx_onnx_interpreter.py", "plugin_data": null, "size": 31922, "suppressed": ["onnxscript.function_libs.torch_lib", "onnxscript"], "version_id": "1.15.0"}