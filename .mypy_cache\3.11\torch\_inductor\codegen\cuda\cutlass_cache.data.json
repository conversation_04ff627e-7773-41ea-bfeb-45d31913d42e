{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cuda.cutlass_cache", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CONFIG_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.CONFIG_PREFIX", "name": "CONFIG_PREFIX", "type": "builtins.str"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_generate_config_filename": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_cache._generate_config_filename", "name": "_generate_config_filename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request_key"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_config_filename", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache_dir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.cache_dir_utils.cache_dir", "kind": "Gdef"}, "clear_on_fresh_cache": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.clear_on_fresh_cache", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "cutlass_key": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.cutlass_key", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_config_request_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["arch", "cuda_version", "instantiation_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.get_config_request_key", "name": "get_config_request_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["arch", "cuda_version", "instantiation_level"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_config_request_key", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cuda_arch": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_env.get_cuda_arch", "kind": "Gdef"}, "get_cuda_version": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_env.get_cuda_version", "kind": "Gdef"}, "get_cutlass_operation_serializer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.serialization.get_cutlass_operation_serializer", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "maybe_fetch_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.maybe_fetch_ops", "name": "maybe_fetch_ops", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maybe_fetch_ops", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_cache.maybe_fetch_ops", "name": "maybe_fetch_ops", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cutlass_cache.py"}