{"data_mtime": 1755649448, "dep_lines": [12, 25, 27, 11, 13, 14, 15, 19, 19, 20, 21, 24, 26, 28, 29, 6, 11, 19, 28, 2, 3, 4, 5, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 5, 5, 20, 20, 20, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch._C._dynamo.guards", "torch.fx.experimental.symbolic_shapes", "torch.fx.passes.reinplace", "torch.fx.node", "torch._dispatch.python", "torch._dynamo.utils", "torch._higher_order_ops.triton_kernel_wrap", "torch._inductor.config", "torch._inductor.inductor_prims", "torch._inductor.fx_utils", "torch._inductor.lowering", "torch._inductor.virtualized", "torch.fx.immutable_collections", "torch.utils._pytree", "torch.utils._ordered_set", "collections.abc", "torch.fx", "torch._inductor", "torch.utils", "itertools", "logging", "operator", "collections", "dataclasses", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "contextlib", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._higher_order_ops", "torch._ops", "torch._tensor", "torch.fx.graph"], "hash": "73c6ebda3924e0eb5409a48534e516e64830c6a2", "id": "torch._inductor.fx_passes.reinplace", "ignore_all": true, "interface_hash": "a2c3c85a334019a3700b2625df3a0c1caf538a61", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\reinplace.py", "plugin_data": null, "size": 30608, "suppressed": [], "version_id": "1.15.0"}