{"data_mtime": 1755656860, "dep_lines": [11, 15, 5, 7, 9, 14, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 5, 10, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["referencing._attrs", "referencing.typing", "__future__", "typing", "attrs", "referencing", "builtins", "_frozen_importlib", "abc", "attr", "attr.setters", "referencing._core"], "hash": "811787a5ef7c1fc58fcd76e9fd4c14f42992d57d", "id": "referencing.exceptions", "ignore_all": true, "interface_hash": "d538b557e2ac664216017d16cf2cf7c07ea28770", "mtime": 1755656309, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\referencing\\exceptions.py", "plugin_data": null, "size": 4176, "suppressed": [], "version_id": "1.15.0"}