{"data_mtime": 1755656862, "dep_lines": [12, 7, 11, 17, 31, 36, 37, 6, 7, 8, 9, 29, 34, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 25, 25, 5, 20, 10, 10, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.util", "os.path", "git.compat", "git.util", "git.types", "git.config", "git.refs", "mmap", "os", "re", "time", "typing", "io", "builtins", "_frozen_importlib", "_io", "abc", "configparser", "enum", "git.objects", "git.refs.symbolic"], "hash": "e55a752397d4f44065cd2e919790c65d188bcd75", "id": "git.refs.log", "ignore_all": true, "interface_hash": "eba47969103e41f665b9bf48c965d8f1d718a5ef", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\refs\\log.py", "plugin_data": null, "size": 12490, "suppressed": [], "version_id": "1.15.0"}