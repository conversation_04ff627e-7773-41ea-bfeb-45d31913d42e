{".class": "MypyFile", "_fullname": "referencing._core", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Anchor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core.Anchor", "name": "<PERSON><PERSON>", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Anchor", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "referencing._core.Anchor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core.Anchor", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Anchor.name", "name": "name", "type": "builtins.str"}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Anchor.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Anchor", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Anchor"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Anchor", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of <PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Anchor.resource", "name": "resource", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Anchor", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core.Anchor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Anchor", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Anchor"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["D"], "typeddict_type": null}}, "AnchorOrResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "fullname": "referencing._core.AnchorOrResource", "name": "AnchorOrResource", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing.typing.Anchor"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "variance": 0}}, "AnchorType": {".class": "SymbolTableNode", "cross_ref": "referencing.typing.Anchor", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "D": {".class": "SymbolTableNode", "cross_ref": "referencing.typing.D", "kind": "Gdef"}, "EMPTY_PREVIOUS_RESOLVERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "referencing._core.EMPTY_PREVIOUS_RESOLVERS", "name": "EMPTY_PREVIOUS_RESOLVERS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "rpds.List"}}}, "EMPTY_UNCRAWLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "referencing._core.EMPTY_UNCRAWLED", "name": "EMPTY_UNCRAWLED", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "rpds.HashTrieSet"}}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HashTrieMap": {".class": "SymbolTableNode", "cross_ref": "rpds.HashTrieMap", "kind": "Gdef"}, "HashTrieSet": {".class": "SymbolTableNode", "cross_ref": "rpds.HashTrieSet", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "rpds.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "Registry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core.Registry", "name": "Registry", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "referencing._core.Registry", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core.Registry", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of Registry", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of Registry", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Registry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rmatmul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.__rmatmul__", "name": "__rmatmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmatmul__ of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_anchors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Registry._anchors", "name": "_anchors", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing.typing.Anchor"}], "extra_attrs": null, "type_ref": "rpds.HashTrieMap"}}}, "_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Registry._resources", "name": "_resources", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "extra_attrs": null, "type_ref": "rpds.HashTrieMap"}}}, "_retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Registry._retrieve", "name": "_retrieve", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing.typing.Retrieve"}}}, "_uncrawled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Registry._uncrawled", "name": "_uncrawled", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "rpds.HashTrieSet"}}}, "anchor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uri", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.anchor", "name": "anchor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "uri", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anchor of Registry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "combine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "registries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.combine", "name": "combine", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "registries"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "combine of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.contents", "name": "contents", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contents of Registry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "crawl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.crawl", "name": "crawl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "crawl of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_or_retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.get_or_retrieve", "name": "get_or_retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_or_retrieve of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "extra_attrs": null, "type_ref": "referencing._core.Retrieved"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of Registry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "base_uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.resolver", "name": "resolver", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "base_uri"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolver of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolver_with_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.resolver_with_root", "name": "resolver_with_root", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolver_with_root of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "pairs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.with_contents", "name": "with_contents", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "pairs", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_contents of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uri", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.with_resource", "name": "with_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "uri", "resource"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_resource of Registry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pairs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Registry.with_resources", "name": "with_resources", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pairs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_resources of Registry", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core.Registry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Registry", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["D"], "typeddict_type": null}}, "Resolved": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core.Resolved", "name": "Resolved", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolved", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "referencing._core.Resolved", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core.Resolved", "builtins.object"], "names": {".class": "SymbolTable", "contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Resolved.contents", "name": "contents", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolved", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "resolver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Resolved.resolver", "name": "resolver", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolved", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core.Resolved.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolved", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolved"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["D"], "typeddict_type": null}}, "Resolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core.Resolver", "name": "Resolver", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "referencing._core.Resolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core.Resolver", "builtins.object"], "names": {".class": "SymbolTable", "_base_uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Resolver._base_uri", "name": "_base_uri", "type": "builtins.str"}}, "_evolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "base_uri", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Resolver._evolve", "name": "_evolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "base_uri", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_evolve of Resolver", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_previous": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Resolver._previous", "name": "_previous", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "rpds.List"}}}, "_registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Resolver._registry", "name": "_registry", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}}}, "dynamic_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Resolver.dynamic_scope", "name": "dynamic_scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dynamic_scope of Resolver", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_subresource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subresource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Resolver.in_subresource", "name": "in_subresource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subresource"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_subresource of Resolver", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Resolver.lookup", "name": "lookup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ref"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lookup of Resolver", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolved"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core.Resolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resolver", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["D"], "typeddict_type": null}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core.Resource", "name": "Resource", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "referencing._core.Resource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core.Resource", "builtins.object"], "names": {".class": "SymbolTable", "_specification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Resource._specification", "name": "_specification", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}}}, "anchors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Resource.anchors", "name": "anchors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anchors of Resource", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing.typing.Anchor"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Resource.contents", "name": "contents", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "from_contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "contents", "default_specification"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "referencing._core.Resource.from_contents", "name": "from_contents", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "contents", "default_specification"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_contents of Resource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "referencing._core.Resource.from_contents", "name": "from_contents", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "contents", "default_specification"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_contents of Resource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Resource.id", "name": "id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "id of Resource", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "opaque": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "referencing._core.Resource.opaque", "name": "opaque", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "contents"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opaque of Resource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "referencing._core.Resource.opaque", "name": "opaque", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "contents"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opaque of Resource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pointer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pointer", "resolver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Resource.pointer", "name": "pointer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pointer", "resolver"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pointer of Resource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolved"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subresources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Resource.subresources", "name": "subresources", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subresources of Resource", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core.Resource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Resource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["D"], "typeddict_type": null}}, "Retrieve": {".class": "SymbolTableNode", "cross_ref": "referencing.typing.Retrieve", "kind": "Gdef"}, "Retrieved": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core.Retrieved", "name": "Retrieved", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Retrieved", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "fullname": "referencing._core.AnchorOrResource", "id": 2, "name": "AnchorOrResource", "namespace": "referencing._core.Retrieved", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing.typing.Anchor"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "referencing._core.Retrieved", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core.Retrieved", "builtins.object"], "names": {".class": "SymbolTable", "registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Retrieved.registry", "name": "registry", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Retrieved", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Registry"}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Retrieved.value", "name": "value", "type": {".class": "TypeVarType", "default": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "fullname": "referencing._core.AnchorOrResource", "id": 2, "name": "AnchorOrResource", "namespace": "referencing._core.Retrieved", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing.typing.Anchor"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "variance": 0}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core.Retrieved.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Retrieved", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "fullname": "referencing._core.AnchorOrResource", "id": 2, "name": "AnchorOrResource", "namespace": "referencing._core.Retrieved", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing.typing.Anchor"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Retrieved"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["D", "AnchorOrResource"], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Specification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core.Specification", "name": "Specification", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "referencing._core.Specification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core.Specification", "builtins.object"], "names": {".class": "SymbolTable", "OPAQUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "referencing._core.Specification.OPAQUE", "name": "OPAQUE", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Specification.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Specification", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_anchors_in": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "referencing._core.Specification._anchors_in", "name": "_anchors_in", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing.typing.Anchor"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "anchors_in": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Specification.anchors_in", "name": "anchors_in", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "contents"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anchors_in of Specification", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core.Specification.create_resource", "name": "create_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "contents"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_resource of Specification", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "referencing._core.Specification.detect", "name": "detect", "type": "referencing._core._SpecificationDetector"}}, "id_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Specification.id_of", "name": "id_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_in_subresource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Specification.maybe_in_subresource", "name": "maybe_in_subresource", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core._MaybeInSubresource"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Specification.name", "name": "name", "type": "builtins.str"}}, "subresources_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "referencing._core.Specification.subresources_of", "name": "subresources_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core.Specification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core.Specification", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["D"], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef"}, "URI": {".class": "SymbolTableNode", "cross_ref": "referencing.typing.URI", "kind": "Gdef"}, "_MaybeInSubresource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core._MaybeInSubresource", "name": "_MaybeInSubresource", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core._MaybeInSubresource", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "referencing._core._MaybeInSubresource", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core._MaybeInSubresource", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "segments", "resolver", "subresource"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "referencing._core._MaybeInSubresource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "segments", "resolver", "subresource"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core._MaybeInSubresource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core._MaybeInSubresource"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core._MaybeInSubresource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core._MaybeInSubresource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resource"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _MaybeInSubresource", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core._MaybeInSubresource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Resolver"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core._MaybeInSubresource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": 1, "name": "D", "namespace": "referencing._core._MaybeInSubresource", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core._MaybeInSubresource"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["D"], "typeddict_type": null}}, "_SpecificationDetector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core._SpecificationDetector", "name": "_SpecificationDetector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "referencing._core._SpecificationDetector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core._SpecificationDetector", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core._SpecificationDetector.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "cls"], "arg_types": ["referencing._core._SpecificationDetector", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._SpecificationDetector.__get__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._SpecificationDetector.__get__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of _SpecificationDetector", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._SpecificationDetector.__get__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._SpecificationDetector.__get__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._SpecificationDetector.__get__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core._SpecificationDetector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "referencing._core._SpecificationDetector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UNSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "referencing._core._UNSET", "name": "_UNSET", "type": "referencing._core._Unset"}}, "_Unset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "referencing._core._Unset", "name": "_Unset", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "referencing._core._Unset", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "referencing._core", "mro": ["referencing._core._Unset", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "SENTINEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "referencing._core._Unset.SENTINEL", "name": "SENTINEL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "referencing._core._Unset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "referencing._core._Unset", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "referencing._core.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "referencing._core.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "referencing._core.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "referencing._core.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "referencing._core.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "referencing._core.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_detect_or_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core._detect_or_default", "name": "_detect_or_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._detect_or_default", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_or_default", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._detect_or_default", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._detect_or_default", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._detect_or_default", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_detect_or_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core._detect_or_error", "name": "_detect_or_error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["contents"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._detect_or_error", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_or_error", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._detect_or_error", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "referencing._core.Specification"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "referencing.typing.D", "id": -1, "name": "D", "namespace": "referencing._core._detect_or_error", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_fail_to_retrieve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "referencing._core._fail_to_retrieve", "name": "_fail_to_retrieve", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["uri"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fail_to_retrieve", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "evolve": {".class": "SymbolTableNode", "cross_ref": "attr.evolve", "kind": "Gdef"}, "exceptions": {".class": "SymbolTableNode", "cross_ref": "referencing.exceptions", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "attrs.field", "kind": "Gdef"}, "frozen": {".class": "SymbolTableNode", "cross_ref": "referencing._attrs.frozen", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "urldefrag": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urldefrag", "kind": "Gdef"}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\referencing\\_core.py"}