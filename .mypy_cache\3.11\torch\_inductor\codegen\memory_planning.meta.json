{"data_mtime": 1755649448, "dep_lines": [18, 13, 15, 16, 17, 15, 29, 2, 4, 5, 6, 7, 8, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 1], "dep_prios": [5, 5, 10, 5, 5, 20, 25, 5, 10, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 10, 20], "dependencies": ["torch._inductor.codegen.wrapper", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor", "collections.abc", "__future__", "collections", "dataclasses", "itertools", "pprint", "typing", "torch", "builtins", "warnings", "operator", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "functools", "sys", "json", "traceback", "re", "html", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._inductor.codegen.common", "torch._inductor.ir", "torch.utils", "typing_extensions", "contextlib"], "hash": "2e570b9291fc64bc49609527b99d224d99f1ee88", "id": "torch._inductor.codegen.memory_planning", "ignore_all": true, "interface_hash": "0879d9020f944205220e07b45a61f41f2c555192", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\memory_planning.py", "plugin_data": null, "size": 25856, "suppressed": ["sympy", "traitlets.utils.warnings"], "version_id": "1.15.0"}