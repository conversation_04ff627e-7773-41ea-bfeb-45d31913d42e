{".class": "MypyFile", "_fullname": "torch._export.pass_base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Argument": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._export.pass_base.Argument", "line": 31, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CodeGen": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph.CodeGen", "kind": "Gdef", "module_public": false}, "ExportPassBaseError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._export.pass_base.ExportPassBaseError", "name": "ExportPassBaseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._export.pass_base.ExportPassBaseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._export.pass_base", "mro": ["torch._export.pass_base.ExportPassBaseError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._export.pass_base.ExportPassBaseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._export.pass_base.ExportPassBaseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeTensor": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor.FakeTensor", "kind": "Gdef", "module_public": false}, "FakeTensorMode": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor.FakeTensorMode", "kind": "Gdef", "module_public": false}, "Fn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._export.pass_base.Fn", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NodeMetadata": {".class": "SymbolTableNode", "cross_ref": "torch._export.pass_infra.node_metadata.NodeMetadata", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PassBase": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.infra.pass_base.PassBase", "kind": "Gdef", "module_public": false}, "PassResult": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.infra.pass_base.PassResult", "kind": "Gdef", "module_public": false}, "PassType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._export.pass_base.PassType", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.passes.infra.pass_base.PassResult"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PropagateUnbackedSymInts": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.PropagateUnbackedSymInts", "kind": "Gdef", "module_public": false}, "ProxyValue": {".class": "SymbolTableNode", "cross_ref": "torch._export.pass_infra.proxy_value.ProxyValue", "kind": "Gdef", "module_public": false}, "PythonKeyTracer": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor.PythonKeyTracer", "kind": "Gdef", "module_public": false}, "TensorMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.shape_prop.TensorMetadata", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnsupportedFakeTensorException": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor.UnsupportedFakeTensorException", "kind": "Gdef", "module_public": false}, "Value": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._export.pass_base.Value", "line": 32, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "_ExportPassBaseDeprecatedDoNotUse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.fx.passes.infra.pass_base.PassBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "name": "_ExportPassBaseDeprecatedDoNotUse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._export.pass_base", "mro": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "torch.fx.passes.infra.pass_base.PassBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "ExportInterpreter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.fx.interpreter.Interpreter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "name": "ExportInterpreter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._export.pass_base", "mro": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "torch.fx.interpreter.Interpreter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "callback", "gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "callback", "gm"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExportInterpreter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.call_function", "name": "call_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.node.Target"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_function of ExportInterpreter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.call_method", "name": "call_method", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_method of ExportInterpreter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.call_module", "name": "call_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.node.Target"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_module of ExportInterpreter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.callback", "name": "callback", "type": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse"}}, "get_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.get_attr", "name": "get_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_attr of ExportInterpreter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.node", "name": "node", "type": "torch.fx.node.Node"}}, "output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.output", "name": "output", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.node.Target"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output of ExportInterpreter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "placeholder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.placeholder", "name": "placeholder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "placeholder of ExportInterpreter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.run_node", "name": "run_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "n"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_node of ExportInterpreter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportInterpreter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExportTracer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.fx.experimental.proxy_tensor.PythonKeyTracer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer", "name": "ExportTracer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._export.pass_base", "mro": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer", "torch.fx.experimental.proxy_tensor.PythonKeyTracer", "torch.fx._symbolic_trace.Tracer", "torch.fx.proxy.TracerBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "callback", "codegen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "callback", "codegen"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer", "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "torch.fx.graph.CodeGen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExportTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.callback", "name": "callback", "type": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse"}}, "create_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.create_arg", "name": "create_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "a"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_arg of ExportTracer", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fake_tensor_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.fake_tensor_mode", "name": "fake_tensor_mode", "type": {".class": "UnionType", "items": ["torch._subclasses.fake_tensor.FakeTensorMode", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "set_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.set_metadata", "name": "set_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "value"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer", "torch.fx.node.Node", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_metadata of ExportTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "submodules": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.submodules", "name": "submodules", "type": {".class": "Instance", "args": ["torch.nn.modules.module.Module", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tensor_attrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.tensor_attrs", "name": "tensor_attrs", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.trace", "name": "trace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trace of ExportTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_dummy_node_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse._create_dummy_node_metadata", "name": "_create_dummy_node_metadata", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse._create_dummy_node_metadata", "name": "_create_dummy_node_metadata", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_dummy_node_metadata of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "kind", "target", "args", "kwargs", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse._fx", "name": "_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "kind", "target", "args", "kwargs", "meta"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.node.Target"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch._export.pass_infra.node_metadata.NodeMetadata"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fx of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse._initialized", "name": "_initialized", "type": "builtins.bool"}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "graph_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "graph_module"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.passes.infra.pass_base.PassResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_cond": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "pred", "true_fn", "false_fn", "inputs", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.call_cond", "name": "call_cond", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "pred", "true_fn", "false_fn", "inputs", "meta"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "torch.fx.graph_module.GraphModule", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._export.pass_infra.node_metadata.NodeMetadata"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_cond of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "key", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.call_getitem", "name": "call_getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "key", "meta"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "builtins.int", "torch._export.pass_infra.node_metadata.NodeMetadata"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_getitem of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "f", "mapped_args", "operands", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.call_map", "name": "call_map", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "f", "mapped_args", "operands", "meta"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._export.pass_infra.node_metadata.NodeMetadata"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_map of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op", "args", "kwargs", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.call_operator", "name": "call_operator", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op", "args", "kwargs", "meta"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch._export.pass_infra.node_metadata.NodeMetadata"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_operator of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_submodule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph_module", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.call_submodule", "name": "call_submodule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph_module", "inputs"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_submodule of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.passes.infra.pass_base.PassResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_sym": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.call_sym", "name": "call_sym", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "meta"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Fn"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "torch._export.pass_infra.node_metadata.NodeMetadata"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_sym of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fake_tensor_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.fake_tensor_mode", "name": "fake_tensor_mode", "type": {".class": "UnionType", "items": ["torch._subclasses.fake_tensor.FakeTensorMode", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "graph_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.inputs", "name": "inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "graph_module"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inputs of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "interpreter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.interpreter", "name": "interpreter", "type": "torch.fx.experimental.symbolic_shapes.PropagateUnbackedSymInts"}}, "node_debug_str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.node_debug_str", "name": "node_debug_str", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "on_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.on_attr", "name": "on_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_attr of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "results", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.output", "name": "output", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "results", "meta"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._export.pass_infra.node_metadata.NodeMetadata"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "placeholder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "arg", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.placeholder", "name": "placeholder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "arg", "meta"], "arg_types": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._export.pass_base.Argument"}, "torch._export.pass_infra.node_metadata.NodeMetadata"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "placeholder of _ExportPassBaseDeprecatedDoNotUse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._export.pass_infra.proxy_value.ProxyValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tracer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.tracer", "name": "tracer", "type": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.ExportTracer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TORCH_SYM_OPS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch._export.pass_base._TORCH_SYM_OPS", "name": "_TORCH_SYM_OPS", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._export.pass_base.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.pass_base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.pass_base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.pass_base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.pass_base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.pass_base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.pass_base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_extract_tensor_metadata": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.shape_prop._extract_tensor_metadata", "kind": "Gdef", "module_public": false}, "_unstack_pytree": {".class": "SymbolTableNode", "cross_ref": "torch._higher_order_ops.utils._unstack_pytree", "kind": "Gdef", "module_public": false}, "compute_unbacked_bindings": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.compute_unbacked_bindings", "kind": "Gdef", "module_public": false}, "enable_python_dispatcher": {".class": "SymbolTableNode", "cross_ref": "torch._dispatch.python.enable_python_dispatcher", "kind": "Gdef", "module_public": false}, "fx": {".class": "SymbolTableNode", "cross_ref": "torch.fx", "kind": "Gdef", "module_public": false}, "fx_traceback": {".class": "SymbolTableNode", "cross_ref": "torch.fx.traceback", "kind": "Gdef", "module_public": false}, "nullcontext": {".class": "SymbolTableNode", "cross_ref": "contextlib.nullcontext", "kind": "Gdef", "module_public": false}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef", "module_public": false}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_export\\pass_base.py"}