{"data_mtime": 1755656862, "dep_lines": [14, 15, 38, 39, 40, 41, 12, 13, 16, 32, 8, 9, 10, 20, 35, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 25, 25, 5, 5, 5, 5, 10, 10, 10, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.blob", "git.objects.util", "git.objects.base", "git.objects.commit", "git.objects.tree", "git.repo.base", "git.cmd", "git.compat", "git.util", "git.types", "enum", "re", "warnings", "typing", "subprocess", "builtins", "_frozen_importlib", "abc", "git.objects", "git.objects.submodule", "git.objects.submodule.base", "git.repo", "os", "types", "typing_extensions"], "hash": "05f3d695631e272205e01b0dad73c22f6f018f24", "id": "git.diff", "ignore_all": true, "interface_hash": "1ca7cca85124d682da13543812298bdbfb873e02", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\diff.py", "plugin_data": null, "size": 27095, "suppressed": [], "version_id": "1.15.0"}