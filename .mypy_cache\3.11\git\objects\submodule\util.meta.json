{"data_mtime": 1755656862, "dep_lines": [32, 16, 17, 23, 28, 29, 30, 12, 13, 15, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 25, 25, 25, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.base", "git.config", "git.exc", "git.types", "git.refs", "git.remote", "git.repo", "io", "weakref", "git", "typing", "builtins", "_frozen_importlib", "_io", "abc", "configparser", "git.objects.base", "git.objects.util", "git.refs.head", "git.refs.reference", "git.refs.remote", "git.refs.symbolic", "git.repo.base", "git.util", "os"], "hash": "357b31bff74b5685bde41e00754b8298d9344968", "id": "git.objects.submodule.util", "ignore_all": true, "interface_hash": "0604add7cc8f2991b16ef2e92641626add4447fb", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\submodule\\util.py", "plugin_data": null, "size": 3509, "suppressed": [], "version_id": "1.15.0"}