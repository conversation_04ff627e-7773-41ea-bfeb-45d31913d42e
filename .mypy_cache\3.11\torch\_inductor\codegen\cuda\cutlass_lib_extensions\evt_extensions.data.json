{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Buffer", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "torch._inductor.ir.In<PERSON>uffer"], "uses_pep604_syntax": false}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CollectiveEpilogue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CollectiveEpilogue", "name": "CollectiveEpilogue", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CollectiveEpilogue", "source_any": null, "type_of_any": 3}}}, "ComputedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "CutlassArgType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CutlassArgType", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "CutlassTensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CutlassTensor", "name": "CutlassTensor", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CutlassTensor", "source_any": null, "type_of_any": 3}}}, "CutlassTupleType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CutlassTupleType", "line": 17, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "CutlassVisitorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CutlassVisitorType", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "DataType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.DataType", "name": "DataType", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.DataType", "source_any": null, "type_of_any": 3}}}, "EmptyByte": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EmptyByte", "name": "EmptyByte", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EmptyByte", "source_any": null, "type_of_any": 3}}}, "EpilogueFunctor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EpilogueFunctor", "line": 15, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "EpilogueFunctorVisitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EpilogueFunctorVisitor", "name": "EpilogueFunctorVisitor", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EpilogueFunctorVisitor", "source_any": null, "type_of_any": 3}}}, "EpilogueScheduleType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EpilogueScheduleType", "name": "EpilogueScheduleType", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EpilogueScheduleType", "source_any": null, "type_of_any": 3}}}, "Expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Expr", "name": "Expr", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Expr", "source_any": null, "type_of_any": 3}}}, "FusionCallbacks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.FusionCallbacks", "name": "FusionCallbacks", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.FusionCallbacks", "source_any": null, "type_of_any": 3}}}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "InputBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.In<PERSON>uffer", "kind": "Gdef"}, "LayoutType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.LayoutType", "name": "LayoutType", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.LayoutType", "source_any": null, "type_of_any": 3}}}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "PythonASTFrontend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.PythonASTFrontend", "name": "PythonASTFrontend", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.PythonASTFrontend", "source_any": null, "type_of_any": 3}}}, "TileDescription": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.TileDescription", "name": "TileDescription", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.TileDescription", "source_any": null, "type_of_any": 3}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CUTLASS_C_DTYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions._CUTLASS_C_DTYPES", "name": "_CUTLASS_C_DTYPES", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_arg_from_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["arg_ty", "node", "size_hint_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions._get_arg_from_node", "name": "_get_arg_from_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["arg_ty", "node", "size_hint_fn"], "arg_types": ["builtins.type", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Buffer"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Expr", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_arg_from_node", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_argument_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["epilogue_functor", "name_to_buffer", "size_hint_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions._render_argument_type", "name": "_render_argument_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["epilogue_functor", "name_to_buffer", "size_hint_fn"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EpilogueFunctor"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Buffer"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Expr", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_argument_type", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_trace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["fn_src", "example_tensors", "cc", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions._trace", "name": "_trace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["fn_src", "example_tensors", "cc", "kwargs"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CutlassTensor", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_trace", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EpilogueFunctor"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef"}, "create_example_tensors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["var_name_to_buffer_name", "name_to_buffer", "size_hint_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.create_example_tensors", "name": "create_example_tensors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["var_name_to_buffer_name", "name_to_buffer", "size_hint_fn"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Buffer"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Expr", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_example_tensors", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CutlassTensor", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ctypes": {".class": "SymbolTableNode", "cross_ref": "ctypes", "kind": "Gdef"}, "cuda_env": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_env", "kind": "Gdef"}, "dtype2ctype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.dtype2ctype", "name": "dtype2ctype", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.dtype2ctype", "source_any": null, "type_of_any": 3}}}, "is_contiguous_strides_for_shape": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.is_contiguous_strides_for_shape", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "torch_dtype_to_cutlass_type": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_utils.torch_dtype_to_cutlass_type", "kind": "Gdef"}, "trace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["fn_src", "example_tensors", "accum_type", "output_type", "tile_description", "epilogue_schedule", "name_to_buffer", "size_hint_fn", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.trace", "name": "trace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["fn_src", "example_tensors", "accum_type", "output_type", "tile_description", "epilogue_schedule", "name_to_buffer", "size_hint_fn", "kwargs"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.CutlassTensor", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.DataType", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.DataType", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.TileDescription", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.EpilogueScheduleType", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Buffer"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions.Expr", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trace", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "try_import_cutlass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_utils.try_import_cutlass", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cutlass_lib_extensions\\evt_extensions.py"}