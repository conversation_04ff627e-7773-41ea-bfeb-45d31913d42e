{".class": "MypyFile", "_fullname": "torch.onnx._internal.fx.dynamo_graph_extractor", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DynamoExport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal._exporter_legacy.FXGraphExtractor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport", "name": "DynamoExport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.dynamo_graph_extractor", "mro": ["torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport", "torch.onnx._internal._exporter_legacy.FXGraphExtractor", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "aten_graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "aten_graph"], "arg_types": ["torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamoExport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aten_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport.aten_graph", "name": "aten_graph", "type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}}}, "generate_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "options", "model", "model_args", "model_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport.generate_fx", "name": "generate_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "options", "model", "model_args", "model_kwargs"], "arg_types": ["torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport", "torch.onnx._internal._exporter_legacy.ResolvedExportOptions", {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_fx of DynamoExport", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_export_passes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "options", "original_model", "fx_module", "fx_module_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport.pre_export_passes", "name": "pre_export_passes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "options", "original_model", "fx_module", "fx_module_args"], "arg_types": ["torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport", "torch.onnx._internal._exporter_legacy.ResolvedExportOptions", {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pre_export_passes of DynamoExport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoExport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamoFlattenOutputStep": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.io_adapter.FlattenOutputStep"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep", "name": "DynamoFlattenOutputStep", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.dynamo_graph_extractor", "mro": ["torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep", "torch.onnx._internal.io_adapter.FlattenOutputStep", "torch.onnx._internal.io_adapter.OutputAdaptStep", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pytree_extension_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pytree_extension_context"], "arg_types": ["torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep", {".class": "UnionType", "items": ["torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamoFlattenOutputStep", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pytree_extension_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep._pytree_extension_context", "name": "_pytree_extension_context", "type": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext"}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_outputs", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_outputs", "model"], "arg_types": ["torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch.export.exported_program.ExportedProgram", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of DynamoFlattenOutputStep", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "_PyTreeExtensionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext", "name": "_PyTreeExtensionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.dynamo_graph_extractor", "mro": ["torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _PyTreeExtensionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext._extensions", "name": "_extensions", "type": {".class": "Instance", "args": ["builtins.type", {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.utils._pytree.FlattenFunc"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.utils._pytree.UnflattenFunc"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_register_huggingface_model_output_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext._register_huggingface_model_output_extension", "name": "_register_huggingface_model_output_extension", "type": null}}, "register_pytree_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "class_type", "flatten_func", "unflatten_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext.register_pytree_node", "name": "register_pytree_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "class_type", "flatten_func", "unflatten_func"], "arg_types": ["torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext", "builtins.type", {".class": "TypeAliasType", "args": [], "type_ref": "torch.utils._pytree.FlattenFunc"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.utils._pytree.UnflattenFunc"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_pytree_node of _PyTreeExtensionContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.dynamo_graph_extractor._PyTreeExtensionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_exporter_legacy": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal._exporter_legacy", "kind": "Gdef"}, "_wrap_model_with_output_adapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "output_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.dynamo_graph_extractor._wrap_model_with_output_adapter", "name": "_wrap_model_with_output_adapter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["model", "output_adapter"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "torch.onnx._internal.fx.dynamo_graph_extractor.DynamoFlattenOutputStep"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_model_with_output_adapter", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "io_adapter": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.io_adapter", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "torch_export": {".class": "SymbolTableNode", "cross_ref": "torch.export", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\dynamo_graph_extractor.py"}