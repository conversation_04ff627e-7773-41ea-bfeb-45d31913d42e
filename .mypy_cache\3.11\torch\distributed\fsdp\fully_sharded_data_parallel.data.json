{".class": "MypyFile", "_fullname": "torch.distributed.fsdp.fully_sharded_data_parallel", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ActivationWrapper": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms._checkpoint.checkpoint_wrapper.ActivationWrapper", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BackwardPrefetch": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.BackwardPrefetch", "kind": "Gdef", "module_public": false}, "CPUOffload": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.CPUOffload", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CustomPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.wrap.CustomPolicy", "kind": "Gdef", "module_public": false}, "DeviceMesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.DeviceMesh", "kind": "Gdef", "module_public": false}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "FLAT_PARAM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FLAT_PARAM", "name": "FLAT_PARAM", "type": "builtins.str"}}, "FSDP_PREFIX": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.FSDP_PREFIX", "kind": "Gdef", "module_public": false}, "FSDP_WRAPPED_MODULE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.FSDP_WRAPPED_MODULE", "kind": "Gdef", "module_public": false}, "FlatParamHandle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParamHandle", "kind": "Gdef", "module_public": false}, "FlatParameter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParameter", "kind": "Gdef", "module_public": false}, "FullOptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.FullOptimStateDictConfig", "kind": "Gdef", "module_public": false}, "FullStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.FullStateDictConfig", "kind": "Gdef", "module_public": false}, "FullyShardedDataParallel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "name": "FullyShardedDataParallel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp.fully_sharded_data_parallel", "mro": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", "torch.distributed._composable_state._State", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "module", "process_group", "sharding_strategy", "cpu_offload", "auto_wrap_policy", "backward_prefetch", "mixed_precision", "ignored_modules", "param_init_fn", "device_id", "sync_module_states", "forward_prefetch", "limit_all_gathers", "use_orig_params", "ignored_states", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "module", "process_group", "sharding_strategy", "cpu_offload", "auto_wrap_policy", "backward_prefetch", "mixed_precision", "ignored_modules", "param_init_fn", "device_id", "sync_module_states", "forward_prefetch", "limit_all_gathers", "use_orig_params", "ignored_states", "device_mesh"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "torch.nn.modules.module.Module", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._init_utils.ProcessGroupType"}, {".class": "UnionType", "items": ["torch.distributed.fsdp.api.ShardingStrategy", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.fsdp.api.CPUOffload", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch.distributed.fsdp.wrap.ModuleWrapPolicy", "torch.distributed.fsdp.wrap.CustomPolicy", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.fsdp.api.BackwardPrefetch", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.fsdp.api.MixedPrecision", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}, {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FullyShardedDataParallel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._apply", "name": "_apply", "type": null}}, "_assert_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._assert_state", "name": "_assert_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", {".class": "UnionType", "items": ["torch.distributed.fsdp._common_utils.TrainingState", {".class": "Instance", "args": ["torch.distributed.fsdp._common_utils.TrainingState"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_state of FullyShardedDataParallel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deregister_orig_params_ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._deregister_orig_params_ctx", "name": "_deregister_orig_params_ctx", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._deregister_orig_params_ctx", "name": "_deregister_orig_params_ctx", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deregister_orig_params_ctx of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_flat_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._flat_param", "name": "_flat_param", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flat_param of FullyShardedDataParallel", "ret_type": {".class": "UnionType", "items": ["torch.distributed.fsdp._flat_param.FlatParameter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._flat_param", "name": "_flat_param", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flat_param of FullyShardedDataParallel", "ret_type": {".class": "UnionType", "items": ["torch.distributed.fsdp._flat_param.FlatParameter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_fsdp_wrapped_module": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._fsdp_wrapped_module", "name": "_fsdp_wrapped_module", "type": "torch.nn.modules.module.Module"}}, "_has_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._has_params", "name": "_has_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_params of FullyShardedDataParallel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._has_params", "name": "_has_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_params of FullyShardedDataParallel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_is_root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._is_root", "name": "_is_root", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_is_using_optim_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["optim_input", "optim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._is_using_optim_input", "name": "_is_using_optim_input", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["optim_input", "optim"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_using_optim_input of FullyShardedDataParallel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._is_using_optim_input", "name": "_is_using_optim_input", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["optim_input", "optim"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_using_optim_input of FullyShardedDataParallel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_low_precision_hook_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._low_precision_hook_enabled", "name": "_low_precision_hook_enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_low_precision_hook_enabled of FullyShardedDataParallel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mixed_precision_enabled_for_buffers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._mixed_precision_enabled_for_buffers", "name": "_mixed_precision_enabled_for_buffers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mixed_precision_enabled_for_buffers of FullyShardedDataParallel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_optim_state_dict_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 5], "arg_names": ["model", "optim", "optim_state_dict", "optim_input", "rank0_only", "full_state_dict", "group", "cpu_offload", "_stacklevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._optim_state_dict_impl", "name": "_optim_state_dict_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 5], "arg_names": ["model", "optim", "optim_state_dict", "optim_input", "rank0_only", "full_state_dict", "group", "cpu_offload", "_stacklevel"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_optim_state_dict_impl of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._optim_state_dict_impl", "name": "_optim_state_dict_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 5], "arg_names": ["model", "optim", "optim_state_dict", "optim_input", "rank0_only", "full_state_dict", "group", "cpu_offload", "_stacklevel"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_optim_state_dict_impl of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_optim_state_dict_to_load_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["optim_state_dict", "model", "optim_input", "optim", "full_state_dict", "rank0_only", "is_named_optimizer", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._optim_state_dict_to_load_impl", "name": "_optim_state_dict_to_load_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["optim_state_dict", "model", "optim_input", "optim", "full_state_dict", "rank0_only", "is_named_optimizer", "group"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_optim_state_dict_to_load_impl of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._optim_state_dict_to_load_impl", "name": "_optim_state_dict_to_load_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["optim_state_dict", "model", "optim_input", "optim", "full_state_dict", "rank0_only", "is_named_optimizer", "group"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_optim_state_dict_to_load_impl of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_reset_lazy_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._reset_lazy_init", "name": "_reset_lazy_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reset_lazy_init of FullyShardedDataParallel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unshard": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "async_op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._unshard", "name": "_unshard", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "async_op"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unshard of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_use_training_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "training_state", "handle_training_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._use_training_state", "name": "_use_training_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "training_state", "handle_training_state"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "torch.distributed.fsdp._common_utils.TrainingState", "torch.distributed.fsdp._common_utils.HandleTrainingState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_use_training_state of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._use_training_state", "name": "_use_training_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "training_state", "handle_training_state"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "torch.distributed.fsdp._common_utils.TrainingState", "torch.distributed.fsdp._common_utils.HandleTrainingState"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_use_training_state of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_wait_unshard_streams_on_current_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._wait_unshard_streams_on_current_stream", "name": "_wait_unshard_streams_on_current_stream", "type": null}}, "_warn_legacy_optim_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["curr", "new", "stacklevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._warn_legacy_optim_state_dict", "name": "_warn_legacy_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["curr", "new", "stacklevel"], "arg_types": ["builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_legacy_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._warn_legacy_optim_state_dict", "name": "_warn_legacy_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["curr", "new", "stacklevel"], "arg_types": ["builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_legacy_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_warn_optim_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["optim_input", "stacklevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._warn_optim_input", "name": "_warn_optim_input", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["optim_input", "stacklevel"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_optim_input of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._warn_optim_input", "name": "_warn_optim_input", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["optim_input", "stacklevel"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_optim_input of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_zero_scalar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel._zero_scalar", "name": "_zero_scalar", "type": {".class": "NoneType"}}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of FullyShardedDataParallel", "ret_type": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_is_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.check_is_root", "name": "check_is_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_is_root of FullyShardedDataParallel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clip_grad_norm_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "max_norm", "norm_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.clip_grad_norm_", "name": "clip_grad_norm_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "max_norm", "norm_type"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clip_grad_norm_ of FullyShardedDataParallel", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.clip_grad_norm_", "name": "clip_grad_norm_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "max_norm", "norm_type"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clip_grad_norm_ of FullyShardedDataParallel", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "flatten_sharded_optim_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["sharded_optim_state_dict", "model", "optim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.flatten_sharded_optim_state_dict", "name": "flatten_sharded_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["sharded_optim_state_dict", "model", "optim"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flatten_sharded_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.flatten_sharded_optim_state_dict", "name": "flatten_sharded_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["sharded_optim_state_dict", "model", "optim"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flatten_sharded_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fsdp_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["module", "root_only"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.fsdp_modules", "name": "fsdp_modules", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["module", "root_only"], "arg_types": ["torch.nn.modules.module.Module", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fsdp_modules of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.fsdp_modules", "name": "fsdp_modules", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["module", "root_only"], "arg_types": ["torch.nn.modules.module.Module", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fsdp_modules of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "full_optim_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["model", "optim", "optim_input", "rank0_only", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.full_optim_state_dict", "name": "full_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["model", "optim", "optim_input", "rank0_only", "group"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.full_optim_state_dict", "name": "full_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["model", "optim", "optim_input", "rank0_only", "group"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_state_dict_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.get_state_dict_type", "name": "get_state_dict_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_state_dict_type of FullyShardedDataParallel", "ret_type": "torch.distributed.fsdp.api.StateDictSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.get_state_dict_type", "name": "get_state_dict_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_state_dict_type of FullyShardedDataParallel", "ret_type": "torch.distributed.fsdp.api.StateDictSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.module", "name": "module", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module of FullyShardedDataParallel", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.module", "name": "module", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module of FullyShardedDataParallel", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "named_buffers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.named_buffers", "name": "named_buffers", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "named_buffers of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "named_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.named_parameters", "name": "named_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "named_parameters of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "torch.nn.parameter.Parameter"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.no_sync", "name": "no_sync", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_sync of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.no_sync", "name": "no_sync", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_sync of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "optim_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.optim_state_dict", "name": "optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "group"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.optim_state_dict", "name": "optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "group"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "optim_state_dict_to_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "is_named_optimizer", "load_directly", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.optim_state_dict_to_load", "name": "optim_state_dict_to_load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "is_named_optimizer", "load_directly", "group"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optim_state_dict_to_load of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.optim_state_dict_to_load", "name": "optim_state_dict_to_load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["model", "optim", "optim_state_dict", "is_named_optimizer", "load_directly", "group"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optim_state_dict_to_load of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register_comm_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.register_comm_hook", "name": "register_comm_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "hook"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "builtins.object", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "callable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_comm_hook of FullyShardedDataParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rekey_optim_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["optim_state_dict", "optim_state_key_type", "model", "optim_input", "optim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.rekey_optim_state_dict", "name": "rekey_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["optim_state_dict", "optim_state_key_type", "model", "optim_input", "optim"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType", "torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rekey_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.rekey_optim_state_dict", "name": "rekey_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["optim_state_dict", "optim_state_key_type", "model", "optim_input", "optim"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType", "torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rekey_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "scatter_full_optim_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["full_optim_state_dict", "model", "optim_input", "optim", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.scatter_full_optim_state_dict", "name": "scatter_full_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["full_optim_state_dict", "model", "optim_input", "optim", "group"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scatter_full_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.scatter_full_optim_state_dict", "name": "scatter_full_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["full_optim_state_dict", "model", "optim_input", "optim", "group"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scatter_full_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_state_dict_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["module", "state_dict_type", "state_dict_config", "optim_state_dict_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.set_state_dict_type", "name": "set_state_dict_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["module", "state_dict_type", "state_dict_config", "optim_state_dict_config"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp.api.StateDictType", {".class": "UnionType", "items": ["torch.distributed.fsdp.api.StateDictConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.fsdp.api.OptimStateDictConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_state_dict_type of FullyShardedDataParallel", "ret_type": "torch.distributed.fsdp.api.StateDictSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.set_state_dict_type", "name": "set_state_dict_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["module", "state_dict_type", "state_dict_config", "optim_state_dict_config"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp.api.StateDictType", {".class": "UnionType", "items": ["torch.distributed.fsdp.api.StateDictConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.fsdp.api.OptimStateDictConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_state_dict_type of FullyShardedDataParallel", "ret_type": "torch.distributed.fsdp.api.StateDictSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shard_full_optim_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["full_optim_state_dict", "model", "optim_input", "optim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.shard_full_optim_state_dict", "name": "shard_full_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["full_optim_state_dict", "model", "optim_input", "optim"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shard_full_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.shard_full_optim_state_dict", "name": "shard_full_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["full_optim_state_dict", "model", "optim_input", "optim"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shard_full_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sharded_optim_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["model", "optim", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.sharded_optim_state_dict", "name": "sharded_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["model", "optim", "group"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sharded_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.sharded_optim_state_dict", "name": "sharded_optim_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["model", "optim", "group"], "arg_types": ["torch.nn.modules.module.Module", "torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sharded_optim_state_dict of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "state_dict_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["module", "state_dict_type", "state_dict_config", "optim_state_dict_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.state_dict_type", "name": "state_dict_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["module", "state_dict_type", "state_dict_config", "optim_state_dict_config"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp.api.StateDictType", {".class": "UnionType", "items": ["torch.distributed.fsdp.api.StateDictConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.fsdp.api.OptimStateDictConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state_dict_type of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.state_dict_type", "name": "state_dict_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["module", "state_dict_type", "state_dict_config", "optim_state_dict_config"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp.api.StateDictType", {".class": "UnionType", "items": ["torch.distributed.fsdp.api.StateDictConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.fsdp.api.OptimStateDictConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state_dict_type of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "summon_full_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["module", "recurse", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.summon_full_params", "name": "summon_full_params", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["module", "recurse", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.nn.modules.module.Module", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summon_full_params of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.summon_full_params", "name": "summon_full_params", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["module", "recurse", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "arg_types": ["torch.nn.modules.module.Module", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summon_full_params of FullyShardedDataParallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "HYBRID_SHARDING_STRATEGIES": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils.HYBRID_SHARDING_STRATEGIES", "kind": "Gdef", "module_public": false}, "HandleTrainingState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.HandleTrainingState", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "LOW_PRECISION_HOOKS": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms._comm_hooks.LOW_PRECISION_HOOKS", "kind": "Gdef", "module_public": false}, "LocalOptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.LocalOptimStateDictConfig", "kind": "Gdef", "module_public": false}, "LocalStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.LocalStateDictConfig", "kind": "Gdef", "module_public": false}, "MixedPrecision": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.MixedPrecision", "kind": "Gdef", "module_public": false}, "ModuleWrapPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.wrap.ModuleWrapPolicy", "kind": "Gdef", "module_public": false}, "OptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.OptimStateDictConfig", "kind": "Gdef", "module_public": false}, "OptimStateKeyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType", "name": "OptimStateKeyType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch.distributed.fsdp.fully_sharded_data_parallel", "mro": ["torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "PARAM_ID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType.PARAM_ID", "name": "PARAM_ID", "type": "enum.auto"}}, "PARAM_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType.PARAM_NAME", "name": "PARAM_NAME", "type": "enum.auto"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp.fully_sharded_data_parallel.OptimStateKeyType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ProcessGroupType": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils.ProcessGroupType", "kind": "Gdef", "module_public": false}, "ShardedOptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardedOptimStateDictConfig", "kind": "Gdef", "module_public": false}, "ShardedStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardedStateDictConfig", "kind": "Gdef", "module_public": false}, "ShardingStrategy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardingStrategy", "kind": "Gdef", "module_public": false}, "StateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictConfig", "kind": "Gdef", "module_public": false}, "StateDictSettings": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictSettings", "kind": "Gdef", "module_public": false}, "StateDictType": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictType", "kind": "Gdef", "module_public": false}, "TrainingState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.TrainingState", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnshardHandle@2045": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.UnshardHandle@2045", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.UnshardHandle@2045", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp.fully_sharded_data_parallel", "mro": ["torch.distributed.fsdp.fully_sharded_data_parallel.UnshardHandle@2045", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flat_param_handle", "unshard_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.UnshardHandle@2045.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flat_param_handle", "unshard_event"], "arg_types": ["torch.distributed.fsdp.fully_sharded_data_parallel.UnshardHandle@2045", {".class": "UnionType", "items": ["torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch._C.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnshardHandle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flat_param_handle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.UnshardHandle@2045._flat_param_handle", "name": "_flat_param_handle", "type": {".class": "UnionType", "items": ["torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_unshard_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.UnshardHandle@2045._unshard_event", "name": "_unshard_event", "type": "torch._C.Event"}}, "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "<EMAIL>", "name": "wait", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp.fully_sharded_data_parallel.UnshardHandle@2045", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CHECKPOINT_WRAPPED_MODULE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms._checkpoint.checkpoint_wrapper._CHECKPOINT_WRAPPED_MODULE", "kind": "Gdef", "module_public": false}, "_FSDPState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._FSDPState", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_annotate_modules_for_dynamo": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._dynamo_utils._annotate_modules_for_dynamo", "kind": "Gdef", "module_public": false}, "_auto_wrap": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._wrap_utils._auto_wrap", "kind": "Gdef", "module_public": false}, "_check_orig_params_flattened": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._check_orig_params_flattened", "kind": "Gdef", "module_public": false}, "_deregister_orig_params": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._unshard_param_utils._deregister_orig_params", "kind": "Gdef", "module_public": false}, "_flatten_optim_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._flatten_optim_state_dict", "kind": "Gdef", "module_public": false}, "_get_fqn_to_param": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel._get_fqn_to_param", "name": "_get_fqn_to_param", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_fqn_to_param", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_fsdp_root_states": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._get_fsdp_root_states", "kind": "Gdef", "module_public": false}, "_get_grad_norm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["params", "norm_type", "zero", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel._get_grad_norm", "name": "_get_grad_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["params", "norm_type", "zero", "device"], "arg_types": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.float", "torch._tensor.Tensor", "torch._C.device"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_grad_norm", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_param_id_to_param_from_optim_input": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._get_param_id_to_param_from_optim_input", "kind": "Gdef", "module_public": false}, "_get_param_key_to_param": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._get_param_key_to_param", "kind": "Gdef", "module_public": false}, "_get_param_to_fqn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp.fully_sharded_data_parallel._get_param_to_fqn", "name": "_get_param_to_fqn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_param_to_fqn", "ret_type": {".class": "Instance", "args": ["torch.nn.parameter.Parameter", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_param_to_fqns": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._get_param_to_fqns", "kind": "Gdef", "module_public": false}, "_get_param_to_param_id_from_optim_input": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._get_param_to_param_id_from_optim_input", "kind": "Gdef", "module_public": false}, "_get_param_to_param_key": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._get_param_to_param_key", "kind": "Gdef", "module_public": false}, "_init_buffer_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_buffer_state", "kind": "Gdef", "module_public": false}, "_init_core_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_core_state", "kind": "Gdef", "module_public": false}, "_init_device_handle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_device_handle", "kind": "Gdef", "module_public": false}, "_init_extension": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_extension", "kind": "Gdef", "module_public": false}, "_init_ignored_module_states": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_ignored_module_states", "kind": "Gdef", "module_public": false}, "_init_param_handle_from_module": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_param_handle_from_module", "kind": "Gdef", "module_public": false}, "_init_prefetching_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_prefetching_state", "kind": "Gdef", "module_public": false}, "_init_process_group_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_process_group_state", "kind": "Gdef", "module_public": false}, "_init_runtime_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_runtime_state", "kind": "Gdef", "module_public": false}, "_init_state_dict_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils._init_state_dict_state", "kind": "Gdef", "module_public": false}, "_is_fsdp_root": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._is_fsdp_root", "kind": "Gdef", "module_public": false}, "_lazy_init": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._lazy_init", "kind": "Gdef", "module_public": false}, "_optim_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._optim_state_dict", "kind": "Gdef", "module_public": false}, "_p_assert": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._p_assert", "kind": "Gdef", "module_public": false}, "_post_forward": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._post_forward", "kind": "Gdef", "module_public": false}, "_post_forward_reshard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._post_forward_reshard", "kind": "Gdef", "module_public": false}, "_pre_forward": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._pre_forward", "kind": "Gdef", "module_public": false}, "_pre_forward_unshard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._pre_forward_unshard", "kind": "Gdef", "module_public": false}, "_register_all_state_dict_hooks": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._state_dict_utils._register_all_state_dict_hooks", "kind": "Gdef", "module_public": false}, "_register_flat_param": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._unshard_param_utils._register_flat_param", "kind": "Gdef", "module_public": false}, "_register_orig_params": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._unshard_param_utils._register_orig_params", "kind": "Gdef", "module_public": false}, "_rekey_sharded_optim_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._rekey_sharded_optim_state_dict", "kind": "Gdef", "module_public": false}, "_root_pre_forward": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._root_pre_forward", "kind": "Gdef", "module_public": false}, "_set_optim_use_dtensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._optim_utils._set_optim_use_dtensor", "kind": "Gdef", "module_public": false}, "_unshard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._unshard", "kind": "Gdef", "module_public": false}, "_unshard_params": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._unshard_param_utils._unshard_params", "kind": "Gdef", "module_public": false}, "_unshard_params_for_summon": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._unshard_param_utils._unshard_params_for_summon", "kind": "Gdef", "module_public": false}, "_wait_for_computation_stream": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._wait_for_computation_stream", "kind": "Gdef", "module_public": false}, "auto": {".class": "SymbolTableNode", "cross_ref": "enum.auto", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef", "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef", "module_public": false}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef", "module_public": false}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef", "module_public": false}, "traversal_utils": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._traversal_utils", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\fully_sharded_data_parallel.py"}