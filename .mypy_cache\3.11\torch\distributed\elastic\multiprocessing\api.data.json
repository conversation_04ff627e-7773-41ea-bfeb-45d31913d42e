{".class": "MypyFile", "_fullname": "torch.distributed.elastic.multiprocessing.api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DefaultLogsSpecs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.multiprocessing.api.LogsSpecs"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", "name": "DefaultLogsSpecs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", "torch.distributed.elastic.multiprocessing.api.LogsSpecs", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of DefaultLogsSpecs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "log_dir", "redirects", "tee", "local_ranks_filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "log_dir", "redirects", "tee", "local_ranks_filter"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DefaultLogsSpecs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of DefaultLogsSpecs", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_log_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "log_dir", "rdzv_run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs._make_log_dir", "name": "_make_log_dir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "log_dir", "rdzv_run_id"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_log_dir of DefaultLogsSpecs", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run_log_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs._run_log_dir", "name": "_run_log_dir", "type": {".class": "NoneType"}}}, "reify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "envs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs.reify", "name": "reify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "envs"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reify of DefaultLogsSpecs", "ret_type": "torch.distributed.elastic.multiprocessing.api.LogsDest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "root_log_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs.root_log_dir", "name": "root_log_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_log_dir of DefaultLogsSpecs", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs.root_log_dir", "name": "root_log_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_log_dir of DefaultLogsSpecs", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrameType": {".class": "SymbolTableNode", "cross_ref": "types.FrameType", "kind": "Gdef", "module_public": false}, "IS_MACOS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.IS_MACOS", "name": "IS_MACOS", "type": "builtins.bool"}}, "IS_WINDOWS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.IS_WINDOWS", "name": "IS_WINDOWS", "type": "builtins.bool"}}, "IntFlag": {".class": "SymbolTableNode", "cross_ref": "enum.IntFlag", "kind": "Gdef", "module_public": false}, "LogsDest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest", "name": "LogsDest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 189, "name": "stdouts", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 190, "name": "stderrs", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 191, "name": "tee_stdouts", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 192, "name": "tee_stderrs", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 193, "name": "error_files", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.LogsDest", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "stdouts", "stderrs", "tee_stdouts", "tee_stderrs", "error_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "stdouts", "stderrs", "tee_stdouts", "tee_stderrs", "error_files"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.LogsDest", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogsDest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "stdouts"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stderrs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tee_stdouts"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tee_stderrs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error_files"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["stdouts", "stderrs", "tee_stdouts", "tee_stderrs", "error_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["stdouts", "stderrs", "tee_stdouts", "tee_stderrs", "error_files"], "arg_types": [{".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LogsDest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["stdouts", "stderrs", "tee_stdouts", "tee_stderrs", "error_files"], "arg_types": [{".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LogsDest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "error_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.error_files", "name": "error_files", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "stderrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.stderrs", "name": "stderrs", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "stdouts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.stdouts", "name": "stdouts", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tee_stderrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.tee_stderrs", "name": "tee_stderrs", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tee_stdouts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.tee_stdouts", "name": "tee_stdouts", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.LogsDest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.LogsDest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogsSpecs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["reify", 1], ["root_log_dir", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs", "name": "LogsSpecs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.LogsSpecs", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "log_dir", "redirects", "tee", "local_ranks_filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "log_dir", "redirects", "tee", "local_ranks_filter"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.LogsSpecs", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogsSpecs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_local_ranks_filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs._local_ranks_filter", "name": "_local_ranks_filter", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_redirects": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs._redirects", "name": "_redirects", "type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "_root_log_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs._root_log_dir", "name": "_root_log_dir", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_tee": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs._tee", "name": "_tee", "type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "reify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "envs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs.reify", "name": "reify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "envs"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.LogsSpecs", {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reify of LogsSpecs", "ret_type": "torch.distributed.elastic.multiprocessing.api.LogsDest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs.reify", "name": "reify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "envs"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.LogsSpecs", {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reify of LogsSpecs", "ret_type": "torch.distributed.elastic.multiprocessing.api.LogsDest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "root_log_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs.root_log_dir", "name": "root_log_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.LogsSpecs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_log_dir of LogsSpecs", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs.root_log_dir", "name": "root_log_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.LogsSpecs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_log_dir of LogsSpecs", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.LogsSpecs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.LogsSpecs", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultiprocessContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.multiprocessing.api.PContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext", "name": "MultiprocessContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.MultiprocessContext", "torch.distributed.elastic.multiprocessing.api.PContext", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "entrypoint", "args", "envs", "start_method", "logs_specs", "log_line_prefixes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "entrypoint", "args", "envs", "start_method", "logs_specs", "log_line_prefixes"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.MultiprocessContext", "builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "torch.distributed.elastic.multiprocessing.api.LogsSpecs", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultiprocessContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "death_sig", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext._close", "name": "_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "death_sig", "timeout"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.MultiprocessContext", "signal.Signals", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_close of MultiprocessContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext._is_done", "name": "_is_done", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.MultiprocessContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_done of MultiprocessContext", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext._pc", "name": "_pc", "type": {".class": "UnionType", "items": ["torch.multiprocessing.spawn.ProcessContext", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext._poll", "name": "_poll", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.MultiprocessContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_poll of MultiprocessContext", "ret_type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.RunProcsResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ret_vals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext._ret_vals", "name": "_ret_vals", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.queues.SimpleQueue"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_return_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext._return_values", "name": "_return_values", "type": {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext._start", "name": "_start", "type": null}}, "_worker_finished_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext._worker_finished_event", "name": "_worker_finished_event", "type": "multiprocessing.synchronize.Event"}}, "pids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext.pids", "name": "pids", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.MultiprocessContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pids of MultiprocessContext", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext.start_method", "name": "start_method", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_close", 1], ["_poll", 1], ["_start", 1], ["pids", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.PContext", "name": "PContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.PContext", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "entrypoint", "args", "envs", "logs_specs", "log_line_prefixes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "entrypoint", "args", "envs", "logs_specs", "log_line_prefixes"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext", "builtins.str", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.distributed.elastic.multiprocessing.api.LogsSpecs", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1], "arg_names": ["self", "death_sig", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext._close", "name": "_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "death_sig", "timeout"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext", "signal.Signals", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_close of PContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext._close", "name": "_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "death_sig", "timeout"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext", "signal.Signals", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_close of PContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext._poll", "name": "_poll", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_poll of PContext", "ret_type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.RunProcsResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext._poll", "name": "_poll", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_poll of PContext", "ret_type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.RunProcsResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext._start", "name": "_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_start of PContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext._start", "name": "_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_start of PContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_stderr_tail": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext._stderr_tail", "name": "_stderr_tail", "type": "torch.distributed.elastic.multiprocessing.tail_log.TailLog"}}, "_stdout_tail": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext._stdout_tail", "name": "_stdout_tail", "type": "torch.distributed.elastic.multiprocessing.tail_log.TailLog"}}, "args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.args", "name": "args", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "death_sig", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "death_sig", "timeout"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext", {".class": "UnionType", "items": ["signal.Signals", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of PContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "entrypoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.entrypoint", "name": "entrypoint", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "uses_pep604_syntax": false}}}, "envs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.envs", "name": "envs", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "error_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.error_files", "name": "error_files", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.name", "name": "name", "type": "builtins.str"}}, "nprocs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.nprocs", "name": "nprocs", "type": "builtins.int"}}, "pids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.pids", "name": "pids", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pids of PContext", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.pids", "name": "pids", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pids of PContext", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.start", "name": "start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start of PContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stderrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.stderrs", "name": "stderrs", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "stdouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.stdouts", "name": "stdouts", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timeout", "period"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "timeout", "period"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.PContext", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of PContext", "ret_type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.RunProcsResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.PContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.PContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProcessFailure": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.errors.ProcessFailure", "kind": "Gdef", "module_public": false}, "RunProcsResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult", "name": "RunProcsResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 421, "name": "return_values", "type": {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 422, "name": "failures", "type": {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.errors.ProcessFailure"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 423, "name": "stdouts", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 424, "name": "stderrs", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.RunProcsResult", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "return_values", "failures", "stdouts", "stderrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "return_values", "failures", "stdouts", "stderrs"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.RunProcsResult", {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.errors.ProcessFailure"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RunProcsResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "return_values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failures"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stdouts"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stderrs"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["return_values", "failures", "stdouts", "stderrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["return_values", "failures", "stdouts", "stderrs"], "arg_types": [{".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.errors.ProcessFailure"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of RunProcsResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["return_values", "failures", "stdouts", "stderrs"], "arg_types": [{".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.errors.ProcessFailure"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of RunProcsResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "failures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.failures", "name": "failures", "type": {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.errors.ProcessFailure"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "is_failed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.is_failed", "name": "is_failed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.RunProcsResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_failed of RunProcsResult", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.return_values", "name": "return_values", "type": {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "stderrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.stderrs", "name": "stderrs", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "stdouts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.stdouts", "name": "stdouts", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.RunProcsResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.RunProcsResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SignalException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.SignalException", "name": "SignalException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.SignalException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.SignalException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "sigval"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.SignalException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "sigval"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.SignalException", "builtins.str", "signal.Signals"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SignalException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sigval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.SignalException.sigval", "name": "sigval", "type": "signal.Signals"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.SignalException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.SignalException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Std": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.Std", "name": "Std", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch.distributed.elastic.multiprocessing.api.Std", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.Std", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.Std.ALL", "name": "ALL", "type": "builtins.int"}}, "ERR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.Std.ERR", "name": "ERR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.Std.NONE", "name": "NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "OUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.Std.OUT", "name": "OUT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "from_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "vm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.distributed.elastic.multiprocessing.api.Std.from_str", "name": "from_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "vm"], "arg_types": [{".class": "TypeType", "item": "torch.distributed.elastic.multiprocessing.api.Std"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_str of Std", "ret_type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.Std.from_str", "name": "from_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "vm"], "arg_types": [{".class": "TypeType", "item": "torch.distributed.elastic.multiprocessing.api.Std"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_str of Std", "ret_type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.Std.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.Std", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SubprocessContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.multiprocessing.api.PContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext", "name": "SubprocessContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.multiprocessing.api", "mro": ["torch.distributed.elastic.multiprocessing.api.SubprocessContext", "torch.distributed.elastic.multiprocessing.api.PContext", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "entrypoint", "args", "envs", "logs_specs", "log_line_prefixes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "entrypoint", "args", "envs", "logs_specs", "log_line_prefixes"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.SubprocessContext", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.distributed.elastic.multiprocessing.api.LogsSpecs", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SubprocessContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "death_sig", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext._close", "name": "_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "death_sig", "timeout"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.SubprocessContext", "signal.Signals", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_close of SubprocessContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_failures": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext._failures", "name": "_failures", "type": {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.errors.ProcessFailure"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext._poll", "name": "_poll", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.SubprocessContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_poll of SubprocessContext", "ret_type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.RunProcsResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_running_local_ranks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext._running_local_ranks", "name": "_running_local_ranks", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext._start", "name": "_start", "type": null}}, "pids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext.pids", "name": "pids", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.multiprocessing.api.SubprocessContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pids of SubprocessContext", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subprocess_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext.subprocess_handlers", "name": "subprocess_handlers", "type": {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.subprocess_handler.subprocess_handler.SubprocessHandler"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.multiprocessing.api.SubprocessContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.multiprocessing.api.SubprocessContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SubprocessHandler": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.subprocess_handler.subprocess_handler.SubprocessHandler", "kind": "Gdef", "module_public": false}, "TailLog": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.tail_log.TailLog", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_MAPPING_REGEX": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api._MAPPING_REGEX", "name": "_MAPPING_REGEX", "type": "builtins.str"}}, "_VALUE_REGEX": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api._VALUE_REGEX", "name": "_VALUE_REGEX", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.api.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_default_signal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api._get_default_signal", "name": "_get_default_signal", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_default_signal", "ret_type": "signal.Signals", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_kill_signal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api._get_kill_signal", "name": "_get_kill_signal", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_kill_signal", "ret_type": "signal.Signals", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_terminate_process_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["signum", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api._terminate_process_handler", "name": "_terminate_process_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["signum", "frame"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["types.FrameType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_terminate_process_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_full_rank": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["d", "nprocs", "what"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api._validate_full_rank", "name": "_validate_full_rank", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["d", "nprocs", "what"], "arg_types": [{".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_full_rank", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["local_rank", "fn", "args", "envs", "stdout_redirects", "stderr_redirects", "ret_vals", "queue_finished_reading_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api._wrap", "name": "_wrap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["local_rank", "fn", "args", "envs", "stdout_redirects", "stderr_redirects", "ret_vals", "queue_finished_reading_event"], "arg_types": ["builtins.int", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.SimpleQueue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "multiprocessing.synchronize.Event"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef", "module_public": false}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef", "module_public": false}, "get_std_cm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["std_rd", "redirect_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.get_std_cm", "name": "get_std_cm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["std_rd", "redirect_fn"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_std_cm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_subprocess_handler": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.subprocess_handler.handlers.get_subprocess_handler", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.api.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "mp": {".class": "SymbolTableNode", "cross_ref": "torch.multiprocessing", "kind": "Gdef", "module_public": false}, "nullcontext": {".class": "SymbolTableNode", "cross_ref": "contextlib.nullcontext", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "record": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.errors.record", "kind": "Gdef", "module_public": false}, "redirect_stderr": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.redirects.redirect_stderr", "kind": "Gdef", "module_public": false}, "redirect_stdout": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.redirects.redirect_stdout", "kind": "Gdef", "module_public": false}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef", "module_public": false}, "signal": {".class": "SymbolTableNode", "cross_ref": "signal", "kind": "Gdef", "module_public": false}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef", "module_public": false}, "synchronize": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.synchronize", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef", "module_public": false}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "to_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["val_or_map", "local_world_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.api.to_map", "name": "to_map", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["val_or_map", "local_world_size"], "arg_types": [{".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.Std", {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_map", "ret_type": {".class": "Instance", "args": ["builtins.int", "torch.distributed.elastic.multiprocessing.api.Std"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\elastic\\multiprocessing\\api.py"}