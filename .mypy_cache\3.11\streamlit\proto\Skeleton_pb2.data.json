{".class": "MypyFile", "_fullname": "streamlit.proto.Skeleton_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Skeleton_pb2.google", "source_any": null, "type_of_any": 3}}}, "Skeleton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Skeleton_pb2.Skeleton", "name": "Skeleton", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Skeleton_pb2", "mro": ["streamlit.proto.Skeleton_pb2.Skeleton", "builtins.object"], "names": {".class": "SymbolTable", "APP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.APP", "name": "APP", "type": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Skeleton_pb2.Skeleton", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "style"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "style"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Clear<PERSON>ield of Skeleton", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Skeleton_pb2.google", "source_any": null, "type_of_any": 3}}}, "ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.ELEMENT", "name": "ELEMENT", "type": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType"}}, "HEIGHT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.HEIGHT_FIELD_NUMBER", "name": "HEIGHT_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Skeleton_pb2.Skeleton", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of Skeleton", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "STYLE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.STYLE_FIELD_NUMBER", "name": "STYLE_FIELD_NUMBER", "type": "builtins.int"}}, "SkeletonStyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.SkeletonStyle", "name": "SkeletonStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.SkeletonStyle", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.Skeleton_pb2", "mro": ["streamlit.proto.Skeleton_pb2.Skeleton.SkeletonStyle", "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.SkeletonStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Skeleton_pb2.Skeleton.SkeletonStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.Skeleton_pb2.Skeleton", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_height"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of Skeleton", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_SkeletonStyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle", "name": "_SkeletonStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Skeleton_pb2", "mro": ["streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.V", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Skeleton_pb2", "mro": ["streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SkeletonStyleEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper", "name": "_SkeletonStyleEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Skeleton_pb2", "mro": ["streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "APP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper.APP", "name": "APP", "type": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Skeleton_pb2.google", "source_any": null, "type_of_any": 3}}}, "ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper.ELEMENT", "name": "ELEMENT", "type": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyleEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "style", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "style", "height"], "arg_types": ["streamlit.proto.Skeleton_pb2.Skeleton", "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Skeleton", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.height", "name": "height", "type": "builtins.int"}}, "style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.style", "name": "style", "type": "streamlit.proto.Skeleton_pb2.Skeleton._SkeletonStyle.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Skeleton_pb2.Skeleton.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Skeleton_pb2.Skeleton", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Skeleton_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___Skeleton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.Skeleton_pb2.global___Skeleton", "line": 71, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Skeleton_pb2.Skeleton"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.Skeleton_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Skeleton_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\Skeleton_pb2.pyi"}