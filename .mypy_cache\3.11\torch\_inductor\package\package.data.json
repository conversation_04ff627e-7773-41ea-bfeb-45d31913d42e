{".class": "MypyFile", "_fullname": "torch._inductor.package.package", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AOTICompiledModel": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive._package.AOTICompiledModel", "kind": "Gdef"}, "AOTI_FILES": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive._package.AOTI_FILES", "kind": "Gdef"}, "BuildOptionsBase": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpp_builder.BuildOptionsBase", "kind": "Gdef"}, "CppBuilder": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.cpp_builder.CppBuilder", "kind": "Gdef"}, "FileLike": {".class": "SymbolTableNode", "cross_ref": "torch.types.FileLike", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.package.package.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.package.package.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.package.package.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.package.package.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.package.package.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.package.package.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "compile_so": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["aoti_dir", "aoti_files", "so_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.package.package.compile_so", "name": "compile_so", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["aoti_dir", "aoti_files", "so_path"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_so", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_package": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["path", "model_name", "run_single_threaded", "num_runners", "device_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.package.package.load_package", "name": "load_package", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["path", "model_name", "run_single_threaded", "num_runners", "device_index"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.types.FileLike"}, "builtins.str", "builtins.bool", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_package", "ret_type": "torch.export.pt2_archive._package.AOTICompiledModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_pt2": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive._package.load_pt2", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.package.package.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "package_aoti": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["archive_file", "aoti_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.package.package.package_aoti", "name": "package_aoti", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["archive_file", "aoti_files"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.types.FileLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.export.pt2_archive._package.AOTI_FILES"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "package_aoti", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.types.FileLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "package_pt2": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive._package.package_pt2", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\package\\package.py"}