{"data_mtime": 1755649449, "dep_lines": [11, 11, 8, 9, 7, 2, 3, 4, 6, 249, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 5, 10, 10, 10, 5, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["torch.distributed.algorithms.ddp_comm_hooks.default_hooks", "torch.distributed.algorithms.ddp_comm_hooks", "torch.distributed.distributed_c10d", "torch.utils._typing_utils", "torch.distributed", "logging", "math", "collections", "torch", "numpy", "builtins", "warnings", "operator", "itertools", "pprint", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "typing", "_frozen_importlib", "_typeshed", "abc", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence", "numpy.random", "numpy.random.bit_generator", "numpy.random.mtrand", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.futures", "contextlib"], "hash": "3d179fcf5c0ce09131f895b4d4cfbe5d9d2d335e", "id": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook", "ignore_all": true, "interface_hash": "77276a9e977915146e2b17843f321404c074ed4a", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\algorithms\\ddp_comm_hooks\\powerSGD_hook.py", "plugin_data": null, "size": 41274, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}