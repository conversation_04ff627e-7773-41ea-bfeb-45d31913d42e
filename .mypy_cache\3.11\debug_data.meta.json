{"data_mtime": 1755653005, "dep_lines": [1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["numpy", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "datetime", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "numpy.lib", "numpy.lib._npyio_impl", "os", "torch._C", "torch._tensor", "torch.types", "types", "typing"], "hash": "ad81ad0fd11e139914a6960b1a8bd4964049568e", "id": "debug_data", "ignore_all": false, "interface_hash": "d1967d640f5fb72b602d5289f30bcad39ee9a425", "mtime": 1755652994, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\sub-station-predictive -maint\\debug_data.py", "plugin_data": null, "size": 1505, "suppressed": [], "version_id": "1.15.0"}