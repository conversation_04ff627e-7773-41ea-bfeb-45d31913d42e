{"data_mtime": 1755649448, "dep_lines": [17, 3, 5, 6, 13, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 15, 1], "dep_prios": [25, 5, 10, 5, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 20, 25, 20], "dependencies": ["torch._ops", "__future__", "dataclasses", "typing", "types", "torch", "builtins", "collections", "warnings", "itertools", "pprint", "inspect", "string", "copy", "os", "logging", "functools", "sys", "json", "traceback", "re", "html", "operator", "math", "torch.distributed._functional_collectives", "torch.nn", "multiprocessing.reduction", "_frozen_importlib", "abc", "contextlib"], "hash": "b494617b947e4db5661ad65025c87c2d163b978e", "id": "torch.onnx._internal.fx.registration", "ignore_all": true, "interface_hash": "809fa58dd1c5582bab0e3dfa90a084f4c597a951", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\registration.py", "plugin_data": null, "size": 3071, "suppressed": ["onnxscript", "traitlets.utils.warnings"], "version_id": "1.15.0"}