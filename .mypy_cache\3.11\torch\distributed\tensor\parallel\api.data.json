{".class": "MypyFile", "_fullname": "torch.distributed.tensor.parallel.api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DeviceMesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.DeviceMesh", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ParallelStyle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.style.ParallelStyle", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.tensor.parallel.api.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.api.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.api.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.api.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.api.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.api.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.api.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_mesh_resources": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh._mesh_resources", "kind": "Gdef", "module_public": false}, "_validate_tp_mesh_dim": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel._utils._validate_tp_mesh_dim", "kind": "Gdef", "module_public": false}, "fnmatch": {".class": "SymbolTableNode", "cross_ref": "fnmatch.fnmatch", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "parallelize_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["module", "device_mesh", "parallelize_plan", "src_data_rank"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.api.parallelize_module", "name": "parallelize_module", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["module", "device_mesh", "parallelize_plan", "src_data_rank"], "arg_types": ["torch.nn.modules.module.Module", {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.tensor.parallel.style.ParallelStyle", {".class": "Instance", "args": ["builtins.str", "torch.distributed.tensor.parallel.style.ParallelStyle"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parallelize_module", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\api.py"}