{".class": "MypyFile", "_fullname": "torch.onnx._internal.exporter._building", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllowedArgType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.exporter._building.AllowedArgType", "line": 37, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.ValidAttributeType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.ValidAttributeType"}], "uses_pep604_syntax": false}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "OpRecorder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.exporter._building.OpRecorder", "name": "OpRecorder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "torch.onnx._internal.exporter._building.OpRecorder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.exporter._building", "mro": ["torch.onnx._internal.exporter._building.OpRecorder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "opset", "constant_farm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building.OpRecorder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "opset", "constant_farm"], "arg_types": ["torch.onnx._internal.exporter._building.OpRecorder", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OpRecorder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_call_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op_signature", "named_inputs", "named_attrs", "num_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building.OpRecorder._call_op", "name": "_call_op", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op_signature", "named_inputs", "named_attrs", "num_outputs"], "arg_types": ["torch.onnx._internal.exporter._building.OpRecorder", "torch.onnx._internal.exporter._schemas.OpSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.ValidAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_call_op of OpRecorder", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.exporter._tensors.SymbolicTensor"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "constant_farm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.exporter._building.OpRecorder.constant_farm", "name": "constant_farm", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "schema", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building.OpRecorder.eval", "name": "eval", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "schema", "args", "kwargs"], "arg_types": ["torch.onnx._internal.exporter._building.OpRecorder", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnx", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eval of OpRecorder", "ret_type": {".class": "UnionType", "items": ["torch.onnx._internal.exporter._tensors.SymbolicTensor", {".class": "Instance", "args": ["torch.onnx._internal.exporter._tensors.SymbolicTensor"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eval_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "function", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building.OpRecorder.eval_function", "name": "eval_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "function", "args", "kwargs"], "arg_types": ["torch.onnx._internal.exporter._building.OpRecorder", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eval_function of OpRecorder", "ret_type": {".class": "UnionType", "items": ["torch.onnx._internal.exporter._tensors.SymbolicTensor", {".class": "Instance", "args": ["torch.onnx._internal.exporter._tensors.SymbolicTensor"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", "builtins.int"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "functions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._building.OpRecorder.functions", "name": "functions", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._building.OpRecorder.nodes", "name": "nodes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "opset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.exporter._building.OpRecorder.opset", "name": "opset", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.exporter._building.OpRecorder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.exporter._building.OpRecorder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "ValidAttributeType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.exporter._building.ValidAttributeType", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.float", "builtins.bool", "builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._building.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._building.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._building.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._building.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._building.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._building.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_allowed_types_are_sequence_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["allowed_types"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._allowed_types_are_sequence_types", "name": "_allowed_types_are_sequence_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["allowed_types"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_allowed_types_are_sequence_types", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_construct_named_inputs_and_attrs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["signature", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._construct_named_inputs_and_attrs", "name": "_construct_named_inputs_and_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["signature", "args", "kwargs"], "arg_types": ["torch.onnx._internal.exporter._schemas.OpSignature", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_construct_named_inputs_and_attrs", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.ValidAttributeType"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_construct_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["signature", "named_inputs", "named_attrs", "opset", "num_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._construct_node", "name": "_construct_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["signature", "named_inputs", "named_attrs", "opset", "num_outputs"], "arg_types": ["torch.onnx._internal.exporter._schemas.OpSignature", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.ValidAttributeType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_construct_node", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_input_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["param", "arg", "type_binding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._determine_input_dtype", "name": "_determine_input_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["param", "arg", "type_binding"], "arg_types": ["torch.onnx._internal.exporter._schemas.Parameter", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}, {".class": "Instance", "args": ["torch.onnx._internal.exporter._schemas.TypeConstraintParam", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_input_dtype", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_output_number": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["signature", "named_attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._determine_output_number", "name": "_determine_output_number", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["signature", "named_attrs"], "arg_types": ["torch.onnx._internal.exporter._schemas.OpSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.ValidAttributeType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_output_number", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_errors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._errors", "kind": "Gdef"}, "_get_or_create_constant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["constant_farm", "arg", "dtype", "opset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._get_or_create_constant", "name": "_get_or_create_constant", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["constant_farm", "arg", "dtype", "opset"], "arg_types": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bool", "builtins.int", "builtins.float", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int", "builtins.float", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_or_create_constant", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_python_constants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["signature", "named_inputs", "type_binding", "constant_farm", "opset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._process_python_constants", "name": "_process_python_constants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["signature", "named_inputs", "type_binding", "constant_farm", "opset"], "arg_types": ["torch.onnx._internal.exporter._schemas.OpSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["torch.onnx._internal.exporter._schemas.TypeConstraintParam", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bool", "builtins.int", "builtins.float", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_python_constants", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_python_sequences": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["signature", "named_inputs", "type_binding", "constant_farm", "opset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._process_python_sequences", "name": "_process_python_sequences", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["signature", "named_inputs", "type_binding", "constant_farm", "opset"], "arg_types": ["torch.onnx._internal.exporter._schemas.OpSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["torch.onnx._internal.exporter._schemas.TypeConstraintParam", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bool", "builtins.int", "builtins.float", "builtins.str", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_python_sequences", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reshape_to_1d_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["opset", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._reshape_to_1d_tensor", "name": "_reshape_to_1d_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["opset", "arg"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reshape_to_1d_tensor", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_parameter_dtypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["signature", "named_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._building._resolve_parameter_dtypes", "name": "_resolve_parameter_dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["signature", "named_inputs"], "arg_types": ["torch.onnx._internal.exporter._schemas.OpSignature", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.exporter._building.AllowedArgType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_parameter_dtypes", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.exporter._schemas.TypeConstraintParam", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_schemas": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._schemas", "kind": "Gdef"}, "_tensors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._tensors", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "evaluator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._building.evaluator", "name": "evaluator", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.evaluator", "source_any": null, "type_of_any": 3}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._building.ir", "name": "ir", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir", "source_any": null, "type_of_any": 3}}}, "ir_convenience": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._building.ir_convenience", "name": "ir_convenience", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.ir_convenience", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._building.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "onnx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._building.onnx", "name": "onnx", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnx", "source_any": null, "type_of_any": 3}}}, "onnxscript": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.exporter._building.onnxscript", "name": "onnxscript", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.exporter._building.onnxscript", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_building.py"}