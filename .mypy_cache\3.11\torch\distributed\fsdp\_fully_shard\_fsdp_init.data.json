{".class": "MypyFile", "_fullname": "torch.distributed.fsdp._fully_shard._fsdp_init", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.DTensor", "kind": "Gdef"}, "DeviceMesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.DeviceMesh", "kind": "Gdef"}, "FSDPMeshInfo": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_common.FSDPMeshInfo", "kind": "Gdef"}, "HSDPMeshInfo": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_common.HSDPMeshInfo", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_adjust_managed_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["modules", "ignored_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._adjust_managed_modules", "name": "_adjust_managed_modules", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["modules", "ignored_params"], "arg_types": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_managed_modules", "ret_type": {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_device_from_mesh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._get_device_from_mesh", "name": "_get_device_from_mesh", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mesh"], "arg_types": ["torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_device_from_mesh", "ret_type": "torch._C.device", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_device_handle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh._get_device_handle", "kind": "Gdef"}, "_get_managed_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["root_modules", "ignored_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._get_managed_modules", "name": "_get_managed_modules", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["root_modules", "ignored_params"], "arg_types": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_managed_modules", "ret_type": {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_managed_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["modules", "ignored_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._get_managed_states", "name": "_get_managed_states", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["modules", "ignored_params"], "arg_types": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_managed_states", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_module_fsdp_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_state._get_module_fsdp_state", "kind": "Gdef"}, "_get_post_forward_mesh_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["reshard_after_forward", "mesh_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._get_post_forward_mesh_info", "name": "_get_post_forward_mesh_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["reshard_after_forward", "mesh_info"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_common.FSDPMeshInfo"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_post_forward_mesh_info", "ret_type": {".class": "UnionType", "items": ["torch.distributed.fsdp._fully_shard._fsdp_common.FSDPMeshInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ignore_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["module", "ignored_params", "ignore_decision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._ignore_module", "name": "_ignore_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["module", "ignored_params", "ignore_decision"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["torch.nn.modules.module.Module", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ignore_module", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_default_fully_shard_mesh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._init_default_fully_shard_mesh", "name": "_init_default_fully_shard_mesh", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_default_fully_shard_mesh", "ret_type": "torch.distributed.device_mesh.DeviceMesh", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_composable_with_fsdp": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_common._is_composable_with_fsdp", "kind": "Gdef"}, "_move_states_to_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["params", "buffers", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._move_states_to_device", "name": "_move_states_to_device", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["params", "buffers", "device"], "arg_types": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._C.device"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_move_states_to_device", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_verify_managed_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "param"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init._verify_managed_param", "name": "_verify_managed_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "param"], "arg_types": ["builtins.str", "torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_managed_param", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "init_device_mesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.init_device_mesh", "kind": "Gdef"}, "is_traceable_wrapper_subclass": {".class": "SymbolTableNode", "cross_ref": "torch.utils._python_dispatch.is_traceable_wrapper_subclass", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._fully_shard._fsdp_init.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "warning_once": {".class": "SymbolTableNode", "cross_ref": "torch._logging._internal.warning_once", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_fully_shard\\_fsdp_init.py"}