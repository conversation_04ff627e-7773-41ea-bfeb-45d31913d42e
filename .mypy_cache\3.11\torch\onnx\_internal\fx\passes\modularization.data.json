{".class": "MypyFile", "_fullname": "torch.onnx._internal.fx.passes.modularization", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Modularize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx._pass.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.modularization.Modularize", "name": "Modularize", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization.Modularize", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.modularization", "mro": ["torch.onnx._internal.fx.passes.modularization.Modularize", "torch.onnx._internal.fx._pass.Transform", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "module", "is_exported_program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization.Modularize.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "module", "is_exported_program"], "arg_types": ["torch.onnx._internal.fx.passes.modularization.Modularize", "torch.fx.graph_module.GraphModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Modularize", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization.Modularize._run", "name": "_run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization.Modularize"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run of Modularize", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_exported_program": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization.Modularize.is_exported_program", "name": "is_exported_program", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.modularization.Modularize.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.modularization.Modularize", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "_DYNAMO_NN_MODULE_META_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.fx.passes.modularization._DYNAMO_NN_MODULE_META_TYPE", "line": 25, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.type"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_DYNAMO_NN_MODULE_STACK_META_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.fx.passes.modularization._DYNAMO_NN_MODULE_STACK_META_TYPE", "line": 27, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._DYNAMO_NN_MODULE_META_TYPE"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_FX_TRACER_NN_MODULE_META_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.fx.passes.modularization._FX_TRACER_NN_MODULE_META_TYPE", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.type"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_FX_TRACER_NN_MODULE_STACK_META_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._internal.fx.passes.modularization._FX_TRACER_NN_MODULE_STACK_META_TYPE", "line": 22, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "collections.OrderedDict"}}}, "_IRNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["stack_meta", 1], ["stack_trace", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.modularization._IRNode", "name": "_IRNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.onnx._internal.fx.passes.modularization._IRNode", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.modularization", "mro": ["torch.onnx._internal.fx.passes.modularization._IRNode", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "stack_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "torch.onnx._internal.fx.passes.modularization._IRNode.stack_meta", "name": "stack_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_meta of _IRNode", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._IRNode.stack_meta", "name": "stack_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_meta of _IRNode", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stack_trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "torch.onnx._internal.fx.passes.modularization._IRNode.stack_trace", "name": "stack_trace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_trace of _IRNode", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._IRNode.stack_trace", "name": "stack_trace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_trace of _IRNode", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.modularization._IRNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.modularization._IRNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LeafNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx.passes.modularization._IRNode"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode", "name": "_LeafNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.modularization", "mro": ["torch.onnx._internal.fx.passes.modularization._LeafNode", "torch.onnx._internal.fx.passes.modularization._IRNode", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "is_exported_program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "is_exported_program"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode", "torch.fx.node.Node", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _LeafNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of _LeafNode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode._node", "name": "_node", "type": "torch.fx.node.Node"}}, "_stack_meta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode._stack_meta", "name": "_stack_meta", "type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"}}, "fx_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.fx_node", "name": "fx_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fx_node of _LeafNode", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.fx_node", "name": "fx_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fx_node of _LeafNode", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fx_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.fx_op", "name": "fx_op", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fx_op of _LeafNode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.fx_op", "name": "fx_op", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fx_op of _LeafNode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stack_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.stack_meta", "name": "stack_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_meta of _LeafNode", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.stack_meta", "name": "stack_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_meta of _LeafNode", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stack_trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.stack_trace", "name": "stack_trace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_trace of _LeafNode", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.stack_trace", "name": "stack_trace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_trace of _LeafNode", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.modularization._LeafNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.modularization._LeafNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ModuleMeta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "name": "_ModuleMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.modularization", "mro": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of _ModuleMeta", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of _ModuleMeta", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "module_name", "module_class", "raw_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "module_name", "module_class", "raw_meta"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta", "builtins.str", {".class": "UnionType", "items": ["builtins.type", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ModuleMeta", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "final_unset_in_class", "final_set_in_init", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta._module_class", "name": "_module_class", "type": {".class": "UnionType", "items": ["builtins.type", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_module_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "final_unset_in_class", "final_set_in_init", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta._module_name", "name": "_module_name", "type": "builtins.str"}}, "_raw_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "final_unset_in_class", "final_set_in_init", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta._raw_meta", "name": "_raw_meta", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "create_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.create_root", "name": "create_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.modularization._ModuleMeta"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_root of _ModuleMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.create_root", "name": "create_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.modularization._ModuleMeta"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_root of _ModuleMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_dynamo_produced_raw_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.from_dynamo_produced_raw_meta", "name": "from_dynamo_produced_raw_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.modularization._ModuleMeta"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._DYNAMO_NN_MODULE_META_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dynamo_produced_raw_meta of _ModuleMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.from_dynamo_produced_raw_meta", "name": "from_dynamo_produced_raw_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.modularization._ModuleMeta"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._DYNAMO_NN_MODULE_META_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dynamo_produced_raw_meta of _ModuleMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_fx_tracer_produced_raw_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.from_fx_tracer_produced_raw_meta", "name": "from_fx_tracer_produced_raw_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.modularization._ModuleMeta"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._FX_TRACER_NN_MODULE_META_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_fx_tracer_produced_raw_meta of _ModuleMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.from_fx_tracer_produced_raw_meta", "name": "from_fx_tracer_produced_raw_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.modularization._ModuleMeta"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._FX_TRACER_NN_MODULE_META_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_fx_tracer_produced_raw_meta of _ModuleMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_raw_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.from_raw_meta", "name": "from_raw_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.modularization._ModuleMeta"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._FX_TRACER_NN_MODULE_META_TYPE"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._DYNAMO_NN_MODULE_META_TYPE"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_raw_meta of _ModuleMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.from_raw_meta", "name": "from_raw_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "raw_meta"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.modularization._ModuleMeta"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._FX_TRACER_NN_MODULE_META_TYPE"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._DYNAMO_NN_MODULE_META_TYPE"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_raw_meta of _ModuleMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "module_class_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.module_class_name", "name": "module_class_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_class_name of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.module_class_name", "name": "module_class_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_class_name of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "module_display_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.module_display_name", "name": "module_display_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_display_name of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.module_display_name", "name": "module_display_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_display_name of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "module_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.module_name", "name": "module_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_name of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.module_name", "name": "module_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_name of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "qualified_module_class_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.qualified_module_class_name", "name": "qualified_module_class_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qualified_module_class_name of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.qualified_module_class_name", "name": "qualified_module_class_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qualified_module_class_name of _ModuleMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "raw_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.raw_meta", "name": "raw_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_meta of _ModuleMeta", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.raw_meta", "name": "raw_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_meta of _ModuleMeta", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ModuleNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx.passes.modularization._IRNode"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode", "name": "_ModuleNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.modularization", "mro": ["torch.onnx._internal.fx.passes.modularization._ModuleNode", "torch.onnx._internal.fx.passes.modularization._IRNode", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "reference_root_module", "stack_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "reference_root_module", "stack_meta"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode", "torch.fx.graph_module.GraphModule", "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ModuleNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of _ModuleNode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode._nodes", "name": "_nodes", "type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.modularization._IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_reference_module": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode._reference_module", "name": "_reference_module", "type": "torch.fx.graph_module.GraphModule"}}, "_stack_meta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode._stack_meta", "name": "_stack_meta", "type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"}}, "add_leaf_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "leaf_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.add_leaf_node", "name": "add_leaf_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "leaf_node"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode", "torch.onnx._internal.fx.passes.modularization._LeafNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_leaf_node of _ModuleNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.build_module", "name": "build_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module_names"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_module of _ModuleNode", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fx_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.fx_nodes", "name": "fx_nodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fx_nodes of _ModuleNode", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_parent_module_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.is_parent_module_of", "name": "is_parent_module_of", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode", "torch.onnx._internal.fx.passes.modularization._IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_parent_module_of of _ModuleNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_same_module_as": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.is_same_module_as", "name": "is_same_module_as", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode", "torch.onnx._internal.fx.passes.modularization._IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_same_module_as of _ModuleNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.module_inputs", "name": "module_inputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_inputs of _ModuleNode", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.module_outputs", "name": "module_outputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_outputs of _ModuleNode", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stack_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.stack_meta", "name": "stack_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_meta of _ModuleNode", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.stack_meta", "name": "stack_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_meta of _ModuleNode", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stack_trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.stack_trace", "name": "stack_trace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_trace of _ModuleNode", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.stack_trace", "name": "stack_trace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_trace of _ModuleNode", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.modularization._ModuleNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ModuleStackMeta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "name": "_ModuleStackMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.modularization", "mro": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of _ModuleStackMeta", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ModuleStackMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "nn_module_stack_meta", "is_exported_program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "nn_module_stack_meta", "is_exported_program"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "collections.OrderedDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.passes.modularization._DYNAMO_NN_MODULE_STACK_META_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ModuleStackMeta", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of _ModuleStackMeta", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _ModuleStackMeta", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of _ModuleStackMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_module_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "final_unset_in_class", "final_set_in_init", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta._module_stack", "name": "_module_stack", "type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "is_empty_or_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.is_empty_or_root", "name": "is_empty_or_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_empty_or_root of _ModuleStackMeta", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_superset_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module_stack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.is_superset_of", "name": "is_superset_of", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module_stack"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_superset_of of _ModuleStackMeta", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.module_class", "name": "module_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_class of _ModuleStackMeta", "ret_type": {".class": "UnionType", "items": ["builtins.type", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.module_class", "name": "module_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_class of _ModuleStackMeta", "ret_type": {".class": "UnionType", "items": ["builtins.type", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "module_display_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.module_display_name", "name": "module_display_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_display_name of _ModuleStackMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.module_display_name", "name": "module_display_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_display_name of _ModuleStackMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module_meta"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "torch.onnx._internal.fx.passes.modularization._ModuleMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of _ModuleStackMeta", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "qualified_module_class_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.qualified_module_class_name", "name": "qualified_module_class_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qualified_module_class_name of _ModuleStackMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.qualified_module_class_name", "name": "qualified_module_class_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qualified_module_class_name of _ModuleStackMeta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "raw_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.raw_meta", "name": "raw_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_meta of _ModuleStackMeta", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.type"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.raw_meta", "name": "raw_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_meta of _ModuleStackMeta", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.type"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "top": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.top", "name": "top", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.modularization._ModuleStackMeta"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "top of _ModuleStackMeta", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.modularization.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_unique_module_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module_names", "module_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._get_unique_module_name", "name": "_get_unique_module_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module_names", "module_name"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_unique_module_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_module_stack_meta_from_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["node", "is_exported_program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.modularization._module_stack_meta_from_node", "name": "_module_stack_meta_from_node", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["node", "is_exported_program"], "arg_types": ["torch.fx.node.Node", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_module_stack_meta_from_node", "ret_type": "torch.onnx._internal.fx.passes.modularization._ModuleStackMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pass": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx._pass", "kind": "Gdef"}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\passes\\modularization.py"}