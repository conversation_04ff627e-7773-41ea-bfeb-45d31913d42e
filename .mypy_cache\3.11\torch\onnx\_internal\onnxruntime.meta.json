{"data_mtime": 1755649448, "dep_lines": [32, 33, 927, 1119, 15, 18, 19, 20, 31, 32, 1176, 13, 15, 16, 17, 21, 6, 11, 12, 13, 14, 15, 21, 2, 3, 4, 5, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 1175, 25, 26], "dep_prios": [20, 20, 20, 20, 10, 5, 5, 5, 20, 20, 20, 10, 20, 5, 5, 10, 5, 10, 10, 20, 10, 20, 20, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20], "dependencies": ["torch.onnx._internal.fx.decomposition_table", "torch.onnx._internal.fx.passes", "torch.onnx._internal.fx.fx_onnx_interpreter", "torch.fx.passes.infra.partitioner", "torch.onnx._internal._lazy_import", "torch.fx.passes.fake_tensor_prop", "torch.fx.passes.operator_support", "torch.fx.passes.tools_common", "torch.onnx._internal._exporter_legacy", "torch.onnx._internal.fx", "torch._dynamo.backends.common", "torch._prims.executor", "torch.onnx._internal", "torch._subclasses.fake_tensor", "torch.fx._compatibility", "torch.utils._pytree", "collections.abc", "torch._C", "torch._ops", "torch._prims", "torch.fx", "torch.onnx", "torch.utils", "dataclasses", "importlib", "logging", "os", "typing", "typing_extensions", "torch", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "torch._subclasses", "torch._tensor", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.interpreter", "torch.fx.node", "torch.fx.passes", "torch.fx.passes.infra", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.onnx._internal.fx._pass", "torch.onnx._internal.fx.onnxfunction_dispatcher", "torch.onnx._internal.fx.passes.type_promotion", "torch.onnx._internal.fx.passes.virtualization", "torch.testing", "torch.testing._comparison", "torch.types", "torch.utils._python_dispatch"], "hash": "29b126e48c0b87d6f46c209f102dc518227f06c1", "id": "torch.onnx._internal.onnxruntime", "ignore_all": true, "interface_hash": "46e4a7b2159688ea9185eb5e43404369c1690ac5", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\onnxruntime.py", "plugin_data": null, "size": 54204, "suppressed": ["onnxruntime.capi", "functorch.compile", "onnx", "onnxruntime"], "version_id": "1.15.0"}