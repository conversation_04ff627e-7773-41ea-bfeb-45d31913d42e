{"data_mtime": 1755649448, "dep_lines": [14, 13, 14, 12, 13, 11, 12, 15, 8, 9, 10, 11, 15, 2, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 10, 20, 10, 20, 10, 10, 10, 10, 20, 20, 5, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.fx.passes._utils", "torch.onnx._internal.fx._pass", "torch.onnx._internal.fx.passes", "torch.fx.experimental.proxy_tensor", "torch.onnx._internal.fx", "torch._subclasses.fake_tensor", "torch.fx.experimental", "torch.utils._pytree", "torch._ops", "torch.func", "torch.fx", "torch._subclasses", "torch.utils", "__future__", "contextlib", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "copy", "_frozen_importlib", "abc", "torch.fx.graph_module", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "4abb2bf9be9ea97c6325fc99e3c61796eb9c43dc", "id": "torch.onnx._internal.fx.passes.functionalization", "ignore_all": true, "interface_hash": "17ff5cd89a8526813dbd3d2fa8940b8e079939a0", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\passes\\functionalization.py", "plugin_data": null, "size": 6413, "suppressed": [], "version_id": "1.15.0"}