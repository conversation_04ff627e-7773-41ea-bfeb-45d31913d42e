{"data_mtime": 1755649448, "dep_lines": [13, 14, 15, 27, 6, 25, 19, 2, 4, 20, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22], "dep_prios": [5, 5, 5, 25, 5, 25, 25, 5, 5, 25, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["torch._inductor.codegen.cuda.cuda_cpp_scheduling", "torch._inductor.codegen.rocm.rocm_cpp_scheduling", "torch._inductor.codegen.triton", "torch._inductor.codegen.common", "torch._inductor.scheduler", "torch.utils._ordered_set", "collections.abc", "__future__", "typing", "typing_extensions", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "enum", "torch._C", "torch._inductor.codegen.cuda", "torch._inductor.codegen.rocm", "torch._inductor.codegen.simd", "torch.utils"], "hash": "bded71258fe901639ce4283c05800aa839317fea", "id": "torch._inductor.codegen.cuda_combined_scheduling", "ignore_all": true, "interface_hash": "97fb37e6a1324ae522c48fbd475dcaf021a1aac8", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda_combined_scheduling.py", "plugin_data": null, "size": 5167, "suppressed": ["sympy"], "version_id": "1.15.0"}