import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
import warnings
warnings.filterwarnings('ignore')

def clean_and_regenerate_data():
    """Clean the existing data and regenerate proper LSTM sequences"""
    
    print("Loading original dataset...")
    try:
        # Load the original CSV dataset
        dataset = pd.read_csv('substation_equipment_dataset.csv')
        print(f"Original dataset shape: {dataset.shape}")
        
        # Check for NaN values
        nan_counts = dataset.isnull().sum()
        print(f"NaN values per column:")
        for col, count in nan_counts.items():
            if count > 0:
                print(f"  {col}: {count}")
        
        # Clean the data
        print("\nCleaning data...")
        
        # Fill NaN values with appropriate strategies
        numeric_cols = dataset.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if dataset[col].isnull().any():
                # Fill with median for numeric columns
                median_val = dataset[col].median()
                dataset[col].fillna(median_val, inplace=True)
                print(f"  Filled {col} NaN values with median: {median_val:.3f}")
        
        # Fill categorical NaN values
        categorical_cols = dataset.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            if dataset[col].isnull().any():
                mode_val = dataset[col].mode()[0] if not dataset[col].mode().empty else 'unknown'
                dataset[col].fillna(mode_val, inplace=True)
                print(f"  Filled {col} NaN values with mode: {mode_val}")
        
        print(f"After cleaning - NaN values: {dataset.isnull().sum().sum()}")
        
    except FileNotFoundError:
        print("Original CSV not found. Generating new synthetic data...")
        # If CSV doesn't exist, create simple synthetic data
        dataset = generate_simple_synthetic_data()
    
    # Prepare LSTM data with proper validation
    print("\nPreparing LSTM sequences...")
    X, y = prepare_clean_lstm_data(dataset)
    
    # Validate the generated data
    print(f"\nValidating generated data...")
    print(f"X shape: {X.shape}")
    print(f"y shape: {y.shape}")
    print(f"X has NaN: {np.isnan(X).any()}")
    print(f"y has NaN: {np.isnan(y).any()}")
    print(f"X has Inf: {np.isinf(X).any()}")
    print(f"y has Inf: {np.isinf(y).any()}")
    print(f"X range: [{X.min():.6f}, {X.max():.6f}]")
    print(f"y range: [{y.min():.6f}, {y.max():.6f}]")
    
    # Save the cleaned data
    np.save('lstm_sequences_X.npy', X)
    np.save('lstm_sequences_y.npy', y)
    
    print("\nCleaned data saved successfully!")
    return X, y

def generate_simple_synthetic_data():
    """Generate simple synthetic data if original is not available"""
    print("Generating simple synthetic data...")
    
    # Create simple time series data
    n_equipment = 50
    n_timesteps = 2000
    n_features = 29
    
    data_list = []
    
    for eq_id in range(n_equipment):
        # Generate realistic time series for each equipment
        timestamps = pd.date_range('2020-01-01', periods=n_timesteps, freq='H')
        
        # Base degradation trend (slowly increasing over time)
        base_degradation = np.linspace(0.1, 0.8, n_timesteps) + np.random.normal(0, 0.05, n_timesteps)
        base_degradation = np.clip(base_degradation, 0, 1)
        
        # Generate correlated features
        data = {
            'timestamp': timestamps,
            'equipment_id': f'EQ{eq_id:03d}',
            'equipment_type': np.random.choice(['transformer', 'breaker', 'ct']),
            'degradation_factor': base_degradation,
        }
        
        # Add 25 more synthetic features
        for i in range(25):
            # Create features that correlate with degradation
            noise_level = 0.1
            trend_factor = np.random.uniform(0.5, 2.0)
            feature_values = (base_degradation * trend_factor + 
                            np.random.normal(0, noise_level, n_timesteps))
            data[f'feature_{i:02d}'] = feature_values
        
        df = pd.DataFrame(data)
        data_list.append(df)
    
    return pd.concat(data_list, ignore_index=True)

def prepare_clean_lstm_data(dataset, sequence_length=168, target_column='degradation_factor'):
    """Prepare clean LSTM data with proper validation"""
    
    # Select only numeric features for LSTM
    exclude_cols = ['timestamp', 'equipment_id', 'equipment_type', 'health_status']
    numeric_cols = dataset.select_dtypes(include=[np.number]).columns.tolist()
    feature_cols = [col for col in numeric_cols if col not in exclude_cols]
    
    print(f"Using {len(feature_cols)} features: {feature_cols[:5]}...")
    
    X_sequences = []
    y_sequences = []
    
    for equipment_id in dataset['equipment_id'].unique():
        equipment_data = dataset[dataset['equipment_id'] == equipment_id].copy()
        equipment_data = equipment_data.sort_values('timestamp')
        
        # Extract numeric features and ensure they are float
        feature_data = equipment_data[feature_cols].astype(np.float64).values

        # Check for any remaining NaN or inf values
        if np.isnan(feature_data).any() or np.isinf(feature_data).any():
            print(f"Warning: Found NaN/Inf in equipment {equipment_id}, skipping...")
            continue
        
        # Scale features with robust scaling
        scaler = MinMaxScaler(feature_range=(0, 1))
        try:
            scaled_features = scaler.fit_transform(feature_data)
        except Exception as e:
            print(f"Scaling failed for equipment {equipment_id}: {e}")
            continue
        
        # Validate scaled features
        if np.isnan(scaled_features).any() or np.isinf(scaled_features).any():
            print(f"Warning: Scaling produced NaN/Inf for equipment {equipment_id}, skipping...")
            continue
        
        # Create sequences
        target_idx = feature_cols.index(target_column)
        for i in range(sequence_length, len(scaled_features)):
            X_sequences.append(scaled_features[i-sequence_length:i])
            y_sequences.append(scaled_features[i, target_idx])
    
    X = np.array(X_sequences, dtype=np.float32)
    y = np.array(y_sequences, dtype=np.float32)
    
    # Final validation
    if np.isnan(X).any() or np.isnan(y).any():
        raise ValueError("Generated data still contains NaN values!")
    
    if np.isinf(X).any() or np.isinf(y).any():
        raise ValueError("Generated data contains infinite values!")
    
    return X, y

if __name__ == "__main__":
    X, y = clean_and_regenerate_data()
    print("Data cleaning completed successfully!")
