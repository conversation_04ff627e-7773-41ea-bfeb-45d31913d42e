{"data_mtime": 1755649448, "dep_lines": [16, 18, 38, 39, 13, 14, 15, 17, 23, 24, 26, 27, 8, 12, 13, 26, 2, 3, 4, 5, 6, 7, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 10, 20, 20, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch._inductor.fx_passes.dedupe_symint_uses", "torch.fx.experimental.symbolic_shapes", "torch._inductor.fx_passes.decompose_mem_bound_mm", "torch._inductor.fx_passes.replace_random", "torch.utils._pytree", "torch._dynamo.utils", "torch._inductor.constant_folding", "torch._inductor.utils", "torch.multiprocessing.reductions", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.pattern_matcher", "collections.abc", "torch._guards", "torch.utils", "torch._inductor", "functools", "itertools", "logging", "operator", "typing", "collections", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._ops", "torch._tensor", "torch.fx", "torch.fx.graph_module", "torch.fx.interpreter", "torch.fx.node", "torch.multiprocessing", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "ff5fd4dc2f69707d3d4fda6a556867e20a2bf48c", "id": "torch._inductor.fx_passes.joint_graph", "ignore_all": true, "interface_hash": "bcf3494100ac0a56468e572ef77156b6f8fa8d5e", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\joint_graph.py", "plugin_data": null, "size": 34589, "suppressed": [], "version_id": "1.15.0"}