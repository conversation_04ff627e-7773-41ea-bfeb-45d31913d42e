{".class": "MypyFile", "_fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "GradBucket": {".class": "SymbolTableNode", "cross_ref": "torch._C._distributed_c10d.GradBucket", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "noop_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks.noop_hook", "name": "noop_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_", "bucket"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "torch._C._distributed_c10d.GradBucket"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "noop_hook", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\algorithms\\ddp_comm_hooks\\debugging_hooks.py"}