{".class": "MypyFile", "_fullname": "git.objects.util", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef", "module_public": false}, "Actor": {".class": "SymbolTableNode", "cross_ref": "git.util.Actor", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Blob": {".class": "SymbolTableNode", "cross_ref": "git.objects.blob.Blob", "kind": "Gdef", "module_public": false}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Commit": {".class": "SymbolTableNode", "cross_ref": "git.objects.commit.Commit", "kind": "Gdef", "module_public": false}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef", "module_public": false}, "Has_id_attribute": {".class": "SymbolTableNode", "cross_ref": "git.types.Has_id_attribute", "kind": "Gdef", "module_public": false}, "IterableList": {".class": "SymbolTableNode", "cross_ref": "git.util.IterableList", "kind": "Gdef", "module_public": false}, "IterableObj": {".class": "SymbolTableNode", "cross_ref": "git.util.IterableObj", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "Popen": {".class": "SymbolTableNode", "cross_ref": "subprocess.Popen", "kind": "Gdef", "module_public": false}, "ProcessStreamAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.util.ProcessStreamAdapter", "name": "ProcessStreamAdapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.objects.util.ProcessStreamAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.objects.util", "mro": ["git.objects.util.ProcessStreamAdapter", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.ProcessStreamAdapter.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["git.objects.util.ProcessStreamAdapter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of ProcessStreamAdapter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "process", "stream_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.ProcessStreamAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "process", "stream_name"], "arg_types": ["git.objects.util.ProcessStreamAdapter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "subprocess.Popen"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProcessStreamAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.objects.util.ProcessStreamAdapter.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_proc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.util.ProcessStreamAdapter._proc", "name": "_proc", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "subprocess.Popen"}}}, "_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "git.objects.util.ProcessStreamAdapter._stream", "name": "_stream", "type": "_io.StringIO"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.ProcessStreamAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.util.ProcessStreamAdapter", "values": [], "variance": 0}, "slots": ["_proc", "_stream"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Serializable": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_deserialize", 2], ["_serialize", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.util.Serializable", "name": "Serializable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "git.objects.util.Serializable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.objects.util", "mro": ["git.objects.util.Serializable", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.objects.util.Serializable.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_deserialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "git.objects.util.Serializable._deserialize", "name": "_deserialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["git.objects.util.Serializable", "_io.BytesIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deserialize of Serializable", "ret_type": "git.objects.util.Serializable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "git.objects.util.Serializable._serialize", "name": "_serialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["git.objects.util.Serializable", "_io.BytesIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serialize of Serializable", "ret_type": "git.objects.util.Serializable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.Serializable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.util.Serializable", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef", "module_public": false}, "Submodule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.Submodule", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "T_TIobj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "name": "T_TIobj", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}}, "TagObject": {".class": "SymbolTableNode", "cross_ref": "git.objects.tag.TagObject", "kind": "Gdef", "module_public": false}, "Traversable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_get_intermediate_items", 1], ["list_traverse", 1], ["traverse", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.util.Traversable", "name": "Traversable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "git.objects.util.Traversable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.objects.util", "mro": ["git.objects.util.Traversable", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.objects.util.Traversable.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_get_intermediate_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["cls", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "git.objects.util.Traversable._get_intermediate_items", "name": "_get_intermediate_items", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "item"], "arg_types": [{".class": "TypeType", "item": "git.objects.util.Traversable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_intermediate_items of Traversable", "ret_type": {".class": "Instance", "args": ["git.objects.util.Traversable"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.objects.util.Traversable._get_intermediate_items", "name": "_get_intermediate_items", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "item"], "arg_types": [{".class": "TypeType", "item": "git.objects.util.Traversable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_intermediate_items of Traversable", "ret_type": {".class": "Instance", "args": ["git.objects.util.Traversable"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_list_traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "as_edge", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.Traversable._list_traverse", "name": "_list_traverse", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "as_edge", "args", "kwargs"], "arg_types": ["git.objects.util.Traversable", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_list_traverse of Traversable", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["git.objects.commit.Commit", "git.objects.submodule.base.Submodule", "git.objects.tree.Tree", "git.objects.blob.Blob"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.Traversable._traverse", "name": "_traverse", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": ["git.objects.util.Traversable", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "TypeAliasType", "args": [], "type_ref": "git.objects.util.TraversedTup"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "TypeAliasType", "args": [], "type_ref": "git.objects.util.TraversedTup"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse of Traversable", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.util.TraversedTup"}], "extra_attrs": null, "type_ref": "typing.Iterator"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.objects.util.Traversable.list_traverse", "name": "list_traverse", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.objects.util.Traversable", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_traverse of Traversable", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.objects.util.Traversable.list_traverse", "name": "list_traverse", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.objects.util.Traversable", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_traverse of Traversable", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "git.objects.util.Traversable.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.objects.util.Traversable", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of Traversable", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "git.objects.util.Traversable.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.objects.util.Traversable", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of Traversable", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.Traversable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.util.Traversable", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraversableIterableObj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_get_intermediate_items", 1], ["_id_attribute_", 1], ["iter_items", 1]], "alt_promote": null, "bases": ["git.util.IterableObj", "git.objects.util.Traversable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.util.TraversableIterableObj", "name": "TraversableIterableObj", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "git.objects.util.TraversableIterableObj", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.objects.util", "mro": ["git.objects.util.TraversableIterableObj", "git.util.IterableObj", "git.objects.util.Traversable", "builtins.object"], "names": {".class": "SymbolTable", "TIobj_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": 1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.TIobj_tuple", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "column": 4, "fullname": "git.objects.util.TraversableIterableObj.TIobj_tuple", "line": 617, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": 1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.TIobj_tuple", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": 1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.TIobj_tuple", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.objects.util.TraversableIterableObj.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "list_traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.TraversableIterableObj.list_traverse", "name": "list_traverse", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.list_traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.list_traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.list_traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}}, "traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "git.objects.util.TraversableIterableObj.traverse", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "extra_attrs": null, "type_ref": "typing.Iterator"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.objects.util.TraversableIterableObj.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#0", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#1", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#2", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "predicate", "prune", "depth", "branch_first", "visit_once", "ignore_self", "as_edge"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "git.objects.util.TraversableIterableObj.TIobj_tuple"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of TraversableIterableObj", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.T_TIobj", "id": -1, "name": "T_TIobj", "namespace": "git.objects.util.TraversableIterableObj.traverse#3", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraversableIterableObj.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.util.TraversableIterableObj", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraverseNT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.util.TraverseNT", "name": "TraverseNT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "git.objects.util.TraverseNT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["depth", "item", "src"]}}, "module_name": "git.objects.util", "mro": ["git.objects.util.TraverseNT", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.objects.util.TraverseNT.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.objects.util.TraverseNT.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.objects.util.TraverseNT.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "depth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "item"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "src"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "depth", "item", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "git.objects.util.TraverseNT.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "depth", "item", "src"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of TraverseNT", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.TraverseNT._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of TraverseNT", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.objects.util.TraverseNT._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.objects.util.TraverseNT._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.objects.util.TraverseNT._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.objects.util.TraverseNT._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of TraverseNT", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "git.objects.util.TraverseNT._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of TraverseNT", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "depth", "item", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.TraverseNT._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "depth", "item", "src"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of TraverseNT", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT._NT", "id": -1, "name": "_NT", "namespace": "git.objects.util.TraverseNT._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.objects.util.TraverseNT._source", "name": "_source", "type": "builtins.str"}}, "depth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.objects.util.TraverseNT.depth", "name": "depth", "type": "builtins.int"}}, "depth-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.objects.util.TraverseNT.depth", "kind": "<PERSON><PERSON><PERSON>"}, "item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.objects.util.TraverseNT.item", "name": "item", "type": {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}}}, "item-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.objects.util.TraverseNT.item", "kind": "<PERSON><PERSON><PERSON>"}, "src": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.objects.util.TraverseNT.src", "name": "src", "type": {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "src-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.objects.util.TraverseNT.src", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.TraverseNT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "git.objects.util.TraverseNT"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["git.objects.util.Traversable", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "git.objects.util.Traversable", "git.objects.blob.Blob", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "TraversedTreeTup": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.TraversedTreeTup", "kind": "Gdef", "module_public": false}, "TraversedTup": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.objects.util.TraversedTup", "line": 79, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["git.objects.util.Traversable", {".class": "NoneType"}], "uses_pep604_syntax": false}, "git.objects.util.Traversable"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "git.objects.tree.TraversedTreeTup"}], "uses_pep604_syntax": false}}}, "Tree": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.Tree", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "ZERO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.util.ZERO", "name": "ZERO", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.util.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.util.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_re_actor_epoch": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.util._re_actor_epoch", "name": "_re_actor_epoch", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_re_only_actor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.util._re_only_actor", "name": "_re_only_actor", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "altz_to_utctz_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["altz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.altz_to_utctz_str", "name": "altz_to_utctz_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["altz"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "altz_to_utctz_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "calendar": {".class": "SymbolTableNode", "cross_ref": "calendar", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_public": false}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef", "module_public": false}, "digits": {".class": "SymbolTableNode", "cross_ref": "string.digits", "kind": "Gdef", "module_public": false}, "from_timestamp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["timestamp", "tz_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.from_timestamp", "name": "from_timestamp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["timestamp", "tz_offset"], "arg_types": ["builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_timestamp", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_object_type_by_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["object_type_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.get_object_type_by_name", "name": "get_object_type_by_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["object_type_name"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_object_type_by_name", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "git.objects.commit.Commit"}, {".class": "TypeType", "item": "git.objects.tag.TagObject"}, {".class": "TypeType", "item": "git.objects.tree.Tree"}, {".class": "TypeType", "item": "git.objects.blob.Blob"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode_str_to_int": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["modestr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.mode_str_to_int", "name": "mode_str_to_int", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["modestr"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode_str_to_int", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "parse_actor_and_date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.parse_actor_and_date", "name": "parse_actor_and_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["line"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_actor_and_date", "ret_type": {".class": "TupleType", "implicit": false, "items": ["git.util.Actor", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["string_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.parse_date", "name": "parse_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["string_date"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_date", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing.runtime_checkable", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "tzinfo": {".class": "SymbolTableNode", "cross_ref": "datetime.tzinfo", "kind": "Gdef", "module_public": false}, "tzoffset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datetime.tzinfo"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.util.tzoffset", "name": "tzoffset", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.objects.util.tzoffset", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.objects.util", "mro": ["git.objects.util.tzoffset", "datetime.tzinfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "secs_west_of_utc", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.tzoffset.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "secs_west_of_utc", "name"], "arg_types": ["git.objects.util.tzoffset", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of tzoffset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.tzoffset.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.util.tzoffset"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of tzoffset", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "git.objects.util.tzoffset"}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.util.tzoffset._name", "name": "_name", "type": "builtins.str"}}, "_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.util.tzoffset._offset", "name": "_offset", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "dst": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.tzoffset.dst", "name": "dst", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dt"], "arg_types": ["git.objects.util.tzoffset", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dst of tzoffset", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tzname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.tzoffset.tzname", "name": "tzname", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dt"], "arg_types": ["git.objects.util.tzoffset", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tzname of tzoffset", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "utcoffset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.tzoffset.utcoffset", "name": "utcoffset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dt"], "arg_types": ["git.objects.util.tzoffset", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "utcoffset of tzoffset", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.util.tzoffset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.util.tzoffset", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "utc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.util.utc", "name": "utc", "type": "git.objects.util.tzoffset"}}, "utctz_to_altz": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["utctz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.utctz_to_altz", "name": "utctz_to_altz", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["utctz"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "utctz_to_altz", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_utctz": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.util.verify_utctz", "name": "verify_utctz", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["offset"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_utctz", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\util.py"}