{".class": "MypyFile", "_fullname": "streamlit.elements.lib.streamlit_plotly_theme", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BG_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000038", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.BG_COLOR", "name": "BG_COLOR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000038"}, "type_ref": "builtins.str"}}}, "BG_MIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000040", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.BG_MIX", "name": "BG_MIX", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000040"}, "type_ref": "builtins.str"}}}, "CATEGORY_0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000001", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_0", "name": "CATEGORY_0", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000001"}, "type_ref": "builtins.str"}}}, "CATEGORY_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000002", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_1", "name": "CATEGORY_1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000002"}, "type_ref": "builtins.str"}}}, "CATEGORY_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000003", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_2", "name": "CATEGORY_2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000003"}, "type_ref": "builtins.str"}}}, "CATEGORY_3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000004", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_3", "name": "CATEGORY_3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000004"}, "type_ref": "builtins.str"}}}, "CATEGORY_4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000005", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_4", "name": "CATEGORY_4", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000005"}, "type_ref": "builtins.str"}}}, "CATEGORY_5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000006", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_5", "name": "CATEGORY_5", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000006"}, "type_ref": "builtins.str"}}}, "CATEGORY_6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000007", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_6", "name": "CATEGORY_6", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000007"}, "type_ref": "builtins.str"}}}, "CATEGORY_7": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000008", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_7", "name": "CATEGORY_7", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000008"}, "type_ref": "builtins.str"}}}, "CATEGORY_8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000009", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_8", "name": "CATEGORY_8", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000009"}, "type_ref": "builtins.str"}}}, "CATEGORY_9": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000010", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.CATEGORY_9", "name": "CATEGORY_9", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000010"}, "type_ref": "builtins.str"}}}, "DECREASING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000033", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DECREASING", "name": "DECREASING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000033"}, "type_ref": "builtins.str"}}}, "DIVERGING_0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000021", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_0", "name": "DIVERGING_0", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000021"}, "type_ref": "builtins.str"}}}, "DIVERGING_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000022", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_1", "name": "DIVERGING_1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000022"}, "type_ref": "builtins.str"}}}, "DIVERGING_10": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000031", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_10", "name": "DIVERGING_10", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000031"}, "type_ref": "builtins.str"}}}, "DIVERGING_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000023", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_2", "name": "DIVERGING_2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000023"}, "type_ref": "builtins.str"}}}, "DIVERGING_3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000024", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_3", "name": "DIVERGING_3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000024"}, "type_ref": "builtins.str"}}}, "DIVERGING_4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000025", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_4", "name": "DIVERGING_4", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000025"}, "type_ref": "builtins.str"}}}, "DIVERGING_5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000026", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_5", "name": "DIVERGING_5", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000026"}, "type_ref": "builtins.str"}}}, "DIVERGING_6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000027", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_6", "name": "DIVERGING_6", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000027"}, "type_ref": "builtins.str"}}}, "DIVERGING_7": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000028", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_7", "name": "DIVERGING_7", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000028"}, "type_ref": "builtins.str"}}}, "DIVERGING_8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000029", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_8", "name": "DIVERGING_8", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000029"}, "type_ref": "builtins.str"}}}, "DIVERGING_9": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000030", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.DIVERGING_9", "name": "DIVERGING_9", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000030"}, "type_ref": "builtins.str"}}}, "FADED_TEXT_05": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000039", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.FADED_TEXT_05", "name": "FADED_TEXT_05", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000039"}, "type_ref": "builtins.str"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "GRAY_70": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000036", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.GRAY_70", "name": "GRAY_70", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000036"}, "type_ref": "builtins.str"}}}, "GRAY_90": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000037", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.GRAY_90", "name": "GRAY_90", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000037"}, "type_ref": "builtins.str"}}}, "INCREASING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000032", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.INCREASING", "name": "INCREASING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000032"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000011", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_0", "name": "SEQUENTIAL_0", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000011"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000012", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_1", "name": "SEQUENTIAL_1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000012"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000013", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_2", "name": "SEQUENTIAL_2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000013"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000014", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_3", "name": "SEQUENTIAL_3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000014"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000015", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_4", "name": "SEQUENTIAL_4", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000015"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000016", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_5", "name": "SEQUENTIAL_5", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000016"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000017", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_6", "name": "SEQUENTIAL_6", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000017"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_7": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000018", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_7", "name": "SEQUENTIAL_7", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000018"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000019", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_8", "name": "SEQUENTIAL_8", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000019"}, "type_ref": "builtins.str"}}}, "SEQUENTIAL_9": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000020", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.SEQUENTIAL_9", "name": "SEQUENTIAL_9", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000020"}, "type_ref": "builtins.str"}}}, "TOTAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "#000034", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.TOTAL", "name": "TOTAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "#000034"}, "type_ref": "builtins.str"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "configure_streamlit_plotly_theme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.elements.lib.streamlit_plotly_theme.configure_streamlit_plotly_theme", "name": "configure_streamlit_plotly_theme", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure_streamlit_plotly_theme", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\elements\\lib\\streamlit_plotly_theme.py"}