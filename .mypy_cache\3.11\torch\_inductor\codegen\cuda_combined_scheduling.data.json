{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cuda_combined_scheduling", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendFeature": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.BackendFeature", "kind": "Gdef"}, "BaseSchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseSchedulerNode", "kind": "Gdef"}, "BaseScheduling": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseScheduling", "kind": "Gdef"}, "CUDACPPScheduling": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_cpp_scheduling.CUDACPPScheduling", "kind": "Gdef"}, "CUDACombinedScheduling": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.scheduler.BaseScheduling"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "name": "CUDACombinedScheduling", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cuda_combined_scheduling", "mro": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "torch._inductor.scheduler.BaseScheduling", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scheduler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scheduler"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", {".class": "UnionType", "items": ["torch._inductor.scheduler.Scheduler", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDACombinedScheduling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cuda_cpp_scheduling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling._cuda_cpp_scheduling", "name": "_cuda_cpp_scheduling", "type": "torch._inductor.codegen.cuda.cuda_cpp_scheduling.CUDACPPScheduling"}}, "_rocm_cpp_scheduling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling._rocm_cpp_scheduling", "name": "_rocm_cpp_scheduling", "type": "torch._inductor.codegen.rocm.rocm_cpp_scheduling.ROCmCPPScheduling"}}, "_triton_scheduling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling._triton_scheduling", "name": "_triton_scheduling", "type": "torch._inductor.codegen.triton.TritonScheduling"}}, "benchmark_codegened_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.benchmark_codegened_module", "name": "benchmark_codegened_module", "type": null}}, "benchmark_combo_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.benchmark_combo_kernel", "name": "benchmark_combo_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node_list"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", {".class": "Instance", "args": ["torch._inductor.scheduler.BaseSchedulerNode"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "benchmark_combo_kernel of CUDACombinedScheduling", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "benchmark_fused_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.benchmark_fused_nodes", "name": "benchmark_fused_nodes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", {".class": "Instance", "args": ["torch._inductor.scheduler.BaseSchedulerNode"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "benchmark_fused_nodes of CUDACombinedScheduling", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_fuse_horizontal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node1", "node2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.can_fuse_horizontal", "name": "can_fuse_horizontal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node1", "node2"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_fuse_horizontal of CUDACombinedScheduling", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_fuse_vertical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node1", "node2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.can_fuse_vertical", "name": "can_fuse_vertical", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node1", "node2"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "torch._inductor.scheduler.BaseSchedulerNode", "torch._inductor.scheduler.BaseSchedulerNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_fuse_vertical of CUDACombinedScheduling", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "choose_node_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.choose_node_backend", "name": "choose_node_backend", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "torch._inductor.scheduler.BaseSchedulerNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "choose_node_backend of CUDACombinedScheduling", "ret_type": "torch._inductor.scheduler.BaseScheduling", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_combo_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.codegen_combo_kernel", "name": "codegen_combo_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_combo_kernel of CUDACombinedScheduling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.codegen_node", "name": "codegen_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", {".class": "UnionType", "items": ["torch._inductor.scheduler.FusedSchedulerNode", "torch._inductor.scheduler.SchedulerNode"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_node of CUDACombinedScheduling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.codegen_sync", "name": "codegen_sync", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_sync of CUDACombinedScheduling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "template_node", "epilogue_nodes", "prologue_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.codegen_template", "name": "codegen_template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "template_node", "epilogue_nodes", "prologue_nodes"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "torch._inductor.scheduler.BaseSchedulerNode", {".class": "Instance", "args": ["torch._inductor.scheduler.BaseSchedulerNode"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["torch._inductor.scheduler.BaseSchedulerNode"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_template of CUDACombinedScheduling", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flush of CUDACombinedScheduling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_kernel_code_from_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "nodes", "benchmark_kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.generate_kernel_code_from_nodes", "name": "generate_kernel_code_from_nodes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "nodes", "benchmark_kernel"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_kernel_code_from_nodes of CUDACombinedScheduling", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_backend_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.get_backend_features", "name": "get_backend_features", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "torch._C.device"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_backend_features of CUDACombinedScheduling", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.common.BackendFeature"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "group_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.group_fn", "name": "group_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sizes"], "arg_types": ["torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda_combined_scheduling._IntLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group_fn of CUDACombinedScheduling", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda_combined_scheduling._IntLike"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda_combined_scheduling.CUDACombinedScheduling", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.Expr", "name": "Expr", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda_combined_scheduling.Expr", "source_any": null, "type_of_any": 3}}}, "FusedSchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.FusedSchedulerNode", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "ROCmCPPScheduling": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.rocm_cpp_scheduling.ROCmCPPScheduling", "kind": "Gdef"}, "Scheduler": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.Scheduler", "kind": "Gdef"}, "SchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.SchedulerNode", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TritonScheduling": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.triton.TritonScheduling", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_IntLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "torch._inductor.codegen.cuda_combined_scheduling._IntLike", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda_combined_scheduling.Expr", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda_combined_scheduling.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda_combined_scheduling.py"}