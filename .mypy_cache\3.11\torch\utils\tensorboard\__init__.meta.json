{"data_mtime": 1755649448, "dep_lines": [2, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 13, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 20, 5, 10, 20], "dependencies": ["torch._vendor.packaging.version", "torch.utils.tensorboard.writer", "builtins", "collections", "warnings", "torch", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "typing", "_frozen_importlib", "abc", "torch._vendor", "torch._vendor.packaging", "contextlib"], "hash": "eea93e45a0fdfca2253dcfb5fdfd4338443389cd", "id": "torch.utils.tensorboard", "ignore_all": true, "interface_hash": "2f012625032a299fd535a6ab55a31045c6ada748", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\__init__.py", "plugin_data": null, "size": 499, "suppressed": ["tensorboard.summary.writer.record_writer", "tensorboard", "traitlets.utils.warnings"], "version_id": "1.15.0"}