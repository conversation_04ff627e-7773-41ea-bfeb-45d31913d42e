{"data_mtime": 1755649448, "dep_lines": [65, 66, 42, 61, 63, 64, 72, 76, 77, 33, 40, 41, 62, 70, 72, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 25, 25, 5, 10, 10, 5, 5, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["torch.fx.experimental.proxy_tensor", "torch.fx.experimental.symbolic_shapes", "torch._dynamo.debug_utils", "torch._dynamo.utils", "torch._inductor.output_code", "torch._library.fake_class_registry", "torch._dynamo.config", "torch._inductor.compile_fx", "torch._inductor.utils", "collections.abc", "torch.fx", "torch.nn", "torch._environment", "torch.hub", "torch._dynamo", "<PERSON><PERSON><PERSON><PERSON>", "copy", "functools", "io", "logging", "os", "shutil", "subprocess", "sys", "textwrap", "uuid", "importlib", "tempfile", "typing", "typing_extensions", "torch", "builtins", "inspect", "html", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "_frozen_importlib", "abc", "torch._inductor", "torch.nn.modules", "torch.nn.modules.module"], "hash": "645ace724540d83099c266337daecc85f15850b0", "id": "torch._dynamo.repro.after_aot", "ignore_all": true, "interface_hash": "17d8ef961a2bf916b1aec0a238bc432cfbc8f871", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_dynamo\\repro\\after_aot.py", "plugin_data": null, "size": 37376, "suppressed": [], "version_id": "1.15.0"}