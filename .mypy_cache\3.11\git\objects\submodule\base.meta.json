{"data_mtime": 1755656862, "dep_lines": [37, 26, 27, 68, 10, 17, 18, 19, 20, 28, 64, 67, 69, 70, 6, 7, 8, 9, 11, 12, 13, 14, 16, 47, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 10, 5, 5, 5, 5, 5, 5, 25, 25, 25, 10, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.util", "git.objects.base", "git.objects.util", "git.objects.commit", "os.path", "git.cmd", "git.compat", "git.config", "git.exc", "git.util", "git.types", "git.index", "git.refs", "git.repo", "gc", "io", "logging", "os", "stat", "sys", "uuid", "urllib", "git", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "configparser", "git.diff", "git.index.base", "git.index.typ", "git.objects.blob", "git.objects.tag", "git.objects.tree", "git.refs.head", "git.refs.reference", "git.refs.symbolic", "git.remote", "git.repo.base", "types", "typing_extensions"], "hash": "6a141e4afe86d2e401e81f6c7cd68490822c3ea5", "id": "git.objects.submodule.base", "ignore_all": true, "interface_hash": "5e4c5e84269bff15908ae1f43247b1b1e34102df", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\submodule\\base.py", "plugin_data": null, "size": 64343, "suppressed": [], "version_id": "1.15.0"}