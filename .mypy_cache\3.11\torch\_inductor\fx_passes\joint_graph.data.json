{".class": "MypyFile", "_fullname": "torch._inductor.fx_passes.joint_graph", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Arg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Arg", "kind": "Gdef"}, "CallFunction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.CallFunction", "kind": "Gdef"}, "ConstantFolder": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.constant_folding.ConstantFolder", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "KeywordArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.KeywordArg", "kind": "Gdef"}, "MULTIPLE": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.MULTIPLE", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Match", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "PatternMatcherPass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.PatternMatcherPass", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "StorageWeakRef": {".class": "SymbolTableNode", "cross_ref": "torch.multiprocessing.reductions.StorageWeakRef", "kind": "Gdef"}, "UniformValueConstantFolder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.constant_folding.ConstantFolder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", "name": "UniformValueConstantFolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.joint_graph", "mro": ["torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", "torch._inductor.constant_folding.ConstantFolder", "torch.fx.interpreter.Interpreter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "gm", "skip_constructors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "gm", "skip_constructors"], "arg_types": ["torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UniformValueConstantFolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_peephole_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder._add_peephole_patterns", "name": "_add_peephole_patterns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_peephole_patterns of UniformValueConstantFolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deduce_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder._deduce_value", "name": "_deduce_value", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deduce_value of UniformValueConstantFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_support_dynamic_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder._support_dynamic_shape", "name": "_support_dynamic_shape", "type": null}}, "add_node_replacement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.add_node_replacement", "name": "add_node_replacement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "tensor"], "arg_types": ["torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", "torch.fx.node.Node", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_node_replacement of UniformValueConstantFolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "constant_data_ptrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.constant_data_ptrs", "name": "constant_data_ptrs", "type": {".class": "Instance", "args": ["torch.fx.node.Node", "torch.multiprocessing.reductions.StorageWeakRef"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "indexing_op_packets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.indexing_op_packets", "name": "indexing_op_packets", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverloadPacket"}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "insert_placerholder_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "env"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.insert_placerholder_values", "name": "insert_placerholder_values", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "env"], "arg_types": ["torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_placerholder_values of UniformValueConstantFolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "insertable_tensor_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.insertable_tensor_check", "name": "insertable_tensor_check", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "t"], "arg_types": ["torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insertable_tensor_check of UniformValueConstantFolder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node_replacements_shapes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.node_replacements_shapes", "name": "node_replacements_shapes", "type": {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "node_storages_ptrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.node_storages_ptrs", "name": "node_storages_ptrs", "type": {".class": "Instance", "args": ["torch.fx.node.Node", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "symint_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.symint_nodes", "name": "symint_nodes", "type": "torch._inductor.fx_passes.dedupe_symint_uses._SymHashingDict"}}, "view_op_packets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.view_op_packets", "name": "view_op_packets", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "torch._ops.OpOverloadPacket"}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.joint_graph.UniformValueConstantFolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_SymHashingDict": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.dedupe_symint_uses._SymHashingDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.joint_graph.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.joint_graph.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.joint_graph.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.joint_graph.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.joint_graph.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.joint_graph.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_other_is_broadcasted_in_dim": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["match"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph._other_is_broadcasted_in_dim", "name": "_other_is_broadcasted_in_dim", "type": null}}, "_partial_softmax_pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["linear_func", "reverse", "to_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph._partial_softmax_pattern", "name": "_partial_softmax_pattern", "type": null}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.joint_graph.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "bmm_to_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["match", "mat1", "mat2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.joint_graph.bmm_to_mm", "name": "bmm_to_mm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["match", "mat1", "mat2"], "arg_types": ["torch._inductor.pattern_matcher.Match", "torch.fx.node.Node", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bmm_to_mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.bmm_to_mm", "name": "bmm_to_mm", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "bmm_to_mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "canonicalize_aten_ir_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.canonicalize_aten_ir_passes", "name": "canonicalize_aten_ir_passes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canonicalize_aten_ir_passes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "canonicalize_quant_mapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.canonicalize_quant_mapping", "name": "canonicalize_quant_mapping", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canonicalize_quant_mapping", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_device": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.decompose_mem_bound_mm.check_device", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "constant_fold_uniform_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.constant_fold_uniform_value", "name": "constant_fold_uniform_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constant_fold_uniform_value", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "definitely_equal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old_sizes", "new_sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.definitely_equal", "name": "definitely_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old_sizes", "new_sizes"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.SymInt", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch.SymInt", "torch.fx.node.Node", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "definitely_equal", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "div_softmax_pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 5], "arg_names": ["match", "inp", "other", "dim", "keepdim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.div_softmax_pattern", "name": "div_softmax_pattern", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5], "arg_names": ["match", "inp", "other", "dim", "keepdim", "dtype"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "div_softmax_pattern", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fix_iota_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["match", "length", "start", "step", "dtype", "device", "requires_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.joint_graph.fix_iota_device", "name": "fix_iota_device", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["match", "length", "start", "step", "dtype", "device", "requires_grad"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fix_iota_device", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.fix_iota_device", "name": "fix_iota_device", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "fix_iota_device", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_gpu_type": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_gpu_type", "kind": "Gdef"}, "guard_or_false": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.guard_or_false", "kind": "Gdef"}, "guard_or_true": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.guard_or_true", "kind": "Gdef"}, "init_once_fakemode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.init_once_fakemode", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "joint_graph_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.joint_graph_passes", "name": "joint_graph_passes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "joint_graph_passes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lazy_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.joint_graph.lazy_init", "name": "lazy_init", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.lazy_init", "name": "lazy_init", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lazy_init", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.joint_graph.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mul_softmax_pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 5], "arg_names": ["match", "inp", "other", "dim", "keepdim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.mul_softmax_pattern", "name": "mul_softmax_pattern", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5], "arg_names": ["match", "inp", "other", "dim", "keepdim", "dtype"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mul_softmax_pattern", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "pass_patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.joint_graph.pass_patterns", "name": "pass_patterns", "type": {".class": "Instance", "args": ["torch._inductor.pattern_matcher.PatternMatcherPass"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.joint_graph.patterns", "name": "patterns", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "pointless_convert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["match", "arg", "dtype1", "dtype2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.joint_graph.pointless_convert", "name": "pointless_convert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["match", "arg", "dtype1", "dtype2"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._C.dtype", "torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pointless_convert", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.pointless_convert", "name": "pointless_convert", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "pointless_convert", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pointless_permute_pair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["match", "arg", "perm1", "perm2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.joint_graph.pointless_permute_pair", "name": "pointless_permute_pair", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["match", "arg", "perm1", "perm2"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pointless_permute_pair", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.pointless_permute_pair", "name": "pointless_permute_pair", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "pointless_permute_pair", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pointless_view": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["match", "arg", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.joint_graph.pointless_view", "name": "pointless_view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["match", "arg", "size"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pointless_view", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.pointless_view", "name": "pointless_view", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "pointless_view", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pointless_view_pair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["match", "arg", "size1", "size2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.joint_graph.pointless_view_pair", "name": "pointless_view_pair", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["match", "arg", "size1", "size2"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pointless_view_pair", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.joint_graph.pointless_view_pair", "name": "pointless_view_pair", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "pointless_view_pair", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prims": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.joint_graph.prims", "name": "prims", "type": "torch._ops._OpNamespace"}}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "register_graph_pattern": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.register_graph_pattern", "kind": "Gdef"}, "remove_no_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "zeros", "ones"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.remove_no_ops", "name": "remove_no_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["gm", "zeros", "ones"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_no_ops", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_redundant_views": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.joint_graph.remove_redundant_views", "name": "remove_redundant_views", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_redundant_views", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replace_random_passes": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.replace_random.replace_random_passes", "kind": "Gdef"}, "reverse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.joint_graph.reverse", "name": "reverse", "type": "builtins.bool"}}, "stable_topological_sort": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.stable_topological_sort", "kind": "Gdef"}, "statically_known_true": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.statically_known_true", "kind": "Gdef"}, "to_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.joint_graph.to_dtype", "name": "to_dtype", "type": "builtins.bool"}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\joint_graph.py"}