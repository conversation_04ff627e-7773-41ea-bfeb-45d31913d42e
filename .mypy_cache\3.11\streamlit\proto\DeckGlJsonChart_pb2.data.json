{".class": "MypyFile", "_fullname": "streamlit.proto.DeckGlJsonChart_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DeckGlJsonChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "DeckGlJsonChart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart", "name": "DeckGl<PERSON>son<PERSON>hart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.DeckGlJsonChart_pb2", "mro": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "form_id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "form_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mapbox_token"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "mapbox_token"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "selection_mode"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "selection_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tooltip"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "tooltip"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of DeckGlJsonChart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DeckGlJsonChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "FORM_ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.FORM_ID_FIELD_NUMBER", "name": "FORM_ID_FIELD_NUMBER", "type": "builtins.int"}}, "HEIGHT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.HEIGHT_FIELD_NUMBER", "name": "HEIGHT_FIELD_NUMBER", "type": "builtins.int"}}, "ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.ID_FIELD_NUMBER", "name": "ID_FIELD_NUMBER", "type": "builtins.int"}}, "JSON_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.JSON_FIELD_NUMBER", "name": "JSON_FIELD_NUMBER", "type": "builtins.int"}}, "MAPBOX_TOKEN_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.MAPBOX_TOKEN_FIELD_NUMBER", "name": "MAPBOX_TOKEN_FIELD_NUMBER", "type": "builtins.int"}}, "MULTI_OBJECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.MULTI_OBJECT", "name": "MULTI_OBJECT", "type": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType"}}, "SELECTION_MODE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.SELECTION_MODE_FIELD_NUMBER", "name": "SELECTION_MODE_FIELD_NUMBER", "type": "builtins.int"}}, "SINGLE_OBJECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.SINGLE_OBJECT", "name": "SINGLE_OBJECT", "type": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType"}}, "SelectionMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.SelectionMode", "name": "SelectionMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.SelectionMode", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.DeckGlJsonChart_pb2", "mro": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.SelectionMode", "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.SelectionMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.SelectionMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TOOLTIP_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.TOOLTIP_FIELD_NUMBER", "name": "TOOLTIP_FIELD_NUMBER", "type": "builtins.int"}}, "USE_CONTAINER_WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.USE_CONTAINER_WIDTH_FIELD_NUMBER", "name": "USE_CONTAINER_WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.WIDTH_FIELD_NUMBER", "name": "WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "_SelectionMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode", "name": "_SelectionMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.DeckGlJsonChart_pb2", "mro": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.V", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.DeckGlJsonChart_pb2", "mro": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SelectionModeEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper", "name": "_SelectionModeEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.DeckGlJsonChart_pb2", "mro": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DeckGlJsonChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "MULTI_OBJECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper.MULTI_OBJECT", "name": "MULTI_OBJECT", "type": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType"}}, "SINGLE_OBJECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper.SINGLE_OBJECT", "name": "SINGLE_OBJECT", "type": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionModeEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "json", "tooltip", "use_container_width", "id", "mapbox_token", "width", "height", "selection_mode", "form_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "json", "tooltip", "use_container_width", "id", "mapbox_token", "width", "height", "selection_mode", "form_id"], "arg_types": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart", "builtins.str", "builtins.str", "builtins.bool", "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart._SelectionMode.ValueType"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DeckGlJsonChart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "form_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.form_id", "name": "form_id", "type": "builtins.str"}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.height", "name": "height", "type": "builtins.int"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.id", "name": "id", "type": "builtins.str"}}, "json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.json", "name": "json", "type": "builtins.str"}}, "mapbox_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.mapbox_token", "name": "mapbox_token", "type": "builtins.str"}}, "selection_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.selection_mode", "name": "selection_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selection_mode of DeckGlJsonChart", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DeckGlJsonChart_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.selection_mode", "name": "selection_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selection_mode of DeckGlJsonChart", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DeckGlJsonChart_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tooltip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.tooltip", "name": "tooltip", "type": "builtins.str"}}, "use_container_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.use_container_width", "name": "use_container_width", "type": "builtins.bool"}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.width", "name": "width", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___DeckGlJsonChart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.DeckGlJsonChart_pb2.global___DeckGlJsonChart", "line": 102, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.DeckGlJsonChart_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.DeckGlJsonChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\DeckGlJsonChart_pb2.pyi"}