{".class": "MypyFile", "_fullname": "streamlit.proto.PageConfig_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "PageConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig", "name": "PageConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig", "builtins.object"], "names": {".class": "SymbolTable", "AUTO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.AUTO", "name": "AUTO", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "CENTERED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.CENTERED", "name": "CENTERED", "type": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType"}}, "COLLAPSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.COLLAPSED", "name": "COLLAPSED", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "favicon"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "favicon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "initial_sidebar_state"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "initial_sidebar_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "layout"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "layout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "menu_items"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "menu_items"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "title"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "title"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of PageConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "EXPANDED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.EXPANDED", "name": "EXPANDED", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "FAVICON_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.FAVICON_FIELD_NUMBER", "name": "FAVICON_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "menu_items"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "menu_items"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of PageConfig", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INITIAL_SIDEBAR_STATE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.INITIAL_SIDEBAR_STATE_FIELD_NUMBER", "name": "INITIAL_SIDEBAR_STATE_FIELD_NUMBER", "type": "builtins.int"}}, "LAYOUT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.LAYOUT_FIELD_NUMBER", "name": "LAYOUT_FIELD_NUMBER", "type": "builtins.int"}}, "LAYOUT_UNSET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.LAYOUT_UNSET", "name": "LAYOUT_UNSET", "type": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType"}}, "Layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.PageConfig_pb2.PageConfig._Layout"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.Layout", "name": "Layout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.Layout", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig.Layout", "streamlit.proto.PageConfig_pb2.PageConfig._Layout", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.Layout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageConfig_pb2.PageConfig.Layout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MENU_ITEMS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MENU_ITEMS_FIELD_NUMBER", "name": "MENU_ITEMS_FIELD_NUMBER", "type": "builtins.int"}}, "MenuItems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", "name": "MenuItems", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", "builtins.object"], "names": {".class": "SymbolTable", "ABOUT_SECTION_MD_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.ABOUT_SECTION_MD_FIELD_NUMBER", "name": "ABOUT_SECTION_MD_FIELD_NUMBER", "type": "builtins.int"}}, "CLEAR_ABOUT_MD_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.CLEAR_ABOUT_MD_FIELD_NUMBER", "name": "CLEAR_ABOUT_MD_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "about_section_md"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "about_section_md"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "clear_about_md"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "clear_about_md"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "get_help_url"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "get_help_url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hide_get_help"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "hide_get_help"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hide_report_a_bug"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "hide_report_a_bug"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "report_a_bug_url"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "report_a_bug_url"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of MenuItems", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "GET_HELP_URL_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.GET_HELP_URL_FIELD_NUMBER", "name": "GET_HELP_URL_FIELD_NUMBER", "type": "builtins.int"}}, "HIDE_GET_HELP_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.HIDE_GET_HELP_FIELD_NUMBER", "name": "HIDE_GET_HELP_FIELD_NUMBER", "type": "builtins.int"}}, "HIDE_REPORT_A_BUG_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.HIDE_REPORT_A_BUG_FIELD_NUMBER", "name": "HIDE_REPORT_A_BUG_FIELD_NUMBER", "type": "builtins.int"}}, "REPORT_A_BUG_URL_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.REPORT_A_BUG_URL_FIELD_NUMBER", "name": "REPORT_A_BUG_URL_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "get_help_url", "hide_get_help", "report_a_bug_url", "hide_report_a_bug", "about_section_md", "clear_about_md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "get_help_url", "hide_get_help", "report_a_bug_url", "hide_report_a_bug", "about_section_md", "clear_about_md"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", "builtins.str", "builtins.bool", "builtins.str", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MenuItems", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "about_section_md": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.about_section_md", "name": "about_section_md", "type": "builtins.str"}}, "clear_about_md": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.clear_about_md", "name": "clear_about_md", "type": "builtins.bool"}}, "get_help_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.get_help_url", "name": "get_help_url", "type": "builtins.str"}}, "hide_get_help": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.hide_get_help", "name": "hide_get_help", "type": "builtins.bool"}}, "hide_report_a_bug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.hide_report_a_bug", "name": "hide_report_a_bug", "type": "builtins.bool"}}, "report_a_bug_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.report_a_bug_url", "name": "report_a_bug_url", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SIDEBAR_UNSET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.SIDEBAR_UNSET", "name": "SIDEBAR_UNSET", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "SidebarState": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.PageConfig_pb2.PageConfig._SidebarState"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.SidebarState", "name": "SidebarState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.SidebarState", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig.SidebarState", "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.SidebarState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageConfig_pb2.PageConfig.SidebarState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TITLE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.TITLE_FIELD_NUMBER", "name": "TITLE_FIELD_NUMBER", "type": "builtins.int"}}, "WIDE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.WIDE", "name": "WIDE", "type": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType"}}, "_Layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._Layout", "name": "_Layout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._Layout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig._Layout", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.V", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageConfig_pb2.PageConfig._Layout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LayoutEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper", "name": "_LayoutEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "CENTERED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper.CENTERED", "name": "CENTERED", "type": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "LAYOUT_UNSET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper.LAYOUT_UNSET", "name": "LAYOUT_UNSET", "type": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType"}}, "WIDE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper.WIDE", "name": "WIDE", "type": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageConfig_pb2.PageConfig._LayoutEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SidebarState": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState", "name": "_SidebarState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig._SidebarState", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.V", "line": 67, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SidebarStateEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper", "name": "_SidebarStateEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PageConfig_pb2", "mro": ["streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "AUTO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper.AUTO", "name": "AUTO", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "COLLAPSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper.COLLAPSED", "name": "COLLAPSED", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "EXPANDED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper.EXPANDED", "name": "EXPANDED", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "SIDEBAR_UNSET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper.SIDEBAR_UNSET", "name": "SIDEBAR_UNSET", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarStateEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "title", "favicon", "layout", "initial_sidebar_state", "menu_items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "title", "favicon", "layout", "initial_sidebar_state", "menu_items"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig", "builtins.str", "builtins.str", "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType", "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType", {".class": "UnionType", "items": ["streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PageConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "favicon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.favicon", "name": "favicon", "type": "builtins.str"}}, "initial_sidebar_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.initial_sidebar_state", "name": "initial_sidebar_state", "type": "streamlit.proto.PageConfig_pb2.PageConfig._SidebarState.ValueType"}}, "layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.layout", "name": "layout", "type": "streamlit.proto.PageConfig_pb2.PageConfig._Layout.ValueType"}}, "menu_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.menu_items", "name": "menu_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "menu_items of PageConfig", "ret_type": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.menu_items", "name": "menu_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PageConfig_pb2.PageConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "menu_items of PageConfig", "ret_type": "streamlit.proto.PageConfig_pb2.PageConfig.MenuItems", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.title", "name": "title", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PageConfig_pb2.PageConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PageConfig_pb2.PageConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PageConfig_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___PageConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.PageConfig_pb2.global___PageConfig", "line": 146, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PageConfig_pb2.PageConfig"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PageConfig_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PageConfig_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\PageConfig_pb2.pyi"}