{".class": "MypyFile", "_fullname": "torch._inductor.fx_passes.pre_grad", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "GraphTransformObserver": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.graph_transform_observer.GraphTransformObserver", "kind": "Gdef"}, "NormalizedLinearNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode", "name": "NormalizedLinearNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.pre_grad", "mro": ["torch._inductor.fx_passes.pre_grad.NormalizedLinearNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.pre_grad.NormalizedLinearNode", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NormalizedLinearNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode.get_bias", "name": "get_bias", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.pre_grad.NormalizedLinearNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bias of NormalizedLinearNode", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode.get_input", "name": "get_input", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.pre_grad.NormalizedLinearNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_input of NormalizedLinearNode", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode.get_weight", "name": "get_weight", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.pre_grad.NormalizedLinearNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_weight of NormalizedLinearNode", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode.node", "name": "node", "type": "torch.fx.node.Node"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.pre_grad.NormalizedLinearNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NormalizedMatmulNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode", "name": "NormalizedMatmulNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.pre_grad", "mro": ["torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NormalizedMatmulNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode.get_input", "name": "get_input", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_input of NormalizedMatmulNode", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_other": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode.get_other", "name": "get_other", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_other of NormalizedMatmulNode", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode.node", "name": "node", "type": "torch.fx.node.Node"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.pre_grad.NormalizedMatmulNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PRE_GRAD_FUSIONS": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.group_batch_fusion.PRE_GRAD_FUSIONS", "kind": "Gdef"}, "PRE_GRAD_PATTERNS": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.split_cat.PRE_GRAD_PATTERNS", "kind": "Gdef"}, "PatternMatcherPass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.PatternMatcherPass", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "ShapeProp": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.shape_prop.ShapeProp", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.pre_grad.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.pre_grad.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.pre_grad.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.pre_grad.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.pre_grad.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.pre_grad.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_pass_name_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad._get_pass_name_func", "name": "_get_pass_name_func", "type": null}}, "_run_pre_dispatch_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["gm", "example_inputs", "add_passes", "remove_passes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad._run_pre_dispatch_passes", "name": "_run_pre_dispatch_passes", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["gm", "example_inputs", "add_passes", "remove_passes"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_pre_dispatch_passes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_permute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.check_permute", "name": "check_permute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_permute", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "detect_fake_mode": {".class": "SymbolTableNode", "cross_ref": "torch._guards.detect_fake_mode", "kind": "Gdef"}, "efficient_conv_bn_eval_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.efficient_conv_bn_eval_pass", "name": "efficient_conv_bn_eval_pass", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "fetch_attr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["target", "mod"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.fetch_attr", "name": "fetch_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["target", "mod"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_attr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse_chunk_reshape_concat_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.fuse_chunk_reshape_concat_pass", "name": "fuse_chunk_reshape_concat_pass", "type": null}}, "fuse_chunk_reshape_unsqueeze_concat_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.fuse_chunk_reshape_unsqueeze_concat_pass", "name": "fuse_chunk_reshape_unsqueeze_concat_pass", "type": null}}, "fuse_chunk_squeeze_cat_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.fuse_chunk_squeeze_cat_pass", "name": "fuse_chunk_squeeze_cat_pass", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "fuse_conv_bn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["gm", "inplace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.fuse_conv_bn", "name": "fuse_conv_bn", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["gm", "inplace"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse_conv_bn", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse_conv_bn_eval": {".class": "SymbolTableNode", "cross_ref": "torch.nn.utils.fusion.fuse_conv_bn_eval", "kind": "Gdef"}, "fuse_conv_bn_weights": {".class": "SymbolTableNode", "cross_ref": "torch.nn.utils.fusion.fuse_conv_bn_weights", "kind": "Gdef"}, "fuse_fx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "example_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.fuse_fx", "name": "fuse_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["gm", "example_inputs"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse_fx", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse_parallel_linear_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.fuse_parallel_linear_pass", "name": "fuse_parallel_linear_pass", "type": null}}, "fuse_split_getitem_squeeze_cat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.fuse_split_getitem_squeeze_cat", "name": "fuse_split_getitem_squeeze_cat", "type": null}}, "fuse_split_linear_add_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.fuse_split_linear_add_pass", "name": "fuse_split_linear_add_pass", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "group_batch_fusion_passes": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.group_batch_fusion.group_batch_fusion_passes", "kind": "Gdef"}, "init_once_fakemode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.init_once_fakemode", "kind": "Gdef"}, "is_cpu_device": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.is_cpu_device", "kind": "Gdef"}, "is_same_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["inductor_dict", "optimus_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.is_same_dict", "name": "is_same_dict", "type": null}}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "lazy_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.pre_grad.lazy_init", "name": "lazy_init", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.pre_grad.lazy_init", "name": "lazy_init", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lazy_init", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "linear_permute_fusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.linear_permute_fusion", "name": "linear_permute_fusion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linear_permute_fusion", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linear_transpose": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["input", "weight", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.linear_transpose", "name": "linear_transpose", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["input", "weight", "bias"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linear_transpose", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "matches_module_function_pattern": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_utils.matches_module_function_pattern", "kind": "Gdef"}, "matches_module_pattern": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.optimization.matches_module_pattern", "kind": "Gdef"}, "merge_concats_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.merge_concats_pass", "name": "merge_concats_pass", "type": null}}, "merge_getitem_cat_pass_aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.merge_getitem_cat_pass_aten", "name": "merge_getitem_cat_pass_aten", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "merge_splits_pass_aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.merge_splits_pass_aten", "name": "merge_splits_pass_aten", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "merge_stack_tahn_unbind_pass_aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.merge_stack_tahn_unbind_pass_aten", "name": "merge_stack_tahn_unbind_pass_aten", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "mutate_cat_pass_aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.mutate_cat_pass_aten", "name": "mutate_cat_pass_aten", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "normalization_pass_aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.normalization_pass_aten", "name": "normalization_pass_aten", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "normalize_node_kwargs_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.normalize_node_kwargs_pass", "name": "normalize_node_kwargs_pass", "type": null}}, "numpy_compat_normalization": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.fx_passes.misc_patterns.numpy_compat_normalization", "kind": "Gdef"}, "pass_execution_and_save": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.pass_execution_and_save", "kind": "Gdef"}, "permute_linear_fusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.permute_linear_fusion", "name": "permute_linear_fusion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "permute_linear_fusion", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "permute_matmul_fusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.permute_matmul_fusion", "name": "permute_matmul_fusion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "permute_matmul_fusion", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_grad_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["gm", "example_inputs", "add_passes", "remove_passes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.pre_grad_passes", "name": "pre_grad_passes", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["gm", "example_inputs", "add_passes", "remove_passes"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pre_grad_passes", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "relu_nan_to_num": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.relu_nan_to_num", "name": "relu_nan_to_num", "type": null}}, "remove_identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.remove_identity", "name": "remove_identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_identity", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_noop_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.remove_noop_pass", "name": "remove_noop_pass", "type": null}}, "remove_reshape_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.remove_reshape_pass", "name": "remove_reshape_pass", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "remove_split_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["graph", "shape_prop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.remove_split_ops", "name": "remove_split_ops", "type": null}}, "remove_split_ops_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.remove_split_ops_pass", "name": "remove_split_ops_pass", "type": null}}, "remove_split_with_size_one_pass_aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.remove_split_with_size_one_pass_aten", "name": "remove_split_with_size_one_pass_aten", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "replace_node_module": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.optimization.replace_node_module", "kind": "Gdef"}, "save_inductor_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["pass_to_compare"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.save_inductor_dict", "name": "save_inductor_dict", "type": null}}, "shape_prop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mod"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.shape_prop", "name": "shape_prop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mod"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape_prop", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sink_cat_after_pointwise": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.sink_cat_after_pointwise", "name": "sink_cat_after_pointwise", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sink_cat_after_pointwise", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split_cat_pass_aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.split_cat_pass_aten", "name": "split_cat_pass_aten", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "stable_topological_sort": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.stable_topological_sort", "kind": "Gdef"}, "stack_to_unsqueeze_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.stack_to_unsqueeze_pass", "name": "stack_to_unsqueeze_pass", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "trace_structured": {".class": "SymbolTableNode", "cross_ref": "torch._logging._internal.trace_structured", "kind": "Gdef"}, "transpose_linear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["input", "weight", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.transpose_linear", "name": "transpose_linear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["input", "weight", "bias"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transpose_linear", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transpose_matmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["A", "B", "Atrans", "Btrans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.transpose_matmul", "name": "transpose_matmul", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["A", "B", "Atrans", "Btrans"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transpose_matmul", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "unbind_stack_pass_aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.pre_grad.unbind_stack_pass_aten", "name": "unbind_stack_pass_aten", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "use_matmul_fuse_lce_replace_first_LCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.use_matmul_fuse_lce_replace_first_LCE", "name": "use_matmul_fuse_lce_replace_first_LCE", "type": null}}, "use_matmul_lce_replace_normal_LCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.use_matmul_lce_replace_normal_LCE", "name": "use_matmul_lce_replace_normal_LCE", "type": null}}, "use_triton_dot_compress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.use_triton_dot_compress", "name": "use_triton_dot_compress", "type": null}}, "use_triton_lce_replace_normal_LCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.use_triton_lce_replace_normal_LCE", "name": "use_triton_lce_replace_normal_LCE", "type": null}}, "use_triton_lce_replace_normal_LCE_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "shape_prop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.use_triton_lce_replace_normal_LCE_helper", "name": "use_triton_lce_replace_normal_LCE_helper", "type": null}}, "use_triton_lce_replace_simple_LCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.use_triton_lce_replace_simple_LCE", "name": "use_triton_lce_replace_simple_LCE", "type": null}}, "use_triton_lce_replace_simple_LCE_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "shape_prop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.pre_grad.use_triton_lce_replace_simple_LCE_helper", "name": "use_triton_lce_replace_simple_LCE_helper", "type": null}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\pre_grad.py"}