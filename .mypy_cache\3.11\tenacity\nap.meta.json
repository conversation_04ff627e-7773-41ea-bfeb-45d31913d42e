{"data_mtime": 1755656859, "dep_lines": [18, 19, 22, 1, 1, 1], "dep_prios": [10, 10, 25, 5, 30, 30], "dependencies": ["time", "typing", "threading", "builtins", "_frozen_importlib", "abc"], "hash": "b586e8e91a90b3770906a7d73800a474714bb3f3", "id": "tenacity.nap", "ignore_all": true, "interface_hash": "d72b5c7664a711bc176c0bc458c9d7d3c41e8c33", "mtime": 1755656298, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\tenacity\\nap.py", "plugin_data": null, "size": 1383, "suppressed": [], "version_id": "1.15.0"}