{".class": "MypyFile", "_fullname": "torch.distributed.fsdp._fully_shard", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CPUOffloadPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_api.CPUOffloadPolicy", "kind": "Gdef"}, "FSDPModule": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "kind": "Gdef"}, "MixedPrecisionPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "kind": "Gdef"}, "OffloadPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", "kind": "Gdef"}, "UnshardHandle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._fully_shard.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "fully_shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "kind": "Gdef"}, "register_fsdp_forward_method": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fully_shard.register_fsdp_forward_method", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_fully_shard\\__init__.py"}