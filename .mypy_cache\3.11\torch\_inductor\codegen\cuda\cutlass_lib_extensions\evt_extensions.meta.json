{"data_mtime": 1755649448, "dep_lines": [12, 58, 58, 5, 10, 59, 1, 25, 26, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 39, 42, 48, 45, 30, 33, 36, 3, 51], "dep_prios": [5, 10, 20, 5, 5, 5, 5, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["torch._inductor.codegen.cuda.cutlass_utils", "torch._inductor.codegen.cuda.cuda_env", "torch._inductor.codegen.cuda", "torch._inductor.ir", "torch.utils._ordered_set", "torch._inductor.utils", "typing", "ast", "ctypes", "textwrap", "builtins", "os", "torch", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "functools", "torch._inductor.codegen.common", "torch.utils"], "hash": "a53a75aa7b742af73f48c50cc4644bab356f9c5e", "id": "torch._inductor.codegen.cuda.cutlass_lib_extensions.evt_extensions", "ignore_all": true, "interface_hash": "7cc0d057b0160e2c8037b0a274ed51960ba3769c", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cutlass_lib_extensions\\evt_extensions.py", "plugin_data": null, "size": 10083, "suppressed": ["cutlass.backend.evt.backend.emitter_base", "cutlass.backend.evt.backend.sm90_emitter", "cutlass.backend.evt.ir.tensor", "cutlass.backend.evt.frontend", "cutlass.backend.c_types", "cutlass.backend.epilogue", "cutlass.backend.evt", "sympy", "cutlass_library"], "version_id": "1.15.0"}