{"data_mtime": 1755649448, "dep_lines": [7, 27, 11, 12, 14, 15, 16, 17, 18, 26, 4, 15, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 20, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30], "dependencies": ["torch._inductor.codegen.cuda.cutlass_python_evt", "torch._inductor.codegen.common", "torch._inductor.utils", "torch.utils._ordered_set", "torch._dynamo.utils", "torch._inductor.config", "torch._inductor.codecache", "torch._inductor.ir", "torch._inductor.scheduler", "torch._inductor.virtualized", "collections.abc", "torch._inductor", "<PERSON><PERSON><PERSON>", "logging", "typing", "builtins", "os", "torch", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "enum", "torch.utils"], "hash": "fdee9e9df97f0eed9b4ff2c8c59a025872fc15fc", "id": "torch._inductor.codegen.cuda.cuda_cpp_scheduling", "ignore_all": true, "interface_hash": "24e55454289664b5d5cbd22a9a2f19a66753e909", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cuda_cpp_scheduling.py", "plugin_data": null, "size": 12110, "suppressed": [], "version_id": "1.15.0"}