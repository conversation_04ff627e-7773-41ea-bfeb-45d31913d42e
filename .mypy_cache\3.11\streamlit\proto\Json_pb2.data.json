{".class": "MypyFile", "_fullname": "streamlit.proto.Json_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Json_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Json_pb2.google", "source_any": null, "type_of_any": 3}}}, "Json": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Json_pb2.Json", "name": "Json", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.Json_pb2.Json", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Json_pb2", "mro": ["streamlit.proto.Json_pb2.Json", "builtins.object"], "names": {".class": "SymbolTable", "BODY_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Json_pb2.Json.BODY_FIELD_NUMBER", "name": "BODY_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Json_pb2.Json.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Json_pb2.Json", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_max_expand_depth"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_max_expand_depth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "body"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "expanded"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "expanded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_expand_depth"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "max_expand_depth"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Json", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Json_pb2.Json.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Json_pb2.google", "source_any": null, "type_of_any": 3}}}, "EXPANDED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Json_pb2.Json.EXPANDED_FIELD_NUMBER", "name": "EXPANDED_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Json_pb2.Json.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Json_pb2.Json", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_max_expand_depth"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_max_expand_depth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_expand_depth"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "max_expand_depth"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Has<PERSON><PERSON> of Json", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "MAX_EXPAND_DEPTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Json_pb2.Json.MAX_EXPAND_DEPTH_FIELD_NUMBER", "name": "MAX_EXPAND_DEPTH_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Json_pb2.Json.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.Json_pb2.Json", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_max_expand_depth"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_max_expand_depth"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of Json", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "max_expand_depth"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "body", "expanded", "max_expand_depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Json_pb2.Json.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "body", "expanded", "max_expand_depth"], "arg_types": ["streamlit.proto.Json_pb2.Json", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Json_pb2.Json.body", "name": "body", "type": "builtins.str"}}, "expanded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Json_pb2.Json.expanded", "name": "expanded", "type": "builtins.bool"}}, "max_expand_depth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Json_pb2.Json.max_expand_depth", "name": "max_expand_depth", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Json_pb2.Json.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Json_pb2.Json", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Json_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Json_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Json_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Json_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Json_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Json_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___Json": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.Json_pb2.global___Json", "line": 53, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Json_pb2.Json"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.Json_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Json_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\Json_pb2.pyi"}