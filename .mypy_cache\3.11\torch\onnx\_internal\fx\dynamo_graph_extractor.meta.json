{"data_mtime": 1755649449, "dep_lines": [18, 18, 18, 19, 14, 15, 16, 17, 19, 23, 7, 9, 10, 11, 12, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 74, 74, 1], "dep_prios": [10, 10, 20, 10, 10, 10, 10, 10, 20, 25, 5, 10, 10, 10, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 20, 20, 20], "dependencies": ["torch.onnx._internal._exporter_legacy", "torch.onnx._internal.io_adapter", "torch.onnx._internal", "torch.utils._pytree", "torch._dynamo", "torch.export", "torch.fx", "torch.onnx", "torch.utils", "collections.abc", "__future__", "contextlib", "functools", "inspect", "typing", "torch", "builtins", "collections", "warnings", "operator", "itertools", "pprint", "math", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "sys", "json", "traceback", "re", "html", "_frozen_importlib", "abc", "torch.export.exported_program", "torch.fx.graph_module", "torch.nn.modules", "torch.nn.modules.module"], "hash": "6202831386c3a54d66908b280c0bf711689d56d9", "id": "torch.onnx._internal.fx.dynamo_graph_extractor", "ignore_all": true, "interface_hash": "2994447146be444bbb551d3fc4059af192552660", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\dynamo_graph_extractor.py", "plugin_data": null, "size": 8517, "suppressed": ["transformers.modeling_outputs", "transformers", "traitlets.utils.warnings"], "version_id": "1.15.0"}