{".class": "MypyFile", "_fullname": "git.objects.base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AnyGitObject": {".class": "SymbolTableNode", "cross_ref": "git.types.AnyGitObject", "kind": "Gdef", "module_public": false}, "Blob": {".class": "SymbolTableNode", "cross_ref": "git.objects.blob.Blob", "kind": "Gdef", "module_public": false}, "GitObjectTypeString": {".class": "SymbolTableNode", "cross_ref": "git.types.GitObjectTypeString", "kind": "Gdef", "module_public": false}, "IndexObjUnion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.objects.base.IndexObjUnion", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["git.objects.tree.Tree", "git.objects.blob.Blob", "git.objects.submodule.base.Submodule"], "uses_pep604_syntax": false}}}, "IndexObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.objects.base.Object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.base.IndexObject", "name": "IndexObject", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "git.objects.base.IndexObject", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.objects.base", "mro": ["git.objects.base.IndexObject", "git.objects.base.Object", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.IndexObject.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.IndexObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of IndexObject", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "repo", "<PERSON><PERSON>", "mode", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.IndexObject.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "repo", "<PERSON><PERSON>", "mode", "path"], "arg_types": ["git.objects.base.IndexObject", "git.repo.base.Repo", "builtins.bytes", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IndexObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.objects.base.IndexObject.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_id_attribute_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.objects.base.IndexObject._id_attribute_", "name": "_id_attribute_", "type": "builtins.str"}}, "_set_cache_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.IndexObject._set_cache_", "name": "_set_cache_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": ["git.objects.base.IndexObject", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_cache_ of IndexObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abspath": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.objects.base.IndexObject.abspath", "name": "abspath", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.IndexObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "abspath of IndexObject", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.objects.base.IndexObject.abspath", "name": "abspath", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.IndexObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "abspath of IndexObject", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.base.IndexObject.mode", "name": "mode", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.objects.base.IndexObject.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.IndexObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of IndexObject", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.objects.base.IndexObject.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.IndexObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of IndexObject", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.base.IndexObject.path", "name": "path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.base.IndexObject.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.base.IndexObject", "values": [], "variance": 0}, "slots": ["<PERSON><PERSON>", "mode", "path", "repo", "size"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LazyMixin": {".class": "SymbolTableNode", "cross_ref": "git.util.LazyMixin", "kind": "Gdef", "module_public": false}, "OStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.objects.base.OStream", "name": "OStream", "type": {".class": "AnyType", "missing_import_name": "git.objects.base.OStream", "source_any": null, "type_of_any": 3}}}, "Object": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.base.Object", "name": "Object", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "git.objects.base.Object", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.objects.base", "mro": ["git.objects.base.Object", "builtins.object"], "names": {".class": "SymbolTable", "NULL_BIN_SHA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.objects.base.Object.NULL_BIN_SHA", "name": "NULL_BIN_SHA", "type": "builtins.bytes"}}, "NULL_HEX_SHA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.objects.base.Object.NULL_HEX_SHA", "name": "NULL_HEX_SHA", "type": "builtins.str"}}, "TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.objects.base.Object.TYPES", "name": "TYPES", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": {".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": {".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": {".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": {".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.Object.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["git.objects.base.Object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Object", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.Object.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.Object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Object", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "<PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.Object.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "<PERSON><PERSON>"], "arg_types": ["git.objects.base.Object", "git.repo.base.Repo", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Object", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.Object.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["git.objects.base.Object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of Object", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.Object.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["git.objects.base.Object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Object", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.objects.base.Object.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.Object.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["git.objects.base.Object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Object", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_cache_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.Object._set_cache_", "name": "_set_cache_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": ["git.objects.base.Object", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_cache_ of Object", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "binsha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.base.Object.binsha", "name": "<PERSON><PERSON>", "type": "builtins.bytes"}}, "data_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.objects.base.Object.data_stream", "name": "data_stream", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.Object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_stream of Object", "ret_type": {".class": "AnyType", "missing_import_name": "git.objects.base.OStream", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.objects.base.Object.data_stream", "name": "data_stream", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.Object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_stream of Object", "ret_type": {".class": "AnyType", "missing_import_name": "git.objects.base.OStream", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hexsha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.objects.base.Object.hexsha", "name": "hex<PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.Object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of Object", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.objects.base.Object.hexsha", "name": "hex<PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.base.Object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of Object", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "repo", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.objects.base.Object.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "repo", "id"], "arg_types": [{".class": "TypeType", "item": "git.objects.base.Object"}, "git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", "git.refs.reference.Reference"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of Object", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.objects.base.Object.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "repo", "id"], "arg_types": [{".class": "TypeType", "item": "git.objects.base.Object"}, "git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", "git.refs.reference.Reference"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of Object", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new_from_sha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "repo", "sha1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.objects.base.Object.new_from_sha", "name": "new_from_sha", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "repo", "sha1"], "arg_types": [{".class": "TypeType", "item": "git.objects.base.Object"}, "git.repo.base.Repo", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_from_sha of Object", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.objects.base.Object.new_from_sha", "name": "new_from_sha", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "repo", "sha1"], "arg_types": [{".class": "TypeType", "item": "git.objects.base.Object"}, "git.repo.base.Repo", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_from_sha of Object", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "repo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.base.Object.repo", "name": "repo", "type": "git.repo.base.Repo"}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "git.objects.base.Object.size", "name": "size", "type": "builtins.int"}}, "stream_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ostream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.base.Object.stream_data", "name": "stream_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ostream"], "arg_types": ["git.objects.base.Object", {".class": "AnyType", "missing_import_name": "git.objects.base.OStream", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_data of Object", "ret_type": "git.objects.base.Object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "git.objects.base.Object.type", "name": "type", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.GitObjectTypeString"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.base.Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.base.Object", "values": [], "variance": 0}, "slots": ["<PERSON><PERSON>", "repo", "size"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "Reference": {".class": "SymbolTableNode", "cross_ref": "git.refs.reference.Reference", "kind": "Gdef", "module_public": false}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef", "module_public": false}, "Submodule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.Submodule", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tree": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.Tree", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WorkTreeRepositoryUnsupported": {".class": "SymbolTableNode", "cross_ref": "git.exc.WorkTreeRepositoryUnsupported", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.base.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "bin_to_hex": {".class": "SymbolTableNode", "cross_ref": "git.util.bin_to_hex", "kind": "Gdef", "module_public": false}, "dbtyp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.objects.base.dbtyp", "name": "dbtyp", "type": {".class": "AnyType", "missing_import_name": "git.objects.base.dbtyp", "source_any": null, "type_of_any": 3}}}, "get_object_type_by_name": {".class": "SymbolTableNode", "cross_ref": "git.objects.util.get_object_type_by_name", "kind": "Gdef", "module_public": false}, "join_path_native": {".class": "SymbolTableNode", "cross_ref": "git.util.join_path_native", "kind": "Gdef", "module_public": false}, "osp": {".class": "SymbolTableNode", "cross_ref": "os.path", "kind": "Gdef", "module_public": false}, "stream_copy": {".class": "SymbolTableNode", "cross_ref": "git.util.stream_copy", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\base.py"}