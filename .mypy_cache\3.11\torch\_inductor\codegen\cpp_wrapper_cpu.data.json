{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cpp_wrapper_cpu", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ConvertIntKey": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.ConvertIntKey", "kind": "Gdef"}, "CppWrapperCpu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "name": "CppWrapperCpu", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_wrapper_cpu", "mro": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.__init__", "name": "__init__", "type": null}}, "_compatible_with_stableivalue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._compatible_with_stableivalue", "name": "_compatible_with_stableivalue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compatible_with_stableivalue of CppWrapperCpu", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._compatible_with_stableivalue", "name": "_compatible_with_stableivalue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compatible_with_stableivalue of CppWrapperCpu", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_define_kernel_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._define_kernel_helper", "name": "_define_kernel_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_define_kernel_helper of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_extern_kernel_alloc_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extern_kernel", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._generate_extern_kernel_alloc_helper", "name": "_generate_extern_kernel_alloc_helper", "type": null}}, "_generate_extern_kernel_out_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "kernel", "out", "out_view", "args", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._generate_extern_kernel_out_helper", "name": "_generate_extern_kernel_out_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "kernel", "out", "out_view", "args", "device"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_extern_kernel_out_helper of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_kernel_call_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "kernel_name", "call_args", "device", "triton", "arg_types", "raw_keys", "raw_args", "triton_meta", "graph_name", "original_fxnode_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._generate_kernel_call_helper", "name": "_generate_kernel_call_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "kernel_name", "call_args", "device", "triton", "arg_types", "raw_keys", "raw_args", "triton_meta", "graph_name", "original_fxnode_name"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_kernel_call_helper of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_symbolic_call_arg_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._generate_symbolic_call_arg_helper", "name": "_generate_symbolic_call_arg_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg", "graph"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.codegen.wrapper.SymbolicCallArg", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_symbolic_call_arg_helper of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_temporary_array_pointer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["c_type", "elements", "force_mutable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._generate_temporary_array_pointer", "name": "_generate_temporary_array_pointer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["c_type", "elements", "force_mutable"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_temporary_array_pointer of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._generate_temporary_array_pointer", "name": "_generate_temporary_array_pointer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["c_type", "elements", "force_mutable"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_temporary_array_pointer of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_include_extra_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu._include_extra_header", "name": "_include_extra_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "header"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_include_extra_header of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_benchmark_harness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.add_benchmark_harness", "name": "add_benchmark_harness", "type": null}}, "add_device_include": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.add_device_include", "name": "add_device_include", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_device_include of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arg_var_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.arg_var_id", "name": "arg_var_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "c_type_for_prim_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.c_type_for_prim_type", "name": "c_type_for_prim_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "type_"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "c_type_for_prim_type of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cached_output_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.cached_output_id", "name": "cached_output_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "codegen_alloc_from_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "offset", "dtype", "shape", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_alloc_from_pool", "name": "codegen_alloc_from_pool", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "offset", "dtype", "shape", "stride"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_alloc_from_pool of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_conditional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conditional"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_conditional", "name": "codegen_conditional", "type": null}}, "codegen_const_run_driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_const_run_driver", "name": "codegen_const_run_driver", "type": null}}, "codegen_cpp_sizevar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "x", "simplify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_cpp_sizevar", "name": "codegen_cpp_sizevar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "x", "simplify"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cpp_wrapper_cpu.sympy", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_cpp_sizevar of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_device": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_device", "name": "codegen_device", "type": null}}, "codegen_device_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "dst", "non_blocking"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_device_copy", "name": "codegen_device_copy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "dst", "non_blocking"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_device_copy of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_dtype", "name": "codegen_dtype", "type": null}}, "codegen_dynamic_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_dynamic_scalar", "name": "codegen_dynamic_scalar", "type": null}}, "codegen_exact_buffer_reuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "old_name", "new_name", "del_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_exact_buffer_reuse", "name": "codegen_exact_buffer_reuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "old_name", "new_name", "del_line"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_exact_buffer_reuse of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_input_device_type_var_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_input_device_type_var_decl", "name": "codegen_input_device_type_var_decl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "name"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.utils.IndentedBuffer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_input_device_type_var_decl of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_input_size_var_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_input_size_var_decl", "name": "codegen_input_size_var_decl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "name"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.utils.IndentedBuffer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_input_size_var_decl of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_input_stride_var_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_input_stride_var_decl", "name": "codegen_input_stride_var_decl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "name"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.utils.IndentedBuffer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_input_stride_var_decl of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_input_symbol_assignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "value", "bound_vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_input_symbol_assignment", "name": "codegen_input_symbol_assignment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "value", "bound_vars"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", "torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cpp_wrapper_cpu.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_input_symbol_assignment of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_int_array_var": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "int_array", "writeline", "known_statically", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_int_array_var", "name": "codegen_int_array_var", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "int_array", "writeline", "known_statically", "graph"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_int_array_var of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_int_array_var", "name": "codegen_int_array_var", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "codegen_invoke_subgraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "invoke_subgraph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_invoke_subgraph", "name": "codegen_invoke_subgraph", "type": null}}, "codegen_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_layout", "name": "codegen_layout", "type": null}}, "codegen_memory_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "memory_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_memory_format", "name": "codegen_memory_format", "type": null}}, "codegen_model_constructor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_model_constructor", "name": "codegen_model_constructor", "type": null}}, "codegen_model_kernels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_model_kernels", "name": "codegen_model_kernels", "type": null}}, "codegen_multi_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_multi_output", "name": "codegen_multi_output", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.ir.MultiOutput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_multi_output of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_reinterpret_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "data", "size", "stride", "offset", "writeline", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_reinterpret_view", "name": "codegen_reinterpret_view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "data", "size", "stride", "offset", "writeline", "dtype"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_reinterpret_view of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_scalar_to_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_scalar_to_tensor", "name": "codegen_scalar_to_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_scalar_to_tensor of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_shape_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_shape_tuple", "name": "codegen_shape_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cpp_wrapper_cpu.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_shape_tuple of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_sizevar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_sizevar", "name": "codegen_sizevar", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cpp_wrapper_cpu.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_sizevar of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_subgraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subgraph", "outer_inputs", "outer_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_subgraph", "name": "codegen_subgraph", "type": null}}, "codegen_subgraph_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subgraph", "outer_inputs", "outer_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_subgraph_prefix", "name": "codegen_subgraph_prefix", "type": null}}, "codegen_subgraph_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subgraph", "outer_inputs", "outer_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_subgraph_suffix", "name": "codegen_subgraph_suffix", "type": null}}, "codegen_tensor_dtype_var_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_tensor_dtype_var_decl", "name": "codegen_tensor_dtype_var_decl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "name"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.utils.IndentedBuffer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_tensor_dtype_var_decl of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_tensor_item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "dtype", "tensor", "scalar", "indented_buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_tensor_item", "name": "codegen_tensor_item", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "dtype", "tensor", "scalar", "indented_buffer"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._C.dtype", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_tensor_item of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_tuple_access": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "basename", "name", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_tuple_access", "name": "codegen_tuple_access", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "basename", "name", "index"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_tuple_access of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_while_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "while_loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.codegen_while_loop", "name": "codegen_while_loop", "type": null}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "arg_types": ["builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "arg_types": ["builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_tmp_raii_handle_var_if_needed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "handle", "writer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.create_tmp_raii_handle_var_if_needed", "name": "create_tmp_raii_handle_var_if_needed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "handle", "writer"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", {".class": "UnionType", "items": ["torch._inductor.codegen.cpp_wrapper_cpu.HasWriteLine", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_tmp_raii_handle_var_if_needed of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "custom_op_wrapper_loaded": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.custom_op_wrapper_loaded", "name": "custom_op_wrapper_loaded", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "declared_int_array_vars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.declared_int_array_vars", "name": "declared_int_array_vars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "device": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.device", "name": "device", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "device_codegen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.device_codegen", "name": "device_codegen", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ensure_size_computed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.ensure_size_computed", "name": "ensure_size_computed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sym"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cpp_wrapper_cpu.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_size_computed of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finalize_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.finalize_prefix", "name": "finalize_prefix", "type": null}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_inference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate", "name": "generate", "type": null}}, "generate_before_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_before_suffix", "name": "generate_before_suffix", "type": null}}, "generate_c_shim_extern_kernel_alloc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extern_kernel", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_c_shim_extern_kernel_alloc", "name": "generate_c_shim_extern_kernel_alloc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "extern_kernel", "args"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.ir.<PERSON>tern<PERSON><PERSON>", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_c_shim_extern_kernel_alloc of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_c_shim_extern_kernel_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "kernel", "args", "device", "debug_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_c_shim_extern_kernel_call", "name": "generate_c_shim_extern_kernel_call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "kernel", "args", "device", "debug_args"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_c_shim_extern_kernel_call of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_c_shim_fallback_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fallback_kernel", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_c_shim_fallback_kernel", "name": "generate_c_shim_fallback_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fallback_kernel", "args"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "torch._inductor.ir.<PERSON>", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_c_shim_fallback_kernel of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_end", "name": "generate_end", "type": null}}, "generate_end_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_end_graph", "name": "generate_end_graph", "type": null}}, "generate_extern_kernel_args_decl_if_needed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op_overload", "raw_args", "output_args", "raw_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_extern_kernel_args_decl_if_needed", "name": "generate_extern_kernel_args_decl_if_needed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op_overload", "raw_args", "output_args", "raw_outputs"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, "torch._ops.HigherOrderOperator"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_wrapper_cpu._OUTPUT_ARGS_TYPE"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_extern_kernel_args_decl_if_needed of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_fallback_kernel_with_runtime_lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "buf_name", "python_kernel_name", "get_args", "op_overload", "raw_args", "outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_fallback_kernel_with_runtime_lookup", "name": "generate_fallback_kernel_with_runtime_lookup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "buf_name", "python_kernel_name", "get_args", "op_overload", "raw_args", "outputs"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, "torch._ops.HigherOrderOperator"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_fallback_kernel_with_runtime_lookup of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_fallback_kernel_with_runtime_lookup_aot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op_overload", "raw_args", "output_args", "raw_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_fallback_kernel_with_runtime_lookup_aot", "name": "generate_fallback_kernel_with_runtime_lookup_aot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op_overload", "raw_args", "output_args", "raw_outputs"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, "torch._ops.HigherOrderOperator"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_wrapper_cpu._OUTPUT_ARGS_TYPE"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_fallback_kernel_with_runtime_lookup_aot of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_fallback_kernel_with_runtime_lookup_nopython": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "get_args", "op_overload", "output_args", "raw_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_fallback_kernel_with_runtime_lookup_nopython", "name": "generate_fallback_kernel_with_runtime_lookup_nopython", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "get_args", "op_overload", "output_args", "raw_outputs"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_fallback_kernel_with_runtime_lookup_nopython of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_fallback_kernel_with_runtime_lookup_python": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "buf_name", "python_kernel_name", "op_overload", "raw_args", "output_args", "raw_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_fallback_kernel_with_runtime_lookup_python", "name": "generate_fallback_kernel_with_runtime_lookup_python", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "buf_name", "python_kernel_name", "op_overload", "raw_args", "output_args", "raw_outputs"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_fallback_kernel_with_runtime_lookup_python of CppWrapperCpu", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_float_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_float_value", "name": "generate_float_value", "type": null}}, "generate_index_put_fallback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "kernel", "x", "indices", "values", "accumulate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_index_put_fallback", "name": "generate_index_put_fallback", "type": null}}, "generate_inf_and_nan_checker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_inf_and_nan_checker", "name": "generate_inf_and_nan_checker", "type": null}}, "generate_input_output_runtime_checks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_input_output_runtime_checks", "name": "generate_input_output_runtime_checks", "type": null}}, "generate_profiler_mark_wrapper_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_profiler_mark_wrapper_call", "name": "generate_profiler_mark_wrapper_call", "type": null}}, "generate_py_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "py_args_var", "idx", "raw_arg", "arg_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_py_arg", "name": "generate_py_arg", "type": null}}, "generate_reset_kernel_saved_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_reset_kernel_saved_flags", "name": "generate_reset_kernel_saved_flags", "type": null}}, "generate_return": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output_refs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_return", "name": "generate_return", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output_refs"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_return of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_save_uncompiled_kernels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_save_uncompiled_kernels", "name": "generate_save_uncompiled_kernels", "type": null}}, "generate_scatter_fallback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "inputs", "cpp_kernel_name", "python_kernel_name", "src_is_tensor", "reduce", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_scatter_fallback", "name": "generate_scatter_fallback", "type": null}}, "generate_scoped_gil_acquire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "declarations_before_scope", "lines_in_scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_scoped_gil_acquire", "name": "generate_scoped_gil_acquire", "type": null}}, "generate_start_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.generate_start_graph", "name": "generate_start_graph", "type": null}}, "get_c_shim_func_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["kernel", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.get_c_shim_func_name", "name": "get_c_shim_func_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["kernel", "device"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_c_shim_func_name of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.get_c_shim_func_name", "name": "get_c_shim_func_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["kernel", "device"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_c_shim_func_name of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_device_include_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.get_device_include_path", "name": "get_device_include_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["device"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_device_include_path of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.get_device_include_path", "name": "get_device_include_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["device"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_device_include_path of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "included_devices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.included_devices", "name": "included_devices", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "initialized_kernels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.initialized_kernels", "name": "initialized_kernels", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["torch._inductor.codegen.common.CSEVariable"], "extra_attrs": null, "type_ref": "torch._inductor.codegen.common.Kernel"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "int_array_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.int_array_id", "name": "int_array_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kernel_callsite_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.kernel_callsite_id", "name": "kernel_callsite_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "load_custom_op_wrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.load_custom_op_wrapper", "name": "load_custom_op_wrapper", "type": null}}, "make_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "device", "dtype", "shape", "stride", "allocation_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.make_allocation", "name": "make_allocation", "type": null}}, "make_buffer_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.make_buffer_allocation", "name": "make_buffer_allocation", "type": null}}, "make_buffer_free": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.make_buffer_free", "name": "make_buffer_free", "type": null}}, "make_free_by_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names_to_del"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.make_free_by_names", "name": "make_free_by_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names_to_del"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_free_by_names of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mark_output_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.mark_output_type", "name": "mark_output_type", "type": null}}, "output_is_tensor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.output_is_tensor", "name": "output_is_tensor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scalar_to_tensor_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.scalar_to_tensor_id", "name": "scalar_to_tensor_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tmp_tensor_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.tmp_tensor_id", "name": "tmp_tensor_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "used_cached_devices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.used_cached_devices", "name": "used_cached_devices", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "used_cached_dtypes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.used_cached_dtypes", "name": "used_cached_dtypes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "used_cached_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.used_cached_layouts", "name": "used_cached_layouts", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "used_cached_memory_formats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.used_cached_memory_formats", "name": "used_cached_memory_formats", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "used_cond_predicate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.used_cond_predicate", "name": "used_cond_predicate", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "val_to_arg_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "val", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.val_to_arg_str", "name": "val_to_arg_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "val", "type_"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "val_to_arg_str of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "val_to_arg_str_for_prim_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.val_to_arg_str_for_prim_type", "name": "val_to_arg_str_for_prim_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "type_"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "val_to_arg_str_for_prim_type of CppWrapperCpu", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_constant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "hashed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.write_constant", "name": "write_constant", "type": null}}, "write_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.write_header", "name": "write_header", "type": null}}, "write_input_output_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "info_kind", "idx", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.write_input_output_info", "name": "write_input_output_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "info_kind", "idx", "name"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_input_output_info of CppWrapperCpu", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.write_prefix", "name": "write_prefix", "type": null}}, "write_wrapper_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.write_wrapper_decl", "name": "write_wrapper_decl", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEVICE_TO_ATEN": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.DEVICE_TO_ATEN", "kind": "Gdef"}, "DEVICE_TO_INT": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.DEVICE_TO_INT", "kind": "Gdef"}, "DTYPE_TO_ATEN": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.DTYPE_TO_ATEN", "kind": "Gdef"}, "DTYPE_TO_CPP": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.DTYPE_TO_CPP", "kind": "Gdef"}, "DeferredLineBase": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.DeferredLineBase", "kind": "Gdef"}, "DivideByKey": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.DivideByKey", "kind": "Gdef"}, "EnterSubgraphLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.EnterSubgraphLine", "kind": "Gdef"}, "ExitSubgraphLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.ExitSubgraphLine", "kind": "Gdef"}, "GraphLowering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.graph.GraphLowering", "kind": "Gdef"}, "HasWriteLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["writeline", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.HasWriteLine", "name": "HasWriteLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.HasWriteLine", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._inductor.codegen.cpp_wrapper_cpu", "mro": ["torch._inductor.codegen.cpp_wrapper_cpu.HasWriteLine", "builtins.object"], "names": {".class": "SymbolTable", "writeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.HasWriteLine.writeline", "name": "writeline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.cpp_wrapper_cpu.HasWriteLine", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.LineContext"}, "torch._inductor.utils.DeferredLineBase", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "writeline of HasWriteLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.HasWriteLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_wrapper_cpu.HasWriteLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "Kernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.Kernel", "kind": "Gdef"}, "LineContext": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.LineContext", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "PythonWrapperCodegen": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SymT": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.SymT", "kind": "Gdef"}, "SymTypes": {".class": "SymbolTableNode", "cross_ref": "torch.types.py_sym_types", "kind": "Gdef"}, "SymbolicCallArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.SymbolicCallArg", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "_OUTPUT_ARGS_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "torch._inductor.codegen.cpp_wrapper_cpu._OUTPUT_ARGS_TYPE", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_align": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils._align", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cexpr": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.cexpr", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "count": {".class": "SymbolTableNode", "cross_ref": "itertools.count", "kind": "Gdef"}, "ctypes": {".class": "SymbolTableNode", "cross_ref": "ctypes", "kind": "Gdef"}, "dynamo_timed": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.runtime_utils.dynamo_timed", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_device_op_overrides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.get_device_op_overrides", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "maybe_hipify_code_wrapper": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.aoti_hipify_utils.maybe_hipify_code_wrapper", "kind": "Gdef"}, "normalize_name": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.normalize_name", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "symbol_is_type": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.symbol_is_type", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_wrapper_cpu.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cpp_wrapper_cpu.sympy", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_wrapper_cpu.py"}