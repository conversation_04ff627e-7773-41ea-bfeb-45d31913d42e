{".class": "MypyFile", "_fullname": "narwhals._duration", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Container": {".class": "SymbolTableNode", "cross_ref": "typing.Container", "kind": "Gdef", "module_public": false}, "Interval": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._duration.Interval", "name": "Interval", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "narwhals._duration.Interval", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "narwhals._duration", "mro": ["narwhals._duration.Interval", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "narwhals._duration.Interval.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["narwhals._duration.Interval", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "narwhals._duration.IntervalUnit"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Interval", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["every"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "narwhals._duration.Interval._parse", "name": "_parse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["every"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse of Interval", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "narwhals._duration.IntervalUnit"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "narwhals._duration.Interval._parse", "name": "_parse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["every"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse of Interval", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "narwhals._duration.IntervalUnit"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "multiple": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "narwhals._duration.Interval.multiple", "name": "multiple", "type": "builtins.int"}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "every"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "narwhals._duration.Interval.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "every"], "arg_types": [{".class": "TypeType", "item": "narwhals._duration.Interval"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of Interval", "ret_type": "narwhals._duration.Interval", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals._duration.Interval.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "every"], "arg_types": [{".class": "TypeType", "item": "narwhals._duration.Interval"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of Interval", "ret_type": "narwhals._duration.Interval", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parse_no_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "every"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "narwhals._duration.Interval.parse_no_constraints", "name": "parse_no_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "every"], "arg_types": [{".class": "TypeType", "item": "narwhals._duration.Interval"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_no_constraints of Interval", "ret_type": "narwhals._duration.Interval", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals._duration.Interval.parse_no_constraints", "name": "parse_no_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "every"], "arg_types": [{".class": "TypeType", "item": "narwhals._duration.Interval"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_no_constraints of Interval", "ret_type": "narwhals._duration.Interval", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_timedelta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "unsupported"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "narwhals._duration.Interval.to_timedelta", "name": "to_<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "unsupported"], "arg_types": ["narwhals._duration.Interval", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "narwhals._duration.IntervalUnit"}], "extra_attrs": null, "type_ref": "typing.Container"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_<PERSON><PERSON><PERSON> of Interval", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "narwhals._duration.Interval.unit", "name": "unit", "type": {".class": "TypeAliasType", "args": [], "type_ref": "narwhals._duration.IntervalUnit"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._duration.Interval.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals._duration.Interval", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntervalUnit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "narwhals._duration.IntervalUnit", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ns"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "us"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ms"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "s"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "h"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "d"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "q"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "y"}], "uses_pep604_syntax": false}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "MONTH_MULTIPLES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "narwhals._duration.MONTH_MULTIPLES", "name": "MONTH_MULTIPLES", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "PATTERN_INTERVAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "narwhals._duration.PATTERN_INTERVAL", "name": "PATTERN_INTERVAL", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "QUARTER_MULTIPLES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "narwhals._duration.QUARTER_MULTIPLES", "name": "QUARTER_MULTIPLES", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TimedeltaKwd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "narwhals._duration.TimedeltaKwd", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "days"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "minutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "seconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "milliseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "microseconds"}], "uses_pep604_syntax": false}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_public": false}, "UNIT_TO_TIMEDELTA": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "narwhals._duration.UNIT_TO_TIMEDELTA", "name": "UNIT_TO_TIMEDELTA", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "narwhals._duration.IntervalUnit"}, {".class": "TypeAliasType", "args": [], "type_ref": "narwhals._duration.TimedeltaKwd"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "narwhals._duration.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._duration.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._duration.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._duration.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._duration.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._duration.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._duration.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "dt": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_public": false}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing.get_args", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\narwhals\\_duration.py"}