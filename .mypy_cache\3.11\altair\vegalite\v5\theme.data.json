{".class": "MypyFile", "_fullname": "altair.vegalite.v5.theme", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AltairThemes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.theme.AltairThemes", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}], "uses_pep604_syntax": false}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ENTRY_POINT_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "altair.vegalite.v5.theme", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "altair.vegalite.v5.theme.ENTRY_POINT_GROUP", "name": "ENTRY_POINT_GROUP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "altair.vegalite.v5.theme"}, "type_ref": "builtins.str"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "LiteralString": {".class": "SymbolTableNode", "cross_ref": "typing.LiteralString", "kind": "Gdef"}, "Plugin": {".class": "SymbolTableNode", "cross_ref": "altair.utils.plugin_registry.Plugin", "kind": "Gdef"}, "PluginEnabler": {".class": "SymbolTableNode", "cross_ref": "altair.utils.plugin_registry.PluginEnabler", "kind": "Gdef"}, "PluginRegistry": {".class": "SymbolTableNode", "cross_ref": "altair.utils.plugin_registry.PluginRegistry", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "ThemeConfig": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ThemeConfig", "kind": "Gdef"}, "ThemeRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "extra_attrs": null, "type_ref": "altair.utils.plugin_registry.PluginRegistry"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.theme.ThemeRegistry", "name": "ThemeRegistry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.theme.ThemeRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.theme", "mro": ["altair.vegalite.v5.theme.ThemeRegistry", "altair.utils.plugin_registry.PluginRegistry", "builtins.object"], "names": {".class": "SymbolTable", "enable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "name", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.theme.ThemeRegistry.enable", "name": "enable", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "name", "options"], "arg_types": ["altair.vegalite.v5.theme.ThemeRegistry", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.theme.AltairThemes"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.VegaThemes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable of ThemeRegistry", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "extra_attrs": null, "type_ref": "altair.utils.plugin_registry.PluginEnabler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.theme.ThemeRegistry.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["altair.vegalite.v5.theme.ThemeRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of ThemeRegistry", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "extra_attrs": null, "type_ref": "functools.partial"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.theme.ThemeRegistry.names", "name": "names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["altair.vegalite.v5.theme.ThemeRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "names of ThemeRegistry", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": "function altair.vegalite.v5.theme.ThemeRegistry.register is deprecated: Deprecated since `altair=5.5.0`. Use @altair.theme.register instead.", "flags": ["is_decorated"], "fullname": "altair.vegalite.v5.theme.ThemeRegistry.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["altair.vegalite.v5.theme.ThemeRegistry", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of ThemeRegistry", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "altair.vegalite.v5.theme.ThemeRegistry.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["altair.vegalite.v5.theme.ThemeRegistry", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of ThemeRegistry", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.theme.ThemeRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "altair.vegalite.v5.theme.ThemeRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "VEGA_THEMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "altair.vegalite.v5.theme.VEGA_THEMES", "name": "VEGA_THEMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "VegaTheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.theme.VegaTheme", "name": "VegaTheme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.theme.VegaTheme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.theme", "mro": ["altair.vegalite.v5.theme.VegaTheme", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.theme.VegaTheme.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["altair.vegalite.v5.theme.VegaTheme"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of VegaTheme", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "theme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.theme.VegaTheme.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "theme"], "arg_types": ["altair.vegalite.v5.theme.VegaTheme", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of VegaTheme", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.theme.VegaTheme.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["altair.vegalite.v5.theme.VegaTheme"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of VegaTheme", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "theme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "altair.vegalite.v5.theme.VegaTheme.theme", "name": "theme", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.theme.VegaTheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "altair.vegalite.v5.theme.VegaTheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VegaThemes": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.VegaThemes", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.theme.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.theme.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.theme.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.theme.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.theme.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.theme.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "deprecated_static_only": {".class": "SymbolTableNode", "cross_ref": "altair.utils.deprecation.deprecated_static_only", "kind": "Gdef"}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing.get_args", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "theme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "altair.vegalite.v5.theme.theme", "name": "theme", "type": "builtins.str"}}, "themes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.vegalite.v5.theme.themes", "name": "themes", "type": "altair.vegalite.v5.theme.ThemeRegistry"}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\vegalite\\v5\\theme.py"}