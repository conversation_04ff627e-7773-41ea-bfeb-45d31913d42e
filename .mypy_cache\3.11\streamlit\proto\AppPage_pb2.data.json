{".class": "MypyFile", "_fullname": "streamlit.proto.AppPage_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AppPage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.AppPage_pb2.AppPage", "name": "AppPage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.AppPage_pb2.AppPage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.AppPage_pb2", "mro": ["streamlit.proto.AppPage_pb2.AppPage", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.AppPage_pb2.AppPage.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.AppPage_pb2.AppPage", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "icon"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "icon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_default"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "is_default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "page_name"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "page_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "page_script_hash"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "page_script_hash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "section_header"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "section_header"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url_pathname"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "url_pathname"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of AppPage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.AppPage_pb2.google", "source_any": null, "type_of_any": 3}}}, "ICON_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.ICON_FIELD_NUMBER", "name": "ICON_FIELD_NUMBER", "type": "builtins.int"}}, "IS_DEFAULT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.IS_DEFAULT_FIELD_NUMBER", "name": "IS_DEFAULT_FIELD_NUMBER", "type": "builtins.int"}}, "PAGE_NAME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.PAGE_NAME_FIELD_NUMBER", "name": "PAGE_NAME_FIELD_NUMBER", "type": "builtins.int"}}, "PAGE_SCRIPT_HASH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.PAGE_SCRIPT_HASH_FIELD_NUMBER", "name": "PAGE_SCRIPT_HASH_FIELD_NUMBER", "type": "builtins.int"}}, "SECTION_HEADER_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.SECTION_HEADER_FIELD_NUMBER", "name": "SECTION_HEADER_FIELD_NUMBER", "type": "builtins.int"}}, "URL_PATHNAME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.URL_PATHNAME_FIELD_NUMBER", "name": "URL_PATHNAME_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "page_script_hash", "page_name", "icon", "is_default", "section_header", "url_pathname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.AppPage_pb2.AppPage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "page_script_hash", "page_name", "icon", "is_default", "section_header", "url_pathname"], "arg_types": ["streamlit.proto.AppPage_pb2.AppPage", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AppPage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "icon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.icon", "name": "icon", "type": "builtins.str"}}, "is_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.is_default", "name": "is_default", "type": "builtins.bool"}}, "page_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.page_name", "name": "page_name", "type": "builtins.str"}}, "page_script_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.page_script_hash", "name": "page_script_hash", "type": "builtins.str"}}, "section_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.section_header", "name": "section_header", "type": "builtins.str"}}, "url_pathname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.AppPage_pb2.AppPage.url_pathname", "name": "url_pathname", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.AppPage_pb2.AppPage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.AppPage_pb2.AppPage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.AppPage_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.AppPage_pb2.google", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.AppPage_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.AppPage_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.AppPage_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.AppPage_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.AppPage_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.AppPage_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___AppPage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.AppPage_pb2.global___AppPage", "line": 64, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.AppPage_pb2.AppPage"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.AppPage_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.AppPage_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\AppPage_pb2.pyi"}