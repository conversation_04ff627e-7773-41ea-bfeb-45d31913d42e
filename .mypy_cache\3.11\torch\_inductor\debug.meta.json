{"data_mtime": 1755649448, "dep_lines": [23, 27, 28, 24, 26, 30, 31, 33, 33, 34, 41, 11, 16, 18, 22, 25, 29, 33, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 17, 20, 348, 388, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 21], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 5, 10, 5, 5, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch._dynamo.repro.after_aot", "torch.fx.passes.shape_prop", "torch.fx.passes.tools_common", "torch._dynamo.utils", "torch.fx.graph_module", "torch.utils._ordered_set", "torch.utils._pytree", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.scheduler", "torch._inductor.virtualized", "os.path", "collections.abc", "unittest.mock", "torch.fx", "torch._logging", "torch.types", "torch._inductor", "collections", "contextlib", "copy", "dataclasses", "functools", "io", "itertools", "json", "logging", "os", "pickle", "pstats", "shutil", "traceback", "typing", "torch", "filelock", "tarfile", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "_frozen_importlib", "abc", "torch._C", "torch._logging._internal", "torch._tensor", "torch.export", "torch.export.exported_program", "torch.fx.graph", "torch.fx.passes", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "6e234ae32c1fb508644fd77fb7a50c1bc828a24d", "id": "torch._inductor.debug", "ignore_all": true, "interface_hash": "3b7b3463cc570ed1a74e2484fa7264255f995a08", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\debug.py", "plugin_data": null, "size": 33993, "suppressed": ["functorch.compile"], "version_id": "1.15.0"}