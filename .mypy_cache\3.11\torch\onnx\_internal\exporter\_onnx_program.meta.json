{"data_mtime": 1755649448, "dep_lines": [20, 20, 345, 19, 20, 21, 21, 28, 3, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 30], "dep_prios": [10, 10, 20, 5, 20, 10, 20, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["torch.onnx._internal.exporter._dynamic_shapes", "torch.onnx._internal.exporter._ir_passes", "torch.onnx._internal.exporter._core", "torch.onnx._internal._lazy_import", "torch.onnx._internal.exporter", "torch.utils._pytree", "torch.utils", "collections.abc", "__future__", "contextlib", "copy", "gc", "logging", "os", "tempfile", "textwrap", "warnings", "typing", "torch", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "_collections_abc", "_frozen_importlib", "abc", "torch._C", "torch._tensor", "torch.export", "torch.export.exported_program", "torch.onnx.errors"], "hash": "e8c82290084e018f37958ff3122a7e995b541c70", "id": "torch.onnx._internal.exporter._onnx_program", "ignore_all": true, "interface_hash": "76f7e46665077f54fe48d998bf950b72debc4e4e", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_onnx_program.py", "plugin_data": null, "size": 18662, "suppressed": ["onnxruntime"], "version_id": "1.15.0"}