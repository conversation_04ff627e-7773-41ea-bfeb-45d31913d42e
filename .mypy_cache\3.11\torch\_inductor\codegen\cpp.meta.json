{"data_mtime": 1755649448, "dep_lines": [22, 23, 53, 67, 19, 21, 25, 26, 26, 26, 26, 26, 27, 28, 37, 52, 11, 18, 19, 20, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 15], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["torch.utils._sympy.functions", "torch.utils._sympy.symbol", "torch._inductor.codegen.common", "torch._inductor.codegen.cpp_utils", "torch._inductor.dependencies", "torch.utils._ordered_set", "torch._dynamo.utils", "torch._inductor.config", "torch._inductor.cpp_builder", "torch._inductor.cpu_vec_isa", "torch._inductor.ir", "torch._inductor.metrics", "torch._inductor.loop_body", "torch._inductor.scheduler", "torch._inductor.utils", "torch._inductor.virtualized", "collections.abc", "torch.fx", "torch._inductor", "torch._prims_common", "contextlib", "dataclasses", "functools", "itertools", "math", "operator", "re", "sys", "warnings", "enum", "typing", "torch", "builtins", "os", "inspect", "html", "string", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "copy", "_frozen_importlib", "_typeshed", "abc", "logging", "torch._C", "torch._inductor.graph", "torch._inductor.ops_handler", "torch._logging", "torch._logging._internal", "torch.fx.interpreter", "torch.fx.node", "torch.utils", "torch.utils._config_typing", "torch.utils._sympy", "torch.utils._sympy.value_ranges", "typing_extensions"], "hash": "6852cd46cc804a5aa7b784d06ed872babb273934", "id": "torch._inductor.codegen.cpp", "ignore_all": true, "interface_hash": "106541de5023d2951e2d1fa0291b1700c577edd6", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp.py", "plugin_data": null, "size": 229056, "suppressed": ["sympy"], "version_id": "1.15.0"}