{".class": "MypyFile", "_fullname": "torch.distributed.elastic.rendezvous.registry", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "RendezvousHandler": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousHandler", "kind": "Gdef", "module_public": false}, "RendezvousParameters": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousParameters", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.registry.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.registry.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.registry.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.registry.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.registry.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.registry.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.registry.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_create_c10d_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.registry._create_c10d_handler", "name": "_create_c10d_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["torch.distributed.elastic.rendezvous.api.RendezvousParameters"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_c10d_handler", "ret_type": "torch.distributed.elastic.rendezvous.api.RendezvousHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_etcd_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.registry._create_etcd_handler", "name": "_create_etcd_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["torch.distributed.elastic.rendezvous.api.RendezvousParameters"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_etcd_handler", "ret_type": "torch.distributed.elastic.rendezvous.api.RendezvousHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_etcd_v2_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.registry._create_etcd_v2_handler", "name": "_create_etcd_v2_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["torch.distributed.elastic.rendezvous.api.RendezvousParameters"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_etcd_v2_handler", "ret_type": "torch.distributed.elastic.rendezvous.api.RendezvousHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_static_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.registry._create_static_handler", "name": "_create_static_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["torch.distributed.elastic.rendezvous.api.RendezvousParameters"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_static_handler", "ret_type": "torch.distributed.elastic.rendezvous.api.RendezvousHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register_default_handlers": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.registry._register_default_handlers", "name": "_register_default_handlers", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_default_handlers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register_out_of_tree_handlers": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.registry._register_out_of_tree_handlers", "name": "_register_out_of_tree_handlers", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_out_of_tree_handlers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_handler": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.create_handler", "kind": "Gdef", "module_public": false}, "entry_points": {".class": "SymbolTableNode", "cross_ref": "importlib.metadata.entry_points", "kind": "Gdef", "module_public": false}, "get_rendezvous_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.registry.get_rendezvous_handler", "name": "get_rendezvous_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["torch.distributed.elastic.rendezvous.api.RendezvousParameters"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rendezvous_handler", "ret_type": "torch.distributed.elastic.rendezvous.api.RendezvousHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handler_registry": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.rendezvous_handler_registry", "kind": "Gdef", "module_public": false}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.registry.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\elastic\\rendezvous\\registry.py"}