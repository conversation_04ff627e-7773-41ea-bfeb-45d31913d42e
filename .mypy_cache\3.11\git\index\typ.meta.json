{"data_mtime": 1755656862, "dep_lines": [13, 11, 19, 22, 8, 9, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.index.util", "git.objects", "git.types", "git.repo", "<PERSON><PERSON><PERSON><PERSON>", "pathlib", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "git.objects.base", "git.objects.blob", "git.repo.base", "os", "typing_extensions"], "hash": "002212990a4b27bd55ebb631178ad48c064348d4", "id": "git.index.typ", "ignore_all": true, "interface_hash": "0d4b7ea33dec1e9e2ac31973bfaf85489ee45830", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\index\\typ.py", "plugin_data": null, "size": 6570, "suppressed": [], "version_id": "1.15.0"}