{".class": "MypyFile", "_fullname": "altair.vegalite.v5.schema._typing", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AggregateOp_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.AggregateOp_T", "line": 218, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "argmax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a<PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "average"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distinct"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mean"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "median"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "missing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "product"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "q1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "q3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ci0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ci1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stderr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stdev"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stdevp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "valid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "variance"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "variancep"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exponential"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exponentialb"}], "uses_pep604_syntax": false}}}, "Align_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Align_T", "line": 245, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}}}, "AllSortString_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.AllSortString_T", "line": 246, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ascending"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "descending"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "y"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "color"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fill"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stroke"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strokeWidth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fillOpacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strokeOpacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-y"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-color"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-fill"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-stroke"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-strokeWidth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-shape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-fillOpacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-strokeOpacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-opacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-text"}], "uses_pep604_syntax": false}}}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AutosizeType_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.AutosizeType_T", "line": 274, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "pad"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fit-x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fit-y"}], "uses_pep604_syntax": false}}}, "AxisOrient_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.AxisOrient_T", "line": 275, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}}}, "BinnedTimeUnit_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.BinnedTimeUnit_T", "line": 276, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearquarter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearquartermonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearmonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearmonthdate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binned<PERSON>armonthdatehours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearmonthdatehoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearmonthdatehoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearweek"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearweekday"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearweekdayhours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearweekdayhoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedyearweekdayhoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedye<PERSON><PERSON><PERSON>year"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearquarter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearquartermonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedut<PERSON>earmonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearmonthdate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearmonthdatehours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearmonthdatehoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearmonthdatehoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearweek"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearweekday"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearweekdayhours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearweekdayhoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedutcyearweekdayhoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binnedut<PERSON>eardayofyear"}], "uses_pep604_syntax": false}}}, "Blend_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Blend_T", "line": 306, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "multiply"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "screen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overlay"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darken"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighten"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "color-dodge"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "color-burn"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hard-light"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "soft-light"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "difference"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exclusion"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "saturation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "color"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "luminosity"}], "uses_pep604_syntax": false}}}, "BoxPlot_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.BoxPlot_T", "line": 324, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "boxplot"}}}, "ColorHex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ColorHex", "line": 138, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "ColorName_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ColorName_T", "line": 325, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "black"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "silver"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gray"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "white"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "maroon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "red"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fuchsia"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "green"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lime"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "olive"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "navy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "teal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "aqua"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "aliceblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "antiquewhite"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "aquamarine"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "azure"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "beige"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bisque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blanche<PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueviolet"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brown"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "burlywood"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cadetblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "chartreuse"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "chocolate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "coral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cornflowerblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cornsilk"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "crimson"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cyan"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dark<PERSON>an"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgoldenrod"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgray"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON>rey"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkmagenta"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkolivegreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkorange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkorchid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkseagreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkslateblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkslategray"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkslateg<PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkturquoise"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkviolet"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "deeppink"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "deepskyblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dimgray"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dodgerblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "firebrick"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "forestgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gainsboro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ghostwhite"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldenrod"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenyellow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grey"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "honeydew"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hotpink"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "indianred"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "indigo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ivory"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "khaki"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lavender"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lavenderblush"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lawngreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lemon<PERSON>ffon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightcoral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightcyan"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgoldenrodyellow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgray"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON>rey"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightpink"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightseagreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightskyblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightslategray"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightslategrey"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightsteelblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightyellow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "limegreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "linen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "magenta"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumaquamarine"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumorchid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumpurple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumseagreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumslateblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumspringgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumturquoise"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mediumvioletred"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "midnightblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mintcream"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mistyrose"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "moccasin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "navajowhite"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "oldlace"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orchid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "palegoldenrod"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "palegreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "paleturquoise"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "palevioletred"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "papayawhip"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "peachpuff"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "peru"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pink"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "plum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "powderblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rosybrown"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "royalblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "saddlebrown"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "salmon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sandybrown"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "seagreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "seashell"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sienna"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skyblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "slateblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "slategray"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "snow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "springgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "steelblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tan"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "thistle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tomato"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "turquoise"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "violet"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wheat"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "whitesmoke"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rebeccapurple"}], "uses_pep604_syntax": false}}}, "ColorScheme_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ColorScheme_T", "line": 475, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "accent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "category10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "category20"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "category20b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "category20c"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dark2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "paired"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pastel1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pastel2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "set1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "set2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "set3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tableau10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tableau20"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blues"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tealblues"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "teals"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greens"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "browns"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purples"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warmgreys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "oranges"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "turbo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "viridis"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inferno"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "magma"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "plasma"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cividis"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluegreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluegreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluegreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluegreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluegreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluegreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluegreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluegreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluepurple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluepurple-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluepurple-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluepurple-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluepurple-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluepurple-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluepurple-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bluepurple-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldgreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldgreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldgreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldgreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldgreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldgreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldgreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldorange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldorange-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldorange-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldorange-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldorange-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldorange-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldorange-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldorange-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldred"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldred-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldred-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldred-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldred-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldred-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldred-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "goldred-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenblue-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenblue-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenblue-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenblue-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenblue-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenblue-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greenblue-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orangered-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplebluegreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplebluegreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplebluegreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplebluegreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplebluegreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplebluegreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplebluegreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplebluegreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleblue-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleblue-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleblue-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleblue-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleblue-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleblue-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleblue-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplered"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplered-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplered-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplered-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplered-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplered-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplered-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplered-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redpurple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redpurple-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redpurple-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redpurple-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redpurple-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redpurple-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redpurple-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redpurple-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreenblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreenblue-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreenblue-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreenblue-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreenblue-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreenblue-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreenblue-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreenblue-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yellowgreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangebrown"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangebrown-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangebrown-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangebrown-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangebrown-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangebrown-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangebrown-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangebrown-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangered"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangered-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangered-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangered-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangered-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangered-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangered-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yelloworangered-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkblue-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgold-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgold-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgold-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgold-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgold-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgold-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgold-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkgreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkmulti-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkmulti-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkmulti-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkmulti-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkmulti-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkmulti-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkmulti-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "darkred-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyred-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyred-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyred-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyred-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyred-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyred-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyred-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyteal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyteal-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyteal-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyteal-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyteal-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyteal-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyteal-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightgreyteal-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightmulti"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightmulti-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightmulti-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightmulti-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightmulti-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightmulti-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightmulti-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightmulti-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightorange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightorange-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightorange-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightorange-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightorange-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightorange-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightorange-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lightorange-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighttealblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighttealblue-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighttealblue-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighttealblue-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighttealblue-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighttealblue-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighttealblue-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighttealblue-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blueorange-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brownbluegreen-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purplegreen-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pinkyellowgreen-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "purpleorange-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redblue-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redgrey-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowblue-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "redyellowgreen-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-6"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-7"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-9"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spectral-11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rainbow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sinebow"}], "uses_pep604_syntax": false}}}, "CompositeMark_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.CompositeMark_T", "line": 809, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "boxplot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "errorbar"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "errorband"}], "uses_pep604_syntax": false}}}, "Cursor_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Cursor_T", "line": 810, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "context-menu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pointer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wait"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cell"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "crosshair"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vertical-text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "alias"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "copy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "move"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "no-drop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "not-allowed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "e-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "n-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ne-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nw-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "s-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "se-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sw-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ew-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ns-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nesw-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nwse-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "col-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "row-resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "all-scroll"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zoom-in"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zoom-out"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grab"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "grabbing"}], "uses_pep604_syntax": false}}}, "ErrorBand_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ErrorBand_T", "line": 848, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "errorband"}}}, "ErrorBarExtent_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ErrorBarExtent_T", "line": 849, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ci"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "iqr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stderr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stdev"}], "uses_pep604_syntax": false}}}, "ErrorBar_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ErrorBar_T", "line": 850, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "errorbar"}}}, "FontWeight_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.FontWeight_T", "line": 851, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lighter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bolder"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 100}, {".class": "LiteralType", "fallback": "builtins.int", "value": 200}, {".class": "LiteralType", "fallback": "builtins.int", "value": 300}, {".class": "LiteralType", "fallback": "builtins.int", "value": 400}, {".class": "LiteralType", "fallback": "builtins.int", "value": 500}, {".class": "LiteralType", "fallback": "builtins.int", "value": 600}, {".class": "LiteralType", "fallback": "builtins.int", "value": 700}, {".class": "LiteralType", "fallback": "builtins.int", "value": 800}, {".class": "LiteralType", "fallback": "builtins.int", "value": 900}], "uses_pep604_syntax": false}}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_public": false}, "ImputeMethod_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ImputeMethod_T", "line": 854, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "median"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mean"}], "uses_pep604_syntax": false}}}, "Interpolate_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Interpolate_T", "line": 855, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "basis"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "basis-open"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "basis-closed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bundle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cardinal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cardinal-open"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cardinal-closed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "catmull-rom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "linear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "linear-closed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "monotone"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "step"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "step-before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "step-after"}], "uses_pep604_syntax": false}}}, "LayoutAlign_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.LayoutAlign_T", "line": 872, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "each"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": false}}}, "LegendOrient_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.LegendOrient_T", "line": 873, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top-left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top-right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom-left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom-right"}], "uses_pep604_syntax": false}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "LiteralString": {".class": "SymbolTableNode", "cross_ref": "typing.LiteralString", "kind": "Gdef", "module_public": false}, "Map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Map", "line": 216, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MarkInvalidDataMode_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T", "line": 884, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "filter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "break-paths-filter-domains"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "break-paths-show-domains"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "break-paths-show-path-domains"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "show"}], "uses_pep604_syntax": false}}}, "MarkType_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.MarkType_T", "line": 891, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "arc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "area"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "group"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "line"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rect"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rule"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "symbol"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "trail"}], "uses_pep604_syntax": false}}}, "Mark_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Mark_T", "line": 905, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "arc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "area"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bar"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "line"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "point"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rect"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rule"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tick"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "trail"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "circle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "square"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "geoshape"}], "uses_pep604_syntax": false}}}, "MultiTimeUnit_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.MultiTimeUnit_T", "line": 921, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "yearquarter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearquartermonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearmonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearmonthdate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearmonthdatehours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearmonthdatehoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearmonthdatehoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearweek"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearweekday"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearweekdayhours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearweekdayhoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yearweekdayhoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yeardayofyear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quartermonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "monthdate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "monthdatehours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "monthdatehoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "monthdatehoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "weekday"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "weekdayhours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "weekdayhoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "weekdayhoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dayhours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dayhoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dayhoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "minutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "secondsmilliseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearquarter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearquartermonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearmonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearmonthdate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearmonthdatehours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearmonthdatehoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearmonthdatehoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearweek"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearweekday"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearweekdayhours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearweekdayhoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyearweekdayhoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcy<PERSON><PERSON>ofyear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcquartermonth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcmonthdate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcmonthdatehours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcmonthdatehoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcmonthdatehoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcweekday"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcweekdayhours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcweekdayhoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcweekdayhoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcdayhours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcdayhoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcdayhoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utchoursminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utchoursminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcminutesseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcsecondsmilliseconds"}], "uses_pep604_syntax": false}}}, "NonArgAggregateOp_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.NonArgAggregateOp_T", "line": 981, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "average"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distinct"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mean"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "median"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "missing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "product"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "q1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "q3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ci0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ci1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stderr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stdev"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stdevp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "valid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "variance"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "variancep"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exponential"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exponentialb"}], "uses_pep604_syntax": false}}}, "OneOrSeq": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "id": 1, "name": "T", "namespace": "altair.vegalite.v5.schema._typing.OneOrSeq", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.OneOrSeq", "line": 105, "no_args": false, "normalized": false, "python_3_12_type_alias": true, "target": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "id": 1, "name": "T", "namespace": "altair.vegalite.v5.schema._typing.OneOrSeq", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "id": 1, "name": "T", "namespace": "altair.vegalite.v5.schema._typing.OneOrSeq", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}}}, "Orient_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Orient_T", "line": 1006, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}], "uses_pep604_syntax": false}}}, "Orientation_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Orientation_T", "line": 1007, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "horizontal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vertical"}], "uses_pep604_syntax": false}}}, "PaddingKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._typing.PaddingKwds", "name": "PaddingKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._typing.PaddingKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._typing", "mro": ["altair.vegalite.v5.schema._typing.PaddingKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["bottom", "builtins.float"], ["left", "builtins.float"], ["right", "builtins.float"], ["top", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "PrimitiveValue_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.PrimitiveValue_T", "line": 217, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ProjectionType_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ProjectionType_T", "line": 1008, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "albers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "albersUsa"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "azimuthalEqualArea"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "azimuthalEquidistant"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "conicConformal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "conicEqualArea"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "conicEquidistant"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "equalEarth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "equirectangular"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gnomonic"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "identity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mercator"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "naturalEarth1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orthographic"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stereographic"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "transverseMercator"}], "uses_pep604_syntax": false}}}, "RangeEnum_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.RangeEnum_T", "line": 1026, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "symbol"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "category"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ordinal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ramp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "diverging"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "heatmap"}], "uses_pep604_syntax": false}}}, "ResolveMode_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ResolveMode_T", "line": 1029, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "independent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shared"}], "uses_pep604_syntax": false}}}, "RowColKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._typing.RowColKwds", "name": "RowColKwds", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "id": 1, "name": "T", "namespace": "altair.vegalite.v5.schema._typing.RowColKwds", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._typing.RowColKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._typing", "mro": ["altair.vegalite.v5.schema._typing.RowColKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["T"], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["column", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "id": 1, "name": "T", "namespace": "altair.vegalite.v5.schema._typing.RowColKwds", "upper_bound": "builtins.object", "values": [], "variance": 0}], ["row", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "id": 1, "name": "T", "namespace": "altair.vegalite.v5.schema._typing.RowColKwds", "upper_bound": "builtins.object", "values": [], "variance": 0}]], "readonly_keys": [], "required_keys": []}}}, "ScaleInterpolateEnum_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ScaleInterpolateEnum_T", "line": 1030, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rgb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lab"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hcl"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hsl"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hsl-long"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hcl-long"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cubehelix"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cubehelix-long"}], "uses_pep604_syntax": false}}}, "ScaleType_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.ScaleType_T", "line": 1033, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "linear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "log"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sqrt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "symlog"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "identity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sequential"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quantile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quantize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "threshold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bin-ordinal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ordinal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "point"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "band"}], "uses_pep604_syntax": false}}}, "SelectionResolution_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.SelectionResolution_T", "line": 1051, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "global"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "union"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "intersect"}], "uses_pep604_syntax": false}}}, "SelectionType_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.SelectionType_T", "line": 1052, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "point"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "interval"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SingleDefUnitChannel_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.SingleDefUnitChannel_T", "line": 1053, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "y"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "xOffset"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "yOffset"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "y2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "longitude"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "latitude"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "longitude2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "latitude2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "theta"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "theta2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "radius"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "radius2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "color"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fill"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stroke"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fillOpacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strokeOpacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strokeWidth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strokeDash"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "angle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "href"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "description"}], "uses_pep604_syntax": false}}}, "SingleTimeUnit_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.SingleTimeUnit_T", "line": 1085, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "year"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quarter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "month"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "week"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "day"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dayofyear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "minutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "seconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "milliseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcyear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcquarter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utc<PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcweek"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcday"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcdayofyear"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcdate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utchours"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcminutes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcseconds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "utcmilliseconds"}], "uses_pep604_syntax": false}}}, "SortByChannel_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.SortByChannel_T", "line": 1109, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "y"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "color"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fill"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stroke"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strokeWidth"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shape"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fillOpacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strokeOpacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opacity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "uses_pep604_syntax": false}}}, "SortOrder_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.SortOrder_T", "line": 1123, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ascending"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "descending"}], "uses_pep604_syntax": false}}}, "StackOffset_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.StackOffset_T", "line": 1124, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "zero"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "normalize"}], "uses_pep604_syntax": false}}}, "StandardType_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.StandardType_T", "line": 1125, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "quantitative"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ordinal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "temporal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nominal"}], "uses_pep604_syntax": false}}}, "StepFor_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.StepFor_T", "line": 1126, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "position"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "offset"}], "uses_pep604_syntax": false}}}, "StrokeCap_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.StrokeCap_T", "line": 1127, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "butt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "round"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "square"}], "uses_pep604_syntax": false}}}, "StrokeJoin_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.StrokeJoin_T", "line": 1128, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "miter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "round"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bevel"}], "uses_pep604_syntax": false}}}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Temporal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Temporal", "line": 198, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["datetime.date", "datetime.datetime"], "uses_pep604_syntax": false}}}, "TextBaseline_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.TextBaseline_T", "line": 1129, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "alphabetic"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "middle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "line-top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "line-bottom"}], "uses_pep604_syntax": false}}}, "TextDirection_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.TextDirection_T", "line": 1132, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ltr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rtl"}], "uses_pep604_syntax": false}}}, "TimeInterval_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.TimeInterval_T", "line": 1133, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "millisecond"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "second"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "minute"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hour"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "day"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "week"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "month"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "year"}], "uses_pep604_syntax": false}}}, "TitleAnchor_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.TitleAnchor_T", "line": 1136, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "start"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "middle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "end"}], "uses_pep604_syntax": false}}}, "TitleFrame_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.TitleFrame_T", "line": 1137, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "bounds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "group"}], "uses_pep604_syntax": false}}}, "TitleOrient_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.TitleOrient_T", "line": 1138, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}], "uses_pep604_syntax": false}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_public": false}, "TypeAliasType": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAliasType", "kind": "Gdef", "module_public": false}, "TypeForShape_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.TypeForShape_T", "line": 1139, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nominal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ordinal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g<PERSON><PERSON><PERSON>"}], "uses_pep604_syntax": false}}}, "TypeIs": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeIs", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Type_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.Type_T", "line": 1140, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "quantitative"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ordinal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "temporal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nominal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g<PERSON><PERSON><PERSON>"}], "uses_pep604_syntax": false}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._typing.Value", "name": "Value", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "id": 1, "name": "T", "namespace": "altair.vegalite.v5.schema._typing.Value", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._typing.Value", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._typing", "mro": ["altair.vegalite.v5.schema._typing.Value", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["T"], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.vegalite.v5.schema._typing.T", "id": 1, "name": "T", "namespace": "altair.vegalite.v5.schema._typing.Value", "upper_bound": "builtins.object", "values": [], "variance": 0}]], "readonly_keys": [], "required_keys": ["value"]}}}, "VegaThemes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.VegaThemes", "line": 200, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "carbong10"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carbong100"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carbong90"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "carbonwhite"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dark"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "excel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fivethirtyeight"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ggplot2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "googlecharts"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "latimes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "powerbi"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quartz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "urbaninstitute"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vox"}], "uses_pep604_syntax": false}}}, "WindowOnlyOp_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.vegalite.v5.schema._typing.WindowOnlyOp_T", "line": 1141, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "row_number"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rank"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dense_rank"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "percent_rank"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cume_dist"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ntile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lag"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lead"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "first_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nth_value"}], "uses_pep604_syntax": false}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.vegalite.v5.schema._typing.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._typing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._typing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._typing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._typing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._typing.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._typing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef", "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_public": false}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing.get_args", "kind": "Gdef", "module_public": false}, "is_color_hex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._typing.is_color_hex", "name": "is_color_hex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_color_hex", "ret_type": "builtins.bool", "type_guard": null, "type_is": "builtins.str", "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\vegalite\\v5\\schema\\_typing.py"}