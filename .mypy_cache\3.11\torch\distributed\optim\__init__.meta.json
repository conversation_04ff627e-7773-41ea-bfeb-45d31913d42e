{"data_mtime": 1755649448, "dep_lines": [14, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 33, 35, 36, 12, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 20, 20], "dependencies": ["torch.distributed.optim.apply_optimizer_in_backward", "torch.distributed.optim.functional_adadelta", "torch.distributed.optim.functional_adagrad", "torch.distributed.optim.functional_adam", "torch.distributed.optim.functional_adamax", "torch.distributed.optim.functional_adamw", "torch.distributed.optim.functional_rmsprop", "torch.distributed.optim.functional_rprop", "torch.distributed.optim.functional_sgd", "torch.distributed.optim.named_optimizer", "torch.distributed.optim.utils", "torch.distributed.optim.optimizer", "torch.distributed.optim.post_localSGD_optimizer", "torch.distributed.optim.zero_redundancy_optimizer", "torch.optim", "warnings", "torch", "builtins", "collections", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "typing", "_frozen_importlib", "abc", "torch._C", "contextlib"], "hash": "66c138b57d0e94c68b1ab4a659dbcfdce0e70153", "id": "torch.distributed.optim", "ignore_all": true, "interface_hash": "e4ad7926184fb3f5e08b45847d3ce898416da016", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\optim\\__init__.py", "plugin_data": null, "size": 1484, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}