{".class": "MypyFile", "_fullname": "altair.utils.html", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "HTML_TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.utils.html.HTML_TEMPLATE", "name": "HTML_TEMPLATE", "type": "jinja2.environment.Template"}}, "HTML_TEMPLATE_OLLI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.utils.html.HTML_TEMPLATE_OLLI", "name": "HTML_TEMPLATE_OLLI", "type": "jinja2.environment.Template"}}, "HTML_TEMPLATE_UNIVERSAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.utils.html.HTML_TEMPLATE_UNIVERSAL", "name": "HTML_TEMPLATE_UNIVERSAL", "type": "jinja2.environment.Template"}}, "INLINE_HTML_TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.utils.html.INLINE_HTML_TEMPLATE", "name": "INLINE_HTML_TEMPLATE", "type": "jinja2.environment.Template"}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "RenderMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.utils.html.RenderMode", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "vega"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vega-lite"}], "uses_pep604_syntax": false}}}, "TEMPLATES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "altair.utils.html.TEMPLATES", "name": "TEMPLATES", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.utils.html.TemplateName"}, "jinja2.environment.Template"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "TemplateName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "altair.utils.html.TemplateName", "line": 10, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "universal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "olli"}], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.utils.html.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.utils.html.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.utils.html.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.utils.html.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.utils.html.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.utils.html.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "import_vl_convert": {".class": "SymbolTableNode", "cross_ref": "altair.utils._importers.import_vl_convert", "kind": "Gdef"}, "jinja2": {".class": "SymbolTableNode", "cross_ref": "jinja2", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "spec_to_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["spec", "mode", "vega_version", "vegaembed_version", "vegalite_version", "base_url", "output_div", "embed_options", "json_kwds", "fullhtml", "requirejs", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.utils.html.spec_to_html", "name": "spec_to_html", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["spec", "mode", "vega_version", "vegaembed_version", "vegalite_version", "base_url", "output_div", "embed_options", "json_kwds", "fullhtml", "requirejs", "template"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.utils.html.RenderMode"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["jinja2.environment.Template", {".class": "TypeAliasType", "args": [], "type_ref": "altair.utils.html.TemplateName"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spec_to_html", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vl_version_for_vl_convert": {".class": "SymbolTableNode", "cross_ref": "altair.utils._importers.vl_version_for_vl_convert", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\utils\\html.py"}