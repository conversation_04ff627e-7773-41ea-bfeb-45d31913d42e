{".class": "MypyFile", "_fullname": "torch._inductor.codegen.wrapper_fxir", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllocateLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.AllocateLine", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BufferLike": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.BufferLike", "kind": "Gdef"}, "CachingAutotuner": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.triton_heuristics.CachingAutotuner", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CodegenBuffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.wrapper_fxir.CodegenBuffer", "line": 82, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "torch._inductor.codegen.wrapper_fxir.SymbolBuffer"], "uses_pep604_syntax": false}}}, "CodegenSymbol": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.CodegenSymbol", "kind": "Gdef"}, "CommBufferAllocateLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.CommBufferAllocateLine", "kind": "Gdef"}, "CommBufferFreeLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.CommBufferFreeLine", "kind": "Gdef"}, "CommentLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.CommentLine", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "EnterDeviceContextManagerLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine", "kind": "Gdef"}, "EnterSubgraphLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.EnterSubgraphLine", "kind": "Gdef"}, "ExitDeviceContextManagerLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine", "kind": "Gdef"}, "ExitSubgraphLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.ExitSubgraphLine", "kind": "Gdef"}, "ExternKernelAllocLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.ExternKernelAllocLine", "kind": "Gdef"}, "ExternKernelOutLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.ExternKernelOutLine", "kind": "Gdef"}, "FileBackedGraphModule": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.FileBackedGraphModule", "kind": "Gdef"}, "FloorDiv": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.functions.FloorDiv", "kind": "Gdef"}, "FreeIfNotReusedLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "kind": "Gdef"}, "FreeLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.FreeLine", "kind": "Gdef"}, "FxConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter", "name": "FxConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 148, "name": "lines", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.Line"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 149, "name": "prologue", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper_fxir", "mro": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "lines", "prologue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "lines", "prologue"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.Line"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "prologue"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["lines", "prologue"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["lines", "prologue"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.Line"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["lines", "prologue"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.Line"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__post_init__ of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_as_strided": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "input_node", "size", "stride", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._create_as_strided", "name": "_create_as_strided", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "input_node", "size", "stride", "offset"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch.fx.node.Node", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper_fxir.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_as_strided of FxConverter", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_meta_from_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._create_meta_from_buffer", "name": "_create_meta_from_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "buffer"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch.fx.node.Node", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper_fxir.CodegenBuffer"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_meta_from_buffer of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fake_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "size", "stride", "dtype", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._fake_tensor", "name": "_fake_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "size", "stride", "dtype", "device"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fake_tensor of FxConverter", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_free": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._free", "name": "_free", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper_fxir.CodegenBuffer"}, "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_free of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_allocate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_allocate", "name": "_generate_allocate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_allocate of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_buffer", "name": "_generate_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_buffer of FxConverter", "ret_type": {".class": "UnionType", "items": ["torch.fx.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_comm_buffer_allocate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_comm_buffer_allocate", "name": "_generate_comm_buffer_allocate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_comm_buffer_allocate of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_comm_buffer_free": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_comm_buffer_free", "name": "_generate_comm_buffer_free", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_comm_buffer_free of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_comment", "name": "_generate_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_comment of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_enter_device_context_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_enter_device_context_manager", "name": "_generate_enter_device_context_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_enter_device_context_manager of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_enter_subgraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_enter_subgraph", "name": "_generate_enter_subgraph", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_enter_subgraph of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_exit_device_context_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_exit_device_context_manager", "name": "_generate_exit_device_context_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_exit_device_context_manager of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_exit_subgraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_exit_subgraph", "name": "_generate_exit_subgraph", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_exit_subgraph of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_extern_kernel_alloc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_extern_kernel_alloc", "name": "_generate_extern_kernel_alloc", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_extern_kernel_alloc of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_extern_kernel_common": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "kernel", "out_ir_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_extern_kernel_common", "name": "_generate_extern_kernel_common", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "kernel", "out_ir_node"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.ir.<PERSON><PERSON>", "torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_extern_kernel_common of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_extern_kernel_out": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_extern_kernel_out", "name": "_generate_extern_kernel_out", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_extern_kernel_out of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_free": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_free", "name": "_generate_free", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_free of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_free_if_not_reused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_free_if_not_reused", "name": "_generate_free_if_not_reused", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_free_if_not_reused of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_graph_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_graph_inputs", "name": "_generate_graph_inputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_graph_inputs of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_kernel_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_kernel_call", "name": "_generate_kernel_call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_kernel_call of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_kernel_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_kernel_definition", "name": "_generate_kernel_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_kernel_definition of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_line_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_line_context", "name": "_generate_line_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_line_context of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_multi_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_multi_output", "name": "_generate_multi_output", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_multi_output of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_null": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_null", "name": "_generate_null", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_null of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_output", "name": "_generate_output", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_output of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_reinterpret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_reinterpret", "name": "_generate_reinterpret", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_reinterpret of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_reinterpret_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input_buffer", "result_buffer", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_reinterpret_helper", "name": "_generate_reinterpret_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input_buffer", "result_buffer", "layout"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "torch._inductor.ir.Layout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_reinterpret_helper of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_reuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_reuse", "name": "_generate_reuse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_reuse of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_symbolic_call_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_symbolic_call_arg", "name": "_generate_symbolic_call_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_symbolic_call_arg of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_triton_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._generate_triton_call", "name": "_generate_triton_call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_triton_call of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._get_buffer", "name": "_get_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_buffer of FxConverter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper_fxir.CodegenBuffer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_import_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "kernel_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._import_kernel", "name": "_import_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "code", "kernel_name"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_import_kernel of FxConverter", "ret_type": "torch._inductor.runtime.triton_heuristics.CachingAutotuner", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lookup_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._lookup_args", "name": "_lookup_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "args"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lookup_args of FxConverter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_record_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._record_allocation", "name": "_record_allocation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "node"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper_fxir.CodegenBuffer"}, "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_record_allocation of FxConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unique_symbol_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter._unique_symbol_ids", "name": "_unique_symbol_ids", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "collections.Counter"}}}, "buffer_to_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.buffer_to_node", "name": "buffer_to_node", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate of FxConverter", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.gm", "name": "gm", "type": "torch.fx.graph_module.GraphModule"}}, "kernels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.kernels", "name": "kernels", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.codegen.wrapper_fxir.TritonKernel"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.lines", "name": "lines", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.Line"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "prologue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.prologue", "name": "prologue", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper_fxir.FxConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper_fxir.FxConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GraphModule": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph_module.GraphModule", "kind": "Gdef"}, "KernelCallLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.KernelCallLine", "kind": "Gdef"}, "KernelDefinitionLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.KernelDefinitionLine", "kind": "Gdef"}, "Line": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.Line", "kind": "Gdef"}, "LineContext": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.LineContext", "kind": "Gdef"}, "MultiOutputLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.MultiOutputLine", "kind": "Gdef"}, "NullLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.NullLine", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PyCodeCache": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.PyCodeCache", "kind": "Gdef"}, "PythonWrapperCodegen": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "kind": "Gdef"}, "ReinterpretLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.ReinterpretLine", "kind": "Gdef"}, "ReuseLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.ReuseLine", "kind": "Gdef"}, "SymbolBuffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.common.CodegenSymbol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer", "name": "SymbolBuffer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "symbol", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper_fxir.sympy", "source_any": null, "type_of_any": 3}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper_fxir", "mro": ["torch._inductor.codegen.wrapper_fxir.SymbolBuffer", "torch._inductor.codegen.common.CodegenSymbol", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "symbol"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.SymbolBuffer", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper_fxir.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SymbolBuffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "symbol"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["symbol"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper_fxir.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["symbol"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper_fxir.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "get_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.get_example", "name": "get_example", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.SymbolBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_example of SymbolBuffer", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper_fxir.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.SymbolBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name of SymbolBuffer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "symbol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.symbol", "name": "symbol", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper_fxir.sympy", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper_fxir.SymbolBuffer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SymbolicCallArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.SymbolicCallArg", "kind": "Gdef"}, "SymbolicCallArgLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.SymbolicCallArgLine", "kind": "Gdef"}, "TraceableTritonKernelWrapper": {".class": "SymbolTableNode", "cross_ref": "torch._higher_order_ops.triton_kernel_wrap.TraceableTritonKernelWrapper", "kind": "Gdef"}, "TritonKernel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel", "name": "TritonKernel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 91, "name": "tuner", "type": "torch._inductor.runtime.triton_heuristics.CachingAutotuner"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 92, "name": "wrapped", "type": "torch._higher_order_ops.triton_kernel_wrap.TraceableTritonKernelWrapper"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper_fxir", "mro": ["torch._inductor.codegen.wrapper_fxir.TritonKernel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tuner", "wrapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tuner", "wrapped"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.TritonKernel", "torch._inductor.runtime.triton_heuristics.CachingAutotuner", "torch._higher_order_ops.triton_kernel_wrap.TraceableTritonKernelWrapper"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TritonKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tuner"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrapped"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["tuner", "wrapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["tuner", "wrapped"], "arg_types": ["torch._inductor.runtime.triton_heuristics.CachingAutotuner", "torch._higher_order_ops.triton_kernel_wrap.TraceableTritonKernelWrapper"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["tuner", "wrapped"], "arg_types": ["torch._inductor.runtime.triton_heuristics.CachingAutotuner", "torch._higher_order_ops.triton_kernel_wrap.TraceableTritonKernelWrapper"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "tuner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel.tuner", "name": "tuner", "type": "torch._inductor.runtime.triton_heuristics.CachingAutotuner"}}, "wrapped": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel.wrapped", "name": "wrapped", "type": "torch._higher_order_ops.triton_kernel_wrap.TraceableTritonKernelWrapper"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper_fxir.TritonKernel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper_fxir.TritonKernel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "WorkspaceArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceArg", "kind": "Gdef"}, "WorkspaceZeroMode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceZeroMode", "kind": "Gdef"}, "WrapperFxCodegen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen", "name": "WrapperFxCodegen", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.wrapper_fxir", "mro": ["torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "_generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_inference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen._generate", "name": "_generate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "is_inference"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate of WrapperFxCodegen", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._inductor.codegen.common.FileBackedGraphModule", {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen.compile_graph", "name": "compile_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gm"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen", "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_graph of WrapperFxCodegen", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of WrapperFxCodegen", "ret_type": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of WrapperFxCodegen", "ret_type": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "supports_caching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen.supports_caching", "name": "supports_caching", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper_fxir.WrapperFxCodegen", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WrapperLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.WrapperLine", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper_fxir.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper_fxir.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "convert_shape_to_symint": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.convert_shape_to_symint", "kind": "Gdef"}, "convert_to_symint": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.convert_to_symint", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "extern_kernels": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.extern_kernels", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper_fxir.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper_fxir.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper_fxir.sympy", "source_any": null, "type_of_any": 3}}}, "sympy_product": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_product", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tracing_triton_hopifier_singleton": {".class": "SymbolTableNode", "cross_ref": "torch._higher_order_ops.triton_kernel_wrap.tracing_triton_hopifier_singleton", "kind": "Gdef"}, "triton_kernel_wrapper_mutation": {".class": "SymbolTableNode", "cross_ref": "torch._higher_order_ops.triton_kernel_wrap.triton_kernel_wrapper_mutation", "kind": "Gdef"}, "wrap_triton": {".class": "SymbolTableNode", "cross_ref": "torch._library.triton.wrap_triton", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\wrapper_fxir.py"}