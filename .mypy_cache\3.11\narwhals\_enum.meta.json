{"data_mtime": 1755656859, "dep_lines": [1, 4, 5, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["__future__", "enum", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "c09d940967339e55bba66fc64c2f6c0c7fbb512c", "id": "narwhals._enum", "ignore_all": true, "interface_hash": "39de77db0a74b6e17cb3e9a2a447db15e5fc1a2d", "mtime": 1755656304, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\narwhals\\_enum.py", "plugin_data": null, "size": 1192, "suppressed": [], "version_id": "1.15.0"}