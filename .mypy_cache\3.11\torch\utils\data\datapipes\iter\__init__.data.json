{".class": "MypyFile", "_fullname": "torch.utils.data.datapipes.iter", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Batcher": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.grouping.BatcherIterDataPipe", "kind": "Gdef"}, "Collator": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.callable.CollatorIterDataPipe", "kind": "Gdef"}, "Concater": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.combining.ConcaterIterDataPipe", "kind": "Gdef"}, "Demultiplexer": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.combining.DemultiplexerIterDataPipe", "kind": "Gdef"}, "FileLister": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.filelister.FileListerIterDataPipe", "kind": "Gdef"}, "FileOpener": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.fileopener.FileOpenerIterDataPipe", "kind": "Gdef"}, "Filter": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.selecting.FilterIterDataPipe", "kind": "Gdef"}, "Forker": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.combining.ForkerIterDataPipe", "kind": "Gdef"}, "Grouper": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.grouping.GrouperIterDataPipe", "kind": "Gdef"}, "IterableWrapper": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.utils.IterableWrapperIterDataPipe", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.callable.MapperIterDataPipe", "kind": "Gdef"}, "Multiplexer": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.combining.MultiplexerIterDataPipe", "kind": "Gdef"}, "RoutedDecoder": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe", "kind": "Gdef"}, "Sampler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "kind": "Gdef"}, "ShardingFilter": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.sharding.ShardingFilterIterDataPipe", "kind": "Gdef"}, "Shuffler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "kind": "Gdef"}, "StreamReader": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.streamreader.StreamReaderIterDataPipe", "kind": "Gdef"}, "UnBatcher": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.grouping.UnBatcherIterDataPipe", "kind": "Gdef"}, "Zipper": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.iter.combining.ZipperIterDataPipe", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.iter.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\__init__.py"}