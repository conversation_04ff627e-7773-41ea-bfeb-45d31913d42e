{"data_mtime": 1755656863, "dep_lines": [434, 13, 14, 15, 16, 17, 18, 19, 37, 38, 40, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 10, 5, 10, 25, 25, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["tornado.speedups", "array", "asyncio", "inspect", "os", "re", "typing", "zlib", "datetime", "types", "unittest", "builtins", "_frozen_importlib", "abc", "enum", "typing_extensions", "unittest.suite"], "hash": "4ef495cf5c01bf132d50806e2e2ea8d13f8d7403", "id": "tornado.util", "ignore_all": true, "interface_hash": "717c4d05b6dd641a7bb4ed4905a86f2b2085b8b7", "mtime": 1755656297, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\tornado\\util.py", "plugin_data": null, "size": 16225, "suppressed": [], "version_id": "1.15.0"}