{"data_mtime": 1755649448, "dep_lines": [14, 15, 16, 23, 35, 36, 37, 38, 11, 13, 13, 20, 21, 22, 13, 1, 2, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 20, 10, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch._inductor.runtime.hints", "torch._inductor.runtime.runtime_utils", "torch._inductor.runtime.triton_heuristics", "torch._inductor.codegen.common", "torch._inductor.codegen.simd", "torch._inductor.codegen.simd_kernel_features", "torch._inductor.codegen.triton", "torch._inductor.codegen.triton_utils", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.metrics", "torch._inductor.scheduler", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor", "itertools", "logging", "textwrap", "collections", "dataclasses", "typing", "builtins", "os", "torch", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "contextlib", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "torch._inductor.runtime", "torch.utils", "torch.utils._sympy", "torch.utils._sympy.printers", "typing_extensions"], "hash": "18ae90c28555afc039c2f6ac16da6e4c2418d748", "id": "torch._inductor.codegen.triton_combo_kernel", "ignore_all": true, "interface_hash": "9f331248f9dd5584dd462232accbcec4536f2d77", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\triton_combo_kernel.py", "plugin_data": null, "size": 42391, "suppressed": ["sympy"], "version_id": "1.15.0"}