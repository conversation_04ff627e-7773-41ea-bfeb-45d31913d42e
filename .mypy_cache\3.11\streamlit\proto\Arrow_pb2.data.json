{".class": "MypyFile", "_fullname": "streamlit.proto.Arrow_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Arrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow", "name": "Arrow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.Arrow_pb2.Arrow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow", "builtins.object"], "names": {".class": "SymbolTable", "COLUMNS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.COLUMNS_FIELD_NUMBER", "name": "COLUMNS_FIELD_NUMBER", "type": "builtins.int"}}, "COLUMN_ORDER_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.COLUMN_ORDER_FIELD_NUMBER", "name": "COLUMN_ORDER_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_row_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_row_height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "column_order"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "column_order"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "columns"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "columns"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "disabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "editing_mode"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "editing_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "form_id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "form_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "row_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "row_height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "selection_mode"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "selection_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "styler"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "styler"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Clear<PERSON>ield of Arrow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DATA_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.DATA_FIELD_NUMBER", "name": "DATA_FIELD_NUMBER", "type": "builtins.int"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}}}, "DISABLED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.DISABLED_FIELD_NUMBER", "name": "DISABLED_FIELD_NUMBER", "type": "builtins.int"}}, "DYNAMIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.DYNAMIC", "name": "DYNAMIC", "type": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType"}}, "EDITING_MODE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.EDITING_MODE_FIELD_NUMBER", "name": "EDITING_MODE_FIELD_NUMBER", "type": "builtins.int"}}, "EditingMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.Arrow_pb2.Arrow._EditingMode"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow.EditingMode", "name": "EditingMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow.EditingMode", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow.EditingMode", "streamlit.proto.Arrow_pb2.Arrow._EditingMode", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Arrow_pb2.Arrow.EditingMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Arrow_pb2.Arrow.EditingMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FIXED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.FIXED", "name": "FIXED", "type": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType"}}, "FORM_ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.FORM_ID_FIELD_NUMBER", "name": "FORM_ID_FIELD_NUMBER", "type": "builtins.int"}}, "HEIGHT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.HEIGHT_FIELD_NUMBER", "name": "HEIGHT_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_row_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_row_height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "row_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "row_height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "styler"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "styler"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of Arrow", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.ID_FIELD_NUMBER", "name": "ID_FIELD_NUMBER", "type": "builtins.int"}}, "MULTI_COLUMN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.MULTI_COLUMN", "name": "MULTI_COLUMN", "type": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}, "MULTI_ROW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.MULTI_ROW", "name": "MULTI_ROW", "type": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}, "READ_ONLY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.READ_ONLY", "name": "READ_ONLY", "type": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType"}}, "ROW_HEIGHT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.ROW_HEIGHT_FIELD_NUMBER", "name": "ROW_HEIGHT_FIELD_NUMBER", "type": "builtins.int"}}, "SELECTION_MODE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.SELECTION_MODE_FIELD_NUMBER", "name": "SELECTION_MODE_FIELD_NUMBER", "type": "builtins.int"}}, "SINGLE_COLUMN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.SINGLE_COLUMN", "name": "SINGLE_COLUMN", "type": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}, "SINGLE_ROW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.SINGLE_ROW", "name": "SINGLE_ROW", "type": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}, "STYLER_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.STYLER_FIELD_NUMBER", "name": "STYLER_FIELD_NUMBER", "type": "builtins.int"}}, "SelectionMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.Arrow_pb2.Arrow._SelectionMode"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow.SelectionMode", "name": "SelectionMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow.SelectionMode", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow.SelectionMode", "streamlit.proto.Arrow_pb2.Arrow._SelectionMode", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Arrow_pb2.Arrow.SelectionMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Arrow_pb2.Arrow.SelectionMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "USE_CONTAINER_WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.USE_CONTAINER_WIDTH_FIELD_NUMBER", "name": "USE_CONTAINER_WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.WIDTH_FIELD_NUMBER", "name": "WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_row_height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_row_height"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of Arrow", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "row_height"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_EditingMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingMode", "name": "_EditingMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingMode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow._EditingMode", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.V", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Arrow_pb2.Arrow._EditingMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EditingModeEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper", "name": "_EditingModeEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}}}, "DYNAMIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper.DYNAMIC", "name": "DYNAMIC", "type": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType"}}, "FIXED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper.FIXED", "name": "FIXED", "type": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType"}}, "READ_ONLY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper.READ_ONLY", "name": "READ_ONLY", "type": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Arrow_pb2.Arrow._EditingModeEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SelectionMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode", "name": "_SelectionMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow._SelectionMode", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.V", "line": 65, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SelectionModeEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper", "name": "_SelectionModeEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}}}, "MULTI_COLUMN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper.MULTI_COLUMN", "name": "MULTI_COLUMN", "type": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}, "MULTI_ROW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper.MULTI_ROW", "name": "MULTI_ROW", "type": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}, "SINGLE_COLUMN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper.SINGLE_COLUMN", "name": "SINGLE_COLUMN", "type": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}, "SINGLE_ROW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper.SINGLE_ROW", "name": "SINGLE_ROW", "type": "streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Arrow_pb2.Arrow._SelectionModeEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "data", "styler", "width", "height", "use_container_width", "id", "columns", "editing_mode", "disabled", "form_id", "column_order", "selection_mode", "row_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.<PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "data", "styler", "width", "height", "use_container_width", "id", "columns", "editing_mode", "disabled", "form_id", "column_order", "selection_mode", "row_height"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow", "builtins.bytes", {".class": "UnionType", "items": ["streamlit.proto.Arrow_pb2.Styler", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.str", "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType", "builtins.bool", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.Arrow_pb2.Arrow._SelectionMode.ValueType"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Arrow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.column_order", "name": "column_order", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_order of Arrow", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.column_order", "name": "column_order", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_order of Arrow", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.columns", "name": "columns", "type": "builtins.str"}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.data", "name": "data", "type": "builtins.bytes"}}, "disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.disabled", "name": "disabled", "type": "builtins.bool"}}, "editing_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.editing_mode", "name": "editing_mode", "type": "streamlit.proto.Arrow_pb2.Arrow._EditingMode.ValueType"}}, "form_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.form_id", "name": "form_id", "type": "builtins.str"}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.height", "name": "height", "type": "builtins.int"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.id", "name": "id", "type": "builtins.str"}}, "row_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.row_height", "name": "row_height", "type": "builtins.int"}}, "selection_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.selection_mode", "name": "selection_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selection_mode of Arrow", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.selection_mode", "name": "selection_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selection_mode of Arrow", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "styler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.styler", "name": "styler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "styler of Arrow", "ret_type": "streamlit.proto.Arrow_pb2.Styler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.styler", "name": "styler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.Arrow_pb2.Arrow"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "styler of Arrow", "ret_type": "streamlit.proto.Arrow_pb2.Styler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "use_container_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.use_container_width", "name": "use_container_width", "type": "builtins.bool"}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Arrow.width", "name": "width", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Arrow_pb2.Arrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Arrow_pb2.Arrow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Arrow_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}}}, "Styler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Arrow_pb2.Styler", "name": "Styler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.Arrow_pb2.Styler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Arrow_pb2", "mro": ["streamlit.proto.Arrow_pb2.Styler", "builtins.object"], "names": {".class": "SymbolTable", "CAPTION_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.CAPTION_FIELD_NUMBER", "name": "CAPTION_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Styler.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Arrow_pb2.Styler", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "caption"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "caption"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "display_values"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "display_values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "styles"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "styles"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "uuid"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Styler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}}}, "DISPLAY_VALUES_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.DISPLAY_VALUES_FIELD_NUMBER", "name": "DISPLAY_VALUES_FIELD_NUMBER", "type": "builtins.int"}}, "STYLES_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.STYLES_FIELD_NUMBER", "name": "STYLES_FIELD_NUMBER", "type": "builtins.int"}}, "UUID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.UUID_FIELD_NUMBER", "name": "UUID_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "uuid", "caption", "styles", "display_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Arrow_pb2.Styler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "uuid", "caption", "styles", "display_values"], "arg_types": ["streamlit.proto.Arrow_pb2.Styler", "builtins.str", "builtins.str", "builtins.str", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Styler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "caption": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.caption", "name": "caption", "type": "builtins.str"}}, "display_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.display_values", "name": "display_values", "type": "builtins.bytes"}}, "styles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.styles", "name": "styles", "type": "builtins.str"}}, "uuid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Arrow_pb2.Styler.uuid", "name": "uuid", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Arrow_pb2.Styler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Arrow_pb2.Styler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Arrow_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Arrow_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Arrow_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Arrow_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Arrow_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Arrow_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___Arrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.Arrow_pb2.global___Arrow", "line": 156, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Arrow_pb2.Arrow"}}, "global___Styler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.Arrow_pb2.global___Styler", "line": 188, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Arrow_pb2.Styler"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.Arrow_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Arrow_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\Arrow_pb2.pyi"}