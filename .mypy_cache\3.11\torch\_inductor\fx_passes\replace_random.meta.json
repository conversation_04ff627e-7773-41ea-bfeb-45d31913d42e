{"data_mtime": 1755649448, "dep_lines": [6, 7, 9, 9, 10, 16, 9, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 20, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["torch.fx.passes.graph_transform_observer", "torch.fx.passes.shape_prop", "torch._inductor.config", "torch._inductor.inductor_prims", "torch._inductor.pattern_matcher", "torch._inductor.virtualized", "torch._inductor", "collections", "logging", "torch", "builtins", "warnings", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "typing", "_frozen_importlib", "abc", "torch._C", "torch._ops", "torch.fx", "torch.fx.graph", "torch.fx.graph_module", "torch.nn.modules", "torch.nn.modules.module", "contextlib"], "hash": "dfc79305296bd47aee6c82e23c8d06106114f3d2", "id": "torch._inductor.fx_passes.replace_random", "ignore_all": true, "interface_hash": "6dc4ba794eec657124e97eea2d8d2c122920bcbf", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\replace_random.py", "plugin_data": null, "size": 4195, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}