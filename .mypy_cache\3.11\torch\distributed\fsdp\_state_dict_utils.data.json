{".class": "MypyFile", "_fullname": "torch.distributed.fsdp._state_dict_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.DTensor", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "FLAT_PARAM": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._unshard_param_utils.FLAT_PARAM", "kind": "Gdef"}, "FSDP_PREFIX": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.FSDP_PREFIX", "kind": "Gdef"}, "FSDP_WRAPPED_MODULE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.FSDP_WRAPPED_MODULE", "kind": "Gdef"}, "FullStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.FullStateDictConfig", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.shard.Shard", "kind": "Gdef"}, "ShardedTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.api.ShardedTensor", "kind": "Gdef"}, "ShardingStrategy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardingStrategy", "kind": "Gdef"}, "SimpleProfiler": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._debug_utils.SimpleProfiler", "kind": "Gdef"}, "StateDictType": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictType", "kind": "Gdef"}, "_FSDPState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._FSDPState", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._state_dict_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._state_dict_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._state_dict_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._state_dict_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._state_dict_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._state_dict_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cast_buffers_to_dtype_and_device": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._cast_buffers_to_dtype_and_device", "kind": "Gdef"}, "_common_pre_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module", "fsdp_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._common_pre_state_dict_hook", "name": "_common_pre_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module", "fsdp_state"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_common_pre_state_dict_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_common_unshard_post_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix", "param_hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._common_unshard_post_state_dict_hook", "name": "_common_unshard_post_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._common_unshard_post_state_dict_hook", "name": "_common_unshard_post_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_common_unshard_pre_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "offload_to_cpu", "rank0_only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._common_unshard_pre_state_dict_hook", "name": "_common_unshard_pre_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "offload_to_cpu", "rank0_only"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_common_unshard_pre_state_dict_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_to_wrapped_module_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._convert_to_wrapped_module_name", "name": "_convert_to_wrapped_module_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_to_wrapped_module_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_enter_unshard_params_ctx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["module", "fsdp_state", "writeback", "rank0_only", "offload_to_cpu", "with_grads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._enter_unshard_params_ctx", "name": "_enter_unshard_params_ctx", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._enter_unshard_params_ctx", "name": "_enter_unshard_params_ctx", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_exit_unshard_params_ctx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module", "fsdp_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._exit_unshard_params_ctx", "name": "_exit_unshard_params_ctx", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._exit_unshard_params_ctx", "name": "_exit_unshard_params_ctx", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_ext_all_gather_dtensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fsdp_extensions._ext_all_gather_dtensor", "kind": "Gdef"}, "_ext_chunk_dtensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fsdp_extensions._ext_chunk_dtensor", "kind": "Gdef"}, "_ext_chunk_tensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fsdp_extensions._ext_chunk_tensor", "kind": "Gdef"}, "_ext_post_unflatten_transform": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fsdp_extensions._ext_post_unflatten_transform", "kind": "Gdef"}, "_ext_pre_load_state_dict_transform": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fsdp_extensions._ext_pre_load_state_dict_transform", "kind": "Gdef"}, "_full_post_load_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["module", "fsdp_state", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._full_post_load_state_dict_hook", "name": "_full_post_load_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["module", "fsdp_state", "args", "kwargs"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_full_post_load_state_dict_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_full_post_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._full_post_state_dict_hook", "name": "_full_post_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._full_post_state_dict_hook", "name": "_full_post_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_full_pre_load_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._full_pre_load_state_dict_hook", "name": "_full_pre_load_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_full_pre_load_state_dict_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_full_pre_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["fsdp_state", "module", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._full_pre_state_dict_hook", "name": "_full_pre_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._full_pre_state_dict_hook", "name": "_full_pre_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_get_module_fsdp_state_if_fully_sharded_module": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._get_module_fsdp_state_if_fully_sharded_module", "kind": "Gdef"}, "_get_orig_buffer_dtypes": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._get_orig_buffer_dtypes", "kind": "Gdef"}, "_has_fsdp_params": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._has_fsdp_params", "kind": "Gdef"}, "_is_composable": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._is_composable", "kind": "Gdef"}, "_lazy_init": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._lazy_init", "kind": "Gdef"}, "_local_post_load_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["module", "fsdp_state", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._local_post_load_state_dict_hook", "name": "_local_post_load_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["module", "fsdp_state", "args", "kwargs"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_local_post_load_state_dict_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_local_post_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._local_post_state_dict_hook", "name": "_local_post_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._local_post_state_dict_hook", "name": "_local_post_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_local_pre_load_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._local_pre_load_state_dict_hook", "name": "_local_pre_load_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_local_pre_load_state_dict_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_local_pre_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["fsdp_state", "module", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._local_pre_state_dict_hook", "name": "_local_pre_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["fsdp_state", "module", "args", "kwargs"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_local_pre_state_dict_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mesh_resources": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh._mesh_resources", "kind": "Gdef"}, "_module_handle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._module_handle", "kind": "Gdef"}, "_param_name_infos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module", "fsdp_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._param_name_infos", "name": "_param_name_infos", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module", "fsdp_state"], "arg_types": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._common_utils._FSDPState"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_param_name_infos", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_post_load_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["module", "incompatible_keys", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._post_load_state_dict_hook", "name": "_post_load_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._post_load_state_dict_hook", "name": "_post_load_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_post_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["module", "state_dict", "prefix", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._post_state_dict_hook", "name": "_post_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._post_state_dict_hook", "name": "_post_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_pre_load_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["module", "state_dict", "prefix", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._pre_load_state_dict_hook", "name": "_pre_load_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._pre_load_state_dict_hook", "name": "_pre_load_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_pre_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["module", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._pre_state_dict_hook", "name": "_pre_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._pre_state_dict_hook", "name": "_pre_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_register_all_state_dict_hooks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._register_all_state_dict_hooks", "name": "_register_all_state_dict_hooks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_all_state_dict_hooks", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register_state_dict_hooks_base": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "hook_registration_fn_name", "hook", "hook_registration_fn_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._register_state_dict_hooks_base", "name": "_register_state_dict_hooks_base", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._register_state_dict_hooks_base", "name": "_register_state_dict_hooks_base", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_replace_by_prefix": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._replace_by_prefix", "kind": "Gdef"}, "_replace_with_full_state_dict_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fsdp_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._replace_with_full_state_dict_type", "name": "_replace_with_full_state_dict_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fsdp_state"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace_with_full_state_dict_type", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._replace_with_full_state_dict_type", "name": "_replace_with_full_state_dict_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fsdp_state"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace_with_full_state_dict_type", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_reset_flat_param_grad_info_if_needed": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._runtime_utils._reset_flat_param_grad_info_if_needed", "kind": "Gdef"}, "_set_use_dtensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fsdp_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._set_use_dtensor", "name": "_set_use_dtensor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._set_use_dtensor", "name": "_set_use_dtensor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_sharded_post_load_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["module", "fsdp_state", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._sharded_post_load_state_dict_hook", "name": "_sharded_post_load_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._sharded_post_load_state_dict_hook", "name": "_sharded_post_load_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_sharded_post_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._sharded_post_state_dict_hook", "name": "_sharded_post_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._sharded_post_state_dict_hook", "name": "_sharded_post_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_sharded_pre_load_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "fsdp_state", "state_dict", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._state_dict_utils._sharded_pre_load_state_dict_hook", "name": "_sharded_pre_load_state_dict_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._state_dict_utils._sharded_pre_load_state_dict_hook", "name": "_sharded_pre_load_state_dict_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_sharded_pre_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["fsdp_state", "module", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._sharded_pre_state_dict_hook", "name": "_sharded_pre_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["fsdp_state", "module", "args", "kwargs"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sharded_pre_state_dict_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_shared_param_name_infos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module", "fsdp_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._shared_param_name_infos", "name": "_shared_param_name_infos", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module", "fsdp_state"], "arg_types": ["torch.nn.modules.module.Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shared_param_name_infos", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_unshard_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fsdp_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._state_dict_utils._should_unshard_params", "name": "_should_unshard_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fsdp_state"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_unshard_params", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unshard_fsdp_state_params": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._unshard_param_utils._unshard_fsdp_state_params", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "checkpoint_wrapper": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms._checkpoint.checkpoint_wrapper", "kind": "Gdef"}, "clean_tensor_name": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.clean_tensor_name", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "init_from_local_shards": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.init_from_local_shards", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._state_dict_utils.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "no_type_check": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_state_dict_utils.py"}