{".class": "MypyFile", "_fullname": "altair.vegalite.v5.schema._config", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AggregateOp_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.AggregateOp_T", "kind": "Gdef", "module_public": false}, "Align_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Align_T", "kind": "Gdef", "module_public": false}, "AllSortString_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.AllSortString_T", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AreaConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.AreaConfigKwds", "name": "AreaConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.AreaConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.AreaConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["ariaRole", "builtins.str"], ["ariaRoleDescription", "builtins.str"], ["aspect", "builtins.bool"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["blend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Blend_T"}], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["cornerRadius", "builtins.float"], ["cornerRadiusBottomLeft", "builtins.float"], ["cornerRadiusBottomRight", "builtins.float"], ["cornerRadiusTopLeft", "builtins.float"], ["cornerRadiusTopRight", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["description", "builtins.str"], ["dir", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextDirection_T"}], ["dx", "builtins.float"], ["dy", "builtins.float"], ["ellipsis", "builtins.str"], ["endAngle", "builtins.float"], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["filled", "builtins.bool"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["height", "builtins.float"], ["href", "builtins.str"], ["innerRadius", "builtins.float"], ["interpolate", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Interpolate_T"}], ["invalid", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["line", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.OverlayMarkDefKwds"}], "uses_pep604_syntax": true}], ["lineBreak", "builtins.str"], ["lineHeight", "builtins.float"], ["opacity", "builtins.float"], ["order", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["outerRadius", "builtins.float"], ["padAngle", "builtins.float"], ["point", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.OverlayMarkDefKwds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}], "uses_pep604_syntax": true}], ["radius", "builtins.float"], ["radius2", "builtins.float"], ["shape", "builtins.str"], ["size", "builtins.float"], ["smooth", "builtins.bool"], ["startAngle", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOffset", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"], ["tension", "builtins.float"], ["text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["theta", "builtins.float"], ["theta2", "builtins.float"], ["timeUnitBandPosition", "builtins.float"], ["timeUnitBandSize", "builtins.float"], ["tooltip", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TooltipContentKwds"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["url", "builtins.str"], ["width", "builtins.float"], ["x", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["x2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["y", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], ["y2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "AutoSizeParamsKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.AutoSizeParamsKwds", "name": "AutoSizeParamsKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.AutoSizeParamsKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.AutoSizeParamsKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["contains", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "content"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "padding"}], "uses_pep604_syntax": false}], ["resize", "builtins.bool"], ["type", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.AutosizeType_T"}]], "readonly_keys": [], "required_keys": []}}}, "AutosizeType_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.AutosizeType_T", "kind": "Gdef", "module_public": false}, "AxisConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.AxisConfigKwds", "name": "AxisConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.AxisConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.AxisConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["aria", "builtins.bool"], ["bandPosition", "builtins.float"], ["description", "builtins.str"], ["disable", "builtins.bool"], ["domain", "builtins.bool"], ["domainCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["domainColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["domainDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["domainDashOffset", "builtins.float"], ["domainOpacity", "builtins.float"], ["domainWidth", "builtins.float"], ["format", "builtins.str"], ["formatType", "builtins.str"], ["grid", "builtins.bool"], ["gridCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["gridColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["gridDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["gridDashOffset", "builtins.float"], ["gridOpacity", "builtins.float"], ["gridWidth", "builtins.float"], ["labelAlign", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["labelAngle", "builtins.float"], ["labelBaseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["labelBound", {".class": "UnionType", "items": ["builtins.bool", "builtins.float"], "uses_pep604_syntax": true}], ["labelColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["labelExpr", "builtins.str"], ["labelFlush", {".class": "UnionType", "items": ["builtins.bool", "builtins.float"], "uses_pep604_syntax": true}], ["labelFlushOffset", "builtins.float"], ["labelFont", "builtins.str"], ["labelFontSize", "builtins.float"], ["labelFontStyle", "builtins.str"], ["labelFontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["labelLimit", "builtins.float"], ["labelLineHeight", "builtins.float"], ["labelOffset", "builtins.float"], ["labelOpacity", "builtins.float"], ["labelOverlap", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "greedy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "parity"}], "uses_pep604_syntax": true}], ["labelPadding", "builtins.float"], ["labelSeparation", "builtins.float"], ["labels", "builtins.bool"], ["maxExtent", "builtins.float"], ["minExtent", "builtins.float"], ["offset", "builtins.float"], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.AxisOrient_T"}], ["position", "builtins.float"], ["style", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["tickBand", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "extent"}], "uses_pep604_syntax": false}], ["tickCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["tickColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["tickCount", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TimeIntervalStepKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TimeInterval_T"}], "uses_pep604_syntax": true}], ["tickDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["tickDashOffset", "builtins.float"], ["tickExtra", "builtins.bool"], ["tickMinStep", "builtins.float"], ["tickOffset", "builtins.float"], ["tickOpacity", "builtins.float"], ["tickRound", "builtins.bool"], ["tickSize", "builtins.float"], ["tickWidth", "builtins.float"], ["ticks", "builtins.bool"], ["title", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["titleAlign", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["titleAnchor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleAnchor_T"}], ["titleAngle", "builtins.float"], ["titleBaseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["titleColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["titleFont", "builtins.str"], ["titleFontSize", "builtins.float"], ["titleFontStyle", "builtins.str"], ["titleFontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["titleLimit", "builtins.float"], ["titleLineHeight", "builtins.float"], ["titleOpacity", "builtins.float"], ["titlePadding", "builtins.float"], ["titleX", "builtins.float"], ["titleY", "builtins.float"], ["translate", "builtins.float"], ["values", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DateTimeKwds"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["zindex", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "AxisOrient_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.AxisOrient_T", "kind": "Gdef", "module_public": false}, "AxisResolveMapKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.AxisResolveMapKwds", "name": "AxisResolveMapKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.AxisResolveMapKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.AxisResolveMapKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["x", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["y", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}]], "readonly_keys": [], "required_keys": []}}}, "BarConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.BarConfigKwds", "name": "BarConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.BarConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.BarConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["ariaRole", "builtins.str"], ["ariaRoleDescription", "builtins.str"], ["aspect", "builtins.bool"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["binSpacing", "builtins.float"], ["blend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Blend_T"}], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["continuousBandSize", "builtins.float"], ["cornerRadius", "builtins.float"], ["cornerRadiusBottomLeft", "builtins.float"], ["cornerRadiusBottomRight", "builtins.float"], ["cornerRadiusEnd", "builtins.float"], ["cornerRadiusTopLeft", "builtins.float"], ["cornerRadiusTopRight", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["description", "builtins.str"], ["dir", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextDirection_T"}], ["discreteBandSize", "builtins.float"], ["dx", "builtins.float"], ["dy", "builtins.float"], ["ellipsis", "builtins.str"], ["endAngle", "builtins.float"], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["filled", "builtins.bool"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["height", "builtins.float"], ["href", "builtins.str"], ["innerRadius", "builtins.float"], ["interpolate", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Interpolate_T"}], ["invalid", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["lineBreak", "builtins.str"], ["lineHeight", "builtins.float"], ["minBandSize", "builtins.float"], ["opacity", "builtins.float"], ["order", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["outerRadius", "builtins.float"], ["padAngle", "builtins.float"], ["radius", "builtins.float"], ["radius2", "builtins.float"], ["shape", "builtins.str"], ["size", "builtins.float"], ["smooth", "builtins.bool"], ["startAngle", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOffset", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"], ["tension", "builtins.float"], ["text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["theta", "builtins.float"], ["theta2", "builtins.float"], ["timeUnitBandPosition", "builtins.float"], ["timeUnitBandSize", "builtins.float"], ["tooltip", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TooltipContentKwds"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["url", "builtins.str"], ["width", "builtins.float"], ["x", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["x2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["y", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], ["y2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "BindCheckboxKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.BindCheckboxKwds", "name": "BindCheckboxKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.BindCheckboxKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.BindCheckboxKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["input", {".class": "LiteralType", "fallback": "builtins.str", "value": "checkbox"}], ["debounce", "builtins.float"], ["element", "builtins.str"], ["name", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "BindDirectKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.BindDirectKwds", "name": "BindDirectKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.BindDirectKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.BindDirectKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["element", "builtins.str"], ["debounce", "builtins.float"], ["event", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "BindInputKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.BindInputKwds", "name": "BindInputKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.BindInputKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.BindInputKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["autocomplete", "builtins.str"], ["debounce", "builtins.float"], ["element", "builtins.str"], ["input", "builtins.str"], ["name", "builtins.str"], ["placeholder", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "BindRadioSelectKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.BindRadioSelectKwds", "name": "BindRadioSelectKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.BindRadioSelectKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.BindRadioSelectKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["input", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "radio"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "select"}], "uses_pep604_syntax": false}], ["options", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["debounce", "builtins.float"], ["element", "builtins.str"], ["labels", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["name", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "BindRangeKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.BindRangeKwds", "name": "BindRangeKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.BindRangeKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.BindRangeKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["input", {".class": "LiteralType", "fallback": "builtins.str", "value": "range"}], ["debounce", "builtins.float"], ["element", "builtins.str"], ["max", "builtins.float"], ["min", "builtins.float"], ["name", "builtins.str"], ["step", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "BinnedTimeUnit_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.BinnedTimeUnit_T", "kind": "Gdef", "module_public": false}, "Blend_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Blend_T", "kind": "Gdef", "module_public": false}, "BoxPlotConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.BoxPlotConfigKwds", "name": "BoxPlotConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.BoxPlotConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.BoxPlotConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["box", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}], ["extent", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "min-max"}], "uses_pep604_syntax": true}], ["median", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}], ["outliers", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}], ["rule", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}], ["size", "builtins.float"], ["ticks", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "BoxPlot_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.BoxPlot_T", "kind": "Gdef", "module_public": false}, "BrushConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.BrushConfigKwds", "name": "BrushConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.BrushConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.BrushConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "ColorHex": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ColorHex", "kind": "Gdef", "module_public": false}, "ColorName_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ColorName_T", "kind": "Gdef", "module_public": false}, "ColorScheme_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ColorScheme_T", "kind": "Gdef", "module_public": false}, "CompositeMark_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.CompositeMark_T", "kind": "Gdef", "module_public": false}, "CompositionConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.CompositionConfigKwds", "name": "CompositionConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.CompositionConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.CompositionConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["columns", "builtins.float"], ["spacing", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "ConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ConfigKwds", "name": "ConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["arc", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}], ["area", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}], ["aria", "builtins.bool"], ["autosize", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AutoSizeParamsKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.AutosizeType_T"}], "uses_pep604_syntax": true}], ["axis", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisBand", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisBottom", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisDiscrete", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisLeft", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisPoint", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisQuantitative", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisRight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisTemporal", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisTop", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisX", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisXBand", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisXDiscrete", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisXPoint", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisXQuantitative", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisXTemporal", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisY", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisYBand", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisYDiscrete", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisYPoint", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisYQuantitative", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["axisYTemporal", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds"}], ["background", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["bar", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}], ["boxplot", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BoxPlotConfigKwds"}], ["circle", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["concat", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.CompositionConfigKwds"}], ["countTitle", "builtins.str"], ["customFormatTypes", "builtins.bool"], ["errorband", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ErrorBandConfigKwds"}], ["errorbar", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ErrorBarConfigKwds"}], ["facet", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.CompositionConfigKwds"}], ["fieldTitle", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "verbal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "functional"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}], "uses_pep604_syntax": false}], ["font", "builtins.str"], ["geoshape", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["header", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.HeaderConfigKwds"}], ["headerColumn", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.HeaderConfigKwds"}], ["headerFacet", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.HeaderConfigKwds"}], ["headerRow", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.HeaderConfigKwds"}], ["image", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}], ["legend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LegendConfigKwds"}], ["line", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}], ["lineBreak", "builtins.str"], ["locale", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LocaleKwds"}], ["mark", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["normalizedNumberFormat", "builtins.str"], ["normalizedNumberFormatType", "builtins.str"], ["numberFormat", "builtins.str"], ["numberFormatType", "builtins.str"], ["padding", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.PaddingKwds"}], "uses_pep604_syntax": true}], ["params", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.VariableParameterKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TopLevelSelectionParameterKwds"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["point", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["projection", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ProjectionConfigKwds"}], ["range", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RangeConfigKwds"}], ["rect", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}], ["rule", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["scale", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ScaleConfigKwds"}], ["selection", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.SelectionConfigKwds"}], ["square", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["style", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.StyleConfigIndexKwds"}], ["text", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["tick", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], ["timeFormat", "builtins.str"], ["timeFormatType", "builtins.str"], ["title", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TitleConfigKwds"}], ["tooltipFormat", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.FormatConfigKwds"}], ["trail", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}], ["view", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ViewConfigKwds"}]], "readonly_keys": [], "required_keys": []}}}, "Cursor_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Cursor_T", "kind": "Gdef", "module_public": false}, "DateTimeKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.DateTimeKwds", "name": "DateTimeKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.DateTimeKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.DateTimeKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["date", "builtins.float"], ["day", {".class": "UnionType", "items": ["builtins.str", "builtins.float"], "uses_pep604_syntax": true}], ["hours", "builtins.float"], ["milliseconds", "builtins.float"], ["minutes", "builtins.float"], ["month", {".class": "UnionType", "items": ["builtins.str", "builtins.float"], "uses_pep604_syntax": true}], ["quarter", "builtins.float"], ["seconds", "builtins.float"], ["utc", "builtins.bool"], ["year", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "DerivedStreamKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.DerivedStreamKwds", "name": "DerivedStreamKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.DerivedStreamKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.DerivedStreamKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["stream", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["between", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["consume", "builtins.bool"], ["debounce", "builtins.float"], ["filter", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["markname", "builtins.str"], ["marktype", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkType_T"}], ["throttle", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "ErrorBandConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ErrorBandConfigKwds", "name": "ErrorBandConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ErrorBandConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ErrorBandConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["band", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}], ["borders", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}], ["extent", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ErrorBarExtent_T"}], ["interpolate", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Interpolate_T"}], ["tension", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "ErrorBand_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ErrorBand_T", "kind": "Gdef", "module_public": false}, "ErrorBarConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ErrorBarConfigKwds", "name": "ErrorBarConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ErrorBarConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ErrorBarConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["extent", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ErrorBarExtent_T"}], ["rule", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}], ["size", "builtins.float"], ["thickness", "builtins.float"], ["ticks", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "ErrorBarExtent_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ErrorBarExtent_T", "kind": "Gdef", "module_public": false}, "ErrorBar_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ErrorBar_T", "kind": "Gdef", "module_public": false}, "FeatureGeometryGeoJsonPropertiesKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.FeatureGeometryGeoJsonPropertiesKwds", "name": "FeatureGeometryGeoJsonPropertiesKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.FeatureGeometryGeoJsonPropertiesKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.FeatureGeometryGeoJsonPropertiesKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["geometry", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.PointKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.PolygonKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineStringKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiPointKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiPolygonKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiLineStringKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeometryCollectionKwds"}], "uses_pep604_syntax": true}], ["properties", {".class": "NoneType"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "Feature"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["id", {".class": "UnionType", "items": ["builtins.str", "builtins.float"], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "FontWeight_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.FontWeight_T", "kind": "Gdef", "module_public": false}, "FormatConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.FormatConfigKwds", "name": "FormatConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.FormatConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.FormatConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["normalizedNumberFormat", "builtins.str"], ["normalizedNumberFormatType", "builtins.str"], ["numberFormat", "builtins.str"], ["numberFormatType", "builtins.str"], ["timeFormat", "builtins.str"], ["timeFormatType", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "GeoJsonFeatureCollectionKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.GeoJsonFeatureCollectionKwds", "name": "GeoJsonFeatureCollectionKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.GeoJsonFeatureCollectionKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.GeoJsonFeatureCollectionKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["features", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.FeatureGeometryGeoJsonPropertiesKwds"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "FeatureCollection"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "GeoJsonFeatureKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds", "name": "GeoJsonFeatureKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.GeoJsonFeatureKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["geometry", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.PointKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.PolygonKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineStringKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiPointKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiPolygonKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiLineStringKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeometryCollectionKwds"}], "uses_pep604_syntax": true}], ["properties", {".class": "NoneType"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "Feature"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["id", {".class": "UnionType", "items": ["builtins.str", "builtins.float"], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "GeometryCollectionKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.GeometryCollectionKwds", "name": "GeometryCollectionKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.GeometryCollectionKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.GeometryCollectionKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["geometries", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.PointKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.PolygonKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineStringKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiPointKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiPolygonKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MultiLineStringKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeometryCollectionKwds"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "GeometryCollection"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "GradientStopKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.GradientStopKwds", "name": "GradientStopKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.GradientStopKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.GradientStopKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["offset", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "HeaderConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.HeaderConfigKwds", "name": "HeaderConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.HeaderConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.HeaderConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["format", "builtins.str"], ["formatType", "builtins.str"], ["labelAlign", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["labelAnchor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleAnchor_T"}], ["labelAngle", "builtins.float"], ["labelBaseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["labelColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["labelExpr", "builtins.str"], ["labelFont", "builtins.str"], ["labelFontSize", "builtins.float"], ["labelFontStyle", "builtins.str"], ["labelFontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["labelLimit", "builtins.float"], ["labelLineHeight", "builtins.float"], ["labelOrient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orient_T"}], ["labelPadding", "builtins.float"], ["labels", "builtins.bool"], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orient_T"}], ["title", {".class": "NoneType"}], ["titleAlign", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["titleAnchor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleAnchor_T"}], ["titleAngle", "builtins.float"], ["titleBaseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["titleColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["titleFont", "builtins.str"], ["titleFontSize", "builtins.float"], ["titleFontStyle", "builtins.str"], ["titleFontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["titleLimit", "builtins.float"], ["titleLineHeight", "builtins.float"], ["titleOrient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orient_T"}], ["titlePadding", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "ImputeMethod_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ImputeMethod_T", "kind": "Gdef", "module_public": false}, "Interpolate_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Interpolate_T", "kind": "Gdef", "module_public": false}, "IntervalSelectionConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.IntervalSelectionConfigKwds", "name": "IntervalSelectionConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.IntervalSelectionConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.IntervalSelectionConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "interval"}], ["clear", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["encodings", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SingleDefUnitChannel_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["fields", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["mark", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BrushConfigKwds"}], ["on", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["resolve", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SelectionResolution_T"}], ["translate", {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}], ["zoom", {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "IntervalSelectionConfigWithoutTypeKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.IntervalSelectionConfigWithoutTypeKwds", "name": "IntervalSelectionConfigWithoutTypeKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.IntervalSelectionConfigWithoutTypeKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.IntervalSelectionConfigWithoutTypeKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["clear", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["encodings", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SingleDefUnitChannel_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["fields", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["mark", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BrushConfigKwds"}], ["on", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["resolve", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SelectionResolution_T"}], ["translate", {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}], ["zoom", {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "LayoutAlign_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.LayoutAlign_T", "kind": "Gdef", "module_public": false}, "LegendConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.LegendConfigKwds", "name": "LegendConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.LegendConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.LegendConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["aria", "builtins.bool"], ["clipHeight", "builtins.float"], ["columnPadding", "builtins.float"], ["columns", "builtins.float"], ["cornerRadius", "builtins.float"], ["description", "builtins.str"], ["direction", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["disable", "builtins.bool"], ["fillColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["gradientDirection", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["gradientHorizontalMaxLength", "builtins.float"], ["gradientHorizontalMinLength", "builtins.float"], ["gradientLabelLimit", "builtins.float"], ["gradientLabelOffset", "builtins.float"], ["gradientLength", "builtins.float"], ["gradientOpacity", "builtins.float"], ["gradientStrokeColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["gradientStrokeWidth", "builtins.float"], ["gradientThickness", "builtins.float"], ["gradientVerticalMaxLength", "builtins.float"], ["gradientVerticalMinLength", "builtins.float"], ["gridAlign", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.LayoutAlign_T"}], ["labelAlign", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["labelBaseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["labelColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["labelFont", "builtins.str"], ["labelFontSize", "builtins.float"], ["labelFontStyle", "builtins.str"], ["labelFontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["labelLimit", "builtins.float"], ["labelOffset", "builtins.float"], ["labelOpacity", "builtins.float"], ["labelOverlap", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "greedy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "parity"}], "uses_pep604_syntax": true}], ["labelPadding", "builtins.float"], ["labelSeparation", "builtins.float"], ["layout", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Map"}], ["legendX", "builtins.float"], ["legendY", "builtins.float"], ["offset", "builtins.float"], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.LegendOrient_T"}], ["padding", "builtins.float"], ["rowPadding", "builtins.float"], ["strokeColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeWidth", "builtins.float"], ["symbolBaseFillColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["symbolBaseStrokeColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["symbolDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["symbolDashOffset", "builtins.float"], ["symbolDirection", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["symbolFillColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["symbolLimit", "builtins.float"], ["symbolOffset", "builtins.float"], ["symbolOpacity", "builtins.float"], ["symbolSize", "builtins.float"], ["symbolStrokeColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["symbolStrokeWidth", "builtins.float"], ["symbolType", "builtins.str"], ["tickCount", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TimeIntervalStepKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TimeInterval_T"}], "uses_pep604_syntax": true}], ["title", {".class": "NoneType"}], ["titleAlign", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["titleAnchor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleAnchor_T"}], ["titleBaseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["titleColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["titleFont", "builtins.str"], ["titleFontSize", "builtins.float"], ["titleFontStyle", "builtins.str"], ["titleFontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["titleLimit", "builtins.float"], ["titleLineHeight", "builtins.float"], ["titleOpacity", "builtins.float"], ["titleOrient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orient_T"}], ["titlePadding", "builtins.float"], ["unselectedOpacity", "builtins.float"], ["zindex", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "LegendOrient_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.LegendOrient_T", "kind": "Gdef", "module_public": false}, "LegendResolveMapKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.LegendResolveMapKwds", "name": "LegendResolveMapKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.LegendResolveMapKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.LegendResolveMapKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["angle", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["color", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["fill", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["fillOpacity", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["opacity", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["shape", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["size", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["stroke", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["strokeDash", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["strokeOpacity", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["strokeWidth", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}]], "readonly_keys": [], "required_keys": []}}}, "LegendStreamBindingKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.LegendStreamBindingKwds", "name": "LegendStreamBindingKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.LegendStreamBindingKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.LegendStreamBindingKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["legend", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "LineConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.LineConfigKwds", "name": "LineConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.LineConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.LineConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["ariaRole", "builtins.str"], ["ariaRoleDescription", "builtins.str"], ["aspect", "builtins.bool"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["blend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Blend_T"}], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["cornerRadius", "builtins.float"], ["cornerRadiusBottomLeft", "builtins.float"], ["cornerRadiusBottomRight", "builtins.float"], ["cornerRadiusTopLeft", "builtins.float"], ["cornerRadiusTopRight", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["description", "builtins.str"], ["dir", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextDirection_T"}], ["dx", "builtins.float"], ["dy", "builtins.float"], ["ellipsis", "builtins.str"], ["endAngle", "builtins.float"], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["filled", "builtins.bool"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["height", "builtins.float"], ["href", "builtins.str"], ["innerRadius", "builtins.float"], ["interpolate", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Interpolate_T"}], ["invalid", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["lineBreak", "builtins.str"], ["lineHeight", "builtins.float"], ["opacity", "builtins.float"], ["order", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["outerRadius", "builtins.float"], ["padAngle", "builtins.float"], ["point", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.OverlayMarkDefKwds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "transparent"}], "uses_pep604_syntax": true}], ["radius", "builtins.float"], ["radius2", "builtins.float"], ["shape", "builtins.str"], ["size", "builtins.float"], ["smooth", "builtins.bool"], ["startAngle", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOffset", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"], ["tension", "builtins.float"], ["text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["theta", "builtins.float"], ["theta2", "builtins.float"], ["timeUnitBandPosition", "builtins.float"], ["timeUnitBandSize", "builtins.float"], ["tooltip", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TooltipContentKwds"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["url", "builtins.str"], ["width", "builtins.float"], ["x", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["x2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["y", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], ["y2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "LineStringKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.LineStringKwds", "name": "LineStringKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.LineStringKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.LineStringKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["coordinates", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "LineString"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "LinearGradientKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.LinearGradientKwds", "name": "LinearGradientKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.LinearGradientKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.LinearGradientKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["gradient", {".class": "LiteralType", "fallback": "builtins.str", "value": "linear"}], ["stops", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GradientStopKwds"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["id", "builtins.str"], ["x1", "builtins.float"], ["x2", "builtins.float"], ["y1", "builtins.float"], ["y2", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "LocaleKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.LocaleKwds", "name": "LocaleKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.LocaleKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.LocaleKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["number", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.NumberLocaleKwds"}], ["time", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TimeLocaleKwds"}]], "readonly_keys": [], "required_keys": []}}}, "Map": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Map", "kind": "Gdef", "module_public": false}, "MarkConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.MarkConfigKwds", "name": "MarkConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.MarkConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.MarkConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["ariaRole", "builtins.str"], ["ariaRoleDescription", "builtins.str"], ["aspect", "builtins.bool"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["blend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Blend_T"}], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["cornerRadius", "builtins.float"], ["cornerRadiusBottomLeft", "builtins.float"], ["cornerRadiusBottomRight", "builtins.float"], ["cornerRadiusTopLeft", "builtins.float"], ["cornerRadiusTopRight", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["description", "builtins.str"], ["dir", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextDirection_T"}], ["dx", "builtins.float"], ["dy", "builtins.float"], ["ellipsis", "builtins.str"], ["endAngle", "builtins.float"], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["filled", "builtins.bool"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["height", "builtins.float"], ["href", "builtins.str"], ["innerRadius", "builtins.float"], ["interpolate", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Interpolate_T"}], ["invalid", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["lineBreak", "builtins.str"], ["lineHeight", "builtins.float"], ["opacity", "builtins.float"], ["order", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["outerRadius", "builtins.float"], ["padAngle", "builtins.float"], ["radius", "builtins.float"], ["radius2", "builtins.float"], ["shape", "builtins.str"], ["size", "builtins.float"], ["smooth", "builtins.bool"], ["startAngle", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOffset", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"], ["tension", "builtins.float"], ["text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["theta", "builtins.float"], ["theta2", "builtins.float"], ["timeUnitBandPosition", "builtins.float"], ["timeUnitBandSize", "builtins.float"], ["tooltip", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TooltipContentKwds"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["url", "builtins.str"], ["width", "builtins.float"], ["x", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["x2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["y", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], ["y2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "MarkInvalidDataMode_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T", "kind": "Gdef", "module_public": false}, "MarkType_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.MarkType_T", "kind": "Gdef", "module_public": false}, "Mark_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Mark_T", "kind": "Gdef", "module_public": false}, "MergedStreamKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.MergedStreamKwds", "name": "MergedStreamKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.MergedStreamKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.MergedStreamKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["merge", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["between", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["consume", "builtins.bool"], ["debounce", "builtins.float"], ["filter", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["markname", "builtins.str"], ["marktype", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkType_T"}], ["throttle", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "MultiLineStringKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.MultiLineStringKwds", "name": "MultiLineStringKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.MultiLineStringKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.MultiLineStringKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["coordinates", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "MultiLineString"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "MultiPointKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.MultiPointKwds", "name": "MultiPointKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.MultiPointKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.MultiPointKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["coordinates", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "MultiPoint"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "MultiPolygonKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.MultiPolygonKwds", "name": "MultiPolygonKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.MultiPolygonKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.MultiPolygonKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["coordinates", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "MultiPolygon"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "MultiTimeUnit_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.MultiTimeUnit_T", "kind": "Gdef", "module_public": false}, "NonArgAggregateOp_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.NonArgAggregateOp_T", "kind": "Gdef", "module_public": false}, "NumberLocaleKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.NumberLocaleKwds", "name": "NumberLocaleKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.NumberLocaleKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.NumberLocaleKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["currency", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["decimal", "builtins.str"], ["grouping", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["thousands", "builtins.str"], ["minus", "builtins.str"], ["nan", "builtins.str"], ["numerals", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["percent", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "OneOrSeq": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.OneOrSeq", "kind": "Gdef", "module_public": false}, "Orient_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Orient_T", "kind": "Gdef", "module_public": false}, "Orientation_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Orientation_T", "kind": "Gdef", "module_public": false}, "OverlayMarkDefKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.OverlayMarkDefKwds", "name": "OverlayMarkDefKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.OverlayMarkDefKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.OverlayMarkDefKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["ariaRole", "builtins.str"], ["ariaRoleDescription", "builtins.str"], ["aspect", "builtins.bool"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["blend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Blend_T"}], ["clip", "builtins.bool"], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["cornerRadius", "builtins.float"], ["cornerRadiusBottomLeft", "builtins.float"], ["cornerRadiusBottomRight", "builtins.float"], ["cornerRadiusTopLeft", "builtins.float"], ["cornerRadiusTopRight", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["description", "builtins.str"], ["dir", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextDirection_T"}], ["dx", "builtins.float"], ["dy", "builtins.float"], ["ellipsis", "builtins.str"], ["endAngle", "builtins.float"], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["filled", "builtins.bool"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["height", "builtins.float"], ["href", "builtins.str"], ["innerRadius", "builtins.float"], ["interpolate", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Interpolate_T"}], ["invalid", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["lineBreak", "builtins.str"], ["lineHeight", "builtins.float"], ["opacity", "builtins.float"], ["order", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["outerRadius", "builtins.float"], ["padAngle", "builtins.float"], ["radius", "builtins.float"], ["radius2", "builtins.float"], ["radius2Offset", "builtins.float"], ["radiusOffset", "builtins.float"], ["shape", "builtins.str"], ["size", "builtins.float"], ["smooth", "builtins.bool"], ["startAngle", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOffset", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"], ["style", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["tension", "builtins.float"], ["text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["theta", "builtins.float"], ["theta2", "builtins.float"], ["theta2Offset", "builtins.float"], ["thetaOffset", "builtins.float"], ["timeUnitBandPosition", "builtins.float"], ["timeUnitBandSize", "builtins.float"], ["tooltip", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TooltipContentKwds"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["url", "builtins.str"], ["width", "builtins.float"], ["x", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["x2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["x2Offset", "builtins.float"], ["xOffset", "builtins.float"], ["y", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], ["y2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], ["y2Offset", "builtins.float"], ["yOffset", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "PaddingKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.PaddingKwds", "kind": "Gdef"}, "PointKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.PointKwds", "name": "PointKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.PointKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.PointKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["coordinates", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "Point"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "PointSelectionConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.PointSelectionConfigKwds", "name": "PointSelectionConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.PointSelectionConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.PointSelectionConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "point"}], ["clear", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["encodings", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SingleDefUnitChannel_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["fields", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["nearest", "builtins.bool"], ["on", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["resolve", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SelectionResolution_T"}], ["toggle", {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "PointSelectionConfigWithoutTypeKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.PointSelectionConfigWithoutTypeKwds", "name": "PointSelectionConfigWithoutTypeKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.PointSelectionConfigWithoutTypeKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.PointSelectionConfigWithoutTypeKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["clear", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["encodings", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SingleDefUnitChannel_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["fields", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["nearest", "builtins.bool"], ["on", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds"}], "uses_pep604_syntax": true}], ["resolve", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SelectionResolution_T"}], ["toggle", {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "PolygonKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.PolygonKwds", "name": "PolygonKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.PolygonKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.PolygonKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["coordinates", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "Polygon"}], ["bbox", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "PrimitiveValue_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.PrimitiveValue_T", "kind": "Gdef", "module_public": false}, "ProjectionConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ProjectionConfigKwds", "name": "ProjectionConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ProjectionConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ProjectionConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["center", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["clipAngle", "builtins.float"], ["clipExtent", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["coefficient", "builtins.float"], ["distance", "builtins.float"], ["extent", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["fit", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureCollectionKwds"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureCollectionKwds"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["fraction", "builtins.float"], ["lobes", "builtins.float"], ["parallel", "builtins.float"], ["parallels", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["pointRadius", "builtins.float"], ["precision", "builtins.float"], ["radius", "builtins.float"], ["ratio", "builtins.float"], ["reflectX", "builtins.bool"], ["reflectY", "builtins.bool"], ["rotate", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["scale", "builtins.float"], ["size", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["spacing", "builtins.float"], ["tilt", "builtins.float"], ["translate", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ProjectionType_T"}]], "readonly_keys": [], "required_keys": []}}}, "ProjectionKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ProjectionKwds", "name": "ProjectionKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ProjectionKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ProjectionKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["center", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["clipAngle", "builtins.float"], ["clipExtent", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["coefficient", "builtins.float"], ["distance", "builtins.float"], ["extent", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["fit", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureCollectionKwds"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureCollectionKwds"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["fraction", "builtins.float"], ["lobes", "builtins.float"], ["parallel", "builtins.float"], ["parallels", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["pointRadius", "builtins.float"], ["precision", "builtins.float"], ["radius", "builtins.float"], ["ratio", "builtins.float"], ["reflectX", "builtins.bool"], ["reflectY", "builtins.bool"], ["rotate", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["scale", "builtins.float"], ["size", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["spacing", "builtins.float"], ["tilt", "builtins.float"], ["translate", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["type", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ProjectionType_T"}]], "readonly_keys": [], "required_keys": []}}}, "ProjectionType_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ProjectionType_T", "kind": "Gdef", "module_public": false}, "RadialGradientKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.RadialGradientKwds", "name": "RadialGradientKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.RadialGradientKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.RadialGradientKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["gradient", {".class": "LiteralType", "fallback": "builtins.str", "value": "radial"}], ["stops", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.GradientStopKwds"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["id", "builtins.str"], ["r1", "builtins.float"], ["r2", "builtins.float"], ["x1", "builtins.float"], ["x2", "builtins.float"], ["y1", "builtins.float"], ["y2", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "RangeConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.RangeConfigKwds", "name": "RangeConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.RangeConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.RangeConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["category", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.RangeEnum_T"}], "uses_pep604_syntax": true}], ["diverging", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.RangeEnum_T"}], "uses_pep604_syntax": true}], ["heatmap", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.RangeEnum_T"}], "uses_pep604_syntax": true}], ["ordinal", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.RangeEnum_T"}], "uses_pep604_syntax": true}], ["ramp", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.RangeEnum_T"}], "uses_pep604_syntax": true}], ["symbol", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "RangeEnum_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.RangeEnum_T", "kind": "Gdef", "module_public": false}, "RectConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.RectConfigKwds", "name": "RectConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.RectConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.RectConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["ariaRole", "builtins.str"], ["ariaRoleDescription", "builtins.str"], ["aspect", "builtins.bool"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["binSpacing", "builtins.float"], ["blend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Blend_T"}], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["continuousBandSize", "builtins.float"], ["cornerRadius", "builtins.float"], ["cornerRadiusBottomLeft", "builtins.float"], ["cornerRadiusBottomRight", "builtins.float"], ["cornerRadiusTopLeft", "builtins.float"], ["cornerRadiusTopRight", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["description", "builtins.str"], ["dir", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextDirection_T"}], ["discreteBandSize", "builtins.float"], ["dx", "builtins.float"], ["dy", "builtins.float"], ["ellipsis", "builtins.str"], ["endAngle", "builtins.float"], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["filled", "builtins.bool"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["height", "builtins.float"], ["href", "builtins.str"], ["innerRadius", "builtins.float"], ["interpolate", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Interpolate_T"}], ["invalid", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["lineBreak", "builtins.str"], ["lineHeight", "builtins.float"], ["minBandSize", "builtins.float"], ["opacity", "builtins.float"], ["order", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["outerRadius", "builtins.float"], ["padAngle", "builtins.float"], ["radius", "builtins.float"], ["radius2", "builtins.float"], ["shape", "builtins.str"], ["size", "builtins.float"], ["smooth", "builtins.bool"], ["startAngle", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOffset", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"], ["tension", "builtins.float"], ["text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["theta", "builtins.float"], ["theta2", "builtins.float"], ["timeUnitBandPosition", "builtins.float"], ["timeUnitBandSize", "builtins.float"], ["tooltip", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TooltipContentKwds"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["url", "builtins.str"], ["width", "builtins.float"], ["x", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["x2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["y", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], ["y2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "ResolveKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ResolveKwds", "name": "ResolveKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ResolveKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ResolveKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["axis", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AxisResolveMapKwds"}], ["legend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LegendResolveMapKwds"}], ["scale", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ScaleResolveMapKwds"}]], "readonly_keys": [], "required_keys": []}}}, "ResolveMode_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T", "kind": "Gdef", "module_public": false}, "RowColKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.RowColKwds", "kind": "Gdef"}, "ScaleConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ScaleConfigKwds", "name": "ScaleConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ScaleConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ScaleConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["bandPaddingInner", "builtins.float"], ["bandPaddingOuter", "builtins.float"], ["bandWithNestedOffsetPaddingInner", "builtins.float"], ["bandWithNestedOffsetPaddingOuter", "builtins.float"], ["barBandPaddingInner", "builtins.float"], ["clamp", "builtins.bool"], ["continuousPadding", "builtins.float"], ["invalid", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ScaleInvalidDataConfigKwds"}], ["maxBandSize", "builtins.float"], ["maxFontSize", "builtins.float"], ["maxOpacity", "builtins.float"], ["maxSize", "builtins.float"], ["maxStrokeWidth", "builtins.float"], ["minBandSize", "builtins.float"], ["minFontSize", "builtins.float"], ["minOpacity", "builtins.float"], ["minSize", "builtins.float"], ["minStrokeWidth", "builtins.float"], ["offsetBandPaddingInner", "builtins.float"], ["offsetBandPaddingOuter", "builtins.float"], ["pointPadding", "builtins.float"], ["quantileCount", "builtins.float"], ["quantizeCount", "builtins.float"], ["rectBandPaddingInner", "builtins.float"], ["round", "builtins.bool"], ["tickBandPaddingInner", "builtins.float"], ["useUnaggregatedDomain", "builtins.bool"], ["xReverse", "builtins.bool"], ["zero", "builtins.bool"]], "readonly_keys": [], "required_keys": []}}}, "ScaleInterpolateEnum_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ScaleInterpolateEnum_T", "kind": "Gdef", "module_public": false}, "ScaleInvalidDataConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ScaleInvalidDataConfigKwds", "name": "ScaleInvalidDataConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ScaleInvalidDataConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ScaleInvalidDataConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["angle", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["color", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], "type_ref": "altair.vegalite.v5.schema._typing.Value"}], "uses_pep604_syntax": true}], ["fill", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "type_ref": "altair.vegalite.v5.schema._typing.Value"}], "uses_pep604_syntax": true}], ["fillOpacity", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["opacity", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["radius", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["shape", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.str"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["size", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["stroke", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "type_ref": "altair.vegalite.v5.schema._typing.Value"}], "uses_pep604_syntax": true}], ["strokeDash", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "type_ref": "altair.vegalite.v5.schema._typing.Value"}], "uses_pep604_syntax": true}], ["strokeOpacity", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["strokeWidth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["theta", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["x", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], "type_ref": "altair.vegalite.v5.schema._typing.Value"}], "uses_pep604_syntax": true}], ["xOffset", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}], ["y", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], "type_ref": "altair.vegalite.v5.schema._typing.Value"}], "uses_pep604_syntax": true}], ["yOffset", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.Value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero-or-min"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "ScaleResolveMapKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ScaleResolveMapKwds", "name": "ScaleResolveMapKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ScaleResolveMapKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ScaleResolveMapKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["angle", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["color", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["fill", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["fillOpacity", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["opacity", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["radius", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["shape", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["size", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["stroke", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["strokeDash", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["strokeOpacity", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["strokeWidth", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["theta", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["x", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["xOffset", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["y", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}], ["yOffset", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ResolveMode_T"}]], "readonly_keys": [], "required_keys": []}}}, "ScaleType_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.ScaleType_T", "kind": "Gdef", "module_public": false}, "SelectionConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.SelectionConfigKwds", "name": "SelectionConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.SelectionConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.SelectionConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["interval", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.IntervalSelectionConfigWithoutTypeKwds"}], ["point", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.PointSelectionConfigWithoutTypeKwds"}]], "readonly_keys": [], "required_keys": []}}}, "SelectionResolution_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.SelectionResolution_T", "kind": "Gdef", "module_public": false}, "SelectionType_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.SelectionType_T", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SingleDefUnitChannel_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.SingleDefUnitChannel_T", "kind": "Gdef", "module_public": false}, "SingleTimeUnit_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.SingleTimeUnit_T", "kind": "Gdef", "module_public": false}, "SortByChannel_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.SortByChannel_T", "kind": "Gdef", "module_public": false}, "SortOrder_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.SortOrder_T", "kind": "Gdef", "module_public": false}, "StackOffset_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.StackOffset_T", "kind": "Gdef", "module_public": false}, "StandardType_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.StandardType_T", "kind": "Gdef", "module_public": false}, "StepFor_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.StepFor_T", "kind": "Gdef", "module_public": false}, "StepKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.StepKwds", "name": "StepKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.StepKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.StepKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["step", "builtins.float"], ["__extra_items__", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StepFor_T"}]], "readonly_keys": [], "required_keys": []}}}, "StrokeCap_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T", "kind": "Gdef", "module_public": false}, "StrokeJoin_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T", "kind": "Gdef", "module_public": false}, "StyleConfigIndexKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.StyleConfigIndexKwds", "name": "StyleConfigIndexKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.StyleConfigIndexKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.StyleConfigIndexKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["arc", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}], ["area", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds"}], ["bar", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BarConfigKwds"}], ["circle", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["geoshape", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["image", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}], ["line", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}], ["mark", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["point", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["rect", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RectConfigKwds"}], ["rule", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["square", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["text", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}], ["tick", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TickConfigKwds"}], ["trail", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LineConfigKwds"}], ["__extra_items__", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds"}]], "readonly_keys": [], "required_keys": []}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Temporal": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Temporal", "kind": "Gdef", "module_public": false}, "TextBaseline_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T", "kind": "Gdef", "module_public": false}, "TextDirection_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.TextDirection_T", "kind": "Gdef", "module_public": false}, "ThemeConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ThemeConfig", "name": "ThemeConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ThemeConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ThemeConfig", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.LayoutAlign_T"}], "type_ref": "altair.vegalite.v5.schema._typing.RowColKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.LayoutAlign_T"}], "uses_pep604_syntax": true}], ["autosize", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.AutoSizeParamsKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.AutosizeType_T"}], "uses_pep604_syntax": true}], ["background", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["bounds", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "full"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "flush"}], "uses_pep604_syntax": false}], ["center", {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "altair.vegalite.v5.schema._typing.RowColKwds"}], "uses_pep604_syntax": true}], ["config", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ConfigKwds"}], ["description", "builtins.str"], ["height", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.StepKwds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "container"}], "uses_pep604_syntax": true}], ["name", "builtins.str"], ["padding", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.PaddingKwds"}], "uses_pep604_syntax": true}], ["params", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.VariableParameterKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TopLevelSelectionParameterKwds"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["projection", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ProjectionKwds"}], ["resolve", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ResolveKwds"}], ["spacing", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "altair.vegalite.v5.schema._typing.RowColKwds"}], "uses_pep604_syntax": true}], ["title", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TitleParamsKwds"}], "uses_pep604_syntax": true}], ["usermeta", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Map"}], ["view", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ViewBackgroundKwds"}], ["width", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.StepKwds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "container"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "TickConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.TickConfigKwds", "name": "TickConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.TickConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.TickConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["ariaRole", "builtins.str"], ["ariaRoleDescription", "builtins.str"], ["aspect", "builtins.bool"], ["bandSize", "builtins.float"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["blend", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Blend_T"}], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}], "uses_pep604_syntax": true}], ["cornerRadius", "builtins.float"], ["cornerRadiusBottomLeft", "builtins.float"], ["cornerRadiusBottomRight", "builtins.float"], ["cornerRadiusTopLeft", "builtins.float"], ["cornerRadiusTopRight", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["description", "builtins.str"], ["dir", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextDirection_T"}], ["dx", "builtins.float"], ["dy", "builtins.float"], ["ellipsis", "builtins.str"], ["endAngle", "builtins.float"], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["filled", "builtins.bool"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["height", "builtins.float"], ["href", "builtins.str"], ["innerRadius", "builtins.float"], ["interpolate", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Interpolate_T"}], ["invalid", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.MarkInvalidDataMode_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["lineBreak", "builtins.str"], ["lineHeight", "builtins.float"], ["opacity", "builtins.float"], ["order", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Orientation_T"}], ["outerRadius", "builtins.float"], ["padAngle", "builtins.float"], ["radius", "builtins.float"], ["radius2", "builtins.float"], ["shape", "builtins.str"], ["size", "builtins.float"], ["smooth", "builtins.bool"], ["startAngle", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOffset", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"], ["tension", "builtins.float"], ["text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["theta", "builtins.float"], ["theta2", "builtins.float"], ["thickness", "builtins.float"], ["timeUnitBandPosition", "builtins.float"], ["timeUnitBandSize", "builtins.float"], ["tooltip", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.TooltipContentKwds"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["url", "builtins.str"], ["width", "builtins.float"], ["x", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["x2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}], "uses_pep604_syntax": true}], ["y", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}], ["y2", {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "TimeIntervalStepKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.TimeIntervalStepKwds", "name": "TimeIntervalStepKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.TimeIntervalStepKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.TimeIntervalStepKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["interval", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TimeInterval_T"}], ["step", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "TimeInterval_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.TimeInterval_T", "kind": "Gdef", "module_public": false}, "TimeLocaleKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.TimeLocaleKwds", "name": "TimeLocaleKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.TimeLocaleKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.TimeLocaleKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["date", "builtins.str"], ["dateTime", "builtins.str"], ["days", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["months", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["periods", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["shortDays", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["shortMonths", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["time", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "TitleAnchor_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.TitleAnchor_T", "kind": "Gdef", "module_public": false}, "TitleConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.TitleConfigKwds", "name": "TitleConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.TitleConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.TitleConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["anchor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleAnchor_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["dx", "builtins.float"], ["dy", "builtins.float"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["frame", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleFrame_T"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["lineHeight", "builtins.float"], ["offset", "builtins.float"], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleOrient_T"}], ["subtitleColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["subtitleFont", "builtins.str"], ["subtitleFontSize", "builtins.float"], ["subtitleFontStyle", "builtins.str"], ["subtitleFontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["subtitleLineHeight", "builtins.float"], ["subtitlePadding", "builtins.float"], ["zindex", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "TitleFrame_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.TitleFrame_T", "kind": "Gdef", "module_public": false}, "TitleOrient_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.TitleOrient_T", "kind": "Gdef", "module_public": false}, "TitleParamsKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.TitleParamsKwds", "name": "TitleParamsKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.TitleParamsKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.TitleParamsKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["align", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Align_T"}], ["anchor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleAnchor_T"}], ["angle", "builtins.float"], ["aria", "builtins.bool"], ["baseline", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TextBaseline_T"}], ["color", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["dx", "builtins.float"], ["dy", "builtins.float"], ["font", "builtins.str"], ["fontSize", "builtins.float"], ["fontStyle", "builtins.str"], ["fontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["frame", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleFrame_T"}], "uses_pep604_syntax": true}], ["limit", "builtins.float"], ["lineHeight", "builtins.float"], ["offset", "builtins.float"], ["orient", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.TitleOrient_T"}], ["style", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["subtitle", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], ["subtitleColor", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["subtitleFont", "builtins.str"], ["subtitleFontSize", "builtins.float"], ["subtitleFontStyle", "builtins.str"], ["subtitleFontWeight", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.FontWeight_T"}], ["subtitleLineHeight", "builtins.float"], ["subtitlePadding", "builtins.float"], ["zindex", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "TooltipContentKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.TooltipContentKwds", "name": "TooltipContentKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.TooltipContentKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.TooltipContentKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["content", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "encoding"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}}}, "TopLevelSelectionParameterKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.TopLevelSelectionParameterKwds", "name": "TopLevelSelectionParameterKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.TopLevelSelectionParameterKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.TopLevelSelectionParameterKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["select", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.PointSelectionConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.IntervalSelectionConfigKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.SelectionType_T"}], "uses_pep604_syntax": true}], ["bind", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindInputKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindRangeKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindDirectKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindCheckboxKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindRadioSelectKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.LegendStreamBindingKwds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "legend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scales"}], "uses_pep604_syntax": true}], ["value", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.DateTimeKwds"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Map"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.PrimitiveValue_T"}], "uses_pep604_syntax": true}], ["views", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": []}}}, "TypeForShape_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.TypeForShape_T", "kind": "Gdef", "module_public": false}, "Type_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Type_T", "kind": "Gdef", "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "Value": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.Value", "kind": "Gdef", "module_public": false}, "VariableParameterKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.VariableParameterKwds", "name": "VariableParameterKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.VariableParameterKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.VariableParameterKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["bind", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindInputKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindRangeKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindDirectKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindCheckboxKwds"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.BindRadioSelectKwds"}], "uses_pep604_syntax": true}], ["expr", "builtins.str"], ["react", "builtins.bool"], ["value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]], "readonly_keys": [], "required_keys": []}}}, "VegaThemes": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.VegaThemes", "kind": "Gdef", "module_public": false}, "ViewBackgroundKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ViewBackgroundKwds", "name": "ViewBackgroundKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ViewBackgroundKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ViewBackgroundKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["cornerRadius", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["opacity", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"], ["style", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "ViewConfigKwds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "altair.vegalite.v5.schema._config.ViewConfigKwds", "name": "ViewConfigKwds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "altair.vegalite.v5.schema._config.ViewConfigKwds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "altair.vegalite.v5.schema._config", "mro": ["altair.vegalite.v5.schema._config.ViewConfigKwds", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["clip", "builtins.bool"], ["continuousHeight", "builtins.float"], ["continuousWidth", "builtins.float"], ["cornerRadius", "builtins.float"], ["cursor", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.Cursor_T"}], ["discreteHeight", "builtins.float"], ["discreteWidth", "builtins.float"], ["fill", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["fillOpacity", "builtins.float"], ["opacity", "builtins.float"], ["step", "builtins.float"], ["stroke", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.ColorName_T"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strokeCap", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeCap_T"}], ["strokeDash", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["strokeDashOffset", "builtins.float"], ["<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.StrokeJoin_T"}], ["strokeMiterLimit", "builtins.float"], ["strokeOpacity", "builtins.float"], ["strokeWidth", "builtins.float"]], "readonly_keys": [], "required_keys": []}}}, "WindowOnlyOp_T": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.WindowOnlyOp_T", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.vegalite.v5.schema._config.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._config.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.vegalite.v5.schema._config.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "is_color_hex": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.is_color_hex", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\vegalite\\v5\\schema\\_config.py"}