{".class": "MypyFile", "_fullname": "streamlit.proto.LabelVisibilityMessage_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.LabelVisibilityMessage_pb2.google", "source_any": null, "type_of_any": 3}}}, "LabelVisibilityMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage", "name": "LabelVisibilityMessage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.LabelVisibilityMessage_pb2", "mro": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage", "builtins.object"], "names": {".class": "SymbolTable", "COLLAPSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.COLLAPSED", "name": "COLLAPSED", "type": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of LabelVisibilityMessage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.LabelVisibilityMessage_pb2.google", "source_any": null, "type_of_any": 3}}}, "HIDDEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.HIDDEN", "name": "HIDDEN", "type": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"}}, "LabelVisibilityOptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.LabelVisibilityOptions", "name": "LabelVisibilityOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.LabelVisibilityOptions", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.LabelVisibilityMessage_pb2", "mro": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.LabelVisibilityOptions", "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.LabelVisibilityOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.LabelVisibilityOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.VALUE_FIELD_NUMBER", "name": "VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "VISIBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.VISIBLE", "name": "VISIBLE", "type": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"}}, "_LabelVisibilityOptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions", "name": "_LabelVisibilityOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.LabelVisibilityMessage_pb2", "mro": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.V", "line": 40, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.LabelVisibilityMessage_pb2", "mro": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LabelVisibilityOptionsEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper", "name": "_LabelVisibilityOptionsEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.LabelVisibilityMessage_pb2", "mro": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "COLLAPSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper.COLLAPSED", "name": "COLLAPSED", "type": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.LabelVisibilityMessage_pb2.google", "source_any": null, "type_of_any": 3}}}, "HIDDEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper.HIDDEN", "name": "HIDDEN", "type": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"}}, "VISIBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper.VISIBLE", "name": "VISIBLE", "type": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptionsEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "value"], "arg_types": ["streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage", "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LabelVisibilityMessage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.value", "name": "value", "type": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage._LabelVisibilityOptions.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___LabelVisibilityMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.global___LabelVisibilityMessage", "line": 68, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.LabelVisibilityMessage_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.LabelVisibilityMessage_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\LabelVisibilityMessage_pb2.pyi"}