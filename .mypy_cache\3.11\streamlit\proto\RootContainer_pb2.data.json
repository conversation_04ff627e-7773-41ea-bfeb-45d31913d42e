{".class": "MypyFile", "_fullname": "streamlit.proto.RootContainer_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BOTTOM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.BOTTOM", "name": "BOTTOM", "type": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.RootContainer_pb2.google", "source_any": null, "type_of_any": 3}}}, "EVENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.EVENT", "name": "EVENT", "type": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}, "MAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.MAIN", "name": "MAIN", "type": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}, "RootContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.RootContainer_pb2._RootContainer"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.RootContainer_pb2.RootContainer", "name": "RootContainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.RootContainer_pb2.RootContainer", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.RootContainer_pb2", "mro": ["streamlit.proto.RootContainer_pb2.RootContainer", "streamlit.proto.RootContainer_pb2._RootContainer", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.RootContainer_pb2.RootContainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.RootContainer_pb2.RootContainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SIDEBAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.SIDEBAR", "name": "SIDEBAR", "type": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}, "_RootContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.RootContainer_pb2._RootContainer", "name": "_RootContainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.RootContainer_pb2._RootContainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.RootContainer_pb2", "mro": ["streamlit.proto.RootContainer_pb2._RootContainer", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "streamlit.proto.RootContainer_pb2._RootContainer.V", "line": 35, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.RootContainer_pb2", "mro": ["streamlit.proto.RootContainer_pb2._RootContainer.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.RootContainer_pb2._RootContainer.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.RootContainer_pb2._RootContainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.RootContainer_pb2._RootContainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RootContainerEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper", "name": "_RootContainerEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.RootContainer_pb2", "mro": ["streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "BOTTOM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper.BOTTOM", "name": "BOTTOM", "type": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.RootContainer_pb2.google", "source_any": null, "type_of_any": 3}}}, "EVENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper.EVENT", "name": "EVENT", "type": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}, "MAIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper.MAIN", "name": "MAIN", "type": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}, "SIDEBAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper.SIDEBAR", "name": "SIDEBAR", "type": "streamlit.proto.RootContainer_pb2._RootContainer.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.RootContainer_pb2._RootContainerEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.RootContainer_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___RootContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.RootContainer_pb2.global___RootContainer", "line": 56, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.RootContainer_pb2.RootContainer"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.RootContainer_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.RootContainer_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\RootContainer_pb2.pyi"}