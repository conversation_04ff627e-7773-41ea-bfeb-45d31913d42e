{"data_mtime": 1755649449, "dep_lines": [40, 40, 38, 39, 39, 39, 39, 40, 38, 39, 34, 35, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 20, 20, 20, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 20, 20], "dependencies": ["torch.onnx._internal.jit_utils", "torch.onnx._internal.registration", "torch._<PERSON>._onnx", "torch.onnx._type_utils", "torch.onnx.errors", "torch.onnx.symbolic_helper", "torch.onnx.symbolic_opset9", "torch.onnx._internal", "torch._C", "torch.onnx", "functools", "warnings", "torch", "builtins", "collections", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "sys", "json", "traceback", "re", "html", "typing", "_frozen_importlib", "abc", "contextlib"], "hash": "0302bd9640090867fe10604ca6596b055be8bed3", "id": "torch.onnx.symbolic_opset8", "ignore_all": true, "interface_hash": "c2dab1b6b0d99dd33543b5ea6f990523d24d0111", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\symbolic_opset8.py", "plugin_data": null, "size": 15455, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}