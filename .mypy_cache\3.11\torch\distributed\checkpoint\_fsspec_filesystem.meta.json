{"data_mtime": 1755649449, "dep_lines": [13, 14, 6, 4, 5, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 11, 23], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 5, 25], "dependencies": ["torch.distributed.checkpoint._extension", "torch.distributed.checkpoint.filesystem", "collections.abc", "io", "os", "contextlib", "pathlib", "typing", "builtins", "torch", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "torch.distributed", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "copy", "_frozen_importlib", "_io", "abc", "enum", "torch.distributed.checkpoint.staging", "torch.distributed.checkpoint.storage"], "hash": "252f189b4f28cd09803efa5c750fbf72d3d997d3", "id": "torch.distributed.checkpoint._fsspec_filesystem", "ignore_all": true, "interface_hash": "3d69586dd6e451abca519e75d5aca7f59a99ae4c", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_fsspec_filesystem.py", "plugin_data": null, "size": 5750, "suppressed": ["fsspec.core", "fsspec"], "version_id": "1.15.0"}