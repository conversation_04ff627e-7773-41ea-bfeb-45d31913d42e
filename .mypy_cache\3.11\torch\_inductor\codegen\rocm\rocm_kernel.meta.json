{"data_mtime": 1755649448, "dep_lines": [14, 15, 16, 20, 7, 12, 13, 6, 8, 10, 11, 3, 6, 2, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 10, 5, 5, 5, 5, 20, 10, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch._inductor.codegen.rocm.rocm_benchmark_request", "torch._inductor.codegen.rocm.rocm_template_buffer", "torch._inductor.codegen.rocm.rocm_utils", "torch._inductor.codegen.rocm.rocm_template", "torch._inductor.codegen.cpp_wrapper_cpu", "torch._inductor.codegen.common", "torch._inductor.codegen.cpp_utils", "torch._inductor.config", "torch._inductor.utils", "torch._inductor.ir", "torch._inductor.virtualized", "collections.abc", "torch._inductor", "logging", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "enum", "torch._C", "torch._inductor.autotune_process", "torch._inductor.codegen.wrapper", "torch._inductor.graph", "torch._inductor.ops_handler", "torch._inductor.sizevars", "torch.fx", "torch.fx.interpreter", "torch.utils", "torch.utils._sympy", "torch.utils._sympy.printers"], "hash": "cc8e9cf9e1eadf49b15ef233cd39a3b1845d38ff", "id": "torch._inductor.codegen.rocm.rocm_kernel", "ignore_all": true, "interface_hash": "53efb81428eb6450a1abecf8d6864ec75e19f8e0", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\rocm\\rocm_kernel.py", "plugin_data": null, "size": 10662, "suppressed": [], "version_id": "1.15.0"}