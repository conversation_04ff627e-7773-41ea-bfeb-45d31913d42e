{".class": "MypyFile", "_fullname": "torch.distributed.nn", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Function": {".class": "SymbolTableNode", "cross_ref": "torch.autograd.function.Function", "kind": "Gdef"}, "ReduceOp": {".class": "SymbolTableNode", "cross_ref": "torch._C._distributed_c10d.ReduceOp", "kind": "Gdef"}, "RemoteModule": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.api.remote_module.RemoteModule", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "all_gather": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.all_gather", "kind": "Gdef"}, "all_reduce": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.all_reduce", "kind": "Gdef"}, "all_to_all": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.all_to_all", "kind": "Gdef"}, "all_to_all_single": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.all_to_all_single", "kind": "Gdef"}, "broadcast": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.broadcast", "kind": "Gdef"}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "gather": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.gather", "kind": "Gdef"}, "group": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.distributed_c10d.group", "kind": "Gdef"}, "reduce": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.reduce", "kind": "Gdef"}, "reduce_scatter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.reduce_scatter", "kind": "Gdef"}, "scatter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.functional.scatter", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\nn\\__init__.py"}