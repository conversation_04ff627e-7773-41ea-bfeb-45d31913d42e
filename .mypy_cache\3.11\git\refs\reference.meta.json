{"data_mtime": 1755656862, "dep_lines": [8, 6, 14, 17, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.refs.symbolic", "git.util", "git.types", "git.repo", "typing", "builtins", "_frozen_importlib", "abc", "git.diff", "git.objects", "git.objects.base", "git.objects.blob", "git.objects.commit", "git.objects.tag", "git.objects.tree", "git.objects.util", "git.repo.base", "os", "typing_extensions"], "hash": "b78c4e9317cd22a0148b66b1aa5083c7805c6bf0", "id": "git.refs.reference", "ignore_all": true, "interface_hash": "14f513eef3f041691a44a6a8665c9c2586660fdc", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\refs\\reference.py", "plugin_data": null, "size": 5630, "suppressed": [], "version_id": "1.15.0"}