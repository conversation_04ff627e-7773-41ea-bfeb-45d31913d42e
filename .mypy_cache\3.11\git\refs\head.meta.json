{"data_mtime": 1755656862, "dep_lines": [15, 16, 174, 11, 12, 13, 22, 25, 26, 27, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 5, 5, 5, 5, 25, 25, 25, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.refs.reference", "git.refs.symbolic", "git.refs.remote", "git.config", "git.exc", "git.util", "git.types", "git.objects", "git.refs", "git.repo", "typing", "builtins", "_frozen_importlib", "abc", "configparser", "git.cmd", "git.diff", "git.objects.base", "git.objects.commit", "git.objects.tag", "git.objects.util", "git.repo.base", "os"], "hash": "054110c6026d7c31c0cd91b5f32247947e966bf4", "id": "git.refs.head", "ignore_all": true, "interface_hash": "39efa56b3ddc74c8047f1ff762e599e2ab4635ad", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\refs\\head.py", "plugin_data": null, "size": 10513, "suppressed": [], "version_id": "1.15.0"}