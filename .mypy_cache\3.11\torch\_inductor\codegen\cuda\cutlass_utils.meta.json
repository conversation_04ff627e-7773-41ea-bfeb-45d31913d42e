{"data_mtime": 1755649448, "dep_lines": [23, 20, 22, 16, 18, 19, 21, 470, 18, 468, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 468, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 13], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 20, 20, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 10], "dependencies": ["torch._inductor.codegen.cuda.cuda_env", "torch._inductor.runtime.runtime_utils", "torch._inductor.codegen.cpp_utils", "torch._inductor.utils", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.virtualized", "torch._inductor.codecache", "torch._inductor", "unittest.mock", "atexit", "functools", "logging", "os", "shutil", "sys", "time", "dataclasses", "pathlib", "typing", "torch", "unittest", "builtins", "inspect", "html", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C"], "hash": "c7bf5ddc71ccc39dfba2045eb56314860856dd8b", "id": "torch._inductor.codegen.cuda.cutlass_utils", "ignore_all": true, "interface_hash": "aa3f74035833777e00b07dfb6e465fc283cfc9f1", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cutlass_utils.py", "plugin_data": null, "size": 17539, "suppressed": ["sympy"], "version_id": "1.15.0"}