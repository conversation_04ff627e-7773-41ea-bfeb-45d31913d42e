{"data_mtime": 1755649448, "dep_lines": [12, 10, 13, 15, 16, 21, 6, 11, 15, 2, 3, 4, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 26, 26, 26, 26], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 20, 5, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 20, 20, 20], "dependencies": ["torch.fx.passes.graph_transform_observer", "torch._dynamo.utils", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.pattern_matcher", "torch._inductor.utils", "collections.abc", "torch._logging", "torch._inductor", "collections", "logging", "operator", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._C._VariableFunctions", "torch._ops", "torch._tensor", "torch.fx", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.node", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils"], "hash": "98abd3dd0f46ad5f753db75c270395875dae9057", "id": "torch._inductor.fx_passes.group_batch_fusion", "ignore_all": true, "interface_hash": "0e029dadc643c80827ecabdef35e14acd21af8d4", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\group_batch_fusion.py", "plugin_data": null, "size": 60133, "suppressed": ["deeplearning.fbgemm.fbgemm_gpu.fb.inductor_lowerings", "deeplearning.fbgemm.fbgemm_gpu.fb", "deeplearning.fbgemm.fbgemm_gpu", "deeplearning.fbgemm", "deeplearning"], "version_id": "1.15.0"}