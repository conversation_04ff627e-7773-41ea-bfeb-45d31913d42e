{".class": "MypyFile", "_fullname": "torch.distributed.fsdp._runtime_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackwardPrefetch": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.BackwardPrefetch", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "FlatParamHandle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParamHandle", "kind": "Gdef"}, "FlatParameter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParameter", "kind": "Gdef"}, "HOMOGENEOUS_ATTR_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._runtime_utils.HOMOGENEOUS_ATTR_NAMES", "name": "HOMOGENEOUS_ATTR_NAMES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "HYBRID_SHARDING_STRATEGIES": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._init_utils.HYBRID_SHARDING_STRATEGIES", "kind": "Gdef"}, "HandleShardingStrategy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.HandleShardingStrategy", "kind": "Gdef"}, "HandleTrainingState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.HandleTrainingState", "kind": "Gdef"}, "LOW_PRECISION_HOOKS": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms._comm_hooks.LOW_PRECISION_HOOKS", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RESHARD_AFTER_FORWARD_HANDLE_STRATEGIES": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.RESHARD_AFTER_FORWARD_HANDLE_STRATEGIES", "kind": "Gdef"}, "TrainingState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.TrainingState", "kind": "Gdef"}, "Variable": {".class": "SymbolTableNode", "cross_ref": "torch.autograd.variable.Variable", "kind": "Gdef"}, "_FSDPState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._FSDPState", "kind": "Gdef"}, "_PrefetchMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._runtime_utils._PrefetchMode", "name": "_PrefetchMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch.distributed.fsdp._runtime_utils._PrefetchMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch.distributed.fsdp._runtime_utils", "mro": ["torch.distributed.fsdp._runtime_utils._PrefetchMode", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BACKWARD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._runtime_utils._PrefetchMode.BACKWARD", "name": "BACKWARD", "type": "enum.auto"}}, "FORWARD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._runtime_utils._PrefetchMode.FORWARD", "name": "FORWARD", "type": "enum.auto"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._runtime_utils._PrefetchMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp._runtime_utils._PrefetchMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._runtime_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._runtime_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._runtime_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._runtime_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._runtime_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._runtime_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_accumulate_sharded_grad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "handle", "sharded_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._accumulate_sharded_grad", "name": "_accumulate_sharded_grad", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._accumulate_sharded_grad", "name": "_accumulate_sharded_grad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_apply_to_tensors": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._apply_to_tensors", "kind": "Gdef"}, "_assert_in_training_states": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._assert_in_training_states", "kind": "Gdef"}, "_cast_buffers_to_dtype_and_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["buffers", "buffer_dtypes", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._cast_buffers_to_dtype_and_device", "name": "_cast_buffers_to_dtype_and_device", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["buffers", "buffer_dtypes", "device"], "arg_types": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._C.device"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_buffers_to_dtype_and_device", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cast_forward_inputs": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._cast_forward_inputs", "kind": "Gdef"}, "_cast_grad_to_param_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "sharded_grad", "param"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._cast_grad_to_param_dtype", "name": "_cast_grad_to_param_dtype", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._cast_grad_to_param_dtype", "name": "_cast_grad_to_param_dtype", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_catch_all_reshard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._catch_all_reshard", "name": "_catch_all_reshard", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._catch_all_reshard", "name": "_catch_all_reshard", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_check_flat_params_on_expected_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._check_flat_params_on_expected_device", "name": "_check_flat_params_on_expected_device", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "module"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_flat_params_on_expected_device", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_grad_to_accumulate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["new_sharded_grad", "accumulated_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._check_grad_to_accumulate", "name": "_check_grad_to_accumulate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["new_sharded_grad", "accumulated_grad"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_grad_to_accumulate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_div_if_needed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tensor", "div_factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._div_if_needed", "name": "_div_if_needed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tensor", "div_factor"], "arg_types": ["torch._tensor.Tensor", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div_if_needed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_finalize_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._finalize_params", "name": "_finalize_params", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._finalize_params", "name": "_finalize_params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_get_buffers_and_dtypes_for_computation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "root_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._get_buffers_and_dtypes_for_computation", "name": "_get_buffers_and_dtypes_for_computation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._get_buffers_and_dtypes_for_computation", "name": "_get_buffers_and_dtypes_for_computation", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_get_fsdp_root_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._get_fsdp_root_states", "name": "_get_fsdp_root_states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_fsdp_root_states", "ret_type": {".class": "Instance", "args": ["torch.distributed.fsdp._common_utils._FSDPState"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_fsdp_root_states_with_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._get_fsdp_root_states_with_modules", "name": "_get_fsdp_root_states_with_modules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_fsdp_root_states_with_modules", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["torch.distributed.fsdp._common_utils._FSDPState"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_handle_to_prefetch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "current_handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._get_handle_to_prefetch", "name": "_get_handle_to_prefetch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._get_handle_to_prefetch", "name": "_get_handle_to_prefetch", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_get_module_fsdp_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._get_module_fsdp_state", "kind": "Gdef"}, "_get_orig_buffer_dtypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "buffer_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._get_orig_buffer_dtypes", "name": "_get_orig_buffer_dtypes", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._get_orig_buffer_dtypes", "name": "_get_orig_buffer_dtypes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_get_reduce_scatter_tensors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "unsharded_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._get_reduce_scatter_tensors", "name": "_get_reduce_scatter_tensors", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._get_reduce_scatter_tensors", "name": "_get_reduce_scatter_tensors", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_get_training_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._get_training_state", "name": "_get_training_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["handle"], "arg_types": ["torch.distributed.fsdp._flat_param.FlatParamHandle"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_training_state", "ret_type": "torch.distributed.fsdp._common_utils.HandleTrainingState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_streams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._init_streams", "name": "_init_streams", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._init_streams", "name": "_init_streams", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_is_composable": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._is_composable", "kind": "Gdef"}, "_is_fsdp_root": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._is_fsdp_root", "name": "_is_fsdp_root", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "module"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_fsdp_root", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lazy_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "root_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._lazy_init", "name": "_lazy_init", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._lazy_init", "name": "_lazy_init", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_log_post_backward_hook": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._log_post_backward_hook", "kind": "Gdef"}, "_low_precision_hook_enabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._low_precision_hook_enabled", "name": "_low_precision_hook_enabled", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._low_precision_hook_enabled", "name": "_low_precision_hook_enabled", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_no_dispatch_record_stream": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._no_dispatch_record_stream", "kind": "Gdef"}, "_offload_grad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "handle", "grad_to_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._offload_grad", "name": "_offload_grad", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._offload_grad", "name": "_offload_grad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_p_assert": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._p_assert", "kind": "Gdef"}, "_post_backward_final_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._post_backward_final_callback", "name": "_post_backward_final_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._post_backward_final_callback", "name": "_post_backward_final_callback", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_post_backward_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["state", "handle", "flat_param", "unused"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._post_backward_hook", "name": "_post_backward_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._post_backward_hook", "name": "_post_backward_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_post_backward_reshard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["state", "handle", "unused"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._post_backward_reshard", "name": "_post_backward_reshard", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["state", "handle", "unused"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_post_backward_reshard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_post_backward_reshard_only_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["state", "handle", "unused"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._post_backward_reshard_only_hook", "name": "_post_backward_reshard_only_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["state", "handle", "unused"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", "torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_post_backward_reshard_only_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_post_backward_use_sharded_grad_views": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._post_backward_use_sharded_grad_views", "name": "_post_backward_use_sharded_grad_views", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._post_backward_use_sharded_grad_views", "name": "_post_backward_use_sharded_grad_views", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_post_forward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["state", "handle", "reshard_fn", "module", "input", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._post_forward", "name": "_post_forward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._post_forward", "name": "_post_forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_post_forward_reshard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._post_forward_reshard", "name": "_post_forward_reshard", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._post_forward_reshard", "name": "_post_forward_reshard", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_post_reduce_grad_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "handle", "grad_to_offload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._post_reduce_grad_callback", "name": "_post_reduce_grad_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._post_reduce_grad_callback", "name": "_post_reduce_grad_callback", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_pre_backward_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 2], "arg_names": ["state", "module", "handle", "grad", "unused"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._pre_backward_hook", "name": "_pre_backward_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._pre_backward_hook", "name": "_pre_backward_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_pre_forward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["state", "handle", "unshard_fn", "module", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._pre_forward", "name": "_pre_forward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._pre_forward", "name": "_pre_forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_pre_forward_unshard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._pre_forward_unshard", "name": "_pre_forward_unshard", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._pre_forward_unshard", "name": "_pre_forward_unshard", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_prefetch_handle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "current_handle", "prefetch_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._prefetch_handle", "name": "_prefetch_handle", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._prefetch_handle", "name": "_prefetch_handle", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_reduce_grad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._reduce_grad", "name": "_reduce_grad", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._reduce_grad", "name": "_reduce_grad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_reduce_grad_no_shard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._reduce_grad_no_shard", "name": "_reduce_grad_no_shard", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._reduce_grad_no_shard", "name": "_reduce_grad_no_shard", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_register_post_backward_final_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._register_post_backward_final_callback", "name": "_register_post_backward_final_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._register_post_backward_final_callback", "name": "_register_post_backward_final_callback", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_register_post_backward_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._register_post_backward_hook", "name": "_register_post_backward_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "handle"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", {".class": "UnionType", "items": ["torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_post_backward_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register_post_backward_reshard_only_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "handle", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._register_post_backward_reshard_only_hook", "name": "_register_post_backward_reshard_only_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "handle", "args", "kwargs"], "arg_types": ["torch.distributed.fsdp._common_utils._FSDPState", {".class": "UnionType", "items": ["torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_post_backward_reshard_only_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register_post_forward_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._register_post_forward_hook", "name": "_register_post_forward_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._register_post_forward_hook", "name": "_register_post_forward_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_register_pre_backward_hooks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "module", "outputs", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._register_pre_backward_hooks", "name": "_register_pre_backward_hooks", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._register_pre_backward_hooks", "name": "_register_pre_backward_hooks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_register_pre_forward_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._register_pre_forward_hook", "name": "_register_pre_forward_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._register_pre_forward_hook", "name": "_register_pre_forward_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_register_root_pre_forward_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._register_root_pre_forward_hook", "name": "_register_root_pre_forward_hook", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._register_root_pre_forward_hook", "name": "_register_root_pre_forward_hook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_reset_flat_param_grad_info_if_needed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["handles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._reset_flat_param_grad_info_if_needed", "name": "_reset_flat_param_grad_info_if_needed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["handles"], "arg_types": [{".class": "Instance", "args": ["torch.distributed.fsdp._flat_param.FlatParamHandle"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reset_flat_param_grad_info_if_needed", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reshard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "handle", "free_unsharded_flat_param"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._reshard", "name": "_reshard", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._reshard", "name": "_reshard", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_reshard_grads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._reshard_grads", "name": "_reshard_grads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["handle"], "arg_types": [{".class": "UnionType", "items": ["torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reshard_grads", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_root_cast_forward_input": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "module", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._root_cast_forward_input", "name": "_root_cast_forward_input", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._root_cast_forward_input", "name": "_root_cast_forward_input", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_root_pre_forward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "module", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._root_pre_forward", "name": "_root_pre_forward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._root_pre_forward", "name": "_root_pre_forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_share_state_and_init_handle_attrs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["root_state", "root_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._share_state_and_init_handle_attrs", "name": "_share_state_and_init_handle_attrs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._share_state_and_init_handle_attrs", "name": "_share_state_and_init_handle_attrs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_should_free_in_backward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._should_free_in_backward", "name": "_should_free_in_backward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._should_free_in_backward", "name": "_should_free_in_backward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_to_kwargs": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._to_kwargs", "kind": "Gdef"}, "_unshard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "handle", "unshard_stream", "pre_unshard_stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._runtime_utils._unshard", "name": "_unshard", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._runtime_utils._unshard", "name": "_unshard", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_unshard_grads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._unshard_grads", "name": "_unshard_grads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["handle"], "arg_types": [{".class": "UnionType", "items": ["torch.distributed.fsdp._flat_param.FlatParamHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unshard_grads", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wait_for_computation_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["computation_stream", "unshard_stream", "pre_unshard_stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._runtime_utils._wait_for_computation_stream", "name": "_wait_for_computation_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["computation_stream", "unshard_stream", "pre_unshard_stream"], "arg_types": ["torch._C.Stream", "torch._C.Stream", "torch._C.Stream"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wait_for_computation_stream", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto": {".class": "SymbolTableNode", "cross_ref": "enum.auto", "kind": "Gdef"}, "clean_tensor_name": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.clean_tensor_name", "kind": "Gdef"}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._runtime_utils.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "no_type_check": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "register_multi_grad_hook": {".class": "SymbolTableNode", "cross_ref": "torch.autograd.graph.register_multi_grad_hook", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "traversal_utils": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._traversal_utils", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_runtime_utils.py"}