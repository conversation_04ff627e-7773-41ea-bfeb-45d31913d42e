{".class": "MypyFile", "_fullname": "altair.theme", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AreaConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.AreaConfigKwds", "kind": "Gdef"}, "AutoSizeParamsKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.AutoSizeParamsKwds", "kind": "Gdef"}, "AxisConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.AxisConfigKwds", "kind": "Gdef"}, "AxisResolveMapKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.AxisResolveMapKwds", "kind": "Gdef"}, "BarConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.BarConfigKwds", "kind": "Gdef"}, "BindCheckboxKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.BindCheckboxKwds", "kind": "Gdef"}, "BindDirectKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.BindDirectKwds", "kind": "Gdef"}, "BindInputKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.BindInputKwds", "kind": "Gdef"}, "BindRadioSelectKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.BindRadioSelectKwds", "kind": "Gdef"}, "BindRangeKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.BindRangeKwds", "kind": "Gdef"}, "BoxPlotConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.BoxPlotConfigKwds", "kind": "Gdef"}, "BrushConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.BrushConfigKwds", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CompositionConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.CompositionConfigKwds", "kind": "Gdef"}, "ConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ConfigKwds", "kind": "Gdef"}, "DateTimeKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.DateTimeKwds", "kind": "Gdef"}, "DerivedStreamKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.DerivedStreamKwds", "kind": "Gdef"}, "ErrorBandConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ErrorBandConfigKwds", "kind": "Gdef"}, "ErrorBarConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ErrorBarConfigKwds", "kind": "Gdef"}, "FeatureGeometryGeoJsonPropertiesKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.FeatureGeometryGeoJsonPropertiesKwds", "kind": "Gdef"}, "FormatConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.FormatConfigKwds", "kind": "Gdef"}, "GeoJsonFeatureCollectionKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureCollectionKwds", "kind": "Gdef"}, "GeoJsonFeatureKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.GeoJsonFeatureKwds", "kind": "Gdef"}, "GeometryCollectionKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.GeometryCollectionKwds", "kind": "Gdef"}, "GradientStopKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.GradientStopKwds", "kind": "Gdef"}, "HeaderConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.HeaderConfigKwds", "kind": "Gdef"}, "IntervalSelectionConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.IntervalSelectionConfigKwds", "kind": "Gdef"}, "IntervalSelectionConfigWithoutTypeKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.IntervalSelectionConfigWithoutTypeKwds", "kind": "Gdef"}, "LegendConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.LegendConfigKwds", "kind": "Gdef"}, "LegendResolveMapKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.LegendResolveMapKwds", "kind": "Gdef"}, "LegendStreamBindingKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.LegendStreamBindingKwds", "kind": "Gdef"}, "LineConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.LineConfigKwds", "kind": "Gdef"}, "LineStringKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.LineStringKwds", "kind": "Gdef"}, "LinearGradientKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.LinearGradientKwds", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "LiteralString": {".class": "SymbolTableNode", "cross_ref": "typing.LiteralString", "kind": "Gdef", "module_public": false}, "LocaleKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.LocaleKwds", "kind": "Gdef"}, "MarkConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.MarkConfigKwds", "kind": "Gdef"}, "MergedStreamKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.MergedStreamKwds", "kind": "Gdef"}, "MultiLineStringKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.MultiLineStringKwds", "kind": "Gdef"}, "MultiPointKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.MultiPointKwds", "kind": "Gdef"}, "MultiPolygonKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.MultiPolygonKwds", "kind": "Gdef"}, "NumberLocaleKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.NumberLocaleKwds", "kind": "Gdef"}, "OverlayMarkDefKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.OverlayMarkDefKwds", "kind": "Gdef"}, "P": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "altair.theme.P", "name": "P", "upper_bound": "builtins.object", "variance": 0}}, "PaddingKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.PaddingKwds", "kind": "Gdef"}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing.ParamSpec", "kind": "Gdef", "module_public": false}, "Plugin": {".class": "SymbolTableNode", "cross_ref": "altair.utils.plugin_registry.Plugin", "kind": "Gdef", "module_public": false}, "PointKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.PointKwds", "kind": "Gdef"}, "PointSelectionConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.PointSelectionConfigKwds", "kind": "Gdef"}, "PointSelectionConfigWithoutTypeKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.PointSelectionConfigWithoutTypeKwds", "kind": "Gdef"}, "PolygonKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.PolygonKwds", "kind": "Gdef"}, "ProjectionConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ProjectionConfigKwds", "kind": "Gdef"}, "ProjectionKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ProjectionKwds", "kind": "Gdef"}, "RadialGradientKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.RadialGradientKwds", "kind": "Gdef"}, "RangeConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.RangeConfigKwds", "kind": "Gdef"}, "RectConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.RectConfigKwds", "kind": "Gdef"}, "ResolveKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ResolveKwds", "kind": "Gdef"}, "RowColKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._typing.RowColKwds", "kind": "Gdef"}, "ScaleConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ScaleConfigKwds", "kind": "Gdef"}, "ScaleInvalidDataConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ScaleInvalidDataConfigKwds", "kind": "Gdef"}, "ScaleResolveMapKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ScaleResolveMapKwds", "kind": "Gdef"}, "SelectionConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.SelectionConfigKwds", "kind": "Gdef"}, "StepKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.StepKwds", "kind": "Gdef"}, "StyleConfigIndexKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.StyleConfigIndexKwds", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "ThemeConfig": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ThemeConfig", "kind": "Gdef"}, "TickConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.TickConfigKwds", "kind": "Gdef"}, "TimeIntervalStepKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.TimeIntervalStepKwds", "kind": "Gdef"}, "TimeLocaleKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.TimeLocaleKwds", "kind": "Gdef"}, "TitleConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.TitleConfigKwds", "kind": "Gdef"}, "TitleParamsKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.TitleParamsKwds", "kind": "Gdef"}, "TooltipContentKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.TooltipContentKwds", "kind": "Gdef"}, "TopLevelSelectionParameterKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.TopLevelSelectionParameterKwds", "kind": "Gdef"}, "VariableParameterKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.VariableParameterKwds", "kind": "Gdef"}, "ViewBackgroundKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ViewBackgroundKwds", "kind": "Gdef"}, "ViewConfigKwds": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.schema._config.ViewConfigKwds", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.theme.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.theme.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__dir__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.theme.__dir__", "name": "__dir__", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__dir__", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.theme.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.theme.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "altair.theme.__getattr__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "altair.theme.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "altair.theme.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "active"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "altair.theme.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "active"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "altair.theme.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "options"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "altair.theme.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "options"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "active"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "options"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.theme.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.theme.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.theme.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "_register": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.theme._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_themes": {".class": "SymbolTableNode", "cross_ref": "altair.vegalite.v5.theme.themes", "kind": "Gdef", "module_public": false}, "_wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef", "module_public": false}, "active": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.theme.active", "name": "active", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "enable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.theme.enable", "name": "enable", "type": {".class": "CallableType", "arg_kinds": [1, 4], "arg_names": ["name", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.theme.AltairThemes"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._typing.VegaThemes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["altair.vegalite.v5.theme.ThemeRegistry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "extra_attrs": null, "type_ref": "altair.utils.plugin_registry.PluginEnabler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.theme.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["altair.vegalite.v5.theme.ThemeRegistry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "extra_attrs": null, "type_ref": "functools.partial"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "altair.theme.names", "name": "names", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["altair.vegalite.v5.theme.ThemeRegistry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "altair.theme.options", "name": "options", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "register": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["name", "enable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.theme.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["name", "enable"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "unregister": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "altair.theme.unregister", "name": "unregister", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unregister", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "altair.vegalite.v5.schema._config.ThemeConfig"}], "type_ref": "altair.utils.plugin_registry.Plugin"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\theme.py"}