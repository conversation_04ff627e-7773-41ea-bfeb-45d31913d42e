{"data_mtime": 1755649448, "dep_lines": [12, 16, 17, 24, 25, 26, 8, 10, 11, 11, 13, 14, 15, 4, 7, 11, 1, 2, 3, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 20, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 20], "dependencies": ["torch._inductor.kernel.mm_common", "torch._inductor.codegen.cpp", "torch._inductor.codegen.cpp_gemm_template", "torch._inductor.codegen.cpp_micro_gemm", "torch._inductor.codegen.cpp_template_kernel", "torch._inductor.codegen.cpp_utils", "torch.utils._ordered_set", "torch._dynamo.utils", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.select_algorithm", "torch._inductor.utils", "torch._inductor.virtualized", "unittest.mock", "torch.utils", "torch._inductor", "contextlib", "logging", "typing", "torch", "builtins", "collections", "warnings", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "_frozen_importlib", "abc", "torch._inductor.codegen.common", "torch._inductor.codegen.cpp_template", "typing_extensions"], "hash": "e31944b7cab761190c5a9862b1de4c7b964b9bfb", "id": "torch._inductor.codegen.cpp_grouped_gemm_template", "ignore_all": true, "interface_hash": "bdb614dcec3ddd4f46827378208a8fd09ee0d6b9", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_grouped_gemm_template.py", "plugin_data": null, "size": 20882, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}