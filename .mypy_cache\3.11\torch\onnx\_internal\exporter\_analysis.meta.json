{"data_mtime": 1755649448, "dep_lines": [17, 17, 15, 17, 15, 16, 15, 16, 21, 5, 7, 8, 9, 10, 11, 12, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 20, 10, 20, 20, 25, 5, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.exporter._dispatching", "torch.onnx._internal.exporter._registration", "torch._export.serde.schema", "torch.onnx._internal.exporter", "torch._export.serde", "torch.export.graph_signature", "torch._export", "torch.export", "torch.fx", "__future__", "dataclasses", "operator", "textwrap", "traceback", "collections", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch.export.exported_program", "torch.fx.node", "typing_extensions"], "hash": "2c121d5de09af63378cdde55f0202975d905dcd7", "id": "torch.onnx._internal.exporter._analysis", "ignore_all": true, "interface_hash": "dea9c8186a893db7a5c998708226e0c213e1684e", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_analysis.py", "plugin_data": null, "size": 9104, "suppressed": [], "version_id": "1.15.0"}