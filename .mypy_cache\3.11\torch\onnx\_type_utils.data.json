{".class": "MypyFile", "_fullname": "torch.onnx._type_utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "JitScalarType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._type_utils.JitScalarType", "name": "JitScalarType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch.onnx._type_utils.JitScalarType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch.onnx._type_utils", "mro": ["torch.onnx._type_utils.JitScalarType", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BFLOAT16": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.BFLOAT16", "name": "BFLOAT16", "type": "enum.auto"}}, "BOOL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.BOOL", "name": "BOOL", "type": "enum.auto"}}, "COMPLEX128": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.COMPLEX128", "name": "COMPLEX128", "type": "enum.auto"}}, "COMPLEX32": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.COMPLEX32", "name": "COMPLEX32", "type": "enum.auto"}}, "COMPLEX64": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.COMPLEX64", "name": "COMPLEX64", "type": "enum.auto"}}, "DOUBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.DOUBLE", "name": "DOUBLE", "type": "enum.auto"}}, "FLOAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.FLOAT", "name": "FLOAT", "type": "enum.auto"}}, "FLOAT8E4M3FN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.FLOAT8E4M3FN", "name": "FLOAT8E4M3FN", "type": "enum.auto"}}, "FLOAT8E4M3FNUZ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.FLOAT8E4M3FNUZ", "name": "FLOAT8E4M3FNUZ", "type": "enum.auto"}}, "FLOAT8E5M2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.FLOAT8E5M2", "name": "FLOAT8E5M2", "type": "enum.auto"}}, "FLOAT8E5M2FNUZ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.FLOAT8E5M2FNUZ", "name": "FLOAT8E5M2FNUZ", "type": "enum.auto"}}, "HALF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.HALF", "name": "HALF", "type": "enum.auto"}}, "INT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.INT", "name": "INT", "type": "enum.auto"}}, "INT16": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.INT16", "name": "INT16", "type": "enum.auto"}}, "INT64": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.INT64", "name": "INT64", "type": "enum.auto"}}, "INT8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.INT8", "name": "INT8", "type": "enum.auto"}}, "QINT32": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.QINT32", "name": "QINT32", "type": "enum.auto"}}, "QINT8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.QINT8", "name": "QINT8", "type": "enum.auto"}}, "QUINT8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.QUINT8", "name": "QUINT8", "type": "enum.auto"}}, "UINT8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.UINT8", "name": "UINT8", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "UNDEFINED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils.JitScalarType.UNDEFINED", "name": "UNDEFINED", "type": "enum.auto"}}, "_from_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._type_utils.JitScalarType._from_name", "name": "_from_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._type_utils.JitScalarType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.ScalarName"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.TorchName"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_from_name of JitScalarType", "ret_type": "torch.onnx._type_utils.JitScalarType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._type_utils.JitScalarType._from_name", "name": "_from_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._type_utils.JitScalarType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.ScalarName"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.TorchName"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_from_name of JitScalarType", "ret_type": "torch.onnx._type_utils.JitScalarType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._type_utils.JitScalarType.dtype", "name": "dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._type_utils.JitScalarType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype of JitScalarType", "ret_type": "torch._C.dtype", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._type_utils.JitScalarType.from_dtype", "name": "from_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "dtype"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._type_utils.JitScalarType"}, {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dtype of JitScalarType", "ret_type": "torch.onnx._type_utils.JitScalarType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._type_utils.JitScalarType.from_dtype", "name": "from_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "dtype"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._type_utils.JitScalarType"}, {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dtype of JitScalarType", "ret_type": "torch.onnx._type_utils.JitScalarType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_onnx_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "onnx_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._type_utils.JitScalarType.from_onnx_type", "name": "from_onnx_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "onnx_type"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._type_utils.JitScalarType"}, {".class": "UnionType", "items": ["builtins.int", "torch._C._onnx.TensorProtoDataType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_onnx_type of JitScalarType", "ret_type": "torch.onnx._type_utils.JitScalarType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._type_utils.JitScalarType.from_onnx_type", "name": "from_onnx_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "onnx_type"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._type_utils.JitScalarType"}, {".class": "UnionType", "items": ["builtins.int", "torch._C._onnx.TensorProtoDataType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_onnx_type of JitScalarType", "ret_type": "torch.onnx._type_utils.JitScalarType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "value", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._type_utils.JitScalarType.from_value", "name": "from_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "value", "default"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._type_utils.JitScalarType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "torch._C.Value", "torch._tensor.Tensor"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_value of JitScalarType", "ret_type": "torch.onnx._type_utils.JitScalarType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._type_utils.JitScalarType.from_value", "name": "from_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "value", "default"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._type_utils.JitScalarType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "torch._C.Value", "torch._tensor.Tensor"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_value of JitScalarType", "ret_type": "torch.onnx._type_utils.JitScalarType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "onnx_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._type_utils.JitScalarType.onnx_compatible", "name": "onnx_compatible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._type_utils.JitScalarType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "onnx_compatible of JitScalarType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "onnx_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._type_utils.JitScalarType.onnx_type", "name": "onnx_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._type_utils.JitScalarType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "onnx_type of JitScalarType", "ret_type": "torch._C._onnx.TensorProtoDataType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scalar_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._type_utils.JitScalarType.scalar_name", "name": "scalar_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._type_utils.JitScalarType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar_name of JitScalarType", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.ScalarName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._type_utils.JitScalarType.torch_name", "name": "torch_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._type_utils.JitScalarType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "torch_name of JitScalarType", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.TorchName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._type_utils.JitScalarType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._type_utils.JitScalarType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "ScalarName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._type_utils.ScalarName", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "Byte"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Char"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Double"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Half"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Int"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Short"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Bool"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ComplexHalf"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ComplexFloat"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ComplexDouble"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "QInt8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "QUInt8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "QInt32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "BFloat16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Float8E5M2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Float8E4M3FN"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Float8E5M2FNUZ"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Float8E4M3FNUZ"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Undefined"}], "uses_pep604_syntax": false}}}, "TorchName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.onnx._type_utils.TorchName", "line": 43, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uint8_t"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int8_t"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "double"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "half"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int64_t"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int16_t"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex128"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "qint8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quint8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "qint32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bfloat16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float8_e5m2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float8_e4m3fn"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float8_e5m2fnuz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float8_e4m3fnuz"}], "uses_pep604_syntax": false}}}, "_C": {".class": "SymbolTableNode", "cross_ref": "torch._C", "kind": "Gdef"}, "_C_onnx": {".class": "SymbolTableNode", "cross_ref": "torch._<PERSON>._onnx", "kind": "Gdef"}, "_DTYPE_TO_SCALAR_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils._DTYPE_TO_SCALAR_TYPE", "name": "_DTYPE_TO_SCALAR_TYPE", "type": {".class": "Instance", "args": ["torch._C.dtype", "torch.onnx._type_utils.JitScalarType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_ONNX_TO_SCALAR_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils._ONNX_TO_SCALAR_TYPE", "name": "_ONNX_TO_SCALAR_TYPE", "type": {".class": "Instance", "args": ["torch._C._onnx.TensorProtoDataType", "torch.onnx._type_utils.JitScalarType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SCALAR_NAME_TO_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._type_utils._SCALAR_NAME_TO_TYPE", "name": "_SCALAR_NAME_TO_TYPE", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.ScalarName"}, "torch.onnx._type_utils.JitScalarType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SCALAR_TYPE_TO_DTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils._SCALAR_TYPE_TO_DTYPE", "name": "_SCALAR_TYPE_TO_DTYPE", "type": {".class": "Instance", "args": ["torch.onnx._type_utils.JitScalarType", "torch._C.dtype"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SCALAR_TYPE_TO_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._type_utils._SCALAR_TYPE_TO_NAME", "name": "_SCALAR_TYPE_TO_NAME", "type": {".class": "Instance", "args": ["torch.onnx._type_utils.JitScalarType", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.ScalarName"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SCALAR_TYPE_TO_ONNX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._type_utils._SCALAR_TYPE_TO_ONNX", "name": "_SCALAR_TYPE_TO_ONNX", "type": {".class": "Instance", "args": ["torch.onnx._type_utils.JitScalarType", "torch._C._onnx.TensorProtoDataType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SCALAR_TYPE_TO_TORCH_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._type_utils._SCALAR_TYPE_TO_TORCH_NAME", "name": "_SCALAR_TYPE_TO_TORCH_NAME", "type": {".class": "Instance", "args": ["torch.onnx._type_utils.JitScalarType", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.TorchName"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_TORCH_NAME_TO_SCALAR_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.onnx._type_utils._TORCH_NAME_TO_SCALAR_TYPE", "name": "_TORCH_NAME_TO_SCALAR_TYPE", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.TorchName"}, "torch.onnx._type_utils.JitScalarType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._type_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._type_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._type_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._type_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._type_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._type_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "errors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.errors", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "valid_scalar_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["scalar_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._type_utils.valid_scalar_name", "name": "valid_scalar_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["scalar_name"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.ScalarName"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_scalar_name", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "valid_torch_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["torch_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._type_utils.valid_torch_name", "name": "valid_torch_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["torch_name"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._type_utils.TorchName"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_torch_name", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_type_utils.py"}