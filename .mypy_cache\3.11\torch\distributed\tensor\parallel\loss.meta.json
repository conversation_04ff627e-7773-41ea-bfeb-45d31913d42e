{"data_mtime": 1755649449, "dep_lines": [14, 15, 20, 13, 21, 8, 9, 11, 12, 7, 8, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 20, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.tensor._ops._embedding_ops", "torch.distributed.tensor._ops._math_ops", "torch.distributed.tensor._ops.utils", "torch.distributed.tensor._dtensor_spec", "torch.distributed.tensor.placement_types", "torch.distributed._functional_collectives", "torch.distributed.distributed_c10d", "torch.distributed.device_mesh", "torch.distributed.tensor", "torch._prims_common", "torch.distributed", "contextlib", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "copy", "_frozen_importlib", "abc", "torch._C", "torch._ops", "torch._tensor", "torch.distributed.tensor._api"], "hash": "f0a40a13558832c68cdfeb496ddb0ba5bd56ef1f", "id": "torch.distributed.tensor.parallel.loss", "ignore_all": true, "interface_hash": "4aec572b9cb05f36d31d0f70229e12c20b7252a0", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\loss.py", "plugin_data": null, "size": 18283, "suppressed": [], "version_id": "1.15.0"}