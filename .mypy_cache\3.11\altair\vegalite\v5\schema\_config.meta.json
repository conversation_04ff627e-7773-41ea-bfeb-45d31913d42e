{"data_mtime": 1755656860, "dep_lines": [13, 17, 4, 6, 7, 12, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["altair.vegalite.v5.schema._typing", "collections.abc", "__future__", "sys", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "082f15fc2be94653a4c868614b730036b704fd4d", "id": "altair.vegalite.v5.schema._config", "ignore_all": true, "interface_hash": "ccf0cea5750cd4b555c8779e0cd2a66f863ce03a", "mtime": 1755656335, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\vegalite\\v5\\schema\\_config.py", "plugin_data": null, "size": 304714, "suppressed": [], "version_id": "1.15.0"}