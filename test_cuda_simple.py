import torch
import torch.nn as nn
import numpy as np

print("=== CUDA Test ===")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"CUDA version: {torch.version.cuda}")
    
    # Force CUDA device
    device = torch.device('cuda:0')
    print(f"Using device: {device}")
    
    # Test simple operations
    print("\n=== Testing CUDA Operations ===")
    
    # Create tensors on GPU
    x = torch.randn(1000, 1000).to(device)
    y = torch.randn(1000, 1000).to(device)
    
    print(f"Tensor x device: {x.device}")
    print(f"Tensor y device: {y.device}")
    
    # Matrix multiplication on GPU
    z = torch.mm(x, y)
    print(f"Result tensor device: {z.device}")
    
    # Test neural network on GPU
    print("\n=== Testing Neural Network on GPU ===")
    
    class SimpleNet(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(100, 50)
            self.relu = nn.ReLU()
            self.output = nn.Linear(50, 1)
        
        def forward(self, x):
            x = self.linear(x)
            x = self.relu(x)
            x = self.output(x)
            return x
    
    model = SimpleNet().to(device)
    print(f"Model device: {next(model.parameters()).device}")
    
    # Test forward pass
    input_data = torch.randn(32, 100).to(device)
    output = model(input_data)
    print(f"Input device: {input_data.device}")
    print(f"Output device: {output.device}")
    
    # Test loss calculation
    target = torch.randn(32, 1).to(device)
    criterion = nn.MSELoss()
    loss = criterion(output, target)
    print(f"Loss device: {loss.device}")
    print(f"Loss value: {loss.item():.6f}")
    
    print("\n✅ All CUDA operations successful!")
    
else:
    print("❌ CUDA not available")
