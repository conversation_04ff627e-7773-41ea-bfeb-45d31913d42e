import{bu as S,cx as j,s as O,r as s,aT as _,ap as q,cy as T,aE as G,j as u,bL as K,bv as $,bc as N,bw as J}from"./index.DKN5MVff.js";import{u as M}from"./uniqueId.D_5M8Dgf.js";import{I as Q}from"./InputInstructions.D8zoMog9.js";import{a as X}from"./useBasicWidgetState.DB3vMS9V.js";import{u as Y,a as Z,b as tt}from"./useUpdateUiValue.C7ZKpLQK.js";import{u as et,T as it}from"./useTextInputAutoExpand.CBkGkaRt.js";import"./FormClearHelper.DF4gFAOO.js";import"./inputUtils.CptNuJwn.js";import"./base-input.BmvSaPd2.js";const at=(e,t)=>{let a="auto";if(e.heightConfig?.useStretch)a="100%";else if(e.heightConfig?.pixelHeight&&e.heightConfig.pixelHeight>0){const o=S(t.labelVisibility?.value)===j.Collapsed?2:30;a=`${e.heightConfig.pixelHeight-o}px`}return a},st=O("div",{target:"e1r0q00f0"})({height:"100%",display:"flex",flexDirection:"column"}),H=(e,t)=>e.getStringValue(t)??t.default??null,ot=e=>e.default??null,rt=e=>e.value??null,lt=(e,t,a,o)=>{t.setStringValue(e,a.value,{fromUi:a.fromUi},o)},nt=({disabled:e,element:t,widgetMgr:a,fragmentId:o,outerElement:d})=>{const m=s.useRef(M("text_area_")).current,[W,y]=_(),[r,c]=s.useState(!1),[I,b]=s.useState(!1),l=d.heightConfig?.useContent??!1,V=d.heightConfig?.useStretch??!1,v=at(d,t),x=s.useRef(null),[n,h]=s.useState(()=>H(a,t)??null),A=s.useCallback(()=>{h(t.default??null),c(!0)},[t]),[F,g]=X({getStateFromWidgetMgr:H,getDefaultStateFromProto:ot,getCurrStateFromProto:rt,updateWidgetMgrState:lt,element:t,widgetMgr:a,fragmentId:o,onFormCleared:A});Y(F,n,h,r);const i=q(),{height:R,maxHeight:E,updateScrollHeight:C}=et({textareaRef:x,dependencies:[t.placeholder]}),p=s.useCallback(()=>{c(!1),g({value:n,fromUi:!0})},[n,g]),L=s.useCallback(()=>{r&&p(),b(!1)},[r,p]),k=s.useCallback(()=>{b(!0)},[]),z=s.useCallback(()=>{l&&C()},[l,C]),P=Z({formId:t.formId,maxChars:t.maxChars,setDirty:c,setUiValue:h,setValueWithSource:g,additionalAction:z}),U=tt(t.formId,p,r,a,o,!0),{placeholder:D,formId:f}=t,w=T({formId:f})?a.allowFormEnterToSubmit(f):r,B=I&&W>i.breakpoints.hideWidgetDetails;return G(st,{className:"stTextArea","data-testid":"stTextArea",ref:y,children:[u(J,{label:t.label,disabled:e,labelVisibility:S(t.labelVisibility?.value),htmlFor:m,children:t.help&&u(K,{children:u($,{content:t.help,placement:N.TOP_RIGHT})})}),u(it,{inputRef:l?x:void 0,value:n??"",placeholder:D,onBlur:L,onFocus:k,onChange:P,onKeyDown:U,"aria-label":t.label,disabled:e,id:m,overrides:{Input:{style:{fontWeight:i.fontWeights.normal,lineHeight:i.lineHeights.inputWidget,height:l?R:v,maxHeight:l?E:"",minHeight:i.sizes.largestElementHeight,resize:V?"none":"vertical",paddingRight:i.spacing.md,paddingLeft:i.spacing.md,paddingBottom:i.spacing.md,paddingTop:i.spacing.md,"::placeholder":{color:i.colors.fadedText60}}},Root:{props:{"data-testid":"stTextAreaRootElement"},style:{borderLeftWidth:i.sizes.borderWidth,borderRightWidth:i.sizes.borderWidth,borderTopWidth:i.sizes.borderWidth,borderBottomWidth:i.sizes.borderWidth,flexGrow:1}}}}),B&&u(Q,{dirty:r,value:n??"",maxLength:t.maxChars,type:"multiline",inForm:T({formId:f}),allowEnterToSubmit:w})]})},xt=s.memo(nt);export{xt as default};
