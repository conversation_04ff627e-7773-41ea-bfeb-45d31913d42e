{"data_mtime": 1755656862, "dep_lines": [43, 44, 46, 21, 28, 29, 30, 31, 32, 38, 41, 6, 20, 22, 23, 24, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26], "dep_prios": [25, 25, 25, 10, 5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.refs.reference", "git.refs.tag", "git.repo.base", "os.path", "git.cmd", "git.exc", "git.objects", "git.refs", "git.util", "git.types", "git.db", "__future__", "os", "pathlib", "stat", "string", "typing", "builtins", "_frozen_importlib", "abc", "git.diff", "git.objects.base", "git.objects.blob", "git.objects.commit", "git.objects.tag", "git.objects.tree", "git.objects.util", "git.refs.symbolic", "types"], "hash": "f16b0c47853c53e3db291612a88548b846031234", "id": "git.repo.fun", "ignore_all": true, "interface_hash": "ea913a5c76572ba01eae04d4891a753882964cfb", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\repo\\fun.py", "plugin_data": null, "size": 13803, "suppressed": ["gitdb.exc"], "version_id": "1.15.0"}