{".class": "MypyFile", "_fullname": "torch.distributed.fsdp._fully_shard._fully_shard", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"Module\" and \"FSDPModule\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">", "name": "<subclass of \"Module\" and \"FSDPModule\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp._fully_shard._fully_shard", "mro": ["torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">", "torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"Module\" and \"FSDPModule\">1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">1", "name": "<subclass of \"Module\" and \"FSDPModule\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp._fully_shard._fully_shard", "mro": ["torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">1", "torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"Module\" and \"FSDPModule\">2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">2", "name": "<subclass of \"Module\" and \"FSDPModule\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp._fully_shard._fully_shard", "mro": ["torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">2", "torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"Module\" and \"FSDPModule\">3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">3", "name": "<subclass of \"Module\" and \"FSDPModule\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">3", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp._fully_shard._fully_shard", "mro": ["torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">3", "torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"Module\" and \"FSDPModule\">4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">4", "name": "<subclass of \"Module\" and \"FSDPModule\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">4", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp._fully_shard._fully_shard", "mro": ["torch.distributed.fsdp._fully_shard._fully_shard.<subclass of \"Module\" and \"FSDPModule\">4", "torch.nn.modules.module.Module", "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DeviceMesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.DeviceMesh", "kind": "Gdef", "module_public": false}, "FSDPMeshInfo": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_common.FSDPMeshInfo", "kind": "Gdef", "module_public": false}, "FSDPModule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "name": "FSDPModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp._fully_shard._fully_shard", "mro": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.__new__", "name": "__new__", "type": null}}, "_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule._apply", "name": "_apply", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply of FSDPModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_fsdp_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule._get_fsdp_state", "name": "_get_fsdp_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_fsdp_state of FSDPModule", "ret_type": "torch.distributed.fsdp._fully_shard._fsdp_state.FSDPState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_unshard_async_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "async_op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule._set_unshard_async_op", "name": "_set_unshard_async_op", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "async_op"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_unshard_async_op of FSDPModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reshard": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.reshard", "name": "reshard", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reshard of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_all_reduce_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "hook", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_all_reduce_hook", "name": "set_all_reduce_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "hook", "stream"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["torch.cuda.streams.Stream", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_all_reduce_hook of FSDPModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_allocate_memory_from_process_group_for_comm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "enable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_allocate_memory_from_process_group_for_comm", "name": "set_allocate_memory_from_process_group_for_comm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "enable"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_allocate_memory_from_process_group_for_comm of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_force_sum_reduction_for_comms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "enable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_force_sum_reduction_for_comms", "name": "set_force_sum_reduction_for_comms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "enable"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_force_sum_reduction_for_comms of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_gradient_divide_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_gradient_divide_factor", "name": "set_gradient_divide_factor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "factor"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_gradient_divide_factor of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_is_last_backward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_last_backward"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_is_last_backward", "name": "set_is_last_backward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "is_last_backward"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_is_last_backward of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_modules_to_backward_prefetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_modules_to_backward_prefetch", "name": "set_modules_to_backward_prefetch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "modules"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", {".class": "Instance", "args": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_modules_to_backward_prefetch of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_modules_to_forward_prefetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_modules_to_forward_prefetch", "name": "set_modules_to_forward_prefetch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "modules"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", {".class": "Instance", "args": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_modules_to_forward_prefetch of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_post_optim_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_post_optim_event", "name": "set_post_optim_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "torch._C.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_post_optim_event of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_reduce_scatter_divide_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "factor"], "dataclass_transform_spec": null, "deprecated": "function torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_reduce_scatter_divide_factor is deprecated: Use `set_gradient_divide_factor` instead", "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_reduce_scatter_divide_factor", "name": "set_reduce_scatter_divide_factor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "factor"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_reduce_scatter_divide_factor of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_reduce_scatter_divide_factor", "name": "set_reduce_scatter_divide_factor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "factor"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_reduce_scatter_divide_factor of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_requires_all_reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "requires_all_reduce", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_requires_all_reduce", "name": "set_requires_all_reduce", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "requires_all_reduce", "recurse"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_requires_all_reduce of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_requires_gradient_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "requires_gradient_sync", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_requires_gradient_sync", "name": "set_requires_gradient_sync", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "requires_gradient_sync", "recurse"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_requires_gradient_sync of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_reshard_after_backward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "reshard_after_backward", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_reshard_after_backward", "name": "set_reshard_after_backward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "reshard_after_backward", "recurse"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_reshard_after_backward of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_reshard_after_forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "reshard_after_forward", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_reshard_after_forward", "name": "set_reshard_after_forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "reshard_after_forward", "recurse"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_reshard_after_forward of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_unshard_in_backward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "unshard_in_backward"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.set_unshard_in_backward", "name": "set_unshard_in_backward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "unshard_in_backward"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_unshard_in_backward of FSDPModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unshard": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "async_op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.unshard", "name": "unshard", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "async_op"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unshard of FSDPModule", "ret_type": {".class": "UnionType", "items": ["torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FSDPParamGroup": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_param_group.FSDPParamGroup", "kind": "Gdef", "module_public": false}, "FSDPState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_state.FSDPState", "kind": "Gdef", "module_public": false}, "HSDPMeshInfo": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_common.HSDPMeshInfo", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "MixedPrecisionPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "kind": "Gdef", "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_public": false}, "OffloadPolicy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Shard", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnshardHandle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp._fully_shard._fully_shard", "mro": ["torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UnshardHandleImpl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl", "name": "_UnshardHandleImpl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.fsdp._fully_shard._fully_shard", "mro": ["torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl", "torch.distributed.fsdp._fully_shard._fully_shard.UnshardHandle", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fsdp_param_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fsdp_param_group"], "arg_types": ["torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl", {".class": "UnionType", "items": ["torch.distributed.fsdp._fully_shard._fsdp_param_group.FSDPParamGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _UnshardHandleImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fsdp_param_group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl._fsdp_param_group", "name": "_fsdp_param_group", "type": {".class": "UnionType", "items": ["torch.distributed.fsdp._fully_shard._fsdp_param_group.FSDPParamGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl.wait", "name": "wait", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.fsdp._fully_shard._fully_shard._UnshardHandleImpl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_assert_all_fsdp_modules": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard._assert_all_fsdp_modules", "name": "_assert_all_fsdp_modules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["modules"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_all_fsdp_modules", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_device_from_mesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_init._get_device_from_mesh", "kind": "Gdef", "module_public": false}, "_get_managed_modules": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_init._get_managed_modules", "kind": "Gdef", "module_public": false}, "_get_managed_states": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_init._get_managed_states", "kind": "Gdef", "module_public": false}, "_get_module_fsdp_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_state._get_module_fsdp_state", "kind": "Gdef", "module_public": false}, "_get_post_forward_mesh_info": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_init._get_post_forward_mesh_info", "kind": "Gdef", "module_public": false}, "_get_root_modules": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._get_root_modules", "kind": "Gdef", "module_public": false}, "_init_default_fully_shard_mesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_init._init_default_fully_shard_mesh", "kind": "Gdef", "module_public": false}, "_move_states_to_device": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._fully_shard._fsdp_init._move_states_to_device", "kind": "Gdef", "module_public": false}, "_unimplemented_deepcopy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard._unimplemented_deepcopy", "name": "_unimplemented_deepcopy", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unimplemented_deepcopy", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "cls_to_fsdp_cls": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.cls_to_fsdp_cls", "name": "cls_to_fsdp_cls", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "contract": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._composable.contract.contract", "kind": "Gdef", "module_public": false}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_public": false}, "fully_shard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "name": "fully_shard", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Shard", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fully_shard", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "name": "fully_shard", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": [null, "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Shard", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "torch.distributed.fsdp._fully_shard._fsdp_state.FSDPState"], "extra_attrs": null, "type_ref": "torch.distributed._composable.contract._ContractFn"}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "name": "fully_shard", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "arg_types": ["torch.nn.modules.module.Module", {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Shard", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fully_shard", "ret_type": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "name": "fully_shard", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "arg_types": ["torch.nn.modules.module.Module", {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Shard", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fully_shard", "ret_type": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "name": "fully_shard", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "arg_types": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Shard", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fully_shard", "ret_type": {".class": "Instance", "args": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.fully_shard", "name": "fully_shard", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "arg_types": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Shard", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fully_shard", "ret_type": {".class": "Instance", "args": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "arg_types": ["torch.nn.modules.module.Module", {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Shard", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fully_shard", "ret_type": "torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["module", "mesh", "reshard_after_forward", "shard_placement_fn", "mp_policy", "offload_policy", "ignored_params"], "arg_types": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch.distributed.device_mesh.DeviceMesh", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.parameter.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Shard", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.fsdp._fully_shard._fsdp_api.MixedPrecisionPolicy", "torch.distributed.fsdp._fully_shard._fsdp_api.OffloadPolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fully_shard", "ret_type": {".class": "Instance", "args": ["torch.distributed.fsdp._fully_shard._fully_shard.FSDPModule"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "register_fsdp_forward_method": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module", "method_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._fully_shard._fully_shard.register_fsdp_forward_method", "name": "register_fsdp_forward_method", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module", "method_name"], "arg_types": ["torch.nn.modules.module.Module", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_fsdp_forward_method", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_fully_shard\\_fully_shard.py"}