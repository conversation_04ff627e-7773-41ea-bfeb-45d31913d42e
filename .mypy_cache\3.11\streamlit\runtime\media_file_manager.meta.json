{"data_mtime": 1755656862, "dep_lines": [24, 23, 17, 19, 20, 21, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["streamlit.runtime.media_file_storage", "streamlit.logger", "__future__", "collections", "threading", "typing", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "enum", "logging"], "hash": "bd4d77d5e1d3946d62987bb28fc82ae173c9607e", "id": "streamlit.runtime.media_file_manager", "ignore_all": true, "interface_hash": "2ba6e9fe310ec6914bf65fd98cd49fa531f003cb", "mtime": 1755656336, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\runtime\\media_file_manager.py", "plugin_data": null, "size": 8552, "suppressed": [], "version_id": "1.15.0"}