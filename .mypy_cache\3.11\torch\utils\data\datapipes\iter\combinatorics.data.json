{".class": "MypyFile", "_fullname": "torch.utils.data.datapipes.iter.combinatorics", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "IterDataPipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Sampler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.sampler.Sampler", "kind": "Gdef", "module_public": false}, "SamplerIterDataPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "name": "SamplerIterDataPipe", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.iter.combinatorics", "mro": ["torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "datapipe", "sampler", "sampler_args", "sampler_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "datapipe", "sampler", "sampler_args", "sampler_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.sampler.Sampler"}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SamplerIterDataPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of SamplerIterDataPipe", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of SamplerIterDataPipe", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datapipe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe.datapipe", "name": "datapipe", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}}}, "sampler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe.sampler", "name": "sampler", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.sampler.Sampler"}}}, "sampler_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe.sampler_args", "name": "sampler_args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "sampler_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe.sampler_kwargs", "name": "sampler_kwargs", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.SamplerIterDataPipe"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "SequentialSampler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.sampler.SequentialSampler", "kind": "Gdef", "module_public": false}, "ShufflerIterDataPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "name": "ShufflerIterDataPipe", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.iter.combinatorics", "mro": ["torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.__del__", "name": "__del__", "type": null}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "datapipe", "buffer_size", "unbatch_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "datapipe", "buffer_size", "unbatch_level"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShufflerIterDataPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of ShufflerIterDataPipe", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of ShufflerIterDataPipe", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.__setstate__", "name": "__setstate__", "type": null}}, "_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe._buffer", "name": "_buffer", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe._enabled", "name": "_enabled", "type": "builtins.bool"}}, "_rng": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe._rng", "name": "_rng", "type": "random.Random"}}, "_seed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe._seed", "name": "_seed", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_valid_iterator_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe._valid_iterator_id", "name": "_valid_iterator_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "buffer_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.buffer_size", "name": "buffer_size", "type": "builtins.int"}}, "datapipe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.datapipe", "name": "datapipe", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of ShufflerIterDataPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_seed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "seed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.set_seed", "name": "set_seed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "seed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_seed of ShufflerIterDataPipe", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_shuffle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "shuffle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.set_shuffle", "name": "set_shuffle", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "id": 1, "name": "_T_co", "namespace": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.iter.combinatorics.ShufflerIterDataPipe"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "Sized": {".class": "SymbolTableNode", "cross_ref": "typing.Sized", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.combinatorics._T_co", "name": "_T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.combinatorics.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "functional_datapipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes._decorator.functional_datapipe", "kind": "Gdef", "module_public": false}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\combinatorics.py"}