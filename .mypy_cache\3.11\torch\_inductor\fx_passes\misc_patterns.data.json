{".class": "MypyFile", "_fullname": "torch._inductor.fx_passes.misc_patterns", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "NumpyCompatNormalization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization", "name": "NumpyCompatNormalization", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.misc_patterns", "mro": ["torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "graph"], "arg_types": ["torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization", "torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of NumpyCompatNormalization", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NumpyCompatNormalization", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization.cache", "name": "cache", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.node.Target"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "inverse_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization.inverse_mapping", "name": "inverse_mapping", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "numpy_compat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization.numpy_compat", "name": "numpy_compat", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OpOverload": {".class": "SymbolTableNode", "cross_ref": "torch._ops.OpOverload", "kind": "Gdef"}, "OpOverloadPacket": {".class": "SymbolTableNode", "cross_ref": "torch._ops.OpOverloadPacket", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.misc_patterns.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.misc_patterns.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.misc_patterns.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.misc_patterns.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.misc_patterns.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.misc_patterns.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_misc_patterns_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.misc_patterns._misc_patterns_init", "name": "_misc_patterns_init", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.misc_patterns._misc_patterns_init", "name": "_misc_patterns_init", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.misc_patterns.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "fwd_only": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.fwd_only", "kind": "Gdef"}, "numpy_compat_normalization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.misc_patterns.numpy_compat_normalization", "name": "numpy_compat_normalization", "type": "torch._inductor.fx_passes.misc_patterns.NumpyCompatNormalization"}}, "register_replacement": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.register_replacement", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\misc_patterns.py"}