{"data_mtime": 1755649448, "dep_lines": [17, 18, 13, 13, 14, 15, 16, 7, 9, 13, 2, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 11], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 10], "dependencies": ["torch._inductor.codegen.common", "torch._inductor.codegen.cpp_template_kernel", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.autotune_process", "torch._inductor.utils", "torch._inductor.virtualized", "collections.abc", "unittest.mock", "torch._inductor", "ctypes", "functools", "itertools", "logging", "sys", "typing", "builtins", "os", "torch", "inspect", "html", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc"], "hash": "73a010a326248cefb88528108b4c9b91b379bf09", "id": "torch._inductor.codegen.cpp_template", "ignore_all": true, "interface_hash": "1bcd9bc518abcf524528aec2c38898c3c916946a", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_template.py", "plugin_data": null, "size": 5047, "suppressed": ["sympy"], "version_id": "1.15.0"}