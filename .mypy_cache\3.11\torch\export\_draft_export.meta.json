{"data_mtime": 1755649448, "dep_lines": [15, 12, 13, 14, 21, 22, 23, 12, 14, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 20, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch._export.passes.insert_custom_op_guards", "torch._logging._internal", "torch._logging.structured", "torch.utils._pytree", "torch.export._trace", "torch.export.dynamic_shapes", "torch.export.exported_program", "torch._logging", "torch.utils", "getpass", "json", "logging", "os", "re", "tempfile", "dataclasses", "enum", "typing", "torch", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._library", "torch._library.fake_profile", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "typing_extensions"], "hash": "6bd8ce28898948f39df97f0b53c49a996338a59f", "id": "torch.export._draft_export", "ignore_all": true, "interface_hash": "3447ab349788b74259513766d50734c760aea5d4", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\export\\_draft_export.py", "plugin_data": null, "size": 19775, "suppressed": [], "version_id": "1.15.0"}