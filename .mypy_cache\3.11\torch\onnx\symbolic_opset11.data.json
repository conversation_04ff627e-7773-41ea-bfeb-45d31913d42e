{".class": "MypyFile", "_fullname": "torch.onnx.symbolic_opset11", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Delete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.Delete", "name": "Delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Delete", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.Delete", "name": "Delete", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "_C": {".class": "SymbolTableNode", "cross_ref": "torch._C", "kind": "Gdef", "module_public": false}, "_C_onnx": {".class": "SymbolTableNode", "cross_ref": "torch._<PERSON>._onnx", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.symbolic_opset11.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset11.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset11.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset11.__file__", "name": "__file__", "type": "builtins.str"}}, "__getitem_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.__getitem_", "name": "__getitem_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "i"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.__getitem_", "name": "__getitem_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__interpolate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "size", "scale_factor", "mode", "align_corners", "recompute_scale_factor", "antialias"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.__interpolate", "name": "__interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "size", "scale_factor", "mode", "align_corners", "recompute_scale_factor", "antialias"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__interpolate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.__interpolate", "name": "__interpolate", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__lshift_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.__lshift_", "name": "__lshift_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lshift_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.__lshift_", "name": "__lshift_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset11.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset11.__package__", "name": "__package__", "type": "builtins.str"}}, "__rshift_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.__rshift_", "name": "__rshift_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rshift_", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.__rshift_", "name": "__rshift_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.symbolic_opset11.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_dim_arange": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "like", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11._dim_arange", "name": "_dim_arange", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "like", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dim_arange", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11._dim_arange", "name": "_dim_arange", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_get_im2col_indices_along_dim": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input_d", "kernel_size_d", "dilation_d", "padding_d", "stride_d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset11._get_im2col_indices_along_dim", "name": "_get_im2col_indices_along_dim", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input_d", "kernel_size_d", "dilation_d", "padding_d", "stride_d"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_im2col_indices_along_dim", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_im2col_output_shape": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "kernel_h", "kernel_w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset11._get_im2col_output_shape", "name": "_get_im2col_output_shape", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "kernel_h", "kernel_w"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_im2col_output_shape", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_im2col_padded_input": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "padding_h", "padding_w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset11._get_im2col_padded_input", "name": "_get_im2col_padded_input", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "padding_h", "padding_w"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_im2col_padded_input", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_interpolate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "dim", "interpolate_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11._interpolate", "name": "_interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "dim", "interpolate_mode"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_interpolate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11._interpolate", "name": "_interpolate", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_len": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11._len", "name": "_len", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_len", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11._len", "name": "_len", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_onnx_symbolic": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.symbolic_opset11._onnx_symbolic", "name": "_onnx_symbolic", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": {".class": "ExtraAttrs", "attrs": {"__mypy_partial": {".class": "CallableType", "arg_kinds": [0, 5, 1, 1], "arg_names": ["name", "opset", "decorate", "custom"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "onnx_symbolic", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "immutable": [], "mod_name": null}, "type_ref": "functools.partial"}}}, "_prepare_onnx_paddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.symbolic_opset11._prepare_onnx_paddings", "name": "_prepare_onnx_paddings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "pad"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_onnx_paddings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_item": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "tensor_list", "i", "v"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11._set_item", "name": "_set_item", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "tensor_list", "i", "v"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_item", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11._set_item", "name": "_set_item", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_type_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._type_utils", "kind": "Gdef", "module_public": false}, "_unique2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "sorted", "return_inverse", "return_counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11._unique2", "name": "_unique2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "sorted", "return_inverse", "return_counts"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unique2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11._unique2", "name": "_unique2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "add": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "other", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "other", "alpha"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.add", "name": "add", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "append": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "tensor"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.append", "name": "append", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "arange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["g", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.arange", "name": "arange", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["g", "args"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arange", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.arange", "name": "arange", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "argsort": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "decending", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.argsort", "name": "argsor<PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "decending", "out"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "argsor<PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.argsort", "name": "argsor<PERSON>", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "atleast_1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.atleast_1d", "name": "atleast_1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atleast_1d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.atleast_1d", "name": "atleast_1d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "atleast_2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.atleast_2d", "name": "atleast_2d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atleast_2d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.atleast_2d", "name": "atleast_2d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "atleast_3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.atleast_3d", "name": "atleast_3d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atleast_3d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.atleast_3d", "name": "atleast_3d", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "cat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.cat", "name": "cat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cat", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.cat", "name": "cat", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "chunk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "chunks", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.chunk", "name": "chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "chunks", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chunk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.chunk", "name": "chunk", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "clamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "min", "max"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.clamp", "name": "clamp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "min", "max"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clamp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.clamp", "name": "clamp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "clamp_max": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "max"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.clamp_max", "name": "clamp_max", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "max"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clamp_max", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.clamp_max", "name": "clamp_max", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "clamp_min": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "min"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.clamp_min", "name": "clamp_min", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "min"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clamp_min", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.clamp_min", "name": "clamp_min", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "constant_pad_nd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "padding", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.constant_pad_nd", "name": "constant_pad_nd", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "input", "padding", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constant_pad_nd", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.constant_pad_nd", "name": "constant_pad_nd", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "cumsum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.cumsum", "name": "cumsum", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["g", "self", "dim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cumsum", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.cumsum", "name": "cumsum", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "embedding_bag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "embedding_matrix", "indices", "offsets", "scale_grad_by_freq", "mode", "sparse", "per_sample_weights", "include_last_offset", "padding_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.embedding_bag", "name": "embedding_bag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["g", "embedding_matrix", "indices", "offsets", "scale_grad_by_freq", "mode", "sparse", "per_sample_weights", "include_last_offset", "padding_idx"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "embedding_bag", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.embedding_bag", "name": "embedding_bag", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "embedding_renorm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "weight", "indices", "max_norm", "norm_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.embedding_renorm", "name": "embedding_renorm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "weight", "indices", "max_norm", "norm_type"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "embedding_renorm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.embedding_renorm", "name": "embedding_renorm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "errors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.errors", "kind": "Gdef", "module_public": false}, "flatten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "start_dim", "end_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.flatten", "name": "flatten", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "input", "start_dim", "end_dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flatten", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.flatten", "name": "flatten", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "gather": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "index", "sparse_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "index", "sparse_grad"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.gather", "name": "gather", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "hardtanh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "min_val", "max_val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.hardtanh", "name": "hardtanh", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "min_val", "max_val"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hardtanh", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.hardtanh", "name": "hardtanh", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "hstack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "tensor_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.hstack", "name": "hstack", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "tensor_list"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hstack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.hstack", "name": "hstack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "im2col": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "kernel_size", "dilation", "padding", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.im2col", "name": "im2col", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "input", "kernel_size", "dilation", "padding", "stride"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "im2col", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.im2col", "name": "im2col", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "index"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.index", "name": "index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index_copy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.index_copy", "name": "index_copy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "source"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_copy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.index_copy", "name": "index_copy", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index_fill": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.index_fill", "name": "index_fill", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_fill", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.index_fill", "name": "index_fill", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "index_put": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "indices_list_value", "values", "accumulate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.index_put", "name": "index_put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "indices_list_value", "values", "accumulate"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index_put", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.index_put", "name": "index_put", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "insert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "pos", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "pos", "tensor"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.insert", "name": "insert", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "jit_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.jit_utils", "kind": "Gdef", "module_public": false}, "linalg_det": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.linalg_det", "name": "linalg_det", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "self"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linalg_det", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.linalg_det", "name": "linalg_det", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "linalg_vector_norm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "ord", "dim", "keepdim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.linalg_vector_norm", "name": "linalg_vector_norm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "ord", "dim", "keepdim", "dtype"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linalg_vector_norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.linalg_vector_norm", "name": "linalg_vector_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "logdet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.logdet", "name": "logdet", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logdet", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.logdet", "name": "logdet", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "masked_scatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "mask", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.masked_scatter", "name": "masked_scatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "mask", "source"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "masked_scatter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.masked_scatter", "name": "masked_scatter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "masked_select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.masked_select", "name": "masked_select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "mask"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "masked_select", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.masked_select", "name": "masked_select", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.mm", "name": "mm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.mm", "name": "mm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "narrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "dim", "start", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.narrow", "name": "narrow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "dim", "start", "length"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "narrow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.narrow", "name": "narrow", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "normal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["g", "mean", "std", "sizes", "generator", "dtype", "layout", "device", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.normal", "name": "normal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["g", "mean", "std", "sizes", "generator", "dtype", "layout", "device", "pin_memory"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normal", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.normal", "name": "normal", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "opset10": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset10", "kind": "Gdef", "module_public": false}, "opset9": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_opset9", "kind": "Gdef", "module_public": false}, "pad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "pad", "mode", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.pad", "name": "pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "input", "pad", "mode", "value"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value", "torch._C.Value", "torch._C.Value", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.pad", "name": "pad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "pixel_shuffle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "upscale_factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.pixel_shuffle", "name": "pixel_shuffle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "upscale_factor"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pixel_shuffle", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.pixel_shuffle", "name": "pixel_shuffle", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "pop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.pop", "name": "pop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.pop", "name": "pop", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "prim_constant_chunk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "chunks", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.prim_constant_chunk", "name": "prim_constant_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "chunks", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prim_constant_chunk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.prim_constant_chunk", "name": "prim_constant_chunk", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "reflection_pad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.reflection_pad", "name": "reflection_pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "padding"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflection_pad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.reflection_pad", "name": "reflection_pad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "registration": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.registration", "kind": "Gdef", "module_public": false}, "relu6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.relu6", "name": "relu6", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "input"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "relu6", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.relu6", "name": "relu6", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "remainder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.remainder", "name": "remainder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "other"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remainder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.remainder", "name": "remainder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "replication_pad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.replication_pad", "name": "replication_pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "input", "padding"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replication_pad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.replication_pad", "name": "replication_pad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "round": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "decimals"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.round", "name": "round", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "decimals"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "round", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.round", "name": "round", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "scatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.scatter", "name": "scatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index", "src"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scatter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.scatter", "name": "scatter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.select", "name": "select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "self", "dim", "index"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.select", "name": "select", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "size": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.size", "name": "size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "sort": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "decending", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.sort", "name": "sort", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "dim", "decending", "out"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sort", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.sort", "name": "sort", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "split": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_size_or_sizes", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_size_or_sizes", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.split", "name": "split", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "split_with_sizes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_sizes", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.split_with_sizes", "name": "split_with_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["g", "self", "split_sizes", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_with_sizes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.split_with_sizes", "name": "split_with_sizes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "squeeze": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.squeeze", "name": "squeeze", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["g", "self", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "squeeze", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.squeeze", "name": "squeeze", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.stack", "name": "stack", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "tensor_list", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.stack", "name": "stack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "symbolic_helper": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_helper", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "topk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "k", "dim", "largest", "sorted", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.topk", "name": "topk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["g", "self", "k", "dim", "largest", "sorted", "out"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "topk", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.topk", "name": "topk", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "unbind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim", "_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.unbind", "name": "unbind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["g", "self", "dim", "_outputs"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unbind", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.unbind", "name": "unbind", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unique_dim": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "sorted", "return_inverse", "return_counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.unique_dim", "name": "unique_dim", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["g", "self", "dim", "sorted", "return_inverse", "return_counts"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_dim", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.unique_dim", "name": "unique_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "unsqueeze": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.unsqueeze", "name": "unsqueeze", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["g", "self", "dim"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsqueeze", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.unsqueeze", "name": "unsqueeze", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.utils", "kind": "Gdef", "module_public": false}, "vstack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "tensor_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.onnx.symbolic_opset11.vstack", "name": "vstack", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "tensor_list"], "arg_types": ["torch.onnx._internal.jit_utils.GraphContext", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vstack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.symbolic_opset11.vstack", "name": "vstack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\symbolic_opset11.py"}