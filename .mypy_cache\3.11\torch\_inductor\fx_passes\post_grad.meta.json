{"data_mtime": 1755649448, "dep_lines": [21, 25, 59, 60, 61, 62, 63, 64, 65, 13, 16, 17, 18, 22, 24, 24, 24, 27, 28, 50, 12, 13, 14, 15, 19, 20, 3, 4, 5, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 20, 10, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.fx.experimental.symbolic_shapes", "torch._inductor.codegen.common", "torch._inductor.fx_passes.b2b_gemm", "torch._inductor.fx_passes.ddp_fusion", "torch._inductor.fx_passes.group_batch_fusion", "torch._inductor.fx_passes.micro_pipeline_tp", "torch._inductor.fx_passes.pre_grad", "torch._inductor.fx_passes.reinplace", "torch._inductor.fx_passes.split_cat", "torch.utils._pytree", "torch._dynamo.utils", "torch._inductor.comms", "torch._inductor.virtualized", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.pattern_matcher", "torch._inductor.fx_utils", "torch._inductor.lowering", "torch._inductor.utils", "torch._inductor", "torch.utils", "torch.fx", "torch._decomp", "torch._logging", "torch._prims_common", "functools", "itertools", "logging", "operator", "collections", "typing", "typing_extensions", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._ops", "torch._subclasses", "torch._subclasses.fake_tensor", "torch._tensor", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.node", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "f2213417823d1741a193172a0096c36f322c5752", "id": "torch._inductor.fx_passes.post_grad", "ignore_all": true, "interface_hash": "d88557c9465d4d556fcf666cb88e1a65e7bafb82", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\post_grad.py", "plugin_data": null, "size": 64795, "suppressed": [], "version_id": "1.15.0"}