{"data_mtime": 1755649448, "dep_lines": [13, 33, 34, 10, 11, 12, 14, 15, 22, 31, 10, 26, 1, 3, 8, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 29], "dep_prios": [5, 25, 25, 10, 5, 5, 5, 5, 5, 25, 20, 25, 5, 5, 10, 25, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 10, 25], "dependencies": ["torch._inductor.runtime.hints", "torch._inductor.codegen.simd_kernel_features", "torch._inductor.codegen.triton", "torch._inductor.config", "torch._inductor.codecache", "torch._inductor.metrics", "torch._inductor.scheduler", "torch._inductor.template_heuristics", "torch._inductor.virtualized", "torch.utils._ordered_set", "torch._inductor", "collections.abc", "__future__", "typing", "torch", "functools", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._inductor.codegen", "torch._inductor.codegen.common", "torch._inductor.codegen.simd"], "hash": "d14de7b7fa2e74abface12bc187f87ab1a868f86", "id": "torch._inductor.choices", "ignore_all": true, "interface_hash": "3e71e327d3e59756c21aa88ae2c816548e161f45", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\choices.py", "plugin_data": null, "size": 18092, "suppressed": ["sympy", "triton"], "version_id": "1.15.0"}