{".class": "MypyFile", "_fullname": "git.objects.submodule.util", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef", "module_public": false}, "GitConfigParser": {".class": "SymbolTableNode", "cross_ref": "git.config.GitConfigParser", "kind": "Gdef", "module_public": false}, "Head": {".class": "SymbolTableNode", "cross_ref": "git.refs.head.Head", "kind": "Gdef", "module_public": false}, "InvalidGitRepositoryError": {".class": "SymbolTableNode", "cross_ref": "git.exc.InvalidGitRepositoryError", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "ReferenceType": {".class": "SymbolTableNode", "cross_ref": "weakref.ReferenceType", "kind": "Gdef", "module_public": false}, "Remote": {".class": "SymbolTableNode", "cross_ref": "git.remote.Remote", "kind": "Gdef", "module_public": false}, "RemoteReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.remote.RemoteReference", "kind": "Gdef", "module_public": false}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Submodule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.Submodule", "kind": "Gdef", "module_public": false}, "SubmoduleConfigParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.config.GitConfigParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.submodule.util.SubmoduleConfigParser", "name": "SubmoduleConfigParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.SubmoduleConfigParser", "has_param_spec_type": false, "metaclass_type": "git.config.MetaParserBuilder", "metadata": {}, "module_name": "git.objects.submodule.util", "mro": ["git.objects.submodule.util.SubmoduleConfigParser", "git.config.GitConfigParser", "configparser.RawConfigParser", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.SubmoduleConfigParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.objects.submodule.util.SubmoduleConfigParser", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SubmoduleConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auto_write": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.submodule.util.SubmoduleConfigParser._auto_write", "name": "_auto_write", "type": "builtins.bool"}}, "_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.objects.submodule.util.SubmoduleConfigParser._index", "name": "_index", "type": {".class": "NoneType"}}}, "_smref": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "git.objects.submodule.util.SubmoduleConfigParser._smref", "name": "_smref", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["git.objects.submodule.base.Submodule"], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "flush_to_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.SubmoduleConfigParser.flush_to_index", "name": "flush_to_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.submodule.util.SubmoduleConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flush_to_index of SubmoduleConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_submodule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "submodule"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.SubmoduleConfigParser.set_submodule", "name": "set_submodule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "submodule"], "arg_types": ["git.objects.submodule.util.SubmoduleConfigParser", "git.objects.submodule.base.Submodule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_submodule of SubmoduleConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.SubmoduleConfigParser.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.submodule.util.SubmoduleConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of SubmoduleConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.submodule.util.SubmoduleConfigParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.submodule.util.SubmoduleConfigParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.util.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.util.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "find_first_remote_branch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["remotes", "branch_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.find_first_remote_branch", "name": "find_first_remote_branch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["remotes", "branch_name"], "arg_types": [{".class": "Instance", "args": ["git.remote.Remote"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_first_remote_branch", "ret_type": "git.refs.remote.RemoteReference", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "git": {".class": "SymbolTableNode", "cross_ref": "git", "kind": "Gdef", "module_public": false}, "mkhead": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["repo", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.mkhead", "name": "mkhead", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["repo", "path"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mkhead", "ret_type": "git.refs.head.Head", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sm_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["section"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.sm_name", "name": "sm_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["section"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sm_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sm_section": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.util.sm_section", "name": "sm_section", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sm_section", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\submodule\\util.py"}