import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
from sklearn.preprocessing import MinMaxScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Check GPU availability
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

class SubstationDataset(Dataset):
    """Custom PyTorch Dataset for substation equipment time series data"""
    
    def __init__(self, sequences, targets, equipment_ids=None, equipment_types=None):
        """
        Args:
            sequences: numpy array of shape (n_samples, sequence_length, n_features)
            targets: numpy array of shape (n_samples,) or (n_samples, n_outputs)
            equipment_ids: list of equipment identifiers
            equipment_types: list of equipment types
        """
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
        self.equipment_ids = equipment_ids
        self.equipment_types = equipment_types
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

class MultiTaskLSTM(nn.Module):
    """
    Multi-task LSTM for substation equipment predictive maintenance
    Predicts degradation factor and classifies health status
    """
    
    def __init__(self, input_size, hidden_size=128, num_layers=3, 
                 dropout=0.2, bidirectional=True, n_classes=4):
        super(MultiTaskLSTM, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bidirectional = bidirectional
        self.n_classes = n_classes
        
        # LSTM layers with dropout
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
            batch_first=True
        )
        
        # Calculate LSTM output size
        lstm_output_size = hidden_size * 2 if bidirectional else hidden_size
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=lstm_output_size,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # Feature extraction layers
        self.feature_layers = nn.Sequential(
            nn.Linear(lstm_output_size, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(dropout),
        )
        
        # Regression head for degradation factor prediction
        self.regression_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Degradation factor between 0 and 1
        )
        
        # Classification head for health status
        self.classification_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, n_classes)
        )
        
        # Equipment type embedding (optional)
        self.equipment_embedding = nn.Embedding(3, 16)  # 3 equipment types
        
    def forward(self, x, equipment_type_ids=None):
        batch_size = x.size(0)
        
        # LSTM forward pass
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # Apply attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # Use the last timestep output for prediction
        last_output = attn_out[:, -1, :]
        
        # Optional: Add equipment type embedding
        if equipment_type_ids is not None:
            equipment_emb = self.equipment_embedding(equipment_type_ids)
            last_output = torch.cat([last_output, equipment_emb], dim=1)
        
        # Feature extraction
        features = self.feature_layers(last_output)
        
        # Multi-task outputs
        degradation_pred = self.regression_head(features)
        health_class_pred = self.classification_head(features)
        
        return degradation_pred.squeeze(), health_class_pred

class AdvancedLSTM(nn.Module):
    """
    Advanced LSTM with residual connections and layer normalization
    """
    
    def __init__(self, input_size, hidden_size=256, num_layers=4, 
                 dropout=0.3, use_residual=True):
        super(AdvancedLSTM, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.use_residual = use_residual
        
        # Multiple LSTM layers with residual connections
        self.lstm_layers = nn.ModuleList()
        self.layer_norms = nn.ModuleList()
        
        for i in range(num_layers):
            input_dim = input_size if i == 0 else hidden_size
            self.lstm_layers.append(
                nn.LSTM(input_dim, hidden_size, 1, 
                       dropout=0, bidirectional=False, batch_first=True)
            )
            self.layer_norms.append(nn.LayerNorm(hidden_size))
        
        self.dropout = nn.Dropout(dropout)
        
        # Temporal convolutional layers for feature extraction
        self.conv1d_layers = nn.Sequential(
            nn.Conv1d(hidden_size, 128, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv1d(128, 64, kernel_size=3, padding=1),
            nn.ReLU(),
        )
        
        # Final prediction layers
        self.prediction_layers = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        batch_size, seq_len, input_size = x.size()
        
        # Pass through LSTM layers with residual connections
        for i, (lstm_layer, layer_norm) in enumerate(zip(self.lstm_layers, self.layer_norms)):
            lstm_out, _ = lstm_layer(x)
            
            # Apply layer normalization
            lstm_out = layer_norm(lstm_out)
            
            # Residual connection (skip first layer)
            if self.use_residual and i > 0 and lstm_out.size(-1) == x.size(-1):
                lstm_out = lstm_out + x
            
            x = self.dropout(lstm_out)
        
        # Temporal convolution (batch_size, seq_len, features) -> (batch_size, features, seq_len)
        x = x.transpose(1, 2)
        conv_out = self.conv1d_layers(x)
        
        # Final prediction
        prediction = self.prediction_layers(conv_out)
        
        return prediction.squeeze()

class LSTMTrainer:
    """Trainer class for LSTM models with GPU optimization"""
    
    def __init__(self, model, device=device):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        
    def train_model(self, train_loader, val_loader, num_epochs=100, 
                   learning_rate=0.001, weight_decay=1e-5, patience=15):
        """
        Train the LSTM model with early stopping and learning rate scheduling
        """
        # Initialize optimizer and scheduler
        optimizer = optim.AdamW(self.model.parameters(), 
                               lr=learning_rate, weight_decay=weight_decay)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5)
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        print(f"Training on {self.device}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(num_epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            num_batches = 0
            
            for batch_idx, (data, targets) in enumerate(train_loader):
                data, targets = data.to(self.device), targets.to(self.device)

                optimizer.zero_grad()
                outputs = self.model(data)

                # Handle multi-task model output (tuple) vs single-task model output (tensor)
                if isinstance(outputs, tuple):
                    # Multi-task model: use only the regression output (degradation prediction)
                    degradation_pred, health_class_pred = outputs
                    loss = criterion(degradation_pred, targets)
                else:
                    # Single-task model: use output directly
                    loss = criterion(outputs, targets)

                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()
                num_batches += 1
                
                # Print progress every 100 batches
                if batch_idx % 100 == 0:
                    print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, '
                          f'Loss: {loss.item():.6f}')
            
            avg_train_loss = train_loss / num_batches
            self.train_losses.append(avg_train_loss)
            
            # Validation phase
            val_loss = self.evaluate(val_loader, criterion)
            self.val_losses.append(val_loss)
            
            # Learning rate scheduling
            scheduler.step(val_loss)
            
            print(f'Epoch {epoch+1}/{num_epochs}: '
                  f'Train Loss: {avg_train_loss:.6f}, Val Loss: {val_loss:.6f}')
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # Save best model
                torch.save(self.model.state_dict(), 'best_lstm_model.pth')
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                print(f'Early stopping at epoch {epoch+1}')
                break
        
        # Load best model
        self.model.load_state_dict(torch.load('best_lstm_model.pth'))
        print("Training completed!")
        
    def evaluate(self, data_loader, criterion):
        """Evaluate model on validation/test data"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for data, targets in data_loader:
                data, targets = data.to(self.device), targets.to(self.device)
                outputs = self.model(data)

                # Handle multi-task model output (tuple) vs single-task model output (tensor)
                if isinstance(outputs, tuple):
                    # Multi-task model: use only the regression output (degradation prediction)
                    degradation_pred, _ = outputs
                    loss = criterion(degradation_pred, targets)
                else:
                    # Single-task model: use output directly
                    loss = criterion(outputs, targets)

                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def predict(self, data_loader):
        """Make predictions on new data"""
        self.model.eval()
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for data, targets in data_loader:
                data = data.to(self.device)
                outputs = self.model(data)

                # Handle multi-task model output (tuple) vs single-task model output (tensor)
                if isinstance(outputs, tuple):
                    # Multi-task model: use only the regression output (degradation prediction)
                    degradation_pred, _ = outputs
                    predictions.extend(degradation_pred.cpu().numpy())
                else:
                    # Single-task model: use output directly
                    predictions.extend(outputs.cpu().numpy())

                actuals.extend(targets.numpy())
        
        return np.array(predictions), np.array(actuals)
    
    def plot_training_history(self):
        """Plot training and validation loss curves"""
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 2, 1)
        plt.plot(self.train_losses, label='Training Loss', alpha=0.8)
        plt.plot(self.val_losses, label='Validation Loss', alpha=0.8)
        plt.title('Model Loss During Training')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        plt.plot(self.train_losses[-50:], label='Training Loss (Last 50)', alpha=0.8)
        plt.plot(self.val_losses[-50:], label='Validation Loss (Last 50)', alpha=0.8)
        plt.title('Model Loss (Recent Training)')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

def prepare_data_loaders(X, y, batch_size=64, train_ratio=0.7, val_ratio=0.15):
    """
    Prepare PyTorch data loaders for training, validation, and testing
    """
    # Create dataset
    dataset = SubstationDataset(X, y)
    
    # Calculate split sizes
    total_size = len(dataset)
    train_size = int(train_ratio * total_size)
    val_size = int(val_ratio * total_size)
    test_size = total_size - train_size - val_size
    
    # Split dataset
    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size])
    
    # Create data loaders with GPU optimization
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True, 
        num_workers=4, pin_memory=True if torch.cuda.is_available() else False)
    
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False,
        num_workers=4, pin_memory=True if torch.cuda.is_available() else False)
    
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False,
        num_workers=4, pin_memory=True if torch.cuda.is_available() else False)
    
    return train_loader, val_loader, test_loader

def evaluate_model_performance(trainer, test_loader, plot_results=True):
    """
    Comprehensive model evaluation with metrics and visualizations
    """
    predictions, actuals = trainer.predict(test_loader)
    
    # Calculate metrics
    mse = mean_squared_error(actuals, predictions)
    mae = mean_absolute_error(actuals, predictions)
    r2 = r2_score(actuals, predictions)
    
    print("Model Performance Metrics:")
    print(f"Mean Squared Error: {mse:.6f}")
    print(f"Mean Absolute Error: {mae:.6f}")
    print(f"R² Score: {r2:.6f}")
    print(f"Root Mean Squared Error: {np.sqrt(mse):.6f}")
    
    if plot_results:
        _, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Predictions vs Actuals
        axes[0, 0].scatter(actuals, predictions, alpha=0.6)
        axes[0, 0].plot([actuals.min(), actuals.max()], 
                       [actuals.min(), actuals.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('Actual Degradation Factor')
        axes[0, 0].set_ylabel('Predicted Degradation Factor')
        axes[0, 0].set_title(f'Predictions vs Actuals (R² = {r2:.3f})')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Residuals plot
        residuals = predictions - actuals
        axes[0, 1].scatter(predictions, residuals, alpha=0.6)
        axes[0, 1].axhline(y=0, color='r', linestyle='--')
        axes[0, 1].set_xlabel('Predicted Degradation Factor')
        axes[0, 1].set_ylabel('Residuals')
        axes[0, 1].set_title('Residuals Plot')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Distribution of predictions
        axes[1, 0].hist(predictions, bins=50, alpha=0.7, label='Predictions', density=True)
        axes[1, 0].hist(actuals, bins=50, alpha=0.7, label='Actuals', density=True)
        axes[1, 0].set_xlabel('Degradation Factor')
        axes[1, 0].set_ylabel('Density')
        axes[1, 0].set_title('Distribution Comparison')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # Error distribution
        axes[1, 1].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 1].set_xlabel('Prediction Error')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].set_title(f'Error Distribution (MAE = {mae:.4f})')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    return {
        'mse': mse,
        'mae': mae,
        'r2': r2,
        'rmse': np.sqrt(mse),
        'predictions': predictions,
        'actuals': actuals
    }

# Example usage and training script
if __name__ == "__main__":
    # Load the synthetic dataset generated from previous step
    print("Loading synthetic dataset...")
    
    try:
        # Load pre-generated data
        X = np.load('lstm_sequences_X.npy')
        y = np.load('lstm_sequences_y.npy')
        
        print(f"Loaded data shapes: X={X.shape}, y={y.shape}")
        print(f"Target statistics: min={y.min():.3f}, max={y.max():.3f}, mean={y.mean():.3f}")
        
    except FileNotFoundError:
        print("Dataset files not found. Please run the dataset generator first.")
        print("Expected files: lstm_sequences_X.npy, lstm_sequences_y.npy")
        exit()
    
    # Prepare data loaders
    print("Preparing data loaders...")
    train_loader, val_loader, test_loader = prepare_data_loaders(
        X, y, batch_size=128, train_ratio=0.7, val_ratio=0.15)
    
    # Get dataset sizes for logging
    train_size = len(train_loader.dataset.indices)  # type: ignore
    val_size = len(val_loader.dataset.indices)  # type: ignore
    test_size = len(test_loader.dataset.indices)  # type: ignore
    print(f"Data splits: Train={train_size}, Val={val_size}, Test={test_size}")
    
    # Initialize model
    input_size = X.shape[2]  # Number of features
    
    # Option 1: Multi-task LSTM
    model = MultiTaskLSTM(
        input_size=input_size,
        hidden_size=128,
        num_layers=3,
        dropout=0.2,
        bidirectional=True
    )
    
    # Option 2: Advanced LSTM (uncomment to use)
    # model = AdvancedLSTM(
    #     input_size=input_size,
    #     hidden_size=256,
    #     num_layers=4,
    #     dropout=0.3,
    #     use_residual=True
    # )
    
    print(f"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Initialize trainer
    trainer = LSTMTrainer(model, device)
    
    # Train the model
    print("\nStarting training...")
    trainer.train_model(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=150,
        learning_rate=0.001,
        weight_decay=1e-5,
        patience=20
    )
    
    # Plot training history
    trainer.plot_training_history()
    
    # Evaluate model performance
    print("\nEvaluating model on test set...")
    results = evaluate_model_performance(trainer, test_loader, plot_results=True)
    
    # Save the trained model
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_config': {
            'input_size': input_size,
            'hidden_size': model.hidden_size if hasattr(model, 'hidden_size') else 128,
            'num_layers': model.num_layers if hasattr(model, 'num_layers') else 3,
        },
        'results': results
    }, 'trained_lstm_model.pth')
    
    print("\nModel saved as 'trained_lstm_model.pth'")
    print("Training completed successfully!")