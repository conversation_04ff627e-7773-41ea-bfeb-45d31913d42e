{"data_mtime": 1755649448, "dep_lines": [31, 33, 38, 52, 449, 1078, 2992, 4741, 27, 28, 29, 30, 32, 34, 36, 36, 36, 36, 37, 39, 41, 42, 50, 51, 53, 54, 71, 2094, 4673, 21, 27, 4673, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 22, 26, 4445, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2849, 24], "dep_prios": [5, 5, 5, 5, 20, 20, 20, 20, 10, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 25, 20, 20, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 10], "dependencies": ["torch.fx.experimental.symbolic_shapes", "torch.utils._sympy.symbol", "torch._inductor.codegen.common", "torch._inductor.runtime.runtime_utils", "torch._inductor.codegen.wrapper", "torch._inductor.codegen.simd", "torch._inductor.runtime.triton_heuristics", "torch._inductor.codegen.cuda_combined_scheduling", "torch._inductor.async_compile", "torch._dynamo.utils", "torch._inductor.codecache", "torch._inductor.metrics", "torch.utils._ordered_set", "torch.utils._triton", "torch._inductor.comms", "torch._inductor.config", "torch._inductor.dependencies", "torch._inductor.ir", "torch._inductor.analyze_preserves_zero_mask", "torch._inductor.comm_analysis", "torch._inductor.exc", "torch._inductor.fx_utils", "torch._inductor.loop_body", "torch._inductor.memory", "torch._inductor.sizevars", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor.debug", "torch._dynamo.convert_frame", "collections.abc", "torch._inductor", "torch._dynamo", "__future__", "collections", "dataclasses", "functools", "inspect", "itertools", "logging", "math", "operator", "os", "pprint", "textwrap", "traceback", "typing", "types", "torch", "heapq", "builtins", "html", "sys", "string", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "contextlib", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._guards", "torch._inductor.codegen", "torch._inductor.graph", "torch._inductor.select_algorithm", "torch._library", "torch._library.fake_class_registry", "torch._logging", "torch._logging._internal", "torch._tensor", "torch.fx", "torch.fx.interpreter", "torch.fx.node", "torch.utils", "typing_extensions"], "hash": "81c0b73733a4a566f58b0ebcab90ced967276e76", "id": "torch._inductor.scheduler", "ignore_all": true, "interface_hash": "548e9a1d5dd54919034c5bec23a9b22b4c27fc5b", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\scheduler.py", "plugin_data": null, "size": 200819, "suppressed": ["triton.compiler.errors", "sympy"], "version_id": "1.15.0"}