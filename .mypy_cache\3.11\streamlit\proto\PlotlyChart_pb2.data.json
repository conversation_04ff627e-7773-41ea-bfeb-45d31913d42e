{".class": "MypyFile", "_fullname": "streamlit.proto.PlotlyChart_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PlotlyChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "Figure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PlotlyChart_pb2.Figure", "name": "Figure", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.PlotlyChart_pb2.Figure", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PlotlyChart_pb2", "mro": ["streamlit.proto.PlotlyChart_pb2.Figure", "builtins.object"], "names": {".class": "SymbolTable", "CONFIG_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.Figure.CONFIG_FIELD_NUMBER", "name": "CONFIG_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.Figure.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.Figure", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "spec"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Figure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.Figure.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PlotlyChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "SPEC_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.Figure.SPEC_FIELD_NUMBER", "name": "SPEC_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "spec", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.Figure.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "spec", "config"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.Figure", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Figure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.Figure.config", "name": "config", "type": "builtins.str"}}, "spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.Figure.spec", "name": "spec", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PlotlyChart_pb2.Figure.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PlotlyChart_pb2.Figure", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PlotlyChart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PlotlyChart_pb2", "mro": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart", "builtins.object"], "names": {".class": "SymbolTable", "BOX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.BOX", "name": "BOX", "type": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType"}}, "CONFIG_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.CONFIG_FIELD_NUMBER", "name": "CONFIG_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chart"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "chart"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "figure"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "figure"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "form_id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "form_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "selection_mode"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "selection_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spec"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "spec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "theme"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "theme"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_container_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "use_container_width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of PlotlyChart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PlotlyChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "FIGURE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.FIGURE_FIELD_NUMBER", "name": "FIGURE_FIELD_NUMBER", "type": "builtins.int"}}, "FORM_ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.FORM_ID_FIELD_NUMBER", "name": "FORM_ID_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chart"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "chart"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "figure"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "figure"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "url"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of PlotlyChart", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ID_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.ID_FIELD_NUMBER", "name": "ID_FIELD_NUMBER", "type": "builtins.int"}}, "LASSO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.LASSO", "name": "LASSO", "type": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType"}}, "POINTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.POINTS", "name": "POINTS", "type": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType"}}, "SELECTION_MODE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.SELECTION_MODE_FIELD_NUMBER", "name": "SELECTION_MODE_FIELD_NUMBER", "type": "builtins.int"}}, "SPEC_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.SPEC_FIELD_NUMBER", "name": "SPEC_FIELD_NUMBER", "type": "builtins.int"}}, "SelectionMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.SelectionMode", "name": "SelectionMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.SelectionMode", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.PlotlyChart_pb2", "mro": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart.SelectionMode", "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.SelectionMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.SelectionMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "THEME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.THEME_FIELD_NUMBER", "name": "THEME_FIELD_NUMBER", "type": "builtins.int"}}, "URL_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.URL_FIELD_NUMBER", "name": "URL_FIELD_NUMBER", "type": "builtins.int"}}, "USE_CONTAINER_WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.USE_CONTAINER_WIDTH_FIELD_NUMBER", "name": "USE_CONTAINER_WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chart"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "chart"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of PlotlyChart", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "figure"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_SelectionMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode", "name": "_SelectionMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PlotlyChart_pb2", "mro": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.V", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PlotlyChart_pb2", "mro": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SelectionModeEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper", "name": "_SelectionModeEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.PlotlyChart_pb2", "mro": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "BOX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper.BOX", "name": "BOX", "type": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PlotlyChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "LASSO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper.LASSO", "name": "LASSO", "type": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType"}}, "POINTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper.POINTS", "name": "POINTS", "type": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionModeEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "use_container_width", "theme", "id", "selection_mode", "form_id", "spec", "config", "url", "figure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "use_container_width", "theme", "id", "selection_mode", "form_id", "spec", "config", "url", "figure"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart", "builtins.bool", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart._SelectionMode.ValueType"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["streamlit.proto.PlotlyChart_pb2.Figure", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.config", "name": "config", "type": "builtins.str"}}, "figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.figure", "name": "figure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "figure of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "streamlit.proto.PlotlyChart_pb2.Figure", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.figure", "name": "figure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "figure of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "streamlit.proto.PlotlyChart_pb2.Figure", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "form_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.form_id", "name": "form_id", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.id", "name": "id", "type": "builtins.str"}}, "selection_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.selection_mode", "name": "selection_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selection_mode of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PlotlyChart_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.selection_mode", "name": "selection_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.PlotlyChart_pb2.PlotlyChart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selection_mode of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PlotlyChart_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.spec", "name": "spec", "type": "builtins.str"}}, "theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.theme", "name": "theme", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.url", "name": "url", "type": "builtins.str"}}, "use_container_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.use_container_width", "name": "use_container_width", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.PlotlyChart_pb2.PlotlyChart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.PlotlyChart_pb2.PlotlyChart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.PlotlyChart_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___Figure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.PlotlyChart_pb2.global___Figure", "line": 131, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PlotlyChart_pb2.Figure"}}, "global___PlotlyChart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.PlotlyChart_pb2.global___PlotlyChart", "line": 111, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.PlotlyChart_pb2.PlotlyChart"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.PlotlyChart_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.PlotlyChart_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\PlotlyChart_pb2.pyi"}