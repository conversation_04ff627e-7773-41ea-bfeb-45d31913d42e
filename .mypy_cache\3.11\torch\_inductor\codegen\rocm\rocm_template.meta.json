{"data_mtime": 1755649448, "dep_lines": [15, 16, 17, 18, 14, 10, 11, 12, 13, 5, 8, 2, 3, 4, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30], "dependencies": ["torch._inductor.codegen.rocm.rocm_benchmark_request", "torch._inductor.codegen.rocm.rocm_kernel", "torch._inductor.codegen.rocm.rocm_template_buffer", "torch._inductor.codegen.rocm.rocm_utils", "torch._inductor.codegen.common", "torch._inductor.autotune_process", "torch._inductor.ir", "torch._inductor.utils", "torch._inductor.virtualized", "collections.abc", "unittest.mock", "functools", "itertools", "logging", "dataclasses", "typing", "builtins", "os", "torch", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc"], "hash": "b0e0c042f53ebb14537ef96099b8ba01ca841b89", "id": "torch._inductor.codegen.rocm.rocm_template", "ignore_all": true, "interface_hash": "5ba4b4a26a051dda402166221104f99b7311ed98", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\rocm\\rocm_template.py", "plugin_data": null, "size": 6829, "suppressed": [], "version_id": "1.15.0"}