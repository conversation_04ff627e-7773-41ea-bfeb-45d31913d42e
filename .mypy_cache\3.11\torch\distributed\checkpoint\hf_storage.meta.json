{"data_mtime": 1755649450, "dep_lines": [8, 9, 10, 25, 26, 34, 42, 43, 2, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 205], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["torch.distributed._shard._utils", "torch.distributed.checkpoint._fsspec_filesystem", "torch.distributed.checkpoint._hf_utils", "torch.distributed.checkpoint.filesystem", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.storage", "torch.futures", "dataclasses", "json", "queue", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "torch.distributed", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "json.decoder", "torch._C", "torch.distributed.checkpoint._extension", "torch.distributed.checkpoint.staging"], "hash": "21729577f70c33e653a4aaa5fe3a03851804e863", "id": "torch.distributed.checkpoint.hf_storage", "ignore_all": true, "interface_hash": "d771485f2299fbaa1cfe739245175f63d0027546", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\hf_storage.py", "plugin_data": null, "size": 13275, "suppressed": ["safetensors"], "version_id": "1.15.0"}