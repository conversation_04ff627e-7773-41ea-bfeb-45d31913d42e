{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cuda.serialization", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CUTLASSOperationSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer", "name": "CUTLASSOperationSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cuda.serialization", "mro": ["torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer", "builtins.object"], "names": {".class": "SymbolTable", "_SUPPORTED_CLASSES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._SUPPORTED_CLASSES", "name": "_SUPPORTED_CLASSES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_enum_to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "enum_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._enum_to_json", "name": "_enum_to_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._enum_to_json", "name": "_enum_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "enum_value"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_enum_to_json of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_gemm_operation_to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "operation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._gemm_operation_to_json", "name": "_gemm_operation_to_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._gemm_operation_to_json", "name": "_gemm_operation_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operation"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gemm_operation_to_json of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_json_to_enum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "json_dict", "enum_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._json_to_enum", "name": "_json_to_enum", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._json_to_enum", "name": "_json_to_enum", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "json_dict", "enum_class"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_json_to_enum of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_json_to_gemm_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "json_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._json_to_gemm_operation", "name": "_json_to_gemm_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._json_to_gemm_operation", "name": "_json_to_gemm_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json_dict"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_json_to_gemm_operation of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_json_to_tensor_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tensor_json"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._json_to_tensor_description", "name": "_json_to_tensor_description", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._json_to_tensor_description", "name": "_json_to_tensor_description", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tensor_json"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_json_to_tensor_description of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_json_to_tile_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "json_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._json_to_tile_description", "name": "_json_to_tile_description", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._json_to_tile_description", "name": "_json_to_tile_description", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json_dict"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_json_to_tile_description of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_tensor_description_to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tensor_desc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._tensor_description_to_json", "name": "_tensor_description_to_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._tensor_description_to_json", "name": "_tensor_description_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tensor_desc"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tensor_description_to_json of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_tile_description_to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tile_desc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._tile_description_to_json", "name": "_tile_description_to_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer._tile_description_to_json", "name": "_tile_description_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tile_desc"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tile_description_to_json of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "deserialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "json_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer.deserialize", "name": "deserialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json_str"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deserialize of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer.deserialize", "name": "deserialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json_str"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deserialize of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "operation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer.serialize", "name": "serialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operation"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer.serialize", "name": "serialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operation"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize of CUTLASSOperationSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.serialization.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.serialization.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.serialization.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.serialization.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.serialization.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.serialization.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_cutlass_operation_serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.cuda.serialization.get_cutlass_operation_serializer", "name": "get_cutlass_operation_serializer", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cutlass_operation_serializer", "ret_type": {".class": "UnionType", "items": ["torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.serialization.get_cutlass_operation_serializer", "name": "get_cutlass_operation_serializer", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.codegen.cuda.serialization.CUTLASSOperationSerializer", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "try_import_cutlass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_utils.try_import_cutlass", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\serialization.py"}