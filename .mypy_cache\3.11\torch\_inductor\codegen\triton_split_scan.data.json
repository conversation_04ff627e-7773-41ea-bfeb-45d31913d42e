{".class": "MypyFile", "_fullname": "torch._inductor.codegen.triton_split_scan", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CeilDiv": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.functions.CeilDiv", "kind": "Gdef"}, "IterationRangesRoot": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.simd.IterationRangesRoot", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "SplitScanGrid": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.triton_heuristics.SplitScanGrid", "kind": "Gdef"}, "TritonCSEVariable": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.triton.TritonCSEVariable", "kind": "Gdef"}, "TritonKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.triton.TritonKernel", "kind": "Gdef"}, "TritonSplitScanKernel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.triton.TritonKernel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel", "name": "TritonSplitScanKernel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.triton_split_scan", "mro": ["torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel", "torch._inductor.codegen.triton.TritonKernel", "torch._inductor.codegen.simd.SIMDKernel", "torch._inductor.codegen.common.Kernel", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "tiling", "pid_cache", "fixed_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "tiling", "pid_cache", "fixed_config", "kwargs"], "arg_types": ["torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.triton_split_scan.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TritonSplitScanKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_grid_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel._get_grid_type", "name": "_get_grid_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_grid_type of TritonSplitScanKernel", "ret_type": {".class": "TypeType", "item": "torch._inductor.runtime.triton_heuristics.SplitScanGrid"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_heuristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel._get_heuristic", "name": "_get_heuristic", "type": null}}, "initialize_range_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pid_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel.initialize_range_tree", "name": "initialize_range_tree", "type": null}}, "reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dtype", "src_dtype", "reduction_type", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel.reduction", "name": "reduction", "type": null}}, "scan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dtypes", "combine_fn", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel.scan", "name": "scan", "type": null}}, "should_use_cooperative_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel.should_use_cooperative_reduction", "name": "should_use_cooperative_reduction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_use_cooperative_reduction of TritonSplitScanKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_use_persistent_reduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel.should_use_persistent_reduction", "name": "should_use_persistent_reduction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_use_persistent_reduction of TritonSplitScanKernel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.triton_split_scan.TritonSplitScanKernel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.triton_split_scan.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.triton_split_scan.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.triton_split_scan.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.triton_split_scan.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.triton_split_scan.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.triton_split_scan.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "prefix_is_reduction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.prefix_is_reduction", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.triton_split_scan.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.triton_split_scan.sympy", "source_any": null, "type_of_any": 3}}}, "sympy_product": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_product", "kind": "Gdef"}, "triton_compute_type": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.triton.triton_compute_type", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\triton_split_scan.py"}