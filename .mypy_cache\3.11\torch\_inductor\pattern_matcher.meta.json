{"data_mtime": 1755649448, "dep_lines": [65, 66, 69, 60, 61, 62, 64, 67, 68, 70, 72, 73, 74, 77, 78, 79, 246, 708, 1161, 52, 58, 59, 60, 63, 72, 75, 77, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 55, 57, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 5, 20, 20, 20, 5, 10, 5, 20, 5, 20, 5, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.fx.experimental.proxy_tensor", "torch.fx.experimental.symbolic_shapes", "torch.fx.passes.graph_transform_observer", "torch.utils._pytree", "torch._dispatch.python", "torch._dynamo.utils", "torch._subclasses.fake_tensor", "torch.fx.graph_module", "torch.fx.immutable_collections", "torch.utils._ordered_set", "torch._functorch.config", "torch._functorch.aot_autograd", "torch._functorch.partitioners", "torch._inductor.config", "torch._inductor.decomposition", "torch._inductor.lowering", "torch._inductor.virtualized", "torch.fx.operator_schemas", "torch._higher_order_ops.utils", "collections.abc", "torch._guards", "torch.fx", "torch.utils", "torch._prims_common", "torch._functorch", "torch._subclasses", "torch._inductor", "__future__", "contextlib", "dataclasses", "functools", "importlib", "inspect", "itertools", "logging", "operator", "os", "re", "textwrap", "typing", "abc", "collections", "pathlib", "typing_extensions", "torch", "builtins", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "warnings", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "enum", "torch._C", "torch._ops", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.fx.graph", "torch.fx.node", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils._config_typing", "torch.utils._contextlib"], "hash": "d4c82b2c33fbd601700c6c88432dc3dfe5861417", "id": "torch._inductor.pattern_matcher", "ignore_all": true, "interface_hash": "be077624ad0f97621b757a2c4d243275d8cfd7c3", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\pattern_matcher.py", "plugin_data": null, "size": 82881, "suppressed": [], "version_id": "1.15.0"}