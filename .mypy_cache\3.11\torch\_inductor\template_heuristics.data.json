{".class": "MypyFile", "_fullname": "torch._inductor.template_heuristics", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.BaseConfig", "name": "BaseConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "block_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "block_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "block_k", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "num_stages", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "num_warps", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.BaseConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.template_heuristics.BaseConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "block_m", "block_n", "block_k", "num_stages", "num_warps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "block_m", "block_n", "block_k", "num_stages", "num_warps"], "arg_types": ["torch._inductor.template_heuristics.BaseConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.template_heuristics.BaseConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "block_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_stages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_warps"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.template_heuristics.BaseConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of BaseConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.template_heuristics.BaseConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of BaseConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "block_k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.BaseConfig.block_k", "name": "block_k", "type": "builtins.int"}}, "block_m": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.BaseConfig.block_m", "name": "block_m", "type": "builtins.int"}}, "block_n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.BaseConfig.block_n", "name": "block_n", "type": "builtins.int"}}, "num_stages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.BaseConfig.num_stages", "name": "num_stages", "type": "builtins.int"}}, "num_warps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.BaseConfig.num_warps", "name": "num_warps", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.BaseConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.BaseConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseConfigHeuristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic", "name": "BaseConfigHeuristic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic", "has_param_spec_type": false, "metaclass_type": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "metadata": {}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseConfigHeuristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_finalize_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "configs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic._finalize_mm_configs", "name": "_finalize_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "configs"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic", {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finalize_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prune_exhaustive_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "configs", "dtype_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic._prune_exhaustive_configs", "name": "_prune_exhaustive_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "configs", "dtype_size"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic", {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prune_exhaustive_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scale_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "m", "n", "k", "configs", "scale", "has_int8_tensor", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic._scale_mm_configs", "name": "_scale_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "m", "n", "k", "configs", "scale", "has_int8_tensor", "exclude"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.float", "builtins.bool", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scale_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conv_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.conv_configs", "name": "conv_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "exhaustive_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.exhaustive_configs", "name": "exhaustive_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "exhaustive_flex_attn_bwd_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.exhaustive_flex_attn_bwd_configs", "name": "exhaustive_flex_attn_bwd_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "exhaustive_flex_attn_fwd_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.exhaustive_flex_attn_fwd_configs", "name": "exhaustive_flex_attn_fwd_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "exhaustive_flex_decode_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.exhaustive_flex_decode_configs", "name": "exhaustive_flex_decode_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexDecodeConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "extra_mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.extra_mm_configs", "name": "extra_mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "flex_attn_bwd_autotune_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.flex_attn_bwd_autotune_configs", "name": "flex_attn_bwd_autotune_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "flex_attn_fwd_autotune_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.flex_attn_fwd_autotune_configs", "name": "flex_attn_fwd_autotune_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "flex_decode_autotune_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.flex_decode_autotune_configs", "name": "flex_decode_autotune_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexDecodeConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_conv_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_conv_configs", "name": "get_conv_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_conv_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_exhaustive_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_exhaustive_mm_configs", "name": "get_exhaustive_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_exhaustive_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_extra_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_extra_mm_configs", "name": "get_extra_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_extra_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_attn_bwd_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_flex_attn_bwd_configs", "name": "get_flex_attn_bwd_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_attn_bwd_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_attn_fwd_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_flex_attn_fwd_configs", "name": "get_flex_attn_fwd_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_attn_fwd_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_decode_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_flex_decode_configs", "name": "get_flex_decode_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_decode_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexDecodeConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_int8_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_int8_mm_configs", "name": "get_int8_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_int8_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mixed_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_mixed_mm_configs", "name": "get_mixed_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_mm_configs", "name": "get_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mm_plus_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_mm_plus_mm_configs", "name": "get_mm_plus_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mm_plus_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_persistent_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_persistent_mm_configs", "name": "get_persistent_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_persistent_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scaled_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_scaled_mm_configs", "name": "get_scaled_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scaled_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scaled_persistent_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.get_scaled_persistent_mm_configs", "name": "get_scaled_persistent_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scaled_persistent_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "int8_mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.int8_mm_configs", "name": "int8_mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "mixed_mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.mixed_mm_configs", "name": "mixed_mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.mm_configs", "name": "mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "mm_plus_mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.mm_plus_mm_configs", "name": "mm_plus_mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "persistent_mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.persistent_mm_configs", "name": "persistent_mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "preprocess_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "m", "n", "k", "configs", "has_int8_tensor", "scale", "exclude", "dtype_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.preprocess_mm_configs", "name": "preprocess_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "m", "n", "k", "configs", "has_int8_tensor", "scale", "exclude", "dtype_size"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preprocess_mm_configs of BaseConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scaled_mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.scaled_mm_configs", "name": "scaled_mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "scaled_persistent_mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.scaled_persistent_mm_configs", "name": "scaled_persistent_mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "triton_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "num_stages", "num_warps", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.triton_config", "name": "triton_config", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "num_stages", "num_warps", "kwargs"], "arg_types": ["torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "triton_config of BaseConfigHeuristic", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.BaseConfigHeuristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.BaseConfigHeuristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseHeuristicSingleton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "name": "BaseHeuristicSingleton", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.BaseHeuristicSingleton", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.BaseHeuristicSingleton.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": ["torch._inductor.template_heuristics.BaseHeuristicSingleton", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of BaseHeuristicSingleton", "ret_type": "torch._inductor.template_heuristics.BaseConfigHeuristic", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.BaseHeuristicSingleton._instances", "name": "_instances", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.BaseHeuristicSingleton._lock", "name": "_lock", "type": "_thread.LockType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.BaseHeuristicSingleton.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CPUConfigHeuristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.CPUConfigHeuristic", "name": "CPUConfigHeuristic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.CPUConfigHeuristic", "has_param_spec_type": false, "metaclass_type": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "metadata": {}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.CPUConfigHeuristic", "torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.CPUConfigHeuristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.CPUConfigHeuristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUDAConfigHeuristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic", "name": "CUDAConfigHeuristic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic", "has_param_spec_type": false, "metaclass_type": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "metadata": {}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.CUDAConfigHeuristic", "torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.CUDAConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDAConfigHeuristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "a100_default_flex_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic.a100_default_flex_config", "name": "a100_default_flex_config", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch._C.dtype", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._C.dtype", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_flex_attn_bwd_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic.get_flex_attn_bwd_configs", "name": "get_flex_attn_bwd_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.CUDAConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_attn_bwd_configs of CUDAConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_attn_fwd_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic.get_flex_attn_fwd_configs", "name": "get_flex_attn_fwd_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.CUDAConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_attn_fwd_configs of CUDAConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_decode_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic.get_flex_decode_configs", "name": "get_flex_decode_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.CUDAConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_decode_configs of CUDAConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexDecodeConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "h100_default_flex_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic.h100_default_flex_config", "name": "h100_default_flex_config", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch._C.dtype", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._C.dtype", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.CUDAConfigHeuristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.CUDAConfigHeuristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ConvConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.template_heuristics.ConvConfig", "line": 47, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "torch._inductor.template_heuristics.BaseConfig"}}, "FlexConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.FlexConfig", "name": "FlexConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.FlexConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "block_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "block_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "num_stages", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "num_warps", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.FlexConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "block_m", "block_n", "num_stages", "num_warps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.FlexConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "block_m", "block_n", "num_stages", "num_warps"], "arg_types": ["torch._inductor.template_heuristics.FlexConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FlexConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.template_heuristics.FlexConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "block_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_stages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_warps"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["block_m", "block_n", "num_stages", "num_warps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.template_heuristics.FlexConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["block_m", "block_n", "num_stages", "num_warps"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FlexConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["block_m", "block_n", "num_stages", "num_warps"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FlexConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "block_m": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexConfig.block_m", "name": "block_m", "type": "builtins.int"}}, "block_n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexConfig.block_n", "name": "block_n", "type": "builtins.int"}}, "num_stages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexConfig.num_stages", "name": "num_stages", "type": "builtins.int"}}, "num_warps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexConfig.num_warps", "name": "num_warps", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.FlexConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.FlexConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlexDecodeConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig", "name": "FlexDecodeConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "block_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "num_stages", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 76, "name": "num_warps", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.FlexDecodeConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "block_n", "num_stages", "num_warps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "block_n", "num_stages", "num_warps"], "arg_types": ["torch._inductor.template_heuristics.FlexDecodeConfig", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FlexDecodeConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "block_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_stages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_warps"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["block_n", "num_stages", "num_warps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["block_n", "num_stages", "num_warps"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FlexDecodeConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["block_n", "num_stages", "num_warps"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FlexDecodeConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "block_n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.block_n", "name": "block_n", "type": "builtins.int"}}, "num_stages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.num_stages", "name": "num_stages", "type": "builtins.int"}}, "num_warps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.num_warps", "name": "num_warps", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.FlexDecodeConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.FlexDecodeConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GemmConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.BaseConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.GemmConfig", "name": "GemmConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.GemmConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "block_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "block_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "block_k", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "num_stages", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "num_warps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "group_m", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.GemmConfig", "torch._inductor.template_heuristics.BaseConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.template_heuristics.GemmConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.GemmConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m"], "arg_types": ["torch._inductor.template_heuristics.GemmConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.template_heuristics.GemmConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "block_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_stages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_warps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "group_m"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.template_heuristics.GemmConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of GemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.template_heuristics.GemmConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of GemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "group_m": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.GemmConfig.group_m", "name": "group_m", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.GemmConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.GemmConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Lock": {".class": "SymbolTableNode", "cross_ref": "threading.Lock", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "ROCmConfigHeuristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic", "name": "ROCmConfigHeuristic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic", "has_param_spec_type": false, "metaclass_type": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "metadata": {}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.ROCmConfigHeuristic", "torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ROCmConfigHeuristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_filter_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "configs", "new_num_stages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic._filter_configs", "name": "_filter_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "configs", "new_num_stages"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic", {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_finalize_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "configs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic._finalize_mm_configs", "name": "_finalize_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "configs"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic", {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finalize_mm_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_flex_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.default_flex_config", "name": "default_flex_config", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch._C.dtype", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._C.dtype", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "torch._inductor.template_heuristics.ROCmFlexConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "default_num_stages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.default_num_stages", "name": "default_num_stages", "type": "builtins.int"}}, "exhaustive_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.exhaustive_configs", "name": "exhaustive_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "exhaustive_flex_attn_bwd_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.exhaustive_flex_attn_bwd_configs", "name": "exhaustive_flex_attn_bwd_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "exhaustive_flex_attn_fwd_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.exhaustive_flex_attn_fwd_configs", "name": "exhaustive_flex_attn_fwd_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "exhaustive_flex_decode_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.exhaustive_flex_decode_configs", "name": "exhaustive_flex_decode_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexDecodeConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "flex_attn_bwd_autotune_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.flex_attn_bwd_autotune_configs", "name": "flex_attn_bwd_autotune_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "flex_attn_fwd_autotune_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.flex_attn_fwd_autotune_configs", "name": "flex_attn_fwd_autotune_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "flex_decode_autotune_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.flex_decode_autotune_configs", "name": "flex_decode_autotune_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexDecodeConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_conv_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_conv_configs", "name": "get_conv_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_conv_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_extra_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_extra_mm_configs", "name": "get_extra_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_extra_mm_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_attn_bwd_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_flex_attn_bwd_configs", "name": "get_flex_attn_bwd_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_attn_bwd_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_attn_fwd_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_flex_attn_fwd_configs", "name": "get_flex_attn_fwd_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_attn_fwd_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flex_decode_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_flex_decode_configs", "name": "get_flex_decode_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "head_dim", "dtype"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flex_decode_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.FlexDecodeConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_int8_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_int8_mm_configs", "name": "get_int8_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_int8_mm_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mixed_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_mixed_mm_configs", "name": "get_mixed_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_mm_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mm_plus_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_mm_plus_mm_configs", "name": "get_mm_plus_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mm_plus_mm_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_persistent_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_persistent_mm_configs", "name": "get_persistent_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_persistent_mm_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scaled_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_scaled_mm_configs", "name": "get_scaled_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scaled_mm_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scaled_persistent_mm_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.get_scaled_persistent_mm_configs", "name": "get_scaled_persistent_mm_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.template_heuristics.ROCmConfigHeuristic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scaled_persistent_mm_configs of ROCmConfigHeuristic", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "extra_attrs": null, "type_ref": "functools.partial"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mm_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.mm_configs", "name": "mm_configs", "type": {".class": "Instance", "args": ["torch._inductor.template_heuristics.BaseConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.ROCmConfigHeuristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.ROCmConfigHeuristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROCmConvConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.BaseConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.ROCmConvConfig", "name": "ROCmConvConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "block_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "block_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "block_k", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "num_stages", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "num_warps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 97, "name": "matrix_instr_nonkdim", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 98, "name": "waves_per_eu", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 99, "name": "kpack", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.ROCmConvConfig", "torch._inductor.template_heuristics.BaseConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "block_m", "block_n", "block_k", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "block_m", "block_n", "block_k", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["torch._inductor.template_heuristics.ROCmConvConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ROCmConvConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "block_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_stages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_warps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "matrix_instr_nonkdim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "waves_per_eu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "kpack"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ROCmConvConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ROCmConvConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "kpack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.kpack", "name": "kpack", "type": "builtins.int"}}, "matrix_instr_nonkdim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.matrix_instr_nonkdim", "name": "matrix_instr_nonkdim", "type": "builtins.int"}}, "waves_per_eu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.waves_per_eu", "name": "waves_per_eu", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.ROCmConvConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.ROCmConvConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROCmFlexConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.FlexConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig", "name": "ROCmFlexConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "block_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "block_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "num_stages", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "num_warps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 108, "name": "matrix_instr_nonkdim", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 109, "name": "waves_per_eu", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 110, "name": "kpack", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.ROCmFlexConfig", "torch._inductor.template_heuristics.FlexConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "block_m", "block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "block_m", "block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["torch._inductor.template_heuristics.ROCmFlexConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ROCmFlexConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "block_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_stages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_warps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "matrix_instr_nonkdim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "waves_per_eu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "kpack"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ROCmFlexConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ROCmFlexConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "kpack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.kpack", "name": "kpack", "type": "builtins.int"}}, "matrix_instr_nonkdim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.matrix_instr_nonkdim", "name": "matrix_instr_nonkdim", "type": "builtins.int"}}, "waves_per_eu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.waves_per_eu", "name": "waves_per_eu", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.ROCmFlexConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.ROCmFlexConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROCmFlexDecodeConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.FlexDecodeConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig", "name": "ROCmFlexDecodeConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "block_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "num_stages", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 76, "name": "num_warps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 119, "name": "matrix_instr_nonkdim", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 120, "name": "waves_per_eu", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 121, "name": "kpack", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.ROCmFlexDecodeConfig", "torch._inductor.template_heuristics.FlexDecodeConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["torch._inductor.template_heuristics.ROCmFlexDecodeConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ROCmFlexDecodeConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "block_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_stages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_warps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "matrix_instr_nonkdim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "waves_per_eu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "kpack"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ROCmFlexDecodeConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["block_n", "num_stages", "num_warps", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ROCmFlexDecodeConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "kpack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.kpack", "name": "kpack", "type": "builtins.int"}}, "matrix_instr_nonkdim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.matrix_instr_nonkdim", "name": "matrix_instr_nonkdim", "type": "builtins.int"}}, "waves_per_eu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.waves_per_eu", "name": "waves_per_eu", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.ROCmFlexDecodeConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROCmGemmConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.GemmConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig", "name": "ROCmGemmConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "block_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "block_n", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "block_k", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "num_stages", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "num_warps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "group_m", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 86, "name": "matrix_instr_nonkdim", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 87, "name": "waves_per_eu", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 88, "name": "kpack", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.ROCmGemmConfig", "torch._inductor.template_heuristics.GemmConfig", "torch._inductor.template_heuristics.BaseConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["torch._inductor.template_heuristics.ROCmGemmConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ROCmGemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "block_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_stages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_warps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "group_m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "matrix_instr_nonkdim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "waves_per_eu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "kpack"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ROCmGemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["block_m", "block_n", "block_k", "num_stages", "num_warps", "group_m", "matrix_instr_nonkdim", "waves_per_eu", "kpack"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ROCmGemmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "kpack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.kpack", "name": "kpack", "type": "builtins.int"}}, "matrix_instr_nonkdim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.matrix_instr_nonkdim", "name": "matrix_instr_nonkdim", "type": "builtins.int"}}, "waves_per_eu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.waves_per_eu", "name": "waves_per_eu", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.ROCmGemmConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.ROCmGemmConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TritonConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.template_heuristics.TritonConfig", "name": "TritonConfig", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.template_heuristics.TritonConfig", "source_any": null, "type_of_any": 3}}}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "XPUConfigHeuristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.template_heuristics.BaseConfigHeuristic"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.template_heuristics.XPUConfigHeuristic", "name": "XPUConfigHeuristic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.template_heuristics.XPUConfigHeuristic", "has_param_spec_type": false, "metaclass_type": "torch._inductor.template_heuristics.BaseHeuristicSingleton", "metadata": {}, "module_name": "torch._inductor.template_heuristics", "mro": ["torch._inductor.template_heuristics.XPUConfigHeuristic", "torch._inductor.template_heuristics.BaseConfigHeuristic", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.template_heuristics.XPUConfigHeuristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.template_heuristics.XPUConfigHeuristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.template_heuristics.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.template_heuristics.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.template_heuristics.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.template_heuristics.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.template_heuristics.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.template_heuristics.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "get_backend_num_stages": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_backend_num_stages", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\template_heuristics.py"}