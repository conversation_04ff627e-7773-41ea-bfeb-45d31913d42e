{"data_mtime": 1755649448, "dep_lines": [28, 27, 27, 28, 26, 27, 16, 25, 13, 15, 16, 17, 18, 19, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 5, 20, 10, 10, 5, 10, 20, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.exporter._torchlib._torchlib_registry", "torch.onnx._internal.exporter._constants", "torch.onnx._internal.exporter._schemas", "torch.onnx._internal.exporter._torchlib", "torch.onnx._internal._lazy_import", "torch.onnx._internal.exporter", "importlib.util", "torch._ops", "__future__", "dataclasses", "importlib", "logging", "math", "operator", "types", "typing", "typing_extensions", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc"], "hash": "c5a90ba6ba3b1efe5b86bf7cfc4f13b05ddda3d5", "id": "torch.onnx._internal.exporter._registration", "ignore_all": true, "interface_hash": "00fa06b825aa5bbd3dee12617da571502c7b80ab", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_registration.py", "plugin_data": null, "size": 12736, "suppressed": [], "version_id": "1.15.0"}