{".class": "MypyFile", "_fullname": "torch._export.passes.insert_custom_op_guards", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "OpProfile": {".class": "SymbolTableNode", "cross_ref": "torch._library.fake_profile.OpProfile", "kind": "Gdef"}, "TensorMetadata": {".class": "SymbolTableNode", "cross_ref": "torch._library.fake_profile.TensorMetadata", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.insert_custom_op_guards.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.insert_custom_op_guards.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.insert_custom_op_guards.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.insert_custom_op_guards.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.insert_custom_op_guards.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.insert_custom_op_guards.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_node_metadata_hook": {".class": "SymbolTableNode", "cross_ref": "torch._export.passes._node_metadata_hook._node_metadata_hook", "kind": "Gdef"}, "_set_node_metadata_hook": {".class": "SymbolTableNode", "cross_ref": "torch._export.passes._node_metadata_hook._set_node_metadata_hook", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_op_profiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "ops_to_guard"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.passes.insert_custom_op_guards.get_op_profiles", "name": "get_op_profiles", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["gm", "ops_to_guard"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_op_profiles", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["torch._library.fake_profile.OpProfile"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "insert_custom_op_guards": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["gm", "ops_to_guard"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.passes.insert_custom_op_guards.insert_custom_op_guards", "name": "insert_custom_op_guards", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["gm", "ops_to_guard"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_custom_op_guards", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_export\\passes\\insert_custom_op_guards.py"}