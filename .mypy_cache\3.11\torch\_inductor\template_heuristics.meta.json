{"data_mtime": 1755649448, "dep_lines": [474, 11, 13, 14, 15, 13, 19, 1, 3, 4, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 21], "dep_prios": [20, 5, 10, 5, 5, 20, 25, 5, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["torch._inductor.runtime.runtime_utils", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor", "collections.abc", "__future__", "dataclasses", "itertools", "math", "functools", "threading", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_thread", "_typeshed", "abc", "torch._C", "torch.utils", "typing_extensions"], "hash": "a55e376ee4ebc4715fde8dc63b2a79e903e91e86", "id": "torch._inductor.template_heuristics", "ignore_all": true, "interface_hash": "b2aa59f2c91fabfd79fe8cc1c2341b979402bf03", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\template_heuristics.py", "plugin_data": null, "size": 45746, "suppressed": ["triton"], "version_id": "1.15.0"}