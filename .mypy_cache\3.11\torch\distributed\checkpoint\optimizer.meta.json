{"data_mtime": 1755649450, "dep_lines": [10, 11, 14, 15, 16, 17, 18, 27, 28, 32, 33, 34, 40, 39, 41, 42, 4, 8, 9, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed._shard.sharded_tensor.api", "torch.distributed._shard.sharded_tensor.metadata", "torch.distributed._shard.sharded_tensor.shard", "torch.distributed._shard.sharding_spec.chunk_sharding_spec", "torch.distributed.checkpoint._nested_dict", "torch.distributed.checkpoint.default_planner", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.planner_helpers", "torch.distributed.checkpoint.state_dict_loader", "torch.distributed.checkpoint.storage", "torch.distributed.checkpoint.utils", "torch.distributed.fsdp._shard_utils", "torch.distributed.distributed_c10d", "torch.distributed.remote_device", "torch.distributed.tensor", "collections.abc", "torch.distributed", "torch._utils", "dataclasses", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.distributed._shard", "torch.distributed._shard.metadata", "torch.distributed._shard.sharded_tensor", "torch.distributed._shard.sharding_spec", "torch.distributed._shard.sharding_spec.api"], "hash": "eed243585b3b86e8ed4c87a4b62af1c4f7ddae58", "id": "torch.distributed.checkpoint.optimizer", "ignore_all": true, "interface_hash": "6beb17bfd5d04ca8d038b6fcdf8a1b36772a690b", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\optimizer.py", "plugin_data": null, "size": 13510, "suppressed": [], "version_id": "1.15.0"}