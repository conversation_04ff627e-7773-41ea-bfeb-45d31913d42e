{"data_mtime": 1755656860, "dep_lines": [3, 6, 16, 17, 18, 1, 4, 5, 8, 9, 12, 16, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "urllib.parse", "referencing.exceptions", "referencing._attrs", "referencing.typing", "__future__", "enum", "typing", "attrs", "rpds", "typing_extensions", "referencing", "builtins", "_frozen_importlib", "abc", "attr", "attr.setters", "types"], "hash": "57d23321cfc12bdaed8e81397be17e64250343c6", "id": "referencing._core", "ignore_all": true, "interface_hash": "001c6b623b56911189b9dcf4b1c02b440effee2b", "mtime": 1755656309, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\referencing\\_core.py", "plugin_data": null, "size": 24830, "suppressed": [], "version_id": "1.15.0"}