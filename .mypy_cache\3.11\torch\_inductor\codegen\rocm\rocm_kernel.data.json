{".class": "MypyFile", "_fullname": "torch._inductor.codegen.rocm.rocm_kernel", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgInfo": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.rocm_template.ArgInfo", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ChoiceCaller": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.ChoiceCaller", "kind": "Gdef"}, "CppPrinter": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.CppPrinter", "kind": "Gdef"}, "CppWrapperCpu": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_wrapper_cpu.CppWrapperCpu", "kind": "Gdef"}, "DTYPE_TO_ROCM_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.rocm_utils.DTYPE_TO_ROCM_TYPE", "kind": "Gdef"}, "IRNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.IRNode", "kind": "Gdef"}, "Kernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.Kernel", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Layout", "kind": "Gdef"}, "OpOverrides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.OpOverrides", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PrimitiveInfoType": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.PrimitiveInfoType", "kind": "Gdef"}, "ROCmBenchmarkRequest": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.rocm_benchmark_request.ROCmBenchmarkRequest", "kind": "Gdef"}, "ROCmKernel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["torch._inductor.codegen.common.CSEVariable"], "extra_attrs": null, "type_ref": "torch._inductor.codegen.common.Kernel"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmKernel", "name": "ROCmKernel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmKernel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.rocm.rocm_kernel", "mro": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmKernel", "torch._inductor.codegen.common.Kernel", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "overrides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmKernel.overrides", "name": "overrides", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["torch._inductor.codegen.common.OpOverrides"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._inductor.codegen.common.OpOverrides", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmKernel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.rocm.rocm_kernel.ROCmKernel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROCmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.rocm_template.ROCmTemplate", "kind": "Gdef"}, "ROCmTemplateBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.rocm_template_buffer.ROCmTemplateBuffer", "kind": "Gdef"}, "ROCmTemplateCaller": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.ir.ChoiceCaller"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller", "name": "ROCmTemplateCaller", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.rocm.rocm_kernel", "mro": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller", "torch._inductor.ir.ChoiceCaller", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "category", "input_nodes", "layout", "make_kernel_render", "bmreq", "template", "info_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "category", "input_nodes", "layout", "make_kernel_render", "bmreq", "template", "info_kwargs"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller", "builtins.str", "builtins.str", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._inductor.codegen.rocm.rocm_template_buffer.ROCmTemplateBuffer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch._inductor.codegen.rocm.rocm_benchmark_request.ROCmBenchmarkRequest", "torch._inductor.codegen.rocm.rocm_template.ROCmTemplate", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ROCmTemplateCaller", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of ROCmTemplateCaller", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "benchmark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3], "arg_names": ["self", "args", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.benchmark", "name": "benchmark", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3], "arg_names": ["self", "args", "out"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "benchmark of ROCmTemplateCaller", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bmreq": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.bmreq", "name": "bmreq", "type": "torch._inductor.codegen.rocm.rocm_benchmark_request.ROCmBenchmarkRequest"}}, "call_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.call_name", "name": "call_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_name of ROCmTemplateCaller", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "category": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.category", "name": "category", "type": "builtins.str"}}, "hash_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.hash_key", "name": "hash_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hash_key of ROCmTemplateCaller", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.info_dict", "name": "info_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info_dict of ROCmTemplateCaller", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.info_kwargs", "name": "info_kwargs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.ir.PrimitiveInfoType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "make_kernel_render": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.make_kernel_render", "name": "make_kernel_render", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._inductor.codegen.rocm.rocm_template_buffer.ROCmTemplateBuffer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.output_node", "name": "output_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output_node of ROCmTemplateCaller", "ret_type": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "precompile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.precompile", "name": "precompile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "precompile of ROCmTemplateCaller", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "template": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.template", "name": "template", "type": "torch._inductor.codegen.rocm.rocm_template.ROCmTemplate"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateCaller", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROCmTemplateKernel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmKernel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", "name": "ROCmTemplateKernel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.rocm.rocm_kernel", "mro": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", "torch._inductor.codegen.rocm.rocm_kernel.ROCmKernel", "torch._inductor.codegen.common.Kernel", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "_EXTRA_CPP_ARGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel._EXTRA_CPP_ARGS", "name": "_EXTRA_CPP_ARGS", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kernel_name", "runtime_arg_info", "runtime_arg_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "kernel_name", "runtime_arg_info", "runtime_arg_values"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", "builtins.str", {".class": "Instance", "args": ["torch._inductor.codegen.rocm.rocm_template.ArgInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ROCmTemplateKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.call_kernel", "name": "call_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "node"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", "builtins.str", "torch._inductor.codegen.rocm.rocm_template_buffer.ROCmTemplateBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_kernel of ROCmTemplateKernel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "def_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "inputs", "outputs", "size_args", "names_str", "input_reorder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.def_kernel", "name": "def_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "inputs", "outputs", "size_args", "names_str", "input_reorder"], "arg_types": ["torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "def_kernel of ROCmTemplateKernel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.get_signature", "name": "get_signature", "type": null}}, "named_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.named_nodes", "name": "named_nodes", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "runtime_arg_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.runtime_arg_info", "name": "runtime_arg_info", "type": {".class": "Instance", "args": ["torch._inductor.codegen.rocm.rocm_template.ArgInfo"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "runtime_arg_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.runtime_arg_values", "name": "runtime_arg_values", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "signature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.signature", "name": "signature", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.rocm.rocm_kernel.ROCmTemplateKernel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TensorBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "WorkspaceArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceArg", "kind": "Gdef"}, "WorkspaceZeroMode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceZeroMode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_normalize_idx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["index", "total_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.rocm.rocm_kernel._normalize_idx", "name": "_normalize_idx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["index", "total_length"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_idx", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cexpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.cexpr", "name": "cexpr", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["expr", "simplify", "p"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["torch._inductor.codegen.cpp_utils.CppPrinter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "do_bench_using_profiling": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.do_bench_using_profiling", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.rocm.rocm_kernel.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\rocm\\rocm_kernel.py"}