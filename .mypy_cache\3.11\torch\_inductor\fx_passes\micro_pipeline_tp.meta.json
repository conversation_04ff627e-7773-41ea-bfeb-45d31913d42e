{"data_mtime": 1755649448, "dep_lines": [10, 12, 12, 13, 12, 2, 3, 4, 5, 6, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 20, 10, 10, 5, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.inductor_prims", "torch._inductor.pattern_matcher", "torch._inductor", "logging", "operator", "collections", "dataclasses", "math", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "enum", "torch._C", "torch._ops", "torch._tensor", "torch.fx", "torch.fx.graph", "torch.fx.node", "torch.utils"], "hash": "3ce38a224f3cf9705b97be3e078447714ff45fd5", "id": "torch._inductor.fx_passes.micro_pipeline_tp", "ignore_all": true, "interface_hash": "b202faeb14208cefb796ca7a45c8841f9814301b", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\micro_pipeline_tp.py", "plugin_data": null, "size": 40086, "suppressed": [], "version_id": "1.15.0"}