{"data_mtime": 1755649448, "dep_lines": [12, 16, 5, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 20, 20], "dependencies": ["torch._dynamo.decorators", "collections.abc", "__future__", "itertools", "sys", "typing", "typing_extensions", "builtins", "collections", "warnings", "torch", "operator", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "json", "traceback", "re", "html", "_frozen_importlib", "_typeshed", "abc", "contextlib"], "hash": "e83e2df89ddacc0c30570f3a948ffacb01364ce6", "id": "torch._dynamo.polyfills.itertools", "ignore_all": true, "interface_hash": "c00701d939c68070b4b5d7a2e6634aa3d1372f38", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_dynamo\\polyfills\\itertools.py", "plugin_data": null, "size": 6190, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}