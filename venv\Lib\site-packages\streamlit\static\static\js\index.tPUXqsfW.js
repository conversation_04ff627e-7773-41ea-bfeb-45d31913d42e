import{s as a,r as e,aE as t,j as r,aJ as o}from"./index.DKN5MVff.js";import{P as i}from"./ProgressBar.COK9j1l0.js";const l=a("div",{target:"e1675qd10"})(({theme:s})=>({paddingBottom:s.spacing.sm,lineHeight:"normal"}));function d({element:s}){return t("div",{className:"stProgress","data-testid":"stProgress",children:[r(l,{children:r(o,{source:s.text,allowHTML:!1,isLabel:!0})}),r(i,{value:s.value})]})}const m=e.memo(d);export{m as default};
