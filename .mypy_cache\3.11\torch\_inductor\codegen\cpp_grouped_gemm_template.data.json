{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cpp_grouped_gemm_template", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ChoiceCaller": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.ChoiceCaller", "kind": "Gdef"}, "CppGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "kind": "Gdef"}, "CppGroupedGemmTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate", "name": "CppGroupedGemmTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_grouped_gemm_template", "mro": ["torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate", "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "torch._inductor.codegen.cpp_template.CppTemplate", "torch._inductor.codegen.common.KernelTemplate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_nodes", "layout", "num_threads", "register_blocking", "beta", "alpha", "has_bias", "epilogue_creator", "act_mapping", "gemm_grouped_num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_nodes", "layout", "num_threads", "register_blocking", "beta", "alpha", "has_bias", "epilogue_creator", "act_mapping", "gemm_grouped_num"], "arg_types": ["torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}, "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._inductor.ir.Pointwise", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CppGroupedGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate.act_mapping", "name": "act_mapping", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "add_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "choices", "layout", "input_nodes", "beta", "alpha", "has_bias", "trans_w", "input_indices", "epilogue_creator", "act_mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate.add_choices", "name": "add_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "choices", "layout", "input_nodes", "beta", "alpha", "has_bias", "trans_w", "input_indices", "epilogue_creator", "act_mapping"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate"}, {".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._inductor.ir.Pointwise", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_choices of CppGroupedGemmTemplate", "ret_type": "torch._inductor.select_algorithm.DataProcessorTemplateWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate.add_choices", "name": "add_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "choices", "layout", "input_nodes", "beta", "alpha", "has_bias", "trans_w", "input_indices", "epilogue_creator", "act_mapping"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate"}, {".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._inductor.ir.Pointwise", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_choices of CppGroupedGemmTemplate", "ret_type": "torch._inductor.select_algorithm.DataProcessorTemplateWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gemm_grouped_num": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate.gemm_grouped_num", "name": "gemm_grouped_num", "type": "builtins.int"}}, "output_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate.output_node", "name": "output_node", "type": {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "kernel", "template_buffer_node", "flag_template_buffer_has_other_users", "epilogue_nodes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "kernel", "template_buffer_node", "flag_template_buffer_has_other_users", "epilogue_nodes", "kwargs"], "arg_types": ["torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel", {".class": "UnionType", "items": ["torch._inductor.ir.CppTemplateBuffer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of CppGroupedGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_grouped_gemm_template.CppGroupedGemmTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroGemmAMX": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "kind": "Gdef"}, "CppTemplateKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel", "kind": "Gdef"}, "DTYPE_TO_CPP": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.DTYPE_TO_CPP", "kind": "Gdef"}, "DataProcessorTemplateWrapper": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.DataProcessorTemplateWrapper", "kind": "Gdef"}, "GEMM_TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.GEMM_TEMPLATE", "name": "GEMM_TEMPLATE", "type": "builtins.str"}}, "GemmBlocking": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "create_epilogue_with_attr": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.create_epilogue_with_attr", "kind": "Gdef"}, "create_micro_gemm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.create_micro_gemm", "kind": "Gdef"}, "expand_bias": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.expand_bias", "kind": "Gdef"}, "gen_2d_view_of_epilogue_buf": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.gen_2d_view_of_epilogue_buf", "kind": "Gdef"}, "get_deduplicated_act": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["act_mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.get_deduplicated_act", "name": "get_deduplicated_act", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["act_mapping"], "arg_types": [{".class": "Instance", "args": ["builtins.int", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_deduplicated_act", "ret_type": {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_export_declaration": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp.get_export_declaration", "kind": "Gdef"}, "get_gemm_template_output_and_compute_dtype": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.get_gemm_template_output_and_compute_dtype", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_grouped_gemm_template.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mm_args": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_args", "kind": "Gdef"}, "parallel_num_threads": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.parallel_num_threads", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "prune_tensors": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.prune_tensors", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transpose_w": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.transpose_w", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_grouped_gemm_template.py"}