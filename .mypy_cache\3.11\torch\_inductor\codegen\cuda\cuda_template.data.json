{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cuda.cuda_template", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo", "name": "ArgInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 37, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "ty", "type": "builtins.str"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.cuda.cuda_template", "mro": ["torch._inductor.codegen.cuda.cuda_template.ArgInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "ty"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "ty"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.ArgInfo", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ArgInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ty"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["name", "ty"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["name", "ty"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ArgInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["name", "ty"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ArgInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo.name", "name": "name", "type": "builtins.str"}}, "ty": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo.ty", "name": "ty", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.cuda_template.ArgInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.cuda_template.ArgInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseSchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseSchedulerNode", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "CUDABenchmarkRequest": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.autotune_process.CUDABenchmarkRequest", "kind": "Gdef"}, "CUDATemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.common.KernelTemplate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "name": "CUDATemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cuda.cuda_template", "mro": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "torch._inductor.codegen.common.KernelTemplate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "input_nodes", "layout", "input_reorder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "input_nodes", "layout", "input_reorder"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "builtins.str", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUDATemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "description", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "description", "kwargs"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate of CUDATemplate", "ret_type": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_runtime_arg_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.get_runtime_arg_info", "name": "get_runtime_arg_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_runtime_arg_info of CUDATemplate", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.cuda.cuda_template.ArgInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_runtime_arg_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.get_runtime_arg_values", "name": "get_runtime_arg_values", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_runtime_arg_values of CUDATemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "globals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.globals", "name": "globals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "globals of CUDATemplate", "ret_type": "torch._inductor.utils.IndentedBuffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.header", "name": "header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header of CUDATemplate", "ret_type": "torch._inductor.utils.IndentedBuffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.index_counter", "name": "index_counter", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "input_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.input_nodes", "name": "input_nodes", "type": {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "input_reorder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.input_reorder", "name": "input_reorder", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "layout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.layout", "name": "layout", "type": "torch._inductor.ir.Layout"}}, "output_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.output_node", "name": "output_node", "type": "torch._inductor.ir.<PERSON><PERSON><PERSON>"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of CUDATemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_epilogue_fusion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.supports_epilogue_fusion", "name": "supports_epilogue_fusion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_template.GemmOperation"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_epilogue_fusion of CUDATemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.supports_epilogue_fusion", "name": "supports_epilogue_fusion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.cuda_template.GemmOperation"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_epilogue_fusion of CUDATemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUDATemplateBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.CUDATemplateBuffer", "kind": "Gdef"}, "CUDATemplateCaller": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateCaller", "kind": "Gdef"}, "CUDATemplateKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "kind": "Gdef"}, "CUTLASSTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cuda.cuda_template.CUDATemplate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "name": "CUTLASSTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cuda.cuda_template", "mro": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "torch._inductor.codegen.common.KernelTemplate", "builtins.object"], "names": {".class": "SymbolTable", "_DTYPE_TO_CUTLASS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate._DTYPE_TO_CUTLASS", "name": "_DTYPE_TO_CUTLASS", "type": {".class": "Instance", "args": ["torch._C.dtype", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_DTYPE_TO_CUTLASS_SPARSE_META": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate._DTYPE_TO_CUTLASS_SPARSE_META", "name": "_DTYPE_TO_CUTLASS_SPARSE_META", "type": {".class": "Instance", "args": ["torch._C.dtype", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cute_int": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "int_str", "var_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.cute_int", "name": "cute_int", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "int_str", "var_name"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cute_int of CUTLASSTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cutlass_sparse_meta_type_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "ptr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.cutlass_sparse_meta_type_cast", "name": "cutlass_sparse_meta_type_cast", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "ptr"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "torch._inductor.ir.IRNode", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cutlass_sparse_meta_type_cast of CUTLASSTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cutlass_type_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "ptr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.cutlass_type_cast", "name": "cutlass_type_cast", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "ptr"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "torch._inductor.ir.IRNode", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cutlass_type_cast of CUTLASSTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_runtime_arg_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.get_runtime_arg_info", "name": "get_runtime_arg_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_runtime_arg_info of CUTLASSTemplate", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.cuda.cuda_template.ArgInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.get_runtime_arg_info", "name": "get_runtime_arg_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_runtime_arg_info of CUTLASSTemplate", "ret_type": {".class": "Instance", "args": ["torch._inductor.codegen.cuda.cuda_template.ArgInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_runtime_arg_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.get_runtime_arg_values", "name": "get_runtime_arg_values", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_runtime_arg_values of CUTLASSTemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.get_runtime_arg_values", "name": "get_runtime_arg_values", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_runtime_arg_values of CUTLASSTemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "globals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.globals", "name": "globals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "globals of CUTLASSTemplate", "ret_type": "torch._inductor.utils.IndentedBuffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.header", "name": "header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header of CUTLASSTemplate", "ret_type": "torch._inductor.utils.IndentedBuffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DTYPE_TO_CUTLASS_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_utils.DTYPE_TO_CUTLASS_TYPE", "kind": "Gdef"}, "GemmOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.cuda_template.GemmOperation", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "IRNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.IRNode", "kind": "Gdef"}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "KernelTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.KernelTemplate", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Layout", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Placeholder": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.Placeholder", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TensorMeta": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.autotune_process.TensorMeta", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cuda_template.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "autotuning_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cuda_template.autotuning_log", "name": "autotuning_log", "type": "logging.Logger"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "getArtifactLogger": {".class": "SymbolTableNode", "cross_ref": "torch._logging._internal.getArtifactLogger", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cuda_template.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.cuda.cuda_template.sympy", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "unique": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.unique", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cuda_template.py"}