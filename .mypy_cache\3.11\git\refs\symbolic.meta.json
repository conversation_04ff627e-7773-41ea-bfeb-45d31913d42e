{"data_mtime": 1755656862, "dep_lines": [11, 12, 13, 10, 14, 37, 40, 43, 907, 6, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 25, 20, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.objects.base", "git.objects.commit", "git.refs.log", "git.compat", "git.util", "git.types", "git.config", "git.repo", "git.refs", "os", "typing", "builtins", "_frozen_importlib", "_io", "abc", "git.diff", "git.objects", "git.objects.blob", "git.objects.tag", "git.objects.tree", "git.objects.util", "git.repo.base", "io", "typing_extensions"], "hash": "572e388bf1733c98b3fb7d57909ae5a2bd9bcf2b", "id": "git.refs.symbolic", "ignore_all": true, "interface_hash": "6fab60d27da67d559299aa35908aecd506e88d5a", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\refs\\symbolic.py", "plugin_data": null, "size": 34769, "suppressed": ["gitdb.exc"], "version_id": "1.15.0"}