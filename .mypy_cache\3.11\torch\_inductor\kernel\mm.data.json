{".class": "MypyFile", "_fullname": "torch._inductor.kernel.mm", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AHContext": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.autoheuristic.autoheuristic_utils.AHContext", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutoHeuristicSelectAlgorithm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.autoheuristic.autoheuristic.AutoHeuristicSelectAlgorithm", "kind": "Gdef"}, "CKGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.ck_universal_gemm_template.CKGemmTemplate", "kind": "Gdef"}, "CKTileGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template.CKTileGemmTemplate", "kind": "Gdef"}, "CUTLASS2xGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", "kind": "Gdef"}, "CUTLASS3xGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", "kind": "Gdef"}, "CppGemmTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "kind": "Gdef"}, "ExternKernelChoice": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.ExternKernelChoice", "kind": "Gdef"}, "FlexibleLayout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.FlexibleLayout", "kind": "Gdef"}, "L": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.lowerings", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SubgraphTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.subgraph.SubgraphTemplate", "kind": "Gdef"}, "TorchVersion": {".class": "SymbolTableNode", "cross_ref": "torch.torch_version.TorchVersion", "kind": "Gdef"}, "TritonTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.TritonTemplate", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_is_int8_mat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mat"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm._is_int8_mat", "name": "_is_int8_mat", "type": null}}, "_is_large_block_for_cpu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["m", "n", "k"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm._is_large_block_for_cpu", "name": "_is_large_block_for_cpu", "type": null}}, "_is_sm7x_or_older_gpu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm._is_sm7x_or_older_gpu", "name": "_is_sm7x_or_older_gpu", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["index"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_sm7x_or_older_gpu", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm._is_sm7x_or_older_gpu", "name": "_is_sm7x_or_older_gpu", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_is_static_problem": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common._is_static_problem", "kind": "Gdef"}, "_use_cutlass_for_op": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils._use_cutlass_for_op", "kind": "Gdef"}, "add_layout_constraint": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.add_layout_constraint", "kind": "Gdef"}, "addmm_epilogue": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.addmm_epilogue", "kind": "Gdef"}, "apply_scaling": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.apply_scaling", "name": "apply_scaling", "type": "builtins.str"}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "aten__fp8_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.aten__fp8_mm", "name": "aten__fp8_mm", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "aten__int_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.aten__int_mm", "name": "aten__int_mm", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "aten__sparse_semi_structured_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.aten__sparse_semi_structured_mm", "name": "aten__sparse_semi_structured_mm", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "aten_addmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.aten_addmm", "name": "aten_addmm", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "aten_bias_addmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.aten_bias_addmm", "name": "aten_bias_addmm", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "aten_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.aten_mm", "name": "aten_mm", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "autotune_select_algorithm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.autotune_select_algorithm", "kind": "Gdef"}, "bias_addmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["inp", "mat1", "mat2", "out", "alpha", "beta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm.bias_addmm", "name": "bias_addmm", "type": null}}, "check_supported_striding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mat_a", "mat_b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm.check_supported_striding", "name": "check_supported_striding", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mat_a", "mat_b"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_supported_striding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "constrain_to_fx_strides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.constrain_to_fx_strides", "kind": "Gdef"}, "context_add_strides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.autoheuristic.autoheuristic_utils.context_add_strides", "kind": "Gdef"}, "context_add_using_tf32": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.autoheuristic.autoheuristic_utils.context_add_using_tf32", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "decomposeK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["a", "b", "k_splits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm.decomposeK", "name": "decomposeK", "type": null}}, "device_tma": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.device_tma", "name": "device_tma", "type": "builtins.str"}}, "dims_are_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm.dims_are_int", "name": "dims_are_int", "type": null}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_k_splits": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_k_splits", "kind": "Gdef"}, "get_size_hints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["mat1", "mat2", "m", "n", "k"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm.get_size_hints", "name": "get_size_hints", "type": null}}, "get_size_hints_strides": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mat1", "mat2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm.get_size_hints_strides", "name": "get_size_hints_strides", "type": null}}, "get_tma_workspace_arg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_tma_workspace_arg", "kind": "Gdef"}, "has_triton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.has_triton", "name": "has_triton", "type": "builtins.bool"}}, "inductor_config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "is_triton": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.is_triton", "kind": "Gdef"}, "lazy_register_extern_choice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm.lazy_register_extern_choice", "name": "lazy_register_extern_choice", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.lazy_register_extern_choice", "name": "lazy_register_extern_choice", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "load_scales": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.load_scales", "name": "load_scales", "type": "builtins.str"}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_fx": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor.make_fx", "kind": "Gdef"}, "mm_args": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_args", "kind": "Gdef"}, "mm_autoheuristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["mat1", "mat2", "m", "n", "k", "choices", "name", "input_nodes", "ops", "precondition", "top_k", "always_included"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm.mm_autoheuristic", "name": "mm_autoheuristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["mat1", "mat2", "m", "n", "k", "choices", "name", "input_nodes", "ops", "precondition", "top_k", "always_included"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mm_autoheuristic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mm_config_kwargs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_config_kwargs", "kind": "Gdef"}, "mm_grid": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_grid", "kind": "Gdef"}, "mm_operations": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.autoheuristic.autoheuristic_utils.mm_operations", "kind": "Gdef"}, "mm_options": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_options", "kind": "Gdef"}, "mm_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.mm_template", "name": "mm_template", "type": "torch._inductor.select_algorithm.TritonTemplate"}}, "persistent_mm_grid": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.persistent_mm_grid", "kind": "Gdef"}, "persistent_mm_options": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.persistent_mm_options", "kind": "Gdef"}, "persistent_tma_mm_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.persistent_tma_mm_template", "name": "persistent_tma_mm_template", "type": "torch._inductor.select_algorithm.TritonTemplate"}}, "prims": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.prims", "name": "prims", "type": "torch._ops._OpNamespace"}}, "realize_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.realize_inputs", "kind": "Gdef"}, "register_lowering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.register_lowering", "kind": "Gdef"}, "scale_mm_epilogue": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.scale_mm_epilogue", "kind": "Gdef"}, "scaled_mm_device_tma_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.scaled_mm_device_tma_template", "name": "scaled_mm_device_tma_template", "type": "torch._inductor.select_algorithm.TritonTemplate"}}, "scaled_mm_options": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.scaled_mm_options", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.kernel.mm.sympy", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "triton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.triton", "name": "triton", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.kernel.mm.triton", "source_any": null, "type_of_any": 3}}}, "triton_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm.triton_version", "name": "triton_version", "type": "torch.torch_version.TorchVersion"}}, "tuned_addmm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["inp", "mat1", "mat2", "alpha", "beta", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm.tuned_addmm", "name": "tuned_addmm", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.tuned_addmm", "name": "tuned_addmm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["inp", "mat1", "mat2", "alpha", "beta", "layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuned_addmm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tuned_int_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["mat1", "mat2", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm.tuned_int_mm", "name": "tuned_int_mm", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.tuned_int_mm", "name": "tuned_int_mm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["mat1", "mat2", "layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuned_int_mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tuned_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["mat1", "mat2", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm.tuned_mm", "name": "tuned_mm", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.tuned_mm", "name": "tuned_mm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["mat1", "mat2", "layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuned_mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tuned_scaled_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["mat_a", "mat_b", "scale_a", "scale_b", "bias", "scale_result", "out_dtype", "use_fast_accum", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm.tuned_scaled_mm", "name": "tuned_scaled_mm", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.tuned_scaled_mm", "name": "tuned_scaled_mm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["mat_a", "mat_b", "scale_a", "scale_b", "bias", "scale_result", "out_dtype", "use_fast_accum", "layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuned_scaled_mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tuned_sparse_semi_structured_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["mat1", "mat1_meta", "mat2", "out_dtype", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm.tuned_sparse_semi_structured_mm", "name": "tuned_sparse_semi_structured_mm", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.tuned_sparse_semi_structured_mm", "name": "tuned_sparse_semi_structured_mm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["mat1", "mat1_meta", "mat2", "out_dtype", "layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuned_sparse_semi_structured_mm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "use_aten_gemm_kernels": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_aten_gemm_kernels", "kind": "Gdef"}, "use_ck_gemm_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_ck_gemm_template", "kind": "Gdef"}, "use_ck_tile_gemm_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_ck_tile_gemm_template", "kind": "Gdef"}, "use_cpp_gemm_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_cpp_gemm_template", "kind": "Gdef"}, "use_cutlass_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_cutlass_template", "kind": "Gdef"}, "use_decompose_k_choice": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_decompose_k_choice", "kind": "Gdef"}, "use_triton_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_triton_template", "kind": "Gdef"}, "use_triton_tma_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_triton_tma_template", "kind": "Gdef"}, "using_b200": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.kernel.mm.using_b200", "name": "using_b200", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "using_b200", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.kernel.mm.using_b200", "name": "using_b200", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\kernel\\mm.py"}