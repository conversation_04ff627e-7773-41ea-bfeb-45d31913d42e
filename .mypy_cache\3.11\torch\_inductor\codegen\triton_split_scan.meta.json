{"data_mtime": 1755649448, "dep_lines": [8, 9, 14, 16, 7, 15, 18, 7, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 89, 5, 89, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 20, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 20, 20, 10, 20, 20], "dependencies": ["torch._inductor.codegen.simd", "torch._inductor.codegen.triton", "torch._inductor.runtime.triton_heuristics", "torch.utils._sympy.functions", "torch._inductor.config", "torch.utils._ordered_set", "torch._inductor.utils", "torch._inductor", "functools", "typing", "builtins", "collections", "warnings", "torch", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "sys", "json", "traceback", "re", "html", "_frozen_importlib", "abc", "torch._inductor.codegen.common", "torch._inductor.runtime", "contextlib"], "hash": "7052e5a1a82d99122ced7bbbd9f3c7e1ad9475cb", "id": "torch._inductor.codegen.triton_split_scan", "ignore_all": true, "interface_hash": "83a447b62309023fe8870aa94c281e7653c0afeb", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\triton_split_scan.py", "plugin_data": null, "size": 7450, "suppressed": ["triton.language", "sympy", "triton", "traitlets.utils.warnings"], "version_id": "1.15.0"}