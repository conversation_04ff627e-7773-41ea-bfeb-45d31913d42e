{"data_mtime": 1755649448, "dep_lines": [13, 17, 18, 20, 30, 31, 32, 11, 19, 22, 23, 24, 29, 6, 10, 12, 22, 2, 3, 4, 5, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 5, 20, 10, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.fx.experimental.optimization", "torch.fx.passes.graph_transform_observer", "torch.fx.passes.shape_prop", "torch.nn.utils.fusion", "torch._inductor.fx_passes.group_batch_fusion", "torch._inductor.fx_passes.misc_patterns", "torch._inductor.fx_passes.split_cat", "torch._dynamo.utils", "torch.nn.functional", "torch._inductor.config", "torch._inductor.fx_utils", "torch._inductor.pattern_matcher", "torch._inductor.utils", "collections.abc", "torch.nn", "torch._logging", "torch._inductor", "copy", "itertools", "logging", "types", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "warnings", "contextlib", "_frozen_importlib", "abc", "torch._C", "torch._C._VariableFunctions", "torch._C._nn", "torch._tensor", "torch.fx", "torch.fx.graph_module", "torch.fx.node", "torch.nn.modules", "torch.nn.modules.module"], "hash": "a57f4b4cb4893a15a2c302d3c8afdfd61210e398", "id": "torch._inductor.fx_passes.pre_grad", "ignore_all": true, "interface_hash": "8a011eca63323821dfec71bdb841ce93139e4d5c", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\pre_grad.py", "plugin_data": null, "size": 31394, "suppressed": [], "version_id": "1.15.0"}