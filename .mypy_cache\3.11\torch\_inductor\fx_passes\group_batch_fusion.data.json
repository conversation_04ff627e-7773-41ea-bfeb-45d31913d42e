{".class": "MypyFile", "_fullname": "torch._inductor.fx_passes.group_batch_fusion", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BatchAddPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchAddPostGradFusion", "name": "BatchAddPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchAddPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchAddPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchAddPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchAddPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchAddPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchAddPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchAddPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchClampPreGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchClampPreGradFusion", "name": "BatchClampPreGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchClampPreGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchClampPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchClampPreGradFusion.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchClampPreGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchClampPreGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchDetachPreGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchDetachPreGradFusion", "name": "BatchDetachPreGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchDetachPreGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchDetachPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchDetachPreGradFusion.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchDetachPreGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchDetachPreGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchDivPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchDivPostGradFusion", "name": "BatchDivPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchDivPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchDivPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchDivPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchDivPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchDivPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchDivPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchDivPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "name": "BatchFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchLayernormFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion", "name": "BatchLayernormFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of BatchLayernormFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of BatchLayernormFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchLayernormFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchLinearLHSFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion", "name": "BatchLinearLHSFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of BatchLinearLHSFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of BatchLinearLHSFusion", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchLinearLHSFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchMathOpsPreGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "name": "BatchMathOpsPreGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion.__init__", "name": "__init__", "type": null}}, "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of BatchMathOpsPreGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of BatchMathOpsPreGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchMulPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMulPostGradFusion", "name": "BatchMulPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMulPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchMulPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMulPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchMulPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchMulPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchMulPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchMulPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchNanToNumPreGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchNanToNumPreGradFusion", "name": "BatchNanToNumPreGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchNanToNumPreGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchNanToNumPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchMathOpsPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchNanToNumPreGradFusion.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchNanToNumPreGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchNanToNumPreGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchPointwiseMathOpsPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "name": "BatchPointwiseMathOpsPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchPointwiseMathOpsPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pointwise_node_can_be_fused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion._pointwise_node_can_be_fused", "name": "_pointwise_node_can_be_fused", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pointwise_node_can_be_fused of BatchPointwiseMathOpsPostGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of BatchPointwiseMathOpsPostGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of BatchPointwiseMathOpsPostGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchPointwiseOpsFusionFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "name": "BatchPointwiseOpsFusionFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchPointwiseOpsFusionFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "op": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory.op", "name": "op", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchPointwiseOpsPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "name": "BatchPointwiseOpsPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchPointwiseOpsPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of BatchPointwiseOpsPostGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of BatchPointwiseOpsPostGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchPointwiseOpsPreGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "name": "BatchPointwiseOpsPreGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "op", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchPointwiseOpsPreGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of BatchPointwiseOpsPreGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of BatchPointwiseOpsPreGradFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchReLuPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPostGradFusion", "name": "BatchReLuPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchReLuPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchReLuPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchReLuPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchReLuPreGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPreGradFusion", "name": "BatchReLuPreGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPreGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchReLuPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPreGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchReLuPreGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchReLuPreGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPreGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchReLuPreGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchSigmoidPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPostGradFusion", "name": "BatchSigmoidPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchSigmoidPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchSigmoidPreGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPreGradFusion", "name": "BatchSigmoidPreGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPreGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPreGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPreGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchSigmoidPreGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPreGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchSigmoidPreGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchSubPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSubPostGradFusion", "name": "BatchSubPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSubPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchSubPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseMathOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSubPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchSubPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchSubPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchSubPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchSubPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchTanhPostGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPostGradFusion", "name": "BatchTanhPostGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPostGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchTanhPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPostGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPostGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchTanhPostGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchTanhPostGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPostGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPostGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchTanhPreGradFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPreGradFusion", "name": "BatchTanhPreGradFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPreGradFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.BatchTanhPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsPreGradFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchPointwiseOpsFusionFactory", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPreGradFusion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.BatchTanhPreGradFusion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BatchTanhPreGradFusion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPreGradFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.BatchTanhPreGradFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CallFunctionVarArgs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.CallFunctionVarArgs", "kind": "Gdef"}, "DEFAULT_ALPHA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.DEFAULT_ALPHA", "name": "DEFAULT_ALPHA", "type": "builtins.int"}}, "DEFAULT_BETA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.DEFAULT_BETA", "name": "DEFAULT_BETA", "type": "builtins.int"}}, "FUSE_NODES_WITH_SAME_PARENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.FUSE_NODES_WITH_SAME_PARENT", "name": "FUSE_NODES_WITH_SAME_PARENT", "type": "builtins.bool"}}, "Fuse_NODES_WITH_SAME_USERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.Fuse_NODES_WITH_SAME_USERS", "name": "Fuse_NODES_WITH_SAME_USERS", "type": "builtins.bool"}}, "GraphTransformObserver": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.graph_transform_observer.GraphTransformObserver", "kind": "Gdef"}, "GroupBatchFusionBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "name": "GroupBatchFusionBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GroupBatchFusionBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase.fuse", "name": "fuse", "type": null}}, "graph_search_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase.graph_search_options", "name": "graph_search_options", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase.match", "name": "match", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GroupFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupFusion", "name": "GroupFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.GroupFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.GroupFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GroupLinearFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.GroupFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion", "name": "GroupLinearFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "_addmm_node_can_be_fused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion._addmm_node_can_be_fused", "name": "_addmm_node_can_be_fused", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_addmm_node_can_be_fused of GroupLinearFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mm_node_can_be_fused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion._mm_node_can_be_fused", "name": "_mm_node_can_be_fused", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mm_node_can_be_fused of GroupLinearFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of GroupLinearFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of GroupLinearFusion", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.GroupLinearFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "MAX_FUSE_SEARCH_DEPTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.MAX_FUSE_SEARCH_DEPTH", "name": "MAX_FUSE_SEARCH_DEPTH", "type": "builtins.int"}}, "MAX_FUSE_SET_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.MAX_FUSE_SET_SIZE", "name": "MAX_FUSE_SET_SIZE", "type": "builtins.int"}}, "MAX_FUSE_TENSOR_SIZE_GROUP_LINEAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.MAX_FUSE_TENSOR_SIZE_GROUP_LINEAR", "name": "MAX_FUSE_TENSOR_SIZE_GROUP_LINEAR", "type": "builtins.int"}}, "MIN_FUSE_SET_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.MIN_FUSE_SET_SIZE", "name": "MIN_FUSE_SET_SIZE", "type": "builtins.int"}}, "OPTIMUS_EXCLUDE_POST_GRAD": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.OPTIMUS_EXCLUDE_POST_GRAD", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "POST_GRAD_FUSIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.POST_GRAD_FUSIONS", "name": "POST_GRAD_FUSIONS", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PRE_GRAD_FUSIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PRE_GRAD_FUSIONS", "name": "PRE_GRAD_FUSIONS", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PostGradBatchLinearFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion", "name": "PostGradBatchLinearFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "_addmm_node_can_be_fused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion._addmm_node_can_be_fused", "name": "_addmm_node_can_be_fused", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_addmm_node_can_be_fused of PostGradBatchLinearFusion", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_input_2d": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion._is_input_2d", "name": "_is_input_2d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_input_2d of PostGradBatchLinearFusion", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of PostGradBatchLinearFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of PostGradBatchLinearFusion", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.PostGradBatchLinearFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PreGradBatchLinearFusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.fx_passes.group_batch_fusion.BatchFusion"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion", "name": "PreGradBatchLinearFusion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion", "torch._inductor.fx_passes.group_batch_fusion.BatchFusion", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "builtins.object"], "names": {".class": "SymbolTable", "_getitem_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "getitem_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion._getitem_args", "name": "_getitem_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "getitem_node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getitem_args of PreGradBatchLinearFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion.fuse", "name": "fuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "graph", "subset"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse of PreGradBatchLinearFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of PreGradBatchLinearFusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion.PreGradBatchLinearFusion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SEARCH_EXCLUSIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.SEARCH_EXCLUSIONS", "name": "SEARCH_EXCLUSIONS", "type": {".class": "Instance", "args": [{".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._T", "id": -1, "name": "_T", "namespace": "_operator.getitem#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getitem", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._T", "id": -1, "name": "_T", "namespace": "_operator.getitem#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._T", "id": -1, "name": "_T", "namespace": "_operator.getitem#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._K", "id": -1, "name": "_K", "namespace": "_operator.getitem", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._V", "id": -2, "name": "_V", "namespace": "_operator.getitem", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_typeshed.SupportsGetItem"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._K", "id": -1, "name": "_K", "namespace": "_operator.getitem", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getitem", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._V", "id": -2, "name": "_V", "namespace": "_operator.getitem", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._K", "id": -1, "name": "_K", "namespace": "_operator.getitem", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_operator._V", "id": -2, "name": "_V", "namespace": "_operator.getitem", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "SHAPE_BROADCAST_BATCH_LINEAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.SHAPE_BROADCAST_BATCH_LINEAR", "name": "SHAPE_BROADCAST_BATCH_LINEAR", "type": "builtins.bool"}}, "_OrderedSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet", "name": "_OrderedSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.fx_passes.group_batch_fusion", "mro": ["torch._inductor.fx_passes.group_batch_fusion._OrderedSet", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion._OrderedSet", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of _OrderedSet", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "param"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "param"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion._OrderedSet", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _OrderedSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet.__iter__", "name": "__iter__", "type": null}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion._OrderedSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _OrderedSet", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "o"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet.append", "name": "append", "type": null}}, "rep": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet.rep", "name": "rep", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "collections.OrderedDict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.fx_passes.group_batch_fusion._OrderedSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_is_mutable_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tgt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion._is_mutable_node", "name": "_is_mutable_node", "type": null}}, "apply_group_batch_fusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["graph", "rule"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.apply_group_batch_fusion", "name": "apply_group_batch_fusion", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["graph", "rule"], "arg_types": ["torch.fx.graph_module.GraphModule", "torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_group_batch_fusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "decompose_stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["graph", "input_tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.decompose_stack", "name": "decompose_stack", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["graph", "input_tensors"], "arg_types": ["torch.fx.graph_module.GraphModule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decompose_stack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deeplearning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.deeplearning", "name": "deeplearning", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.fx_passes.group_batch_fusion.deeplearning", "source_any": null, "type_of_any": 3}}}, "default_graph_search_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.default_graph_search_options", "name": "default_graph_search_options", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "find_independent_subset_greedy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node_list", "graph_search_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.find_independent_subset_greedy", "name": "find_independent_subset_greedy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node_list", "graph_search_options"], "arg_types": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_independent_subset_greedy", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_fusion_from_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["config_options", "pre_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.generate_fusion_from_config", "name": "generate_fusion_from_config", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["config_options", "pre_grad"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_fusion_from_config", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_arg_value": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.get_arg_value", "kind": "Gdef"}, "get_fusion_candidates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["rule", "root_node", "fused_set"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.get_fusion_candidates", "name": "get_fusion_candidates", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["rule", "root_node", "fused_set"], "arg_types": ["torch._inductor.fx_passes.group_batch_fusion.GroupBatchFusionBase", "torch.fx.node.Node", {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fusion_candidates", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "collections.defaultdict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph_search_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.graph_search_options", "name": "graph_search_options", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "group_batch_fusion_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["graph", "pre_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.group_batch_fusion_passes", "name": "group_batch_fusion_passes", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["graph", "pre_grad"], "arg_types": ["torch.fx.graph.Graph", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group_batch_fusion_passes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_fbgemm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.has_fbgemm", "name": "has_fbgemm", "type": "builtins.bool"}}, "is_linear_node_can_be_fused": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.is_linear_node_can_be_fused", "name": "is_linear_node_can_be_fused", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_linear_node_can_be_fused", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_node_meta_valid": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.is_node_meta_valid", "kind": "Gdef"}, "list_group_batch_fusions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["pre_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.list_group_batch_fusions", "name": "list_group_batch_fusions", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["pre_grad"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_group_batch_fusions", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.group_batch_fusion.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "register_fusion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["name", "pre_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.register_fusion", "name": "register_fusion", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["name", "pre_grad"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_fusion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stable_topological_sort": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.stable_topological_sort", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "trace_structured": {".class": "SymbolTableNode", "cross_ref": "torch._logging._internal.trace_structured", "kind": "Gdef"}, "update_pointwise_example_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["pointwise_node", "input", "other", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.update_pointwise_example_value", "name": "update_pointwise_example_value", "type": null}}, "update_stack_example_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["node", "metadata", "dim", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.group_batch_fusion.update_stack_example_value", "name": "update_stack_example_value", "type": null}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\group_batch_fusion.py"}