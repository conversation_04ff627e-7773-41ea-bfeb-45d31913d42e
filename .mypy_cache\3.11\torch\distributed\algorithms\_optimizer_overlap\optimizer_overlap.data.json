{".class": "MypyFile", "_fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "DistributedDataParallel": {".class": "SymbolTableNode", "cross_ref": "torch.nn.parallel.distributed.DistributedDataParallel", "kind": "Gdef"}, "FullyShardedDataParallel": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "torch.optim.optimizer.Optimizer", "kind": "Gdef"}, "OverlappedOptimizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["register_ddp", 1], ["register_fsdp", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "name": "OverlappedOptimizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap", "mro": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "optim_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "optim_cls"], "arg_types": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OverlappedOptimizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "optim_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer.optim_cls", "name": "optim_cls", "type": "builtins.type"}}, "register_ddp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "ddp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer.register_ddp", "name": "register_ddp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ddp"], "arg_types": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "torch.nn.parallel.distributed.DistributedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_ddp of OverlappedOptimizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer.register_ddp", "name": "register_ddp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ddp"], "arg_types": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "torch.nn.parallel.distributed.DistributedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_ddp of OverlappedOptimizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register_fsdp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "fsdp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer.register_fsdp", "name": "register_fsdp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fsdp"], "arg_types": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_fsdp of OverlappedOptimizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer.register_fsdp", "name": "register_fsdp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fsdp"], "arg_types": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_fsdp of OverlappedOptimizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_OptimizerHookState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms.ddp_comm_hooks.optimizer_overlap_hooks._OptimizerHookState", "kind": "Gdef"}, "_OverlappedStandardOptimizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer", "name": "_OverlappedStandardOptimizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap", "mro": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer", "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.OverlappedOptimizer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "optim_cls", "params", "optim_args", "optim_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "optim_cls", "params", "optim_args", "optim_kwargs"], "arg_types": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer", "builtins.type", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _OverlappedStandardOptimizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_opt_hook_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer._opt_hook_state", "name": "_opt_hook_state", "type": "torch.distributed.algorithms.ddp_comm_hooks.optimizer_overlap_hooks._OptimizerHookState"}}, "register_ddp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ddp_inst"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer.register_ddp", "name": "register_ddp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ddp_inst"], "arg_types": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer", "torch.nn.parallel.distributed.DistributedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_ddp of _OverlappedStandardOptimizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_fsdp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fsdp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer.register_fsdp", "name": "register_fsdp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fsdp"], "arg_types": ["torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer", "torch.distributed.fsdp.fully_sharded_data_parallel.FullyShardedDataParallel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_fsdp of _OverlappedStandardOptimizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._OverlappedStandardOptimizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_as_overlapped_optim": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["optim_cls", "params", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._as_overlapped_optim", "name": "_as_overlapped_optim", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["optim_cls", "params", "args", "kwargs"], "arg_types": ["builtins.type", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_overlapped_optim", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_hook_then_optimizer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms.ddp_comm_hooks.optimizer_overlap_hooks._hook_then_optimizer", "kind": "Gdef"}, "_registered_overlapped_optims": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap._registered_overlapped_optims", "name": "_registered_overlapped_optims", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "allreduce_hook": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms.ddp_comm_hooks.default_hooks.allreduce_hook", "kind": "Gdef"}, "as_functional_optim": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.utils.as_functional_optim", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "register_overlapped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["optim_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap.register_overlapped", "name": "register_overlapped", "type": null}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\algorithms\\_optimizer_overlap\\optimizer_overlap.py"}