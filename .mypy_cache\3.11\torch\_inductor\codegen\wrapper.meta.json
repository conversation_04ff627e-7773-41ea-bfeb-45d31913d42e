{"data_mtime": 1755649448, "dep_lines": [26, 27, 28, 29, 38, 39, 44, 45, 60, 68, 69, 78, 1596, 2013, 23, 25, 36, 37, 41, 41, 41, 42, 44, 46, 59, 77, 1009, 2709, 22, 23, 41, 73, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 21, 1229, 1841, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 18, 75], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 25, 20, 20, 10, 5, 5, 5, 10, 10, 5, 5, 20, 5, 5, 25, 20, 20, 10, 20, 20, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 20], "dependencies": ["torch._inductor.codegen.debug_utils", "torch._inductor.codegen.multi_kernel", "torch._inductor.runtime.runtime_utils", "torch.fx.experimental.symbolic_shapes", "torch.utils._sympy.singleton_int", "torch.utils._sympy.symbol", "torch._inductor.runtime.triton_heuristics", "torch._inductor.runtime.hints", "torch._inductor.codegen.common", "torch._inductor.codegen.cpp_utils", "torch._inductor.codegen.triton_utils", "torch._inductor.codegen.wrapper_fxir", "torch._inductor.codegen.memory_planning", "torch._inductor.codegen.triton", "torch.utils._pytree", "torch._dynamo.utils", "torch.fx.node", "torch.utils._ordered_set", "torch._inductor.async_compile", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.codecache", "torch._inductor.runtime", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor.graph", "torch._C._distributed_c10d", "torch.utils._triton", "torch._ops", "torch.utils", "torch._inductor", "collections.abc", "__future__", "collections", "contextlib", "dataclasses", "dis", "functools", "inspect", "logging", "operator", "random", "re", "tempfile", "itertools", "typing", "torch", "copy", "pickle", "builtins", "os", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "warnings", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._inductor.sizevars", "torch.fx", "torch.fx.interpreter", "torch.utils._config_typing", "torch.utils._sympy", "torch.utils._sympy.printers", "typing_extensions"], "hash": "38c449be70f4fa8cc908cba48701b5c41314dd66", "id": "torch._inductor.codegen.wrapper", "ignore_all": true, "interface_hash": "41ec6af81eca134a924e9b508e2bfe0dbbba9da8", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\wrapper.py", "plugin_data": null, "size": 138351, "suppressed": ["sympy", "triton"], "version_id": "1.15.0"}