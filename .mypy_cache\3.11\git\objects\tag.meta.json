{"data_mtime": 1755656862, "dep_lines": [19, 20, 34, 35, 36, 16, 17, 19, 32, 14, 24, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 25, 25, 5, 5, 20, 25, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.base", "git.objects.util", "git.objects.blob", "git.objects.commit", "git.objects.tree", "git.compat", "git.util", "git.objects", "git.repo", "sys", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "git.db", "git.diff", "git.repo.base"], "hash": "1a7f5e6f9578f56ce7099582a90794016754d688", "id": "git.objects.tag", "ignore_all": true, "interface_hash": "4e2ae6f2d82c5ad27ca356f29fa789c1c458cbea", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\tag.py", "plugin_data": null, "size": 4441, "suppressed": [], "version_id": "1.15.0"}