{".class": "MypyFile", "_fullname": "torch.utils.tensorboard.summary", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "HistogramProto": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.HistogramProto", "name": "HistogramProto", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.HistogramProto", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PrCurvePluginData": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.PrCurvePluginData", "name": "PrCurvePluginData", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.PrCurvePluginData", "source_any": null, "type_of_any": 3}}}, "Summary": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.Summary", "name": "Summary", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.Summary", "source_any": null, "type_of_any": 3}}}, "SummaryMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.SummaryMetadata", "name": "SummaryMetadata", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.SummaryMetadata", "source_any": null, "type_of_any": 3}}}, "TensorProto": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.TensorProto", "name": "TensorProto", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.TensorProto", "source_any": null, "type_of_any": 3}}}, "TensorShapeProto": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.TensorShapeProto", "name": "TensorShapeProto", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.TensorShapeProto", "source_any": null, "type_of_any": 3}}}, "TextPluginData": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.TextPluginData", "name": "TextPluginData", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.TextPluginData", "source_any": null, "type_of_any": 3}}}, "_TENSOR_TYPE_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard.summary._TENSOR_TYPE_MAP", "name": "_TENSOR_TYPE_MAP", "type": {".class": "Instance", "args": ["torch._C.dtype", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tensor_to_list", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard.summary.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.summary.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.summary.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.summary.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.summary.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.summary.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.summary.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_calc_scale_factor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary._calc_scale_factor", "name": "_calc_scale_factor", "type": null}}, "_draw_single_box": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["image", "xmin", "ymin", "xmax", "ymax", "display_str", "color", "color_text", "thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary._draw_single_box", "name": "_draw_single_box", "type": null}}, "_get_json_config": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary._get_json_config", "name": "_get_json_config", "type": null}}, "_get_tensor_summary": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["name", "display_name", "description", "tensor", "content_type", "components", "json_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary._get_tensor_summary", "name": "_get_tensor_summary", "type": null}}, "_prepare_video": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._utils._prepare_video", "kind": "Gdef", "module_public": false}, "_tensor_to_complex_val": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary._tensor_to_complex_val", "name": "_tensor_to_complex_val", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tensor_to_complex_val", "ret_type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tensor_to_half_val": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary._tensor_to_half_val", "name": "_tensor_to_half_val", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tensor_to_half_val", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tensor_to_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary._tensor_to_list", "name": "_tensor_to_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tensor_to_list", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "audio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["tag", "tensor", "sample_rate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.audio", "name": "audio", "type": null}}, "compute_curve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["labels", "predictions", "num_thresholds", "weights"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.compute_curve", "name": "compute_curve", "type": null}}, "convert_to_HWC": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._utils.convert_to_HWC", "kind": "Gdef", "module_public": false}, "custom_scalars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.custom_scalars", "name": "custom_scalars", "type": null}}, "draw_boxes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["disp_image", "boxes", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.draw_boxes", "name": "draw_boxes", "type": null}}, "half_to_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.half_to_int", "name": "half_to_int", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "half_to_int", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "histogram": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["name", "values", "bins", "max_bins"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.histogram", "name": "histogram", "type": null}}, "histogram_raw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["name", "min", "max", "num", "sum", "sum_squares", "bucket_limits", "bucket_counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.histogram_raw", "name": "histogram_raw", "type": null}}, "hparams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["hparam_dict", "metric_dict", "hparam_domain_discrete"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.hparams", "name": "hparams", "type": null}}, "image": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["tag", "tensor", "rescale", "dataformats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.image", "name": "image", "type": null}}, "image_boxes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["tag", "tensor_image", "tensor_boxes", "rescale", "dataformats", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.image_boxes", "name": "image_boxes", "type": null}}, "int_to_half": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.int_to_half", "name": "int_to_half", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["i"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "int_to_half", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "layout_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.layout_pb2", "name": "layout_pb2", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.layout_pb2", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard.summary.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "make_histogram": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["values", "bins", "max_bins"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.make_histogram", "name": "make_histogram", "type": null}}, "make_image": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["tensor", "rescale", "rois", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.make_image", "name": "make_image", "type": null}}, "make_np": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._convert_np.make_np", "kind": "Gdef", "module_public": false}, "make_video": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tensor", "fps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.make_video", "name": "make_video", "type": null}}, "mesh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["tag", "vertices", "colors", "faces", "config_dict", "display_name", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.mesh", "name": "mesh", "type": null}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pr_curve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["tag", "labels", "predictions", "num_thresholds", "weights"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.pr_curve", "name": "pr_curve", "type": null}}, "pr_curve_raw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["tag", "tp", "fp", "tn", "fn", "precision", "recall", "num_thresholds", "weights"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.pr_curve_raw", "name": "pr_curve_raw", "type": null}}, "scalar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["name", "tensor", "collections", "new_style", "double_precision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.scalar", "name": "scalar", "type": null}}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef", "module_public": false}, "struct_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.summary.struct_pb2", "name": "struct_pb2", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.summary.struct_pb2", "source_any": null, "type_of_any": 3}}}, "tensor_proto": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tag", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.tensor_proto", "name": "tensor_proto", "type": null}}, "text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tag", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.text", "name": "text", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "video": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["tag", "tensor", "fps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.summary.video", "name": "video", "type": null}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\summary.py"}