{"data_mtime": 1755649448, "dep_lines": [12, 12, 13, 11, 13, 17, 2, 4, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 10, 20, 25, 5, 10, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.fx._pass", "torch.onnx._internal.fx", "torch.utils._pytree", "torch.fx", "torch.utils", "collections.abc", "__future__", "abc", "collections", "copy", "operator", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "_collections_abc", "_frozen_importlib", "_typeshed", "torch._C", "torch._ops", "torch._tensor", "torch.fx._symbolic_trace", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.node", "torch.fx.proxy", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "1f470dea712813261397753f2fe16300558fd213", "id": "torch.onnx._internal.fx.passes.modularization", "ignore_all": true, "interface_hash": "a6c493d7dafa628f94a15f940735198adc638e09", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\passes\\modularization.py", "plugin_data": null, "size": 34878, "suppressed": [], "version_id": "1.15.0"}