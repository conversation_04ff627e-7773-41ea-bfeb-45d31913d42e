{".class": "MypyFile", "_fullname": "streamlit.proto.GitInfo_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GitInfo_pb2.google", "source_any": null, "type_of_any": 3}}}, "GitInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GitInfo_pb2.GitInfo", "name": "GitInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GitInfo_pb2", "mro": ["streamlit.proto.GitInfo_pb2.GitInfo", "builtins.object"], "names": {".class": "SymbolTable", "AHEAD_OF_REMOTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.AHEAD_OF_REMOTE", "name": "AHEAD_OF_REMOTE", "type": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"}}, "BRANCH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.BRANCH_FIELD_NUMBER", "name": "BRANCH_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.GitInfo_pb2.GitInfo", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "branch"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "branch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "repository"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "repository"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "state"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uncommitted_files"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "uncommitted_files"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "untracked_files"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "untracked_files"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of GitInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DEFAULT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.DEFAULT", "name": "DEFAULT", "type": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GitInfo_pb2.google", "source_any": null, "type_of_any": 3}}}, "GitStates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.GitInfo_pb2.GitInfo._GitStates"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.GitStates", "name": "GitStates", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.GitStates", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.GitInfo_pb2", "mro": ["streamlit.proto.GitInfo_pb2.GitInfo.GitStates", "streamlit.proto.GitInfo_pb2.GitInfo._GitStates", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.GitStates.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GitInfo_pb2.GitInfo.GitStates", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HEAD_DETACHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.HEAD_DETACHED", "name": "HEAD_DETACHED", "type": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"}}, "MODULE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.MODULE_FIELD_NUMBER", "name": "MODULE_FIELD_NUMBER", "type": "builtins.int"}}, "REPOSITORY_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.REPOSITORY_FIELD_NUMBER", "name": "REPOSITORY_FIELD_NUMBER", "type": "builtins.int"}}, "STATE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.STATE_FIELD_NUMBER", "name": "STATE_FIELD_NUMBER", "type": "builtins.int"}}, "UNCOMMITTED_FILES_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.UNCOMMITTED_FILES_FIELD_NUMBER", "name": "UNCOMMITTED_FILES_FIELD_NUMBER", "type": "builtins.int"}}, "UNTRACKED_FILES_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.UNTRACKED_FILES_FIELD_NUMBER", "name": "UNTRACKED_FILES_FIELD_NUMBER", "type": "builtins.int"}}, "_GitStates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates", "name": "_GitStates", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GitInfo_pb2", "mro": ["streamlit.proto.GitInfo_pb2.GitInfo._GitStates", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.V", "line": 44, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GitInfo_pb2", "mro": ["streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GitStatesEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper", "name": "_GitStatesEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.GitInfo_pb2", "mro": ["streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "AHEAD_OF_REMOTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper.AHEAD_OF_REMOTE", "name": "AHEAD_OF_REMOTE", "type": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"}}, "DEFAULT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper.DEFAULT", "name": "DEFAULT", "type": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GitInfo_pb2.google", "source_any": null, "type_of_any": 3}}}, "HEAD_DETACHED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper.HEAD_DETACHED", "name": "HEAD_DETACHED", "type": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GitInfo_pb2.GitInfo._GitStatesEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "repository", "branch", "module", "untracked_files", "uncommitted_files", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "repository", "branch", "module", "untracked_files", "uncommitted_files", "state"], "arg_types": ["streamlit.proto.GitInfo_pb2.GitInfo", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GitInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "branch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.branch", "name": "branch", "type": "builtins.str"}}, "module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.module", "name": "module", "type": "builtins.str"}}, "repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.repository", "name": "repository", "type": "builtins.str"}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.state", "name": "state", "type": "streamlit.proto.GitInfo_pb2.GitInfo._GitStates.ValueType"}}, "uncommitted_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.uncommitted_files", "name": "uncommitted_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.GitInfo_pb2.GitInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "uncommitted_files of GitInfo", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GitInfo_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.uncommitted_files", "name": "uncommitted_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.GitInfo_pb2.GitInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "uncommitted_files of GitInfo", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GitInfo_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "untracked_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.untracked_files", "name": "untracked_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.GitInfo_pb2.GitInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "untracked_files of GitInfo", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GitInfo_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.untracked_files", "name": "untracked_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.GitInfo_pb2.GitInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "untracked_files of GitInfo", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GitInfo_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.GitInfo_pb2.GitInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.GitInfo_pb2.GitInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.GitInfo_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___GitInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.GitInfo_pb2.global___GitInfo", "line": 83, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.GitInfo_pb2.GitInfo"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.GitInfo_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.GitInfo_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\GitInfo_pb2.pyi"}