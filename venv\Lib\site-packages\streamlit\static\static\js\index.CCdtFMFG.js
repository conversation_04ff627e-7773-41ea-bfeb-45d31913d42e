import{s as c,r as a,j as o,aE as p,cc as f,bp as S,aJ as g}from"./index.DKN5MVff.js";const x=c("div",{target:"e8cs83k0"})(({theme:e,cache:n})=>({...n?{paddingBottom:e.spacing.lg,background:`linear-gradient(to bottom, ${e.colors.bgColor} 0%, ${e.colors.bgColor} 80%, transparent 100%)`}:null})),T=c("div",{target:"e8cs83k1"})({display:"flex",alignItems:"center",width:"100%"}),$=c("div",{target:"e8cs83k2"})(({theme:e})=>({display:"flex",gap:e.spacing.sm,alignItems:"baseline"})),h=c("div",{target:"e8cs83k3"})(({theme:e})=>({opacity:.6,fontSize:e.fontSizes.sm})),y=e=>{const n=Math.floor(e/3600),t=Math.floor(e%3600/60),s=e%60;if(n===0&&t===0)return`(${s.toFixed(1)} seconds)`;if(n===0){const d=`${t} minute${t===1?"":"s"}`,m=s===0?"":`, ${s.toFixed(1)} seconds`;return`(${d}${m})`}const l=`${n} hour${n===1?"":"s"}`,r=t===0?"":`, ${t} minute${t===1?"":"s"}`,i=s===0?"":`, ${s.toFixed(1)} seconds`;return`(${l}${r}${i})`};function b({element:e}){const{cache:n,showTime:t}=e,[s,l]=a.useState(0),r=a.useRef(null);return a.useEffect(()=>{if(!t)return;r.current=Date.now();const i=()=>{if(r.current!==null){const u=(Date.now()-r.current)/1e3;l(u)}};i();const d=setInterval(i,100);return()=>clearInterval(d)},[t]),o(x,{className:f({stSpinner:!0,stCacheSpinner:n}),"data-testid":"stSpinner",cache:n,children:p(T,{children:[o(S,{size:"base",margin:"0 md 0 0",padding:"0"}),p($,{children:[o(g,{source:e.text,allowHTML:!1}),t&&o(h,{children:y(s)})]})]})})}const v=a.memo(b);export{v as default};
