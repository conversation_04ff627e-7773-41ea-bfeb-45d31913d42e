{".class": "MypyFile", "_fullname": "torch._inductor.fx_passes.replace_random", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CallFunctionVarArgs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.CallFunctionVarArgs", "kind": "Gdef"}, "GraphTransformObserver": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.graph_transform_observer.GraphTransformObserver", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.Match", "kind": "Gdef"}, "PatternMatcherPass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.PatternMatcherPass", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.replace_random.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.replace_random.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.replace_random.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.replace_random.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.replace_random.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.fx_passes.replace_random.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_extract_tensor_metadata": {".class": "SymbolTableNode", "cross_ref": "torch.fx.passes.shape_prop._extract_tensor_metadata", "kind": "Gdef"}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.replace_random.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "default_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.replace_random.default_kwargs", "name": "default_kwargs", "type": null}}, "fuse_seed_creation_pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.replace_random.fuse_seed_creation_pass", "name": "fuse_seed_creation_pass", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["graph"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fuse_seed_creation_pass", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.replace_random.get_device", "name": "get_device", "type": null}}, "inductor_prims": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.inductor_prims", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.replace_random.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "patterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.fx_passes.replace_random.patterns", "name": "patterns", "type": "torch._inductor.pattern_matcher.PatternMatcherPass"}}, "register_graph_pattern": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.pattern_matcher.register_graph_pattern", "kind": "Gdef"}, "replace_randint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5], "arg_names": ["match", "low", "high", "size", "dtype", "device", "layout", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.replace_random.replace_randint", "name": "replace_randint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5], "arg_names": ["match", "low", "high", "size", "dtype", "device", "layout", "pin_memory"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_randint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.replace_random.replace_randint", "name": "replace_randint", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "replace_randint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "replace_random": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["match", "size", "generator", "dtype", "device", "layout", "pin_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.fx_passes.replace_random.replace_random", "name": "replace_random", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["match", "size", "generator", "dtype", "device", "layout", "pin_memory"], "arg_types": ["torch._inductor.pattern_matcher.Match", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_random", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch._inductor.fx_passes.replace_random.replace_random", "name": "replace_random", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "replace_random", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "replace_random_passes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["gm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.fx_passes.replace_random.replace_random_passes", "name": "replace_random_passes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["gm"], "arg_types": ["torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_random_passes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\fx_passes\\replace_random.py"}