{"data_mtime": 1755649448, "dep_lines": [23, 24, 25, 10, 11, 17, 19, 26, 53, 9, 18, 22, 22, 28, 34, 40, 20, 22, 2, 3, 4, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 68], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 20, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["torch._inductor.codegen.cuda.gemm_template", "torch._inductor.codegen.rocm.ck_tile_universal_gemm_template", "torch._inductor.codegen.rocm.ck_universal_gemm_template", "torch._inductor.autoheuristic.autoheuristic", "torch._inductor.autoheuristic.autoheuristic_utils", "torch._inductor.codegen.cpp_gemm_template", "torch.fx.experimental.proxy_tensor", "torch._inductor.codegen.subgraph", "torch._inductor.kernel.mm_common", "torch._dynamo.utils", "torch._inductor.virtualized", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.lowering", "torch._inductor.select_algorithm", "torch._inductor.utils", "torch.torch_version", "torch._inductor", "functools", "logging", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "enum", "torch._C", "torch._C._VariableFunctions", "torch._inductor.codegen", "torch._inductor.codegen.common", "torch._ops", "torch._prims_common", "torch._tensor", "torch.version", "typing_extensions"], "hash": "ff20775211f70d529f093ae5995f128fba2ac0c7", "id": "torch._inductor.kernel.mm", "ignore_all": true, "interface_hash": "cff5d0f2a516b873c081ebed579270b673a913c2", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\kernel\\mm.py", "plugin_data": null, "size": 46222, "suppressed": ["sympy", "triton"], "version_id": "1.15.0"}