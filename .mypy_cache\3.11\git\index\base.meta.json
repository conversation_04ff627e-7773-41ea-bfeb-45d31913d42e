{"data_mtime": 1755656862, "dep_lines": [29, 41, 51, 52, 78, 16, 25, 26, 27, 28, 30, 73, 79, 11, 12, 13, 14, 15, 17, 18, 19, 20, 26, 56, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22, 23], "dep_prios": [5, 5, 5, 5, 25, 10, 5, 10, 5, 5, 5, 5, 25, 10, 10, 10, 5, 10, 5, 10, 10, 10, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["git.objects.util", "git.index.fun", "git.index.typ", "git.index.util", "git.refs.reference", "os.path", "git.compat", "git.diff", "git.exc", "git.objects", "git.util", "git.types", "git.repo", "contextlib", "datetime", "glob", "io", "os", "stat", "subprocess", "sys", "tempfile", "git", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "git.db", "git.objects.base", "git.objects.blob", "git.objects.commit", "git.objects.submodule", "git.objects.submodule.base", "git.objects.tag", "git.objects.tree", "git.refs", "git.refs.head", "git.refs.symbolic", "git.repo.base", "typing_extensions"], "hash": "3aeabe4cb00c1c4a80e5a88705fccc69e2d5e6af", "id": "git.index.base", "ignore_all": true, "interface_hash": "37269a8d826e02b251ac6103e9c77603bceec0d9", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\index\\base.py", "plugin_data": null, "size": 61065, "suppressed": ["gitdb.base", "gitdb.db"], "version_id": "1.15.0"}