{".class": "MypyFile", "_fullname": "torch.distributed.tensor.parallel", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ColwiseParallel": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.style.ColwiseParallel", "kind": "Gdef"}, "ParallelStyle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.style.ParallelStyle", "kind": "Gdef"}, "PrepareModuleInput": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.style.PrepareModuleInput", "kind": "Gdef"}, "PrepareModuleInputOutput": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.style.PrepareModuleInputOutput", "kind": "Gdef"}, "PrepareModuleOutput": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.style.PrepareModuleOutput", "kind": "Gdef"}, "RowwiseParallel": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.style.RowwiseParallel", "kind": "Gdef"}, "SequenceParallel": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.style.SequenceParallel", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.tensor.parallel.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "loss_parallel": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.loss.loss_parallel", "kind": "Gdef"}, "parallelize_module": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.api.parallelize_module", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\__init__.py"}