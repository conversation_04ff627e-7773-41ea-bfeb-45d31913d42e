import{s as t,b6 as l,ax as v,b7 as n,r as u,b8 as s,j as a,b9 as k,ba as B,bb as C}from"./index.DKN5MVff.js";function x(o,r){switch(o){case n.XSMALL:return{padding:`${r.spacing.twoXS} ${r.spacing.sm}`,fontSize:r.fontSizes.sm};case n.SMALL:return{padding:`${r.spacing.twoXS} ${r.spacing.md}`};case n.LARGE:return{padding:`${r.spacing.md} ${r.spacing.md}`};default:return{padding:`${r.spacing.xs} ${r.spacing.md}`}}}const b=t("a",{target:"eyqil1z0"})(({containerWidth:o,size:r,theme:i})=>({display:"inline-flex",alignItems:"center",justifyContent:"center",fontWeight:i.fontWeights.normal,padding:`${i.spacing.xs} ${i.spacing.md}`,borderRadius:i.radii.button,minHeight:i.sizes.minElementHeight,margin:0,lineHeight:i.lineHeights.base,color:i.colors.primary,textDecoration:"none",width:o?"100%":"auto",userSelect:"none","&:visited":{color:i.colors.primary},"&:focus":{outline:"none"},"&:focus-visible":{boxShadow:`0 0 0 0.2rem ${v(i.colors.primary,.5)}`},"&:hover":{textDecoration:"none"},"&:active":{textDecoration:"none"},...x(r,i)})),L=t(b,{target:"eyqil1z1"})(({theme:o})=>({backgroundColor:o.colors.primary,color:o.colors.white,border:`${o.sizes.borderWidth} solid ${o.colors.primary}`,"&:hover, &:focus-visible":{backgroundColor:l(o.colors.primary,.15),borderColor:l(o.colors.primary,.15)},"&:active":{backgroundColor:o.colors.primary,borderColor:l(o.colors.primary,.15)},"&:visited:not(:active)":{color:o.colors.white},"&[disabled], &[disabled]:hover, &[disabled]:active, &[disabled]:visited":{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}})),S=t(b,{target:"eyqil1z2"})(({theme:o})=>({backgroundColor:o.colors.lightenedBg05,color:o.colors.bodyText,border:`${o.sizes.borderWidth} solid ${o.colors.borderColor}`,"&:visited":{color:o.colors.bodyText},"&:hover, &:focus-visible":{backgroundColor:o.colors.darkenedBgMix15},"&:active":{backgroundColor:o.colors.darkenedBgMix25},"&[disabled], &[disabled]:hover, &[disabled]:active":{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}})),$=t(b,{target:"eyqil1z3"})(({theme:o})=>({padding:o.spacing.none,backgroundColor:o.colors.transparent,color:o.colors.bodyText,border:"none","&:visited":{color:o.colors.bodyText},"&:hover, &:focus-visible":{color:o.colors.primary},"&:hover:not([disabled]), &:focus-visible:not([disabled])":{"span.stMarkdownColoredText":{color:"inherit !important"}},"&:active":{color:l(o.colors.primary,.25)},"&[disabled], &[disabled]:hover, &[disabled]:active":{backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}}));function T({kind:o,size:r,disabled:i,children:d,autoFocus:e,href:p,rel:g,target:y,onClick:f}){let c=L;return o===s.SECONDARY?c=S:o===s.TERTIARY&&(c=$),a(c,{kind:o,size:r||n.MEDIUM,containerWidth:!0,disabled:i||!1,autoFocus:e||!1,href:p,target:y,rel:g,onClick:f,tabIndex:i?-1:0,"data-testid":`stBaseLinkButton-${o}`,children:d})}const z=u.memo(T);function w(o){const{element:r}=o;let i=s.SECONDARY;r.type==="primary"?i=s.PRIMARY:r.type==="tertiary"&&(i=s.TERTIARY);const d=e=>{r.disabled&&e.preventDefault()};return a(C,{className:"stLinkButton","data-testid":"stLinkButton",children:a(k,{help:r.help,containerWidth:!0,children:a(z,{kind:i,size:n.SMALL,disabled:r.disabled,onClick:d,href:r.url,target:"_blank",rel:"noreferrer","aria-disabled":r.disabled,children:a(B,{icon:r.icon,label:r.label})})})})}const A=u.memo(w);export{A as default};
