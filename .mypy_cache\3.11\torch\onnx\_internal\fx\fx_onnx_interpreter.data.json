{".class": "MypyFile", "_fullname": "torch.onnx._internal.fx.fx_onnx_interpreter", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "FxOnnxInterpreter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "name": "FxOnnxInterpreter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.fx_onnx_interpreter", "mro": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "builtins.object"], "names": {".class": "SymbolTable", "call_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "node", "onnxscript_tracer", "fx_name_to_onnxscript_value", "onnxfunction_dispatcher", "fx_graph_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.call_function", "name": "call_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "node", "onnxscript_tracer", "fx_name_to_onnxscript_value", "onnxfunction_dispatcher", "fx_graph_module"], "arg_types": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "torch.fx.node.Node", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_function of FxOnnxInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.call_method", "name": "call_method", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_method of FxOnnxInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "node", "parent_onnxscript_graph", "fx_name_to_onnxscript_value", "tracer", "root_fx_graph_module", "onnxfunction_dispatcher"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.call_module", "name": "call_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "node", "parent_onnxscript_graph", "fx_name_to_onnxscript_value", "tracer", "root_fx_graph_module", "onnxfunction_dispatcher"], "arg_types": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "torch.fx.node.Node", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, "torch.fx.graph_module.GraphModule", "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_module of FxOnnxInterpreter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "onnxscript_graph", "fx_name_to_onnxscript_value", "fx_graph_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.get_attr", "name": "get_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "onnxscript_graph", "fx_name_to_onnxscript_value", "fx_graph_module"], "arg_types": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "torch.fx.node.Node", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.fx.graph_module.GraphModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_attr of FxOnnxInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "onnxscript_graph", "fx_name_to_onnxscript_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.output", "name": "output", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "onnxscript_graph", "fx_name_to_onnxscript_value"], "arg_types": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "torch.fx.node.Node", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output of FxOnnxInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "placeholder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "onnxscript_graph", "fx_name_to_onnxscript_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.placeholder", "name": "placeholder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "onnxscript_graph", "fx_name_to_onnxscript_value"], "arg_types": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "torch.fx.node.Node", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "placeholder of FxOnnxInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "fx_graph_module", "onnxfunction_dispatcher", "parent_onnxscript_graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "fx_graph_module", "onnxfunction_dispatcher", "parent_onnxscript_graph"], "arg_types": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "torch.fx.graph_module.GraphModule", "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of FxOnnxInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "node", "fx_graph_module", "onnxfunction_dispatcher", "onnxscript_graph", "onnxscript_tracer", "fx_name_to_onnxscript_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.run_node", "name": "run_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "node", "fx_graph_module", "onnxfunction_dispatcher", "onnxscript_graph", "onnxscript_tracer", "fx_name_to_onnxscript_value"], "arg_types": ["torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch.fx.graph_module.GraphModule", "torch.onnx._internal.fx.onnxfunction_dispatcher.OnnxFunctionDispatcher", {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_node of FxOnnxInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.fx_onnx_interpreter.FxOnnxInterpreter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_fill_in_default_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter._fill_in_default_kwargs", "name": "_fill_in_default_kwargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fill_in_default_kwargs", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fill_tensor_shape_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["onnxscript_values", "name", "expected_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter._fill_tensor_shape_type", "name": "_fill_tensor_shape_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["onnxscript_values", "name", "expected_values"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.META_VALUE_TYPE"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.META_VALUE_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.META_VALUE_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fill_tensor_shape_type", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fx_graph_to_onnx_message_formatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["fn", "self", "fx_graph_module", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter._fx_graph_to_onnx_message_formatter", "name": "_fx_graph_to_onnx_message_formatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["fn", "self", "fx_graph_module", "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch.fx.graph_module.GraphModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fx_graph_to_onnx_message_formatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fx_node_to_onnx_message_formatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["fn", "self", "node", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter._fx_node_to_onnx_message_formatter", "name": "_fx_node_to_onnx_message_formatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["fn", "self", "node", "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch.fx.node.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fx_node_to_onnx_message_formatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pass": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx._pass", "kind": "Gdef"}, "_pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "_retrieve_or_adapt_input_to_graph_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fx_node_arg", "fx_name_to_onnxscript_value", "tracer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter._retrieve_or_adapt_input_to_graph_set", "name": "_retrieve_or_adapt_input_to_graph_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fx_node_arg", "fx_name_to_onnxscript_value", "tracer"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_retrieve_or_adapt_input_to_graph_set", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrap_fx_args_as_onnxscript_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["complete_args", "complete_kwargs", "fx_name_to_onnxscript_value", "tracer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter._wrap_fx_args_as_onnxscript_args", "name": "_wrap_fx_args_as_onnxscript_args", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["complete_args", "complete_kwargs", "fx_name_to_onnxscript_value", "tracer"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_fx_args_as_onnxscript_args", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}, "builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.complex", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.onnx._internal.fx.type_utils.Argument"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "filter_incompatible_and_dtype_convert_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.filter_incompatible_and_dtype_convert_kwargs", "name": "filter_incompatible_and_dtype_convert_kwargs", "type": null}}, "fx_type_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.type_utils", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "jit_type_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._type_utils", "kind": "Gdef"}, "onnxfunction_dispatcher": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.onnxfunction_dispatcher", "kind": "Gdef"}, "onnxscript": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript", "name": "onnxscript", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript", "source_any": null, "type_of_any": 3}}}, "onnxscript_graph_building": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "name": "onnxscript_graph_building", "type": {".class": "AnyType", "missing_import_name": "torch.onnx._internal.fx.fx_onnx_interpreter.onnxscript_graph_building", "source_any": null, "type_of_any": 3}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\fx_onnx_interpreter.py"}