{".class": "MypyFile", "_fullname": "git.index.fun", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseIndexEntry": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntry", "kind": "Gdef", "module_public": false}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef", "module_public": false}, "CE_NAMEMASK": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.CE_NAMEMASK", "kind": "Gdef", "module_public": false}, "CE_NAMEMASK_INV": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.index.fun.CE_NAMEMASK_INV", "name": "CE_NAMEMASK_INV", "type": "builtins.int"}}, "CE_STAGESHIFT": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.CE_STAGESHIFT", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "GitCmdObjectDB": {".class": "SymbolTableNode", "cross_ref": "git.db.GitCmdObjectDB", "kind": "Gdef", "module_public": false}, "HookExecutionError": {".class": "SymbolTableNode", "cross_ref": "git.exc.HookExecutionError", "kind": "Gdef", "module_public": false}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_public": false}, "IStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.index.fun.IStream", "name": "IStream", "type": {".class": "AnyType", "missing_import_name": "git.index.fun.IStream", "source_any": null, "type_of_any": 3}}}, "IndexEntry": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.IndexEntry", "kind": "Gdef", "module_public": false}, "IndexFile": {".class": "SymbolTableNode", "cross_ref": "git.index.base.IndexFile", "kind": "Gdef", "module_public": false}, "IndexFileSHA1Writer": {".class": "SymbolTableNode", "cross_ref": "git.util.IndexFileSHA1Writer", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "S_IFDIR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFDIR", "kind": "Gdef", "module_public": false}, "S_IFGITLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.index.fun.S_IFGITLINK", "name": "S_IFGITLINK", "type": "builtins.int"}}, "S_IFLNK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFLNK", "kind": "Gdef", "module_public": false}, "S_IFMT": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFMT", "kind": "Gdef", "module_public": false}, "S_IFREG": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFREG", "kind": "Gdef", "module_public": false}, "S_ISDIR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISDIR", "kind": "Gdef", "module_public": false}, "S_ISLNK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISLNK", "kind": "Gdef", "module_public": false}, "S_IXUSR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IXUSR", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TreeCacheTup": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.TreeCacheTup", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnmergedEntriesError": {".class": "SymbolTableNode", "cross_ref": "git.exc.UnmergedEntriesError", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.index.fun.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.fun.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.fun.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.fun.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.fun.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.fun.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.fun.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_has_file_extension": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun._has_file_extension", "name": "_has_file_extension", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_file_extension", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tree_entry_to_baseindexentry": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tree_entry", "stage"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun._tree_entry_to_baseindexentry", "name": "_tree_entry_to_baseindexentry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tree_entry", "stage"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.tree.TreeCacheTup"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tree_entry_to_baseindexentry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aggressive_tree_merge": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["odb", "tree_shas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.aggressive_tree_merge", "name": "aggressive_tree_merge", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["odb", "tree_shas"], "arg_types": ["git.db.GitCmdObjectDB", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aggressive_tree_merge", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "defenc": {".class": "SymbolTableNode", "cross_ref": "git.compat.defenc", "kind": "Gdef", "module_public": false}, "entry_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["entry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.entry_key", "name": "entry_key", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["entry"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entry_key", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finalize_process": {".class": "SymbolTableNode", "cross_ref": "git.util.finalize_process", "kind": "Gdef", "module_public": false}, "force_bytes": {".class": "SymbolTableNode", "cross_ref": "git.compat.force_bytes", "kind": "Gdef", "module_public": false}, "force_text": {".class": "SymbolTableNode", "cross_ref": "git.compat.force_text", "kind": "Gdef", "module_public": false}, "handle_process_output": {".class": "SymbolTableNode", "cross_ref": "git.cmd.handle_process_output", "kind": "Gdef", "module_public": false}, "hook_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "git_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.hook_path", "name": "hook_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "git_dir"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hook_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "osp": {".class": "SymbolTableNode", "cross_ref": "os.path", "kind": "Gdef", "module_public": false}, "pack": {".class": "SymbolTableNode", "cross_ref": "git.index.util.pack", "kind": "Gdef", "module_public": false}, "read_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.read_cache", "name": "read_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["stream"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_cache", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_header": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.read_header", "name": "read_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["stream"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_header", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_commit_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["name", "index", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.run_commit_hook", "name": "run_commit_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["name", "index", "args"], "arg_types": ["builtins.str", "git.index.base.IndexFile", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_commit_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_decode": {".class": "SymbolTableNode", "cross_ref": "git.compat.safe_decode", "kind": "Gdef", "module_public": false}, "safer_popen": {".class": "SymbolTableNode", "cross_ref": "git.cmd.<PERSON>_popen", "kind": "Gdef", "module_public": false}, "stat_mode_to_index_mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.stat_mode_to_index_mode", "name": "stat_mode_to_index_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mode"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stat_mode_to_index_mode", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "str_tree_type": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.index.fun.str_tree_type", "name": "str_tree_type", "type": {".class": "AnyType", "missing_import_name": "git.index.fun.str_tree_type", "source_any": null, "type_of_any": 3}}}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "traverse_tree_recursive": {".class": "SymbolTableNode", "cross_ref": "git.objects.fun.traverse_tree_recursive", "kind": "Gdef", "module_public": false}, "traverse_trees_recursive": {".class": "SymbolTableNode", "cross_ref": "git.objects.fun.traverse_trees_recursive", "kind": "Gdef", "module_public": false}, "tree_to_stream": {".class": "SymbolTableNode", "cross_ref": "git.objects.fun.tree_to_stream", "kind": "Gdef", "module_public": false}, "unpack": {".class": "SymbolTableNode", "cross_ref": "git.index.util.unpack", "kind": "Gdef", "module_public": false}, "write_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["entries", "stream", "extension_data", "ShaStreamCls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.write_cache", "name": "write_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["entries", "stream", "extension_data", "ShaStreamCls"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bytes"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "git.util.IndexFileSHA1Writer"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_cache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_tree_from_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["entries", "odb", "sl", "si"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.fun.write_tree_from_cache", "name": "write_tree_from_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["entries", "odb", "sl", "si"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "git.db.GitCmdObjectDB", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_tree_from_cache", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.tree.TreeCacheTup"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\index\\fun.py"}