{"data_mtime": 1755649448, "dep_lines": [12, 12, 12, 5, 11, 2, 4, 6, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [10, 10, 20, 5, 10, 5, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch.onnx._internal.exporter._registration", "torch.onnx._internal.exporter._schemas", "torch.onnx._internal.exporter", "collections.abc", "torch.fx", "__future__", "logging", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._ops", "torch._tensor", "torch.fx.node"], "hash": "c6ee147f41c1df53bd03825ed60ec8c40266304f", "id": "torch.onnx._internal.exporter._dispatching", "ignore_all": true, "interface_hash": "e26c220bd93d21820eeebfdf728bf8a5a3bbddec", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_dispatching.py", "plugin_data": null, "size": 15169, "suppressed": ["onnxscript"], "version_id": "1.15.0"}