{".class": "MypyFile", "_fullname": "torch.utils.tensorboard", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FileWriter": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.writer.FileWriter", "kind": "Gdef"}, "RecordWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.RecordWriter", "name": "RecordWriter", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.RecordWriter", "source_any": null, "type_of_any": 3}}}, "SummaryWriter": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.writer.SummaryWriter", "kind": "Gdef"}, "Version": {".class": "SymbolTableNode", "cross_ref": "torch._vendor.packaging.version.Version", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "tensorboard": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.tensorboard", "name": "tensorboard", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.tensorboard", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\__init__.py"}