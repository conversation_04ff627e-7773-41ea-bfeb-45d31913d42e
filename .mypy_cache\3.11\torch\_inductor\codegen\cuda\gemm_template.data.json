{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cuda.gemm_template", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseSchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseSchedulerNode", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "CUDATemplateBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.CUDATemplateBuffer", "kind": "Gdef"}, "CUDATemplateKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", "kind": "Gdef"}, "CUTLASS2xGemmTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", "name": "CUTLASS2xGemmTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._inductor.codegen.cuda.gemm_template", "mro": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "torch._inductor.codegen.common.KernelTemplate", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "input_nodes", "layout", "alpha", "beta", "input_reorder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "input_nodes", "layout", "alpha", "beta", "input_reorder"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUTLASS2xGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alignment_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._alignment_match", "name": "_alignment_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alignment_match of CUTLASS2xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_are_inputs_layout_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layouts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._are_inputs_layout_compatible", "name": "_are_inputs_layout_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layouts"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.Layout"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_are_inputs_layout_compatible of CUTLASS2xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_define_gemm_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "op", "evt_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._define_gemm_instance", "name": "_define_gemm_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "op", "evt_name"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.gemm_template.GemmOperation"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_define_gemm_instance of CUTLASS2xGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_extra_inputs_and_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._get_extra_inputs_and_names", "name": "_get_extra_inputs_and_names", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_extra_inputs_and_names of CUTLASS2xGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_supported_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._get_supported_ops", "name": "_get_supported_ops", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_supported_ops of CUTLASS2xGemmTemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._get_supported_ops", "name": "_get_supported_ops", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_supported_ops of CUTLASS2xGemmTemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._get_template", "name": "_get_template", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_template of CUTLASS2xGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_template_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._get_template_args", "name": "_get_template_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_template_args of CUTLASS2xGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_tma_epilogue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._has_tma_epilogue", "name": "_has_tma_epilogue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_tma_epilogue of CUTLASS2xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._has_tma_epilogue", "name": "_has_tma_epilogue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_tma_epilogue of CUTLASS2xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_set_bias_layout_and_alignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._set_bias_layout_and_alignment", "name": "_set_bias_layout_and_alignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_bias_layout_and_alignment of CUTLASS2xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_shape_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._shape_match", "name": "_shape_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shape_match of CUTLASS2xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_arg_names_for_test_call_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg_names", "input_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate._update_arg_names_for_test_call_statement", "name": "_update_arg_names_for_test_call_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg_names", "input_nodes"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_arg_names_for_test_call_statement of CUTLASS2xGemmTemplate", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_cutlass_gemm_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate.add_cutlass_gemm_choices", "name": "add_cutlass_gemm_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_cutlass_gemm_choices of CUTLASS2xGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate.add_cutlass_gemm_choices", "name": "add_cutlass_gemm_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_cutlass_gemm_choices of CUTLASS2xGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "render_gemm_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_type", "argument_template", "epilogue_template", "should_swap_xw", "X", "W", "Bias", "Meta", "Y", "alpha", "beta", "kernel", "epilogue_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate.render_gemm_arguments", "name": "render_gemm_arguments", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_type", "argument_template", "epilogue_template", "should_swap_xw", "X", "W", "Bias", "Meta", "Y", "alpha", "beta", "kernel", "epilogue_args"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "torch._inductor.ir.IRNode", "torch._inductor.ir.IRNode", "torch._inductor.ir.IRNode", "torch._inductor.ir.IRNode", "torch._inductor.ir.IRNode", "builtins.float", "builtins.float", "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_gemm_arguments of CUTLASS2xGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.gemm_template.CUTLASS2xGemmTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUTLASS3xGemmTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", "name": "CUTLASS3xGemmTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._inductor.codegen.cuda.gemm_template", "mro": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "torch._inductor.codegen.common.KernelTemplate", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "input_nodes", "layout", "alpha", "beta", "input_reorder", "use_fast_accum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "input_nodes", "layout", "alpha", "beta", "input_reorder", "use_fast_accum"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUTLASS3xGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alignment_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._alignment_match", "name": "_alignment_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alignment_match of CUTLASS3xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_are_inputs_layout_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layouts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._are_inputs_layout_compatible", "name": "_are_inputs_layout_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layouts"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.Layout"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_are_inputs_layout_compatible of CUTLASS3xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_define_gemm_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "op", "evt_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._define_gemm_instance", "name": "_define_gemm_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "op", "evt_name"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.gemm_template.GemmOperation"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_define_gemm_instance of CUTLASS3xGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_extra_inputs_and_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._get_extra_inputs_and_names", "name": "_get_extra_inputs_and_names", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_extra_inputs_and_names of CUTLASS3xGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_supported_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._get_supported_ops", "name": "_get_supported_ops", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_supported_ops of CUTLASS3xGemmTemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._get_supported_ops", "name": "_get_supported_ops", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_get_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._get_template", "name": "_get_template", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_template of CUTLASS3xGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_template_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._get_template_args", "name": "_get_template_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_template_args of CUTLASS3xGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_tma_epilogue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._has_tma_epilogue", "name": "_has_tma_epilogue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_tma_epilogue of CUTLASS3xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._has_tma_epilogue", "name": "_has_tma_epilogue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_tma_epilogue of CUTLASS3xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_render_evt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "op", "evt_py_code", "var_name_to_buffer_name", "output_dtype", "accumulator_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._render_evt", "name": "_render_evt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "op", "evt_py_code", "var_name_to_buffer_name", "output_dtype", "accumulator_dtype"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.gemm_template.GemmOperation"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch._C.dtype", "torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_evt of CUTLASS3xGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_bias_layout_and_alignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._set_bias_layout_and_alignment", "name": "_set_bias_layout_and_alignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_bias_layout_and_alignment of CUTLASS3xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_shape_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._shape_match", "name": "_shape_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shape_match of CUTLASS3xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_arg_names_for_test_call_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg_names", "input_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate._update_arg_names_for_test_call_statement", "name": "_update_arg_names_for_test_call_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg_names", "input_nodes"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_arg_names_for_test_call_statement of CUTLASS3xGemmTemplate", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_cutlass_gemm_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate.add_cutlass_gemm_choices", "name": "add_cutlass_gemm_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_cutlass_gemm_choices of CUTLASS3xGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate.add_cutlass_gemm_choices", "name": "add_cutlass_gemm_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_cutlass_gemm_choices of CUTLASS3xGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "render_gemm_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "argument_template", "epilogue_template", "should_swap_xw", "X", "W", "Bias", "Y", "alpha", "beta", "kernel", "epilogue_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate.render_gemm_arguments", "name": "render_gemm_arguments", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "argument_template", "epilogue_template", "should_swap_xw", "X", "W", "Bias", "Y", "alpha", "beta", "kernel", "epilogue_args"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", "builtins.str", "builtins.str", "builtins.bool", "torch._inductor.ir.IRNode", "torch._inductor.ir.IRNode", "torch._inductor.ir.IRNode", "torch._inductor.ir.IRNode", "builtins.float", "builtins.float", "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_gemm_arguments of CUTLASS3xGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_epilogue_fusion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate.supports_epilogue_fusion", "name": "supports_epilogue_fusion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.gemm_template.GemmOperation"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_epilogue_fusion of CUTLASS3xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate.supports_epilogue_fusion", "name": "supports_epilogue_fusion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.gemm_template.GemmOperation"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_epilogue_fusion of CUTLASS3xGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.gemm_template.CUTLASS3xGemmTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUTLASSGemmTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_alignment_match", 1], ["_are_inputs_layout_compatible", 1], ["_define_gemm_instance", 1], ["_get_extra_inputs_and_names", 1], ["_get_supported_ops", 1], ["_get_template", 1], ["_get_template_args", 1], ["_has_tma_epilogue", 1], ["_set_bias_layout_and_alignment", 1], ["_shape_match", 1], ["_update_arg_names_for_test_call_statement", 1], ["add_cutlass_gemm_choices", 1]], "alt_promote": null, "bases": ["torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", "name": "CUTLASSGemmTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._inductor.codegen.cuda.gemm_template", "mro": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "torch._inductor.codegen.cuda.cuda_template.CUDATemplate", "torch._inductor.codegen.common.KernelTemplate", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "input_nodes", "layout", "alpha", "beta", "input_reorder", "use_fast_accum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "input_nodes", "layout", "alpha", "beta", "input_reorder", "use_fast_accum"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUTLASSGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_cutlass_gemm_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "extra_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._add_cutlass_gemm_choices", "name": "_add_cutlass_gemm_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "extra_kwargs"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_cutlass_gemm_choices of CUTLASSGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alignment_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._alignment_match", "name": "_alignment_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alignment_match of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._alignment_match", "name": "_alignment_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alignment_match of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_are_inputs_layout_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "layouts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._are_inputs_layout_compatible", "name": "_are_inputs_layout_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layouts"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.Layout"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_are_inputs_layout_compatible of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._are_inputs_layout_compatible", "name": "_are_inputs_layout_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layouts"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "Instance", "args": ["torch._inductor.ir.Layout"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_are_inputs_layout_compatible of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_define_gemm_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1], "arg_names": ["self", "op", "evt_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._define_gemm_instance", "name": "_define_gemm_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "op", "evt_name"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.gemm_template.GemmOperation"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_define_gemm_instance of CUTLASSGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._define_gemm_instance", "name": "_define_gemm_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "op", "evt_name"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.gemm_template.GemmOperation"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_define_gemm_instance of CUTLASSGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_dtype_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._dtype_match", "name": "_dtype_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dtype_match of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_extra_inputs_and_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._get_extra_inputs_and_names", "name": "_get_extra_inputs_and_names", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_extra_inputs_and_names of CUTLASSGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._get_extra_inputs_and_names", "name": "_get_extra_inputs_and_names", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_extra_inputs_and_names of CUTLASSGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_supported_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._get_supported_ops", "name": "_get_supported_ops", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_supported_ops of CUTLASSGemmTemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._get_supported_ops", "name": "_get_supported_ops", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_supported_ops of CUTLASSGemmTemplate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._get_template", "name": "_get_template", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_template of CUTLASSGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._get_template", "name": "_get_template", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_template of CUTLASSGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_template_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._get_template_args", "name": "_get_template_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_template_args of CUTLASSGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._get_template_args", "name": "_get_template_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_template_args of CUTLASSGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_has_tma_epilogue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._has_tma_epilogue", "name": "_has_tma_epilogue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_tma_epilogue of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._has_tma_epilogue", "name": "_has_tma_epilogue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_tma_epilogue of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_render_evt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "op", "evt_py_code", "buffer_renames", "output_dtype", "accumulator_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._render_evt", "name": "_render_evt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "op", "evt_py_code", "buffer_renames", "output_dtype", "accumulator_dtype"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cuda.gemm_template.GemmOperation"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch._C.dtype", "torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_evt of CUTLASSGemmTemplate", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_bias_layout_and_alignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._set_bias_layout_and_alignment", "name": "_set_bias_layout_and_alignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_bias_layout_and_alignment of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._set_bias_layout_and_alignment", "name": "_set_bias_layout_and_alignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_bias_layout_and_alignment of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_shape_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._shape_match", "name": "_shape_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shape_match of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._shape_match", "name": "_shape_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shape_match of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_update_arg_names_for_test_call_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg_names", "input_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._update_arg_names_for_test_call_statement", "name": "_update_arg_names_for_test_call_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg_names", "input_nodes"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_arg_names_for_test_call_statement of CUTLASSGemmTemplate", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate._update_arg_names_for_test_call_statement", "name": "_update_arg_names_for_test_call_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg_names", "input_nodes"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_arg_names_for_test_call_statement of CUTLASSGemmTemplate", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_cutlass_gemm_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated", "is_trivial_body"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.add_cutlass_gemm_choices", "name": "add_cutlass_gemm_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_cutlass_gemm_choices of CUTLASSGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.add_cutlass_gemm_choices", "name": "add_cutlass_gemm_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["choices", "layout", "input_nodes", "alpha", "beta", "input_reorder", "use_fast_accum", "extra_kwargs"], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ChoiceCaller"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._inductor.ir.Layout", {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_cutlass_gemm_choices of CUTLASSGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.alpha", "name": "alpha", "type": "builtins.float"}}, "beta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.beta", "name": "beta", "type": "builtins.float"}}, "cache_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.cache_clear", "name": "cache_clear", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "cache_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.cache_key", "name": "cache_key", "type": "builtins.str"}}, "cutlass_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["torch_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.cutlass_layout", "name": "cutlass_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["torch_layout"], "arg_types": ["torch._inductor.ir.Layout"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cutlass_layout of CUTLASSGemmTemplate", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.cutlass_layout", "name": "cutlass_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["torch_layout"], "arg_types": ["torch._inductor.ir.Layout"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cutlass_layout of CUTLASSGemmTemplate", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "filter_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.filter_op", "name": "filter_op", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_op of CUTLASSGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filtered_ops_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.filtered_ops_cache", "name": "filtered_ops_cache", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fix_op_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "op", "X", "W", "Bias", "Y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.fix_op_layout", "name": "fix_op_layout", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "op", "X", "W", "Bias", "Y"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fix_op_layout of CUTLASSGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flip_cutlass_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cutlass_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.flip_cutlass_layout", "name": "flip_cutlass_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cutlass_layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flip_cutlass_layout of CUTLASSGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.flip_cutlass_layout", "name": "flip_cutlass_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cutlass_layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flip_cutlass_layout of CUTLASSGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gemm_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.gemm_mode", "name": "gemm_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gemm_mode of CUTLASSGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gen_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.gen_ops", "name": "gen_ops", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gen_ops of CUTLASSGemmTemplate", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.header", "name": "header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header of CUTLASSGemmTemplate", "ret_type": "torch._inductor.utils.IndentedBuffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layout_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["torch_layout", "cutlass_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.layout_match", "name": "layout_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["torch_layout", "cutlass_layout"], "arg_types": ["torch._inductor.ir.Layout", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "layout_match of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.layout_match", "name": "layout_match", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "kernel", "op", "template_buffer_node", "epilogue_nodes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "kernel", "op", "template_buffer_node", "epilogue_nodes", "kwargs"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", "torch._inductor.codegen.cuda.cuda_kernel.CUDATemplateKernel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["torch._inductor.ir.CUDATemplateBuffer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.scheduler.BaseSchedulerNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of CUTLASSGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_alignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["torch_layout", "op_element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.set_alignment", "name": "set_alignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["torch_layout", "op_element"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_alignment of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.set_alignment", "name": "set_alignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["torch_layout", "op_element"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_alignment of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "should_swap_XW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.should_swap_XW", "name": "should_swap_XW", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bias"], "arg_types": ["torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_swap_XW of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.should_swap_XW", "name": "should_swap_XW", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bias"], "arg_types": ["torch._inductor.ir.IRNode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_swap_XW of CUTLASSGemmTemplate", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "swap_XW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.swap_XW", "name": "swap_XW", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swap_XW of CUTLASSGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.swap_XW", "name": "swap_XW", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swap_XW of CUTLASSGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_call_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "kernel", "input_nodes", "names_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.test_call_statement", "name": "test_call_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "kernel", "input_nodes", "names_str"], "arg_types": ["torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_call_statement of CUTLASSGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_fast_accum": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.use_fast_accum", "name": "use_fast_accum", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.gemm_template.CUTLASSGemmTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUTLASSTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cuda_template.CUTLASSTemplate", "kind": "Gdef"}, "ChoiceCaller": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.ChoiceCaller", "kind": "Gdef"}, "CutlassEVTCodegen": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_python_evt.CutlassEVTCodegen", "kind": "Gdef"}, "FixedLayout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.FixedLayout", "kind": "Gdef"}, "GEMM_ARGS_CUTLASS_2X": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.GEMM_ARGS_CUTLASS_2X", "name": "GEMM_ARGS_CUTLASS_2X", "type": "builtins.str"}}, "GEMM_ARGS_CUTLASS_3X": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.GEMM_ARGS_CUTLASS_3X", "name": "GEMM_ARGS_CUTLASS_3X", "type": "builtins.str"}}, "GEMM_ARGS_CUTLASS_3X_EPILOGUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.GEMM_ARGS_CUTLASS_3X_EPILOGUE", "name": "GEMM_ARGS_CUTLASS_3X_EPILOGUE", "type": "builtins.str"}}, "GEMM_ARGS_SPARSE_CUTLASS_2X": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.GEMM_ARGS_SPARSE_CUTLASS_2X", "name": "GEMM_ARGS_SPARSE_CUTLASS_2X", "type": "builtins.str"}}, "GEMM_STANDALONE_RUNNER_ADDITIONAL_INCLUDES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.GEMM_STANDALONE_RUNNER_ADDITIONAL_INCLUDES", "name": "GEMM_STANDALONE_RUNNER_ADDITIONAL_INCLUDES", "type": "builtins.str"}}, "GEMM_STANDALONE_RUNNER_TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.GEMM_STANDALONE_RUNNER_TEMPLATE", "name": "GEMM_STANDALONE_RUNNER_TEMPLATE", "type": "builtins.str"}}, "GEMM_TEMPLATE_CUTLASS_2X": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.GEMM_TEMPLATE_CUTLASS_2X", "name": "GEMM_TEMPLATE_CUTLASS_2X", "type": "builtins.str"}}, "GEMM_TEMPLATE_CUTLASS_3X": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.GEMM_TEMPLATE_CUTLASS_3X", "name": "GEMM_TEMPLATE_CUTLASS_3X", "type": "builtins.str"}}, "GemmOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.cuda.gemm_template.GemmOperation", "line": 40, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "IRNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.IRNode", "kind": "Gdef"}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Layout", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Placeholder": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.Placeholder", "kind": "Gdef"}, "ReinterpretView": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.gemm_template.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.gemm_template.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.gemm_template.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.gemm_template.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.gemm_template.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.gemm_template.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "clear_on_fresh_cache": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.clear_on_fresh_cache", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "create_inputs_key": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.create_inputs_key", "kind": "Gdef"}, "cutlass_utils": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_utils", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "gen_cutlass_presets": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_presets.gen_cutlass_presets", "kind": "Gdef"}, "inductor_cuda_config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config.cuda", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "is_dynamic": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.is_dynamic", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.gemm_template.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "maybe_fetch_ops": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_cache.maybe_fetch_ops", "kind": "Gdef"}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "scaled_mm_evt": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_python_evt.scaled_mm_evt", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "torch_dtype_to_cutlass_type": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_utils.torch_dtype_to_cutlass_type", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\gemm_template.py"}