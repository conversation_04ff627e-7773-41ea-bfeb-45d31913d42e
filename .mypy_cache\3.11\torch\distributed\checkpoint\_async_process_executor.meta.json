{"data_mtime": 1755649450, "dep_lines": [19, 20, 13, 14, 15, 16, 17, 18, 176, 5, 11, 12, 3, 4, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 5, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.elastic.agent.server.api", "torch.distributed.elastic.utils.distributed", "torch.distributed.checkpoint._async_executor", "torch.distributed.checkpoint.logger", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.storage", "torch.distributed.checkpoint.utils", "torch.distributed.checkpoint.state_dict_saver", "concurrent.futures", "torch.distributed", "torch.multiprocessing", "logging", "os", "dataclasses", "enum", "typing", "uuid", "torch", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "multiprocessing", "multiprocessing.connection", "multiprocessing.context", "multiprocessing.process", "torch._C", "torch._C._distributed_c10d", "torch.distributed.distributed_c10d", "typing_extensions"], "hash": "e4d79f49d0537961526bcc7c36aa78b32fe8fc9f", "id": "torch.distributed.checkpoint._async_process_executor", "ignore_all": true, "interface_hash": "0f114c551fbce9ed7f2c8ab89151cfed11b01eb8", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_async_process_executor.py", "plugin_data": null, "size": 12715, "suppressed": [], "version_id": "1.15.0"}