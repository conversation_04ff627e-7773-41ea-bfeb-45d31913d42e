{".class": "MypyFile", "_fullname": "streamlit.proto.Code_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Code_pb2.Code", "name": "Code", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.Code_pb2.Code", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Code_pb2", "mro": ["streamlit.proto.Code_pb2.Code", "builtins.object"], "names": {".class": "SymbolTable", "CODE_TEXT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.CODE_TEXT_FIELD_NUMBER", "name": "CODE_TEXT_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Code_pb2.Code.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Code_pb2.Code", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "code_text"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "code_text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "language"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "language"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "show_line_numbers"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "show_line_numbers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap_lines"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "wrap_lines"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Code", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Code_pb2.google", "source_any": null, "type_of_any": 3}}}, "HEIGHT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.HEIGHT_FIELD_NUMBER", "name": "HEIGHT_FIELD_NUMBER", "type": "builtins.int"}}, "LANGUAGE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.LANGUAGE_FIELD_NUMBER", "name": "LANGUAGE_FIELD_NUMBER", "type": "builtins.int"}}, "SHOW_LINE_NUMBERS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.SHOW_LINE_NUMBERS_FIELD_NUMBER", "name": "SHOW_LINE_NUMBERS_FIELD_NUMBER", "type": "builtins.int"}}, "WRAP_LINES_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.WRAP_LINES_FIELD_NUMBER", "name": "WRAP_LINES_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "code_text", "language", "show_line_numbers", "wrap_lines", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Code_pb2.Code.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "code_text", "language", "show_line_numbers", "wrap_lines", "height"], "arg_types": ["streamlit.proto.Code_pb2.Code", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Code", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.code_text", "name": "code_text", "type": "builtins.str"}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.height", "name": "height", "type": "builtins.int"}}, "language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.language", "name": "language", "type": "builtins.str"}}, "show_line_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.show_line_numbers", "name": "show_line_numbers", "type": "builtins.bool"}}, "wrap_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Code_pb2.Code.wrap_lines", "name": "wrap_lines", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Code_pb2.Code.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Code_pb2.Code", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Code_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Code_pb2.google", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Code_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Code_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Code_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Code_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Code_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Code_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___Code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.Code_pb2.global___Code", "line": 56, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Code_pb2.Code"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.Code_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Code_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\Code_pb2.pyi"}