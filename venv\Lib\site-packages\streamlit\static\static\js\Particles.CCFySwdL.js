import{r as s,P as p,b as m,j as o,F as x,s as C,L as g,c as P}from"./index.DKN5MVff.js";const y=({children:t})=>{const e=s.useContext(p)?.();return e?m.createPortal(t,e):o(x,{children:t})},u=C("div",{target:"e1swy67l0"})({"@media print":{display:"none"}}),M=({className:t,scriptRunId:e,numParticles:r,numParticleTypes:a,ParticleComponent:n})=>{const{libConfig:i}=s.useContext(g),c=s.useMemo(()=>P(r).map(()=>Math.floor(Math.random()*a)),[r,a]);return o(u,{className:t,"data-testid":t,children:c.map((l,d)=>o(n,{particleType:l,resourceCrossOriginMode:i.resourceCrossOriginMode},e+d))})},E=s.memo(M);export{E as P,y as R};
