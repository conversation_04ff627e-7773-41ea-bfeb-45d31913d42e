{".class": "MypyFile", "_fullname": "torch.distributed.fsdp._init_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackwardPrefetch": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.BackwardPrefetch", "kind": "Gdef"}, "CPUOffload": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.CPUOffload", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DTensorExtensions": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.parallel.fsdp.DTensorExtensions", "kind": "Gdef"}, "DeviceMesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.DeviceMesh", "kind": "Gdef"}, "FSDP_SYNCED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._init_utils.FSDP_SYNCED", "name": "FSDP_SYNCED", "type": "builtins.str"}}, "FlatParamHandle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParamHandle", "kind": "Gdef"}, "FlatParameter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.FlatParameter", "kind": "Gdef"}, "FullOptimStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.FullOptimStateDictConfig", "kind": "Gdef"}, "FullStateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.FullStateDictConfig", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "HYBRID_SHARDING_STRATEGIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._init_utils.HYBRID_SHARDING_STRATEGIES", "name": "HYBRID_SHARDING_STRATEGIES", "type": {".class": "Instance", "args": ["torch.distributed.fsdp.api.ShardingStrategy"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "HandleShardingStrategy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param.HandleShardingStrategy", "kind": "Gdef"}, "HybridShardProcessGroupType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.fsdp._init_utils.HybridShardProcessGroupType", "line": 62, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["torch._C._distributed_c10d.ProcessGroup", "torch._C._distributed_c10d.ProcessGroup"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "MixedPrecision": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.MixedPrecision", "kind": "Gdef"}, "NO_RESHARD_AFTER_FORWARD_STRATEGIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._init_utils.NO_RESHARD_AFTER_FORWARD_STRATEGIES", "name": "NO_RESHARD_AFTER_FORWARD_STRATEGIES", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.fsdp.api.ShardingStrategy", "torch.distributed.fsdp.api.ShardingStrategy"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PARAM_BROADCAST_BUCKET_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._init_utils.PARAM_BROADCAST_BUCKET_SIZE", "name": "PARAM_BROADCAST_BUCKET_SIZE", "type": "builtins.int"}}, "ProcessGroupType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.fsdp._init_utils.ProcessGroupType", "line": 64, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.fsdp._init_utils.HybridShardProcessGroupType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "RemovableHandle": {".class": "SymbolTableNode", "cross_ref": "torch.utils.hooks.RemovableHandle", "kind": "Gdef"}, "SHARDING_STRATEGY_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._init_utils.SHARDING_STRATEGY_MAP", "name": "SHARDING_STRATEGY_MAP", "type": {".class": "Instance", "args": ["torch.distributed.fsdp.api.ShardingStrategy", "torch.distributed.fsdp._flat_param.HandleShardingStrategy"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ShardingStrategy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.ShardingStrategy", "kind": "Gdef"}, "StateDictConfig": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictConfig", "kind": "Gdef"}, "StateDictType": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.api.StateDictType", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TrainingState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.TrainingState", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_FSDPDeviceHandle": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._FSDPDeviceHandle", "kind": "Gdef"}, "_FSDPState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._FSDPState", "kind": "Gdef"}, "_FSDP_USE_FULL_PREC_IN_EVAL": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._flat_param._FSDP_USE_FULL_PREC_IN_EVAL", "kind": "Gdef"}, "_FreeEventQueue": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._limiter_utils._FreeEventQueue", "kind": "Gdef"}, "_Policy": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.wrap._Policy", "kind": "Gdef"}, "_TORCHDISTX_AVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.fsdp._init_utils._TORCHDISTX_AVAIL", "name": "_TORCHDISTX_AVAIL", "type": "builtins.bool"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._init_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._init_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._init_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._init_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._init_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.fsdp._init_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_check_ignored_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ignored_states", "passed_as_ignored_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._check_ignored_states", "name": "_check_ignored_states", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ignored_states", "passed_as_ignored_states"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_ignored_states", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_module_states_for_sync_module_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._check_module_states_for_sync_module_states", "name": "_check_module_states_for_sync_module_states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module_states"], "arg_types": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_module_states_for_sync_module_states", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_orig_params_flattened": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fsdp_module", "ignored_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._check_orig_params_flattened", "name": "_check_orig_params_flattened", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fsdp_module", "ignored_params"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_orig_params_flattened", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_single_device_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["module", "ignored_params", "device_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._check_single_device_module", "name": "_check_single_device_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["module", "ignored_params", "device_id"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": ["builtins.int", "torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_single_device_module", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_buffer_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["root_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_buffer_names", "name": "_get_buffer_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["root_module"], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_buffer_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_compute_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["module", "ignored_params", "device_from_device_id", "rank", "device_handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_compute_device", "name": "_get_compute_device", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["module", "ignored_params", "device_from_device_id", "rank", "device_handle"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "torch.distributed.fsdp._common_utils._FSDPDeviceHandle"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_compute_device", "ret_type": "torch._C.device", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_default_comm_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sharding_strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_default_comm_hook", "name": "_get_default_comm_hook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sharding_strategy"], "arg_types": ["torch.distributed.fsdp.api.ShardingStrategy"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_default_comm_hook", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_default_comm_hook_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["process_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_default_comm_hook_state", "name": "_get_default_comm_hook_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["process_group"], "arg_types": ["torch._C._distributed_c10d.ProcessGroup"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_default_comm_hook_state", "ret_type": "torch.distributed.algorithms._comm_hooks.default_hooks.DefaultState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_default_group": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.distributed_c10d._get_default_group", "kind": "Gdef"}, "_get_device_from_device_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["device_id", "rank", "device_handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_device_from_device_id", "name": "_get_device_from_device_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["device_id", "rank", "device_handle"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "torch.distributed.fsdp._common_utils._FSDPDeviceHandle"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_device_from_device_id", "ret_type": {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ignored_buffer_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["root_module", "ignored_modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_ignored_buffer_names", "name": "_get_ignored_buffer_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["root_module", "ignored_modules"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ignored_buffer_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ignored_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["root_module", "_ignored_modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_ignored_modules", "name": "_get_ignored_modules", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["root_module", "_ignored_modules"], "arg_types": ["torch.nn.modules.module.Module", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ignored_modules", "ret_type": {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ignored_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["root_module", "ignored_modules", "ignored_parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_ignored_params", "name": "_get_ignored_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["root_module", "ignored_modules", "ignored_parameters"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ignored_params", "ret_type": {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_module_fsdp_state": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._get_module_fsdp_state", "kind": "Gdef"}, "_get_modules_to_materialize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["root_module", "ignored_modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_modules_to_materialize", "name": "_get_modules_to_materialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["root_module", "ignored_modules"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_modules_to_materialize", "ret_type": {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_orig_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module", "ignored_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._get_orig_params", "name": "_get_orig_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module", "ignored_params"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_orig_params", "ret_type": {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_buffer_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_buffer_state", "name": "_init_buffer_state", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_buffer_state", "name": "_init_buffer_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_core_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["state", "sharding_strategy", "mixed_precision", "cpu_offload", "limit_all_gathers", "use_orig_params", "backward_prefetch_limit", "forward_prefetch_limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_core_state", "name": "_init_core_state", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_core_state", "name": "_init_core_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_device_handle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["state", "module", "ignored_params", "device_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_device_handle", "name": "_init_device_handle", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_device_handle", "name": "_init_device_handle", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_extension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["state", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_extension", "name": "_init_extension", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_extension", "name": "_init_extension", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_ignored_module_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["state", "module", "ignored_modules", "ignored_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_ignored_module_states", "name": "_init_ignored_module_states", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_ignored_module_states", "name": "_init_ignored_module_states", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_inter_node_process_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["global_process_group", "num_devices_per_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_inter_node_process_group", "name": "_init_inter_node_process_group", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_inter_node_process_group", "name": "_init_inter_node_process_group", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_intra_and_inter_node_groups": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["global_process_group", "num_devices_per_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._init_intra_and_inter_node_groups", "name": "_init_intra_and_inter_node_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["global_process_group", "num_devices_per_node"], "arg_types": ["torch._C._distributed_c10d.ProcessGroup", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_intra_and_inter_node_groups", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._C._distributed_c10d.ProcessGroup", "torch._C._distributed_c10d.ProcessGroup"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_intra_node_process_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["num_devices_per_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_intra_node_process_group", "name": "_init_intra_node_process_group", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_intra_node_process_group", "name": "_init_intra_node_process_group", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_param_handle_from_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["state", "fully_sharded_module", "device_id", "param_init_fn", "sync_module_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_param_handle_from_module", "name": "_init_param_handle_from_module", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_param_handle_from_module", "name": "_init_param_handle_from_module", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_param_handle_from_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "params", "fully_sharded_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_param_handle_from_params", "name": "_init_param_handle_from_params", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_param_handle_from_params", "name": "_init_param_handle_from_params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_prefetching_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "backward_prefetch", "forward_prefetch"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_prefetching_state", "name": "_init_prefetching_state", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_prefetching_state", "name": "_init_prefetching_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_process_group_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["state", "process_group", "sharding_strategy", "policy", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_process_group_state", "name": "_init_process_group_state", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_process_group_state", "name": "_init_process_group_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_process_group_state_for_hybrid_shard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["state", "process_group", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_process_group_state_for_hybrid_shard", "name": "_init_process_group_state_for_hybrid_shard", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_process_group_state_for_hybrid_shard", "name": "_init_process_group_state_for_hybrid_shard", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_runtime_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_runtime_state", "name": "_init_runtime_state", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_runtime_state", "name": "_init_runtime_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_state_dict_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._init_state_dict_state", "name": "_init_state_dict_state", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._init_state_dict_state", "name": "_init_state_dict_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_is_fsdp_flattened": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._is_fsdp_flattened", "kind": "Gdef"}, "_is_valid_hybrid_shard_device_mesh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._is_valid_hybrid_shard_device_mesh", "name": "_is_valid_hybrid_shard_device_mesh", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._is_valid_hybrid_shard_device_mesh", "name": "_is_valid_hybrid_shard_device_mesh", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_is_valid_hybrid_shard_pg_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["process_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.fsdp._init_utils._is_valid_hybrid_shard_pg_type", "name": "_is_valid_hybrid_shard_pg_type", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils._is_valid_hybrid_shard_pg_type", "name": "_is_valid_hybrid_shard_pg_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_materialize_meta_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["root_module", "device_from_device_id", "ignored_modules", "device_handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._materialize_meta_module", "name": "_materialize_meta_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["root_module", "device_from_device_id", "ignored_modules", "device_handle"], "arg_types": ["torch.nn.modules.module.Module", {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.set"}, "torch.distributed.fsdp._common_utils._FSDPDeviceHandle"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_materialize_meta_module", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_materialize_with_param_init_fn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["root_module", "param_init_fn", "ignored_modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._materialize_with_param_init_fn", "name": "_materialize_with_param_init_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["root_module", "param_init_fn", "ignored_modules"], "arg_types": ["torch.nn.modules.module.Module", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_materialize_with_param_init_fn", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mesh_resources": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh._mesh_resources", "kind": "Gdef"}, "_move_module_to_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "ignored_params", "ignored_buffers", "device_from_device_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._move_module_to_device", "name": "_move_module_to_device", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["module", "ignored_params", "ignored_buffers", "device_from_device_id"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_move_module_to_device", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_move_states_to_device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["params", "buffers", "device_from_device_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._move_states_to_device", "name": "_move_states_to_device", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["params", "buffers", "device_from_device_id"], "arg_types": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_move_states_to_device", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_named_parameters_with_duplicates": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils._named_parameters_with_duplicates", "kind": "Gdef"}, "_need_to_materialize_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["module", "ignored_params", "ignored_modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._need_to_materialize_module", "name": "_need_to_materialize_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["module", "ignored_params", "ignored_modules"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_need_to_materialize_module", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sync_module_params_and_buffers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["module", "params", "process_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._sync_module_params_and_buffers", "name": "_sync_module_params_and_buffers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["module", "params", "process_group"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._C._distributed_c10d.ProcessGroup"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sync_module_params_and_buffers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sync_params_and_buffers": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.utils._sync_params_and_buffers", "kind": "Gdef"}, "_verify_managed_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._verify_managed_params", "name": "_verify_managed_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module", "params"], "arg_types": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_managed_params", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_warn_cpu_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.fsdp._init_utils._warn_cpu_init", "name": "_warn_cpu_init", "type": null}}, "clean_tensor_name": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._common_utils.clean_tensor_name", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "default_hooks": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms._comm_hooks.default_hooks", "kind": "Gdef"}, "deferred_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils.deferred_init", "name": "deferred_init", "type": {".class": "AnyType", "missing_import_name": "torch.distributed.fsdp._init_utils.deferred_init", "source_any": null, "type_of_any": 3}}}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "exec_order_utils": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._exec_order_utils", "kind": "Gdef"}, "fake": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.distributed.fsdp._init_utils.fake", "name": "fake", "type": {".class": "AnyType", "missing_import_name": "torch.distributed.fsdp._init_utils.fake", "source_any": null, "type_of_any": 3}}}, "fsdp_file": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp.fully_sharded_data_parallel", "kind": "Gdef"}, "is_traceable_wrapper_subclass": {".class": "SymbolTableNode", "cross_ref": "torch.utils._python_dispatch.is_traceable_wrapper_subclass", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "no_type_check": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "traversal_utils": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._traversal_utils", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_init_utils.py"}