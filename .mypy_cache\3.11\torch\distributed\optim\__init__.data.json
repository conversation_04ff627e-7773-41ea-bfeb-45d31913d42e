{".class": "MypyFile", "_fullname": "torch.distributed.optim", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DistributedOptimizer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.optimizer.DistributedOptimizer", "kind": "Gdef"}, "PostLocalSGDOptimizer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.post_localSGD_optimizer.PostLocalSGDOptimizer", "kind": "Gdef"}, "ZeroRedundancyOptimizer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.zero_redundancy_optimizer.ZeroRedundancyOptimizer", "kind": "Gdef"}, "_FunctionalAdadelta": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.functional_adadelta._FunctionalAdadelta", "kind": "Gdef", "module_public": false}, "_FunctionalAdagrad": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.functional_adagrad._FunctionalAdagrad", "kind": "Gdef", "module_public": false}, "_FunctionalAdam": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.functional_adam._FunctionalAdam", "kind": "Gdef", "module_public": false}, "_FunctionalAdamW": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.functional_adamw._FunctionalAdamW", "kind": "Gdef", "module_public": false}, "_FunctionalAdamax": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.functional_adamax._FunctionalAdamax", "kind": "Gdef", "module_public": false}, "_FunctionalRMSprop": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.functional_rmsprop._FunctionalRMSprop", "kind": "Gdef", "module_public": false}, "_FunctionalRprop": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.functional_rprop._FunctionalRprop", "kind": "Gdef", "module_public": false}, "_FunctionalSGD": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.functional_sgd._FunctionalSGD", "kind": "Gdef", "module_public": false}, "_NamedOptimizer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.named_optimizer._NamedOptimizer", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.optim.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.optim.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.optim.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.optim.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.optim.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.optim.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.optim.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.optim.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_apply_optimizer_in_backward": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.apply_optimizer_in_backward._apply_optimizer_in_backward", "kind": "Gdef", "module_public": false}, "_get_in_backward_optimizers": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.apply_optimizer_in_backward._get_in_backward_optimizers", "kind": "Gdef", "module_public": false}, "as_functional_optim": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.optim.utils.as_functional_optim", "kind": "Gdef"}, "optim": {".class": "SymbolTableNode", "cross_ref": "torch.optim", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\optim\\__init__.py"}