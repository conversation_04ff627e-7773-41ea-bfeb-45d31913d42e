{"data_mtime": 1755649448, "dep_lines": [37, 50, 74, 90, 91, 95, 101, 105, 106, 107, 131, 151, 24, 26, 29, 30, 30, 30, 30, 36, 49, 53, 58, 59, 65, 66, 75, 86, 93, 96, 97, 98, 99, 100, 100, 103, 104, 108, 109, 111, 123, 22, 24, 25, 26, 30, 49, 87, 88, 92, 127, 130, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 10, 10, 5, 10, 10, 10, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 10, 20, 5, 20, 20, 20, 5, 5, 5, 25, 25, 5, 5, 10, 10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch._dynamo.repro.after_aot", "torch._functorch._aot_autograd.subclass_parametrization", "torch._inductor.runtime.cache_dir_utils", "torch.fx.experimental.symbolic_shapes", "torch.fx.passes.fake_tensor_prop", "torch._dynamo.backends.common", "torch._inductor.codegen.common", "torch._inductor.fx_passes.joint_graph", "torch._inductor.fx_passes.post_grad", "torch._inductor.fx_passes.pre_grad", "torch.export.pt2_archive._package_weights", "torch._functorch._aot_autograd.schemas", "torch._inductor.async_compile", "torch.utils._pytree", "torch._dispatch.python", "torch._dynamo.compiled_autograd", "torch._dynamo.config", "torch._dynamo.logging", "torch._dynamo.utils", "torch._dynamo.device_interface", "torch._functorch.config", "torch._functorch.aot_autograd", "torch._inductor.codecache", "torch._inductor.cudagraph_utils", "torch._inductor.debug", "torch._inductor.output_code", "torch._inductor.utils", "torch._library.fake_class_registry", "torch.utils._ordered_set", "torch._dynamo.exc", "torch.fx._lazy_graph_module", "torch.fx.graph", "torch.utils._triton", "torch._inductor.config", "torch._inductor.metrics", "torch._inductor.decomposition", "torch._inductor.exc", "torch._inductor.graph", "torch._inductor.ir", "torch._inductor.triton_bundler", "torch._inductor.virtualized", "unittest.mock", "torch._inductor", "torch.fx", "torch.utils", "torch._dynamo", "torch._functorch", "torch._logging", "torch._utils_internal", "torch.monitor", "collections.abc", "torch._ops", "__future__", "contextlib", "enum", "functools", "io", "itertools", "json", "logging", "os", "sys", "time", "warnings", "abc", "collections", "inspect", "operator", "typing", "typing_extensions", "unittest", "torch", "builtins", "html", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "copy", "_frozen_importlib", "_io", "_typeshed", "json.encoder", "torch._C", "torch._C._monitor", "torch._dynamo.metrics_context", "torch._dynamo.repro", "torch._functorch._aot_autograd", "torch._guards", "torch._inductor.codegen", "torch._inductor.codegen.wrapper", "torch._inductor.fx_passes", "torch._inductor.scheduler", "torch._library", "torch._logging._internal", "torch._subclasses", "torch._subclasses.fake_tensor", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.export", "torch.export.pt2_archive", "torch.fx._utils", "torch.fx.experimental", "torch.fx.graph_module", "torch.fx.interpreter", "torch.fx.node", "torch.fx.traceback", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.types", "torch.utils._contextlib", "torch.utils._python_dispatch", "torch.utils._sympy", "torch.utils._sympy.printers"], "hash": "92cf9ecf2db236240fd8ace46a570d606950efe1", "id": "torch._inductor.compile_fx", "ignore_all": true, "interface_hash": "7ce52a72f33af3cb340066dc49b0f14b514ca5e1", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\compile_fx.py", "plugin_data": null, "size": 105541, "suppressed": ["functorch.compile"], "version_id": "1.15.0"}