{"data_mtime": 1755649448, "dep_lines": [286, 26, 27, 28, 30, 172, 22, 26, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 10, 5, 5, 5, 20, 5, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30], "dependencies": ["torch._C._dynamo.eval_frame", "torch._inductor.package", "torch._dynamo.precompile_context", "torch.compiler._cache", "torch._dynamo.bytecode_transformation", "torch._dynamo.eval_frame", "collections.abc", "torch._inductor", "contextlib", "dataclasses", "functools", "<PERSON><PERSON><PERSON>", "importlib", "logging", "os", "pickle", "platform", "sys", "types", "typing", "torch", "builtins", "inspect", "html", "string", "operator", "pprint", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "copy", "_frozen_importlib", "abc", "torch.compiler"], "hash": "5145d821d428f639c3a50dc091523f19beb7318d", "id": "torch._dynamo.package", "ignore_all": true, "interface_hash": "b681d921efe44f1dbad9f726b546aba0b006326a", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_dynamo\\package.py", "plugin_data": null, "size": 16368, "suppressed": [], "version_id": "1.15.0"}