{"data_mtime": 1755649448, "dep_lines": [18, 16, 22, 23, 31, 32, 47, 59, 60, 79, 80, 90, 91, 98, 16, 76, 77, 9, 15, 17, 3, 4, 5, 6, 7, 8, 11, 12, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 5, 5, 10, 10, 5, 10, 10, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.algorithms._checkpoint.checkpoint_wrapper", "torch.distributed.fsdp._traversal_utils", "torch.distributed.algorithms._comm_hooks", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp._dynamo_utils", "torch.distributed.fsdp._init_utils", "torch.distributed.fsdp._runtime_utils", "torch.distributed.fsdp._wrap_utils", "torch.distributed.fsdp.api", "torch.distributed.fsdp._flat_param", "torch.distributed.fsdp._optim_utils", "torch.distributed.fsdp._state_dict_utils", "torch.distributed.fsdp._unshard_param_utils", "torch.distributed.fsdp.wrap", "torch.distributed.fsdp", "torch.distributed.tensor", "torch.distributed.utils", "collections.abc", "torch.distributed", "torch.nn", "contextlib", "copy", "functools", "math", "traceback", "warnings", "enum", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "_frozen_importlib", "abc", "torch._C", "torch._C._VariableFunctions", "torch._C._distributed_c10d", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.distributed._composable_state", "torch.distributed.device_mesh", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.nn.parameter", "torch.nn.utils", "torch.optim", "torch.optim.optimizer", "torch.utils", "torch.utils._contextlib", "typing_extensions"], "hash": "1a9c92f326b13cfe18024d637055c082831120a1", "id": "torch.distributed.fsdp.fully_sharded_data_parallel", "ignore_all": true, "interface_hash": "3cdf32d4fc75fea8cf0e67a3124ba767e21f0483", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\fully_sharded_data_parallel.py", "plugin_data": null, "size": 102447, "suppressed": [], "version_id": "1.15.0"}