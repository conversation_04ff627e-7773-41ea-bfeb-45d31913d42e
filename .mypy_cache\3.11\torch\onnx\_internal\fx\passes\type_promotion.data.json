{".class": "MypyFile", "_fullname": "torch.onnx._internal.fx.passes.type_promotion", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllOrAnyReductionTypePromotionRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule", "name": "AllOrAnyReductionTypePromotionRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule", "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op_name"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AllOrAnyReductionTypePromotionRule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preview_type_promotion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule.preview_type_promotion", "name": "preview_type_promotion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preview_type_promotion of AllOrAnyReductionTypePromotionRule", "ret_type": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.AllOrAnyReductionTypePromotionRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DivElementwiseTypePromotionRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.DivElementwiseTypePromotionRule", "name": "DivElementwiseTypePromotionRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.DivElementwiseTypePromotionRule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.DivElementwiseTypePromotionRule", "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.DivElementwiseTypePromotionRule.__init__", "name": "__init__", "type": null}}, "preview_type_promotion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.DivElementwiseTypePromotionRule.preview_type_promotion", "name": "preview_type_promotion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.DivElementwiseTypePromotionRule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preview_type_promotion of DivElementwiseTypePromotionRule", "ret_type": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.DivElementwiseTypePromotionRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.DivElementwiseTypePromotionRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ELEMENTWISE_TYPE_PROMOTION_KIND": {".class": "SymbolTableNode", "cross_ref": "torch._prims_common.ELEMENTWISE_TYPE_PROMOTION_KIND", "kind": "Gdef"}, "ElementwiseTypePromotionRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", "name": "ElementwiseTypePromotionRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_USE_OPMATH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule._USE_OPMATH", "name": "_USE_OPMATH", "type": "builtins.bool"}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of ElementwiseTypePromotionRule", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of ElementwiseTypePromotionRule", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "namespace", "op_name", "promote_args_positions", "promote_kwargs_names", "promotion_kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "namespace", "op_name", "promote_args_positions", "promote_kwargs_names", "promotion_kind"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "torch._prims_common.ELEMENTWISE_TYPE_PROMOTION_KIND"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ElementwiseTypePromotionRule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.__repr__", "name": "__repr__", "type": null}}, "_consolidate_input_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "computed_dtype", "result_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule._consolidate_input_dtype", "name": "_consolidate_input_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "computed_dtype", "result_dtype"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", "torch._C.dtype", "torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_consolidate_input_dtype of ElementwiseTypePromotionRule", "ret_type": "torch._C.dtype", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preview_type_promotion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.preview_type_promotion", "name": "preview_type_promotion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preview_type_promotion of ElementwiseTypePromotionRule", "ret_type": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "promote_args_positions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.promote_args_positions", "name": "promote_args_positions", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "promote_kwargs_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.promote_kwargs_names", "name": "promote_kwargs_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "promotion_kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.promotion_kind", "name": "promotion_kind", "type": "torch._prims_common.ELEMENTWISE_TYPE_PROMOTION_KIND"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ElementwiseTypePromotionRuleSetGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator", "name": "ElementwiseTypePromotionRuleSetGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator", "builtins.object"], "names": {".class": "SymbolTable", "_parse_torch_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "ref_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator._parse_torch_refs", "name": "_parse_torch_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "ref_module"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator"}, "types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_torch_refs of ElementwiseTypePromotionRuleSetGenerator", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator._parse_torch_refs", "name": "_parse_torch_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "ref_module"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator"}, "types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_torch_refs of ElementwiseTypePromotionRuleSetGenerator", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_parse_type_promotion_rule_from_refs_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "decorated_op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator._parse_type_promotion_rule_from_refs_op", "name": "_parse_type_promotion_rule_from_refs_op", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorated_op"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_type_promotion_rule_from_refs_op of ElementwiseTypePromotionRuleSetGenerator", "ret_type": {".class": "UnionType", "items": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator._parse_type_promotion_rule_from_refs_op", "name": "_parse_type_promotion_rule_from_refs_op", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorated_op"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_type_promotion_rule_from_refs_op of ElementwiseTypePromotionRuleSetGenerator", "ret_type": {".class": "UnionType", "items": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "generate_from_torch_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator.generate_from_torch_refs", "name": "generate_from_torch_refs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_from_torch_refs of ElementwiseTypePromotionRuleSetGenerator", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator.generate_from_torch_refs", "name": "generate_from_torch_refs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_from_torch_refs of ElementwiseTypePromotionRuleSetGenerator", "ret_type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRuleSetGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InsertTypePromotion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx._pass.Transform"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion", "name": "InsertTypePromotion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion", "torch.onnx._internal.fx._pass.Transform", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "module", "type_promotion_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "module", "type_promotion_table"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion", "torch.fx.graph_module.GraphModule", {".class": "UnionType", "items": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InsertTypePromotion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fetch_fake_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion._fetch_fake_args", "name": "_fetch_fake_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fetch_fake_args of InsertTypePromotion", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._subclasses.fake_tensor.FakeTensor", "builtins.float", "builtins.int", "builtins.bool", "torch.SymInt", "torch.SymFloat", "torch.SymBool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion._run", "name": "_run", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run of InsertTypePromotion", "ret_type": "torch.fx.graph_module.GraphModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "interpreter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion.interpreter", "name": "interpreter", "type": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.InsertTypePromotion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "ReductionTypePromotionRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", "name": "ReductionTypePromotionRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of ReductionTypePromotionRule", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of ReductionTypePromotionRule", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "namespace", "op_name", "promotion_kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "namespace", "op_name", "promotion_kind"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", "builtins.str", "builtins.str", "torch._prims_common.REDUCTION_OUTPUT_TYPE_KIND"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ReductionTypePromotionRule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule.__repr__", "name": "__repr__", "type": null}}, "preview_type_promotion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule.preview_type_promotion", "name": "preview_type_promotion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preview_type_promotion of ReductionTypePromotionRule", "ret_type": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "promotion_kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule.promotion_kind", "name": "promotion_kind", "type": "torch._prims_common.REDUCTION_OUTPUT_TYPE_KIND"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SumLikeReductionTypePromotionRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.SumLikeReductionTypePromotionRule", "name": "SumLikeReductionTypePromotionRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.SumLikeReductionTypePromotionRule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.SumLikeReductionTypePromotionRule", "torch.onnx._internal.fx.passes.type_promotion.ReductionTypePromotionRule", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "preview_type_promotion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.SumLikeReductionTypePromotionRule.preview_type_promotion", "name": "preview_type_promotion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.SumLikeReductionTypePromotionRule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preview_type_promotion of SumLikeReductionTypePromotionRule", "ret_type": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.SumLikeReductionTypePromotionRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.SumLikeReductionTypePromotionRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypePromotionRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__eq__", 1], ["__hash__", 1], ["__repr__", 1], ["preview_type_promotion", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "name": "TypePromotionRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of TypePromotionRule", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of TypePromotionRule", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of TypePromotionRule", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of TypePromotionRule", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "namespace", "op_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "namespace", "op_name"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypePromotionRule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.__repr__", "name": "__repr__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of TypePromotionRule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_valid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.is_valid", "name": "is_valid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid of TypePromotionRule", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.namespace", "name": "namespace", "type": "builtins.str"}}, "op_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.op_name", "name": "op_name", "type": "builtins.str"}}, "preview_type_promotion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.preview_type_promotion", "name": "preview_type_promotion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preview_type_promotion of TypePromotionRule", "ret_type": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.preview_type_promotion", "name": "preview_type_promotion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preview_type_promotion of TypePromotionRule", "ret_type": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypePromotionSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "name": "TypePromotionSnapshot", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "args_dtypes", "type": {".class": "Instance", "args": ["builtins.int", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "kwargs_dtypes", "type": {".class": "Instance", "args": ["builtins.str", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 59, "name": "out_dtype", "type": "torch._C.dtype"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "args_dtypes", "kwargs_dtypes", "out_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "args_dtypes", "kwargs_dtypes", "out_dtype"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", {".class": "Instance", "args": ["builtins.int", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "torch._C.dtype"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypePromotionSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "args_dtypes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "kwargs_dtypes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "out_dtype"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["args_dtypes", "kwargs_dtypes", "out_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["args_dtypes", "kwargs_dtypes", "out_dtype"], "arg_types": [{".class": "Instance", "args": ["builtins.int", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "torch._C.dtype"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TypePromotionSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["args_dtypes", "kwargs_dtypes", "out_dtype"], "arg_types": [{".class": "Instance", "args": ["builtins.int", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "torch._C.dtype"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TypePromotionSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "args_dtypes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.args_dtypes", "name": "args_dtypes", "type": {".class": "Instance", "args": ["builtins.int", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "kwargs_dtypes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.kwargs_dtypes", "name": "kwargs_dtypes", "type": {".class": "Instance", "args": ["builtins.str", "torch._C.dtype"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "out_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.out_dtype", "name": "out_dtype", "type": "torch._C.dtype"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionSnapshot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypePromotionTable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable", "name": "TypePromotionTable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable.__init__", "name": "__init__", "type": null}}, "_rule_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable._rule_table", "name": "_rule_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "add_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rule"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable.add_rule", "name": "add_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rule"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_rule of TypePromotionTable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "py_op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable.get_rule", "name": "get_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "py_op"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable", {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverloadPacket"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rule of TypePromotionTable", "ret_type": {".class": "UnionType", "items": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EXTRA_TYPE_PROMOTION_RULE_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.passes.type_promotion._EXTRA_TYPE_PROMOTION_RULE_SET", "name": "_EXTRA_TYPE_PROMOTION_RULE_SET", "type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_GENERATED_ATEN_TYPE_PROMOTION_RULE_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.passes.type_promotion._GENERATED_ATEN_TYPE_PROMOTION_RULE_SET", "name": "_GENERATED_ATEN_TYPE_PROMOTION_RULE_SET", "type": {".class": "Instance", "args": ["torch.onnx._internal.fx.passes.type_promotion.ElementwiseTypePromotionRule"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_OpTraceDispatchMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils._python_dispatch.TorchDispatchMode"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion._OpTraceDispatchMode", "name": "_OpTraceDispatchMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._OpTraceDispatchMode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion._OpTraceDispatchMode", "torch.utils._python_dispatch.TorchDispatchMode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._OpTraceDispatchMode.__init__", "name": "__init__", "type": null}}, "__torch_dispatch__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "func", "types", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._OpTraceDispatchMode.__torch_dispatch__", "name": "__torch_dispatch__", "type": null}}, "traced_ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion._OpTraceDispatchMode.traced_ops", "name": "traced_ops", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion._OpTraceDispatchMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion._OpTraceDispatchMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TypePromotionInterpreter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.fx.interpreter.Interpreter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "name": "_TypePromotionInterpreter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.onnx._internal.fx.passes.type_promotion", "mro": ["torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "torch.fx.interpreter.Interpreter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "type_promotion_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "type_promotion_table"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "torch.fx.graph_module.GraphModule", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _TypePromotionInterpreter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "graph", "op_type", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter._create_node", "name": "_create_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "graph", "op_type", "target", "args", "kwargs"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "torch.fx.graph.Graph", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.node.Target"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_node of _TypePromotionInterpreter", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_maybe_promote_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "fx_arg", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter._maybe_promote_arg", "name": "_maybe_promote_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "fx_arg", "dtype"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "torch.fx.node.Node", {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.node.Argument"}, {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_maybe_promote_arg of _TypePromotionInterpreter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.fx.node.Argument"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_maybe_promote_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "rule"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter._maybe_promote_node", "name": "_maybe_promote_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "rule"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "torch.fx.node.Node", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_maybe_promote_node of _TypePromotionInterpreter", "ret_type": "torch.fx.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rerun_node_after_type_promotion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "expected_out_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter._rerun_node_after_type_promotion", "name": "_rerun_node_after_type_promotion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "expected_out_dtype"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "torch.fx.node.Node", "torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rerun_node_after_type_promotion of _TypePromotionInterpreter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run_node_and_set_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter._run_node_and_set_meta", "name": "_run_node_and_set_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_node_and_set_meta of _TypePromotionInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter.run_node", "name": "run_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "n"], "arg_types": ["torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_node of _TypePromotionInterpreter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type_promotion_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter.type_promotion_table", "name": "type_promotion_table", "type": "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.fx.passes.type_promotion._TypePromotionInterpreter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_functional_refs": {".class": "SymbolTableNode", "cross_ref": "torch._refs.nn.functional", "kind": "Gdef"}, "_linalg_refs": {".class": "SymbolTableNode", "cross_ref": "torch._refs.linalg", "kind": "Gdef"}, "_nn_refs": {".class": "SymbolTableNode", "cross_ref": "torch._refs.nn", "kind": "Gdef"}, "_pass": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx._pass", "kind": "Gdef"}, "_prims_common": {".class": "SymbolTableNode", "cross_ref": "torch._prims_common", "kind": "Gdef"}, "_prims_common_wrappers": {".class": "SymbolTableNode", "cross_ref": "torch._prims_common.wrappers", "kind": "Gdef"}, "_python_dispatch": {".class": "SymbolTableNode", "cross_ref": "torch.utils._python_dispatch", "kind": "Gdef"}, "_pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "_refs": {".class": "SymbolTableNode", "cross_ref": "torch._refs", "kind": "Gdef"}, "_special_refs": {".class": "SymbolTableNode", "cross_ref": "torch._refs.special", "kind": "Gdef"}, "_try_getclosurevars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion._try_getclosurevars", "name": "_try_getclosurevars", "type": null}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "fake_tensor": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor", "kind": "Gdef"}, "find_compatible_op_overload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["op", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.find_compatible_op_overload", "name": "find_compatible_op_overload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["op", "args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverloadPacket"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_compatible_op_overload", "ret_type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fx_traceback": {".class": "SymbolTableNode", "cross_ref": "torch.fx.traceback", "kind": "Gdef"}, "fx_type_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.fx.type_utils", "kind": "Gdef"}, "get_type_promotion_rule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node", "type_promotion_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.fx.passes.type_promotion.get_type_promotion_rule", "name": "get_type_promotion_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node", "type_promotion_table"], "arg_types": ["torch.fx.node.Node", "torch.onnx._internal.fx.passes.type_promotion.TypePromotionTable"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_type_promotion_rule", "ret_type": {".class": "UnionType", "items": ["torch.onnx._internal.fx.passes.type_promotion.TypePromotionRule", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx._internal.fx.passes.type_promotion.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "proxy_tensor": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\fx\\passes\\type_promotion.py"}