{".class": "MypyFile", "_fullname": "torch.onnx.utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_public": false}, "GLOBALS": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._globals.GLOBALS", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "_C": {".class": "SymbolTableNode", "cross_ref": "torch._C", "kind": "Gdef", "module_public": false}, "_C_onnx": {".class": "SymbolTableNode", "cross_ref": "torch._<PERSON>._onnx", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.utils.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx.utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_add_block": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._add_block", "name": "_add_block", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_block", "ret_type": "torch._C.Block", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_input_to_block": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._add_input_to_block", "name": "_add_input_to_block", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["block"], "arg_types": ["torch._C.Block"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_input_to_block", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_output_to_block": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["block", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._add_output_to_block", "name": "_add_output_to_block", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["block", "value"], "arg_types": ["torch._C.Block", "torch._C.Value"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_output_to_block", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_friendly_debug_names": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["graph", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._apply_friendly_debug_names", "name": "_apply_friendly_debug_names", "type": null}}, "_check_flatten_did_not_remove": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["original", "jit_flattened"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._check_flatten_did_not_remove", "name": "_check_flatten_did_not_remove", "type": null}}, "_constants": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._constants", "kind": "Gdef", "module_public": false}, "_create_jit_graph": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._create_jit_graph", "name": "_create_jit_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["model", "args"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_jit_graph", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", {".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["torch._<PERSON>.<PERSON>Mod<PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_decide_add_node_names": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["add_node_names", "operator_export_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._decide_add_node_names", "name": "_decide_add_node_names", "type": null}}, "_decide_constant_folding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["do_constant_folding", "operator_export_type", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._decide_constant_folding", "name": "_decide_constant_folding", "type": null}}, "_decide_input_format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._decide_input_format", "name": "_decide_input_format", "type": null}}, "_decide_keep_init_as_input": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["keep_initializers_as_inputs", "operator_export_type", "opset_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._decide_keep_init_as_input", "name": "_decide_keep_init_as_input", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["keep_initializers_as_inputs", "operator_export_type", "opset_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "torch._C._onnx.OperatorExportTypes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_decide_keep_init_as_input", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_export": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "args", "f", "export_params", "verbose", "training", "input_names", "output_names", "operator_export_type", "export_type", "opset_version", "do_constant_folding", "dynamic_axes", "keep_initializers_as_inputs", "fixed_batch_size", "custom_opsets", "add_node_names", "onnx_shape_inference", "export_modules_as_functions", "autograd_inlining"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._export", "name": "_export", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "args", "f", "export_params", "verbose", "training", "input_names", "output_names", "operator_export_type", "export_type", "opset_version", "do_constant_folding", "dynamic_axes", "keep_initializers_as_inputs", "fixed_batch_size", "custom_opsets", "add_node_names", "onnx_shape_inference", "export_modules_as_functions", "autograd_inlining"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_export", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_aten_op_overload_name": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._get_aten_op_overload_name", "name": "_get_aten_op_overload_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["n"], "arg_types": ["torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_aten_op_overload_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_example_outputs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._get_example_outputs", "name": "_get_example_outputs", "type": null}}, "_get_module_attributes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._get_module_attributes", "name": "_get_module_attributes", "type": null}}, "_get_named_param_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["graph", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._get_named_param_dict", "name": "_get_named_param_dict", "type": null}}, "_get_param_count_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["method_graph", "args_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._get_param_count_list", "name": "_get_param_count_list", "type": null}}, "_get_torch_export_args": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._get_torch_export_args", "name": "_get_torch_export_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_torch_export_args", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_constant_tensor_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._is_constant_tensor_list", "name": "_is_constant_tensor_list", "type": null}}, "_model_to_graph": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "args", "verbose", "input_names", "output_names", "operator_export_type", "do_constant_folding", "_disable_torch_constant_prop", "fixed_batch_size", "training", "dynamic_axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._model_to_graph", "name": "_model_to_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "args", "verbose", "input_names", "output_names", "operator_export_type", "do_constant_folding", "_disable_torch_constant_prop", "fixed_batch_size", "training", "dynamic_axes"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_model_to_graph", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_optimize_graph": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["graph", "operator_export_type", "_disable_torch_constant_prop", "fixed_batch_size", "params_dict", "dynamic_axes", "input_names", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._optimize_graph", "name": "_optimize_graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["graph", "operator_export_type", "_disable_torch_constant_prop", "fixed_batch_size", "params_dict", "dynamic_axes", "input_names", "module"], "arg_types": ["torch._<PERSON><PERSON>", "torch._C._onnx.OperatorExportTypes", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_optimize_graph", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_params_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "torch.onnx.utils._params_dict", "name": "_params_dict", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_pre_trace_quant_model": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._pre_trace_quant_model", "name": "_pre_trace_quant_model", "type": null}}, "_qtype_vtype_map": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.onnx.utils._qtype_vtype_map", "name": "_qtype_vtype_map", "type": {".class": "Instance", "args": ["torch._C.dtype", "torch._C.dtype"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_reset_trace_module_map": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._reset_trace_module_map", "name": "_reset_trace_module_map", "type": null}}, "_resolve_args_by_export_type": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["arg_name", "arg_value", "operator_export_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._resolve_args_by_export_type", "name": "_resolve_args_by_export_type", "type": null}}, "_run_symbolic_function": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["graph", "block", "node", "inputs", "env", "values_in_env", "new_nodes", "operator_export_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._run_symbolic_function", "name": "_run_symbolic_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["graph", "block", "node", "inputs", "env", "values_in_env", "new_nodes", "operator_export_type"], "arg_types": ["torch._<PERSON><PERSON>", "torch._C.Block", "torch._<PERSON><PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["torch._C.Value", "torch._C.Value"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["torch._C.Value"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_symbolic_function", "ret_type": {".class": "UnionType", "items": ["torch._C.Value", {".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._C.Value", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run_symbolic_method": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["g", "op_name", "symbolic_fn", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._run_symbolic_method", "name": "_run_symbolic_method", "type": null}}, "_set_input_and_output_names": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["graph", "input_names", "output_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._set_input_and_output_names", "name": "_set_input_and_output_names", "type": null}}, "_setup_trace_module_map": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "export_modules_as_functions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._setup_trace_module_map", "name": "_setup_trace_module_map", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["model", "export_modules_as_functions"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", "torch.jit._script.ScriptModule"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": [{".class": "TypeType", "item": "torch.nn.modules.module.Module"}], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_trace_module_map", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_aten_fallback": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "opset_version", "operator_export_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._should_aten_fallback", "name": "_should_aten_fallback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "opset_version", "operator_export_type"], "arg_types": ["builtins.str", "builtins.int", "torch._C._onnx.OperatorExportTypes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_aten_fallback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_signature": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._signature", "name": "_signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_signature", "ret_type": "inspect.Signature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_split_tensor_list_constants": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._split_tensor_list_constants", "name": "_split_tensor_list_constants", "type": null}}, "_trace": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["func", "args", "operator_export_type", "return_outs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._trace", "name": "_trace", "type": null}}, "_trace_and_get_graph_from_model": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._trace_and_get_graph_from_model", "name": "_trace_and_get_graph_from_model", "type": null}}, "_validate_dynamic_axes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["dynamic_axes", "model", "input_names", "output_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._validate_dynamic_axes", "name": "_validate_dynamic_axes", "type": null}}, "_verify_custom_op_name": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["symbolic_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils._verify_custom_op_name", "name": "_verify_custom_op_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["symbolic_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_custom_op_name", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef", "module_public": false}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_public": false}, "disable_apex_o2_state_dict_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.utils.disable_apex_o2_state_dict_hook is deprecated: Please remove usage of this function. Copy its logic if it is required in user code", "flags": ["is_decorated"], "fullname": "torch.onnx.utils.disable_apex_o2_state_dict_hook", "name": "disable_apex_o2_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._<PERSON><PERSON>"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_apex_o2_state_dict_hook", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.utils.disable_apex_o2_state_dict_hook", "name": "disable_apex_o2_state_dict_hook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._<PERSON><PERSON>"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_apex_o2_state_dict_hook", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "errors": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.errors", "kind": "Gdef", "module_public": false}, "export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model", "args", "f", "kwargs", "export_params", "verbose", "training", "input_names", "output_names", "operator_export_type", "opset_version", "do_constant_folding", "dynamic_axes", "keep_initializers_as_inputs", "custom_opsets", "export_modules_as_functions", "autograd_inlining"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils.export", "name": "export", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model", "args", "f", "kwargs", "export_params", "verbose", "training", "input_names", "output_names", "operator_export_type", "opset_version", "do_constant_folding", "dynamic_axes", "keep_initializers_as_inputs", "custom_opsets", "export_modules_as_functions", "autograd_inlining"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", "torch.jit._script.ScriptModule", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch._<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "torch._tensor.Tensor"], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "torch._C._onnx.TrainingMode", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "torch._C._onnx.OperatorExportTypes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": [{".class": "TypeType", "item": "torch.nn.modules.module.Module"}], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exporter_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "mode", "verbose"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.utils.exporter_context is deprecated: The feature will be removed. Please remove usage of this function and implement equivalent logic if needed", "flags": ["is_decorated"], "fullname": "torch.onnx.utils.exporter_context", "name": "exporter_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "mode", "verbose"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._C._onnx.TrainingMode", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exporter_context", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.utils.exporter_context", "name": "exporter_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "mode", "verbose"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._C._onnx.TrainingMode", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exporter_context", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "jit_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.jit_utils", "kind": "Gdef", "module_public": false}, "model_signature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils.model_signature", "name": "model_signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_signature", "ret_type": "inspect.Signature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "onnx_proto_utils": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.onnx_proto_utils", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "register_custom_op_symbolic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["symbolic_name", "symbolic_fn", "opset_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils.register_custom_op_symbolic", "name": "register_custom_op_symbolic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["symbolic_name", "symbolic_fn", "opset_version"], "arg_types": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_custom_op_symbolic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registration": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.registration", "kind": "Gdef", "module_public": false}, "select_model_mode_for_export": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "mode"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.utils.select_model_mode_for_export is deprecated: Please set training mode before exporting the model", "flags": ["is_decorated"], "fullname": "torch.onnx.utils.select_model_mode_for_export", "name": "select_model_mode_for_export", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["model", "mode"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._C._onnx.TrainingMode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_model_mode_for_export", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.utils.select_model_mode_for_export", "name": "select_model_mode_for_export", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["model", "mode"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._C._onnx.TrainingMode"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_model_mode_for_export", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "setup_onnx_logging": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["verbose"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.utils.setup_onnx_logging is deprecated: The feature will be removed. Please remove usage of this function", "flags": ["is_decorated"], "fullname": "torch.onnx.utils.setup_onnx_logging", "name": "setup_onnx_logging", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["verbose"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_onnx_logging", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.utils.setup_onnx_logging", "name": "setup_onnx_logging", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["verbose"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_onnx_logging", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "symbolic_helper": {".class": "SymbolTableNode", "cross_ref": "torch.onnx.symbolic_helper", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "unconvertible_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["model", "args", "training", "opset_version"], "dataclass_transform_spec": null, "deprecated": "function torch.onnx.utils.unconvertible_ops is deprecated: Unconvertible ops are not definitive. Please remove usage of this function", "flags": ["is_decorated"], "fullname": "torch.onnx.utils.unconvertible_ops", "name": "unconvertible_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["model", "args", "training", "opset_version"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._C._onnx.TrainingMode", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unconvertible_ops", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.onnx.utils.unconvertible_ops", "name": "unconvertible_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["model", "args", "training", "opset_version"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._C._onnx.TrainingMode", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unconvertible_ops", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "unpack_quantized_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "cast_onnx_accepted"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils.unpack_quantized_tensor", "name": "unpack_quantized_tensor", "type": null}}, "unregister_custom_op_symbolic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["symbolic_name", "opset_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils.unregister_custom_op_symbolic", "name": "unregister_custom_op_symbolic", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["symbolic_name", "opset_version"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unregister_custom_op_symbolic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warn_on_static_input_change": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx.utils.warn_on_static_input_change", "name": "warn_on_static_input_change", "type": null}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\utils.py"}