{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EmitGemmUniversal3xInstanceWithEVT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT", "name": "EmitGemmUniversal3xInstanceWithEVT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions", "mro": ["torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "operation_suffix", "evt_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.__init__", "name": "__init__", "type": null}}, "builtin_epilogue_functor_template": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.builtin_epilogue_functor_template", "name": "builtin_epilogue_functor_template", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "emit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.emit", "name": "emit", "type": null}}, "emit_block_scale_epilogue_functor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.emit_block_scale_epilogue_functor", "name": "emit_block_scale_epilogue_functor", "type": null}}, "evt_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.evt_name", "name": "evt_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "gemm_template": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.gemm_template", "name": "gemm_template", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "includes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.includes", "name": "includes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "instance_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.instance_template", "name": "instance_template", "type": null}}, "operation_suffix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.operation_suffix", "name": "operation_suffix", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pointerize_if_grouped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operation", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.pointerize_if_grouped", "name": "pointerize_if_grouped", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.pointerize_if_grouped", "name": "pointerize_if_grouped", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operation", "layout"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pointerize_if_grouped of EmitGemmUniversal3xInstanceWithEVT", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "problem_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["operation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.problem_shape", "name": "problem_shape", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.problem_shape", "name": "problem_shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["operation"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "problem_shape of EmitGemmUniversal3xInstanceWithEVT", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.EmitGemmUniversal3xInstanceWithEVT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions._LOGGER", "name": "_LOGGER", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "try_import_cutlass": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cuda.cutlass_utils.try_import_cutlass", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cutlass_lib_extensions\\gemm_operation_extensions.py"}