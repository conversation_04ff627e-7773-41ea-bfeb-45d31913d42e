{"data_mtime": 1755649448, "dep_lines": [15, 24, 25, 34, 35, 36, 11, 13, 14, 14, 14, 16, 17, 23, 7, 10, 14, 2, 3, 4, 5, 6, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 10, 20, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch._inductor.kernel.mm_common", "torch._inductor.codegen.cpp", "torch._inductor.codegen.cpp_micro_gemm", "torch._inductor.codegen.cpp_template", "torch._inductor.codegen.cpp_template_kernel", "torch._inductor.codegen.cpp_utils", "torch.utils._ordered_set", "torch._dynamo.utils", "torch._inductor.config", "torch._inductor.ir", "torch._inductor.lowering", "torch._inductor.select_algorithm", "torch._inductor.utils", "torch._inductor.virtualized", "unittest.mock", "torch.utils", "torch._inductor", "contextlib", "logging", "math", "functools", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "copy", "_frozen_importlib", "abc", "torch._C", "torch._inductor.codegen.common", "torch._inductor.graph", "torch._tensor", "torch.fx", "torch.fx.interpreter", "unittest"], "hash": "b4386aed29b49f2f6a5da443ae261b432be09173", "id": "torch._inductor.codegen.cpp_gemm_template", "ignore_all": true, "interface_hash": "fa1926f348495d335ae3479a5bd3648b6c4b63d2", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_gemm_template.py", "plugin_data": null, "size": 77657, "suppressed": [], "version_id": "1.15.0"}