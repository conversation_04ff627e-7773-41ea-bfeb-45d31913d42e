{"data_mtime": 1755656862, "dep_lines": [88, 46, 89, 13, 24, 25, 26, 27, 28, 33, 34, 35, 36, 37, 56, 6, 10, 11, 12, 14, 15, 16, 17, 18, 65, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 21, 22, 20], "dep_prios": [25, 5, 25, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10], "dependencies": ["git.objects.submodule.base", "git.repo.fun", "git.refs.symbolic", "os.path", "git.cmd", "git.compat", "git.config", "git.db", "git.exc", "git.index", "git.objects", "git.refs", "git.remote", "git.util", "git.types", "__future__", "gc", "logging", "os", "pathlib", "re", "shlex", "sys", "warnings", "typing", "builtins", "_frozen_importlib", "abc", "configparser", "enum", "git.diff", "git.index.base", "git.objects.base", "git.objects.blob", "git.objects.commit", "git.objects.submodule", "git.objects.tag", "git.objects.tree", "git.objects.util", "git.refs.head", "git.refs.reference", "git.refs.tag", "types", "typing_extensions"], "hash": "99663eb3643d86736cc7872202439160aa1c20c5", "id": "git.repo.base", "ignore_all": true, "interface_hash": "cf8f002d9551aeac37e007a7ecab39f1126df564", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\repo\\base.py", "plugin_data": null, "size": 59972, "suppressed": ["gitdb.db.loose", "gitdb.exc", "gitdb"], "version_id": "1.15.0"}