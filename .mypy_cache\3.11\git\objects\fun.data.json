{".class": "MypyFile", "_fullname": "git.objects.fun", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "EntryTup": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.objects.fun.EntryTup", "line": 35, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "EntryTupOrNone": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.objects.fun.EntryTupOrNone", "line": 36, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "GitCmdObjectDB": {".class": "SymbolTableNode", "cross_ref": "git.db.GitCmdObjectDB", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_public": false}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_public": false}, "S_ISDIR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISDIR", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.fun.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.fun.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.fun.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.fun.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.fun.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.fun.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.fun.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_find_by_name": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["tree_data", "name", "is_dir", "start_at"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.fun._find_by_name", "name": "_find_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["tree_data", "name", "is_dir", "start_at"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTupOrNone"}], "extra_attrs": null, "type_ref": "typing.MutableSequence"}, "builtins.str", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_by_name", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTupOrNone"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_to_full_path": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "git.objects.fun._to_full_path", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "git.objects.fun._to_full_path", "name": "_to_full_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTupOrNone"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_path", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTupOrNone"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "git.objects.fun._to_full_path", "name": "_to_full_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "arg_types": [{".class": "NoneType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_path", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.objects.fun._to_full_path", "name": "_to_full_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "arg_types": [{".class": "NoneType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_path", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "git.objects.fun._to_full_path", "name": "_to_full_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_path", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.objects.fun._to_full_path", "name": "_to_full_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_path", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "arg_types": [{".class": "NoneType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_path", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "path_prefix"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_path", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "defenc": {".class": "SymbolTableNode", "cross_ref": "git.compat.defenc", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "safe_decode": {".class": "SymbolTableNode", "cross_ref": "git.compat.safe_decode", "kind": "Gdef", "module_public": false}, "traverse_tree_recursive": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["odb", "tree_sha", "path_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.fun.traverse_tree_recursive", "name": "traverse_tree_recursive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["odb", "tree_sha", "path_prefix"], "arg_types": ["git.db.GitCmdObjectDB", "builtins.bytes", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse_tree_recursive", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "traverse_trees_recursive": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["odb", "tree_shas", "path_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.fun.traverse_trees_recursive", "name": "traverse_trees_recursive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["odb", "tree_shas", "path_prefix"], "arg_types": ["git.db.GitCmdObjectDB", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse_trees_recursive", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTupOrNone"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tree_entries_from_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.fun.tree_entries_from_data", "name": "tree_entries_from_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tree_entries_from_data", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tree_to_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["entries", "write"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.fun.tree_to_stream", "name": "tree_to_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["entries", "write"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.objects.fun.EntryTup"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.Buffer"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tree_to_stream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\fun.py"}