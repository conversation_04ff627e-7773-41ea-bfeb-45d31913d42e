{"data_mtime": 1755649448, "dep_lines": [18, 19, 20, 21, 22, 23, 11, 4, 5, 6, 8, 234, 235, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 15, 16, 13, 12, 953], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 10, 10, 5, 10, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 20], "dependencies": ["torch.utils.tensorboard._convert_np", "torch.utils.tensorboard._embedding", "torch.utils.tensorboard._onnx_graph", "torch.utils.tensorboard._pytorch_graph", "torch.utils.tensorboard._utils", "torch.utils.tensorboard.summary", "matplotlib.figure", "os", "time", "typing", "torch", "socket", "datetime", "builtins", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matplotlib.artist", "numpy", "torch._C", "torch._tensor", "typing_extensions"], "hash": "7a053ea22385d67b9f40b87424b225dc21b584fe", "id": "torch.utils.tensorboard.writer", "ignore_all": true, "interface_hash": "c49c36593b898e076b31c64324b3227bfee4af63", "mtime": 1755648858, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\writer.py", "plugin_data": null, "size": 47867, "suppressed": ["tensorboard.compat.proto.event_pb2", "tensorboard.plugins.projector.projector_config_pb2", "tensorboard.summary.writer.event_file_writer", "tensorboard.compat.proto", "tensorboard.compat", "google.protobuf"], "version_id": "1.15.0"}