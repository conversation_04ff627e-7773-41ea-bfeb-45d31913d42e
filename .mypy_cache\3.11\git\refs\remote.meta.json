{"data_mtime": 1755656862, "dep_lines": [12, 10, 18, 21, 22, 8, 16, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["git.refs.head", "git.util", "git.types", "git.remote", "git.repo", "os", "typing", "builtins", "_frozen_importlib", "abc", "git.refs.reference", "git.refs.symbolic", "git.repo.base"], "hash": "df6ff2272ffa610fcf1003b68b2f54265aa7abd5", "id": "git.refs.remote", "ignore_all": true, "interface_hash": "cce131bd6cbe131a4fe5e30b640b6ed2a519a472", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\refs\\remote.py", "plugin_data": null, "size": 2806, "suppressed": [], "version_id": "1.15.0"}