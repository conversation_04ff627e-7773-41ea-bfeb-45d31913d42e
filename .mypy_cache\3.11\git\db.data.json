{".class": "MypyFile", "_fullname": "git.db", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadObject": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.db.<PERSON>", "name": "BadObject", "type": {".class": "AnyType", "missing_import_name": "git.db.<PERSON>", "source_any": null, "type_of_any": 3}}}, "Git": {".class": "SymbolTableNode", "cross_ref": "git.cmd.Git", "kind": "Gdef", "module_public": false}, "GitCmdObjectDB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.db.GitCmdObjectDB", "name": "GitCmdObjectDB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "git.db.GitCmdObjectDB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.db", "mro": ["git.db.GitCmdObjectDB", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "root_path", "git"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.db.GitCmdObjectDB.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "root_path", "git"], "arg_types": ["git.db.GitCmdObjectDB", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "git.cmd.Git"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GitCmdObjectDB", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_git": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.db.GitCmdObjectDB._git", "name": "_git", "type": "git.cmd.Git"}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.db.GitCmdObjectDB.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON>"], "arg_types": ["git.db.GitCmdObjectDB", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of GitCmdObjectDB", "ret_type": {".class": "AnyType", "missing_import_name": "git.db.OInfo", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "partial_to_complete_sha_hex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "partial_hexsha"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.db.GitCmdObjectDB.partial_to_complete_sha_hex", "name": "partial_to_complete_sha_hex", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "partial_hexsha"], "arg_types": ["git.db.GitCmdObjectDB", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partial_to_complete_sha_hex of GitCmdObjectDB", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.db.GitCmdObjectDB.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON>"], "arg_types": ["git.db.GitCmdObjectDB", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of GitCmdObjectDB", "ret_type": {".class": "AnyType", "missing_import_name": "git.db.<PERSON><PERSON>am", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.db.GitCmdObjectDB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.db.GitCmdObjectDB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GitCommandError": {".class": "SymbolTableNode", "cross_ref": "git.exc.GitCommandError", "kind": "Gdef", "module_public": false}, "GitDB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.db.GitDB", "name": "GitDB", "type": {".class": "AnyType", "missing_import_name": "git.db.GitDB", "source_any": null, "type_of_any": 3}}}, "LooseObjectDB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.db.LooseObjectDB", "name": "LooseObjectDB", "type": {".class": "AnyType", "missing_import_name": "git.db.LooseObjectDB", "source_any": null, "type_of_any": 3}}}, "OInfo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.db.OInfo", "name": "OInfo", "type": {".class": "AnyType", "missing_import_name": "git.db.OInfo", "source_any": null, "type_of_any": 3}}}, "OStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.db.<PERSON><PERSON>am", "name": "OStream", "type": {".class": "AnyType", "missing_import_name": "git.db.<PERSON><PERSON>am", "source_any": null, "type_of_any": 3}}}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.db.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.db.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.db.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.db.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.db.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.db.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.db.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "bin_to_hex": {".class": "SymbolTableNode", "cross_ref": "git.util.bin_to_hex", "kind": "Gdef", "module_public": false}, "hex_to_bin": {".class": "SymbolTableNode", "cross_ref": "git.util.hex_to_bin", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\db.py"}