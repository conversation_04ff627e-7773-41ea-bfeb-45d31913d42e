{"data_mtime": 1755649448, "dep_lines": [12, 20, 21, 22, 10, 11, 14, 14, 14, 14, 15, 16, 17, 19, 3, 14, 2, 4, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 6], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["torch.utils._sympy.symbol", "torch._inductor.codegen.common", "torch._inductor.codegen.cpp", "torch._inductor.codegen.cpp_utils", "torch._inductor.utils", "torch.utils._ordered_set", "torch._inductor.config", "torch._inductor.cpp_builder", "torch._inductor.ir", "torch._inductor.lowering", "torch._inductor.autotune_process", "torch._inductor.loop_body", "torch._inductor.select_algorithm", "torch._inductor.virtualized", "collections.abc", "torch._inductor", "itertools", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "warnings", "contextlib", "copy", "_collections_abc", "_frozen_importlib", "abc", "enum", "torch._C", "torch.utils", "torch.utils._sympy"], "hash": "c37e4af7981ba15f0db6db48b0d4c3c2926ae3a6", "id": "torch._inductor.codegen.cpp_template_kernel", "ignore_all": true, "interface_hash": "354efd056a45a8f2e9a7f073568a173bd92e84ae", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_template_kernel.py", "plugin_data": null, "size": 25713, "suppressed": ["sympy.parsing.sympy_parser", "sympy"], "version_id": "1.15.0"}