{".class": "MypyFile", "_fullname": "streamlit.proto.IFrame_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.IFrame_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.IFrame_pb2.google", "source_any": null, "type_of_any": 3}}}, "IFrame": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.IFrame_pb2.IFrame", "name": "IFrame", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.IFrame_pb2.IFrame", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.IFrame_pb2", "mro": ["streamlit.proto.IFrame_pb2.IFrame", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.IFrame_pb2.IFrame.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_tab_index"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_tab_index"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "has_width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "has_width"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "height"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scrolling"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "scrolling"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "src"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "src"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srcdoc"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "srcdoc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tab_index"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "tab_index"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "width"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "width"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of IFrame", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.IFrame_pb2.google", "source_any": null, "type_of_any": 3}}}, "HAS_WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.HAS_WIDTH_FIELD_NUMBER", "name": "HAS_WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "HEIGHT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.HEIGHT_FIELD_NUMBER", "name": "HEIGHT_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.IFrame_pb2.IFrame.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_tab_index"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_tab_index"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "src"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "src"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srcdoc"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "srcdoc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tab_index"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "tab_index"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "type"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Has<PERSON><PERSON> of IFrame", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "SCROLLING_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.SCROLLING_FIELD_NUMBER", "name": "SCROLLING_FIELD_NUMBER", "type": "builtins.int"}}, "SRCDOC_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.SRCDOC_FIELD_NUMBER", "name": "SRCDOC_FIELD_NUMBER", "type": "builtins.int"}}, "SRC_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.SRC_FIELD_NUMBER", "name": "SRC_FIELD_NUMBER", "type": "builtins.int"}}, "TAB_INDEX_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.TAB_INDEX_FIELD_NUMBER", "name": "TAB_INDEX_FIELD_NUMBER", "type": "builtins.int"}}, "WIDTH_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.WIDTH_FIELD_NUMBER", "name": "WIDTH_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "streamlit.proto.IFrame_pb2.IFrame.WhichOneof", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_tab_index"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_tab_index"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of IFrame", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tab_index"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_tab_index"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_tab_index"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of IFrame", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tab_index"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "type"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of IFrame", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "src"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srcdoc"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "type"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of IFrame", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "src"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srcdoc"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_tab_index"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "_tab_index"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of IFrame", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tab_index"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "type"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of IFrame", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "src"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srcdoc"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "src", "srcdoc", "width", "has_width", "height", "scrolling", "tab_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.IFrame_pb2.IFrame.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "src", "srcdoc", "width", "has_width", "height", "scrolling", "tab_index"], "arg_types": ["streamlit.proto.IFrame_pb2.IFrame", "builtins.str", "builtins.str", "builtins.float", "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IFrame", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.has_width", "name": "has_width", "type": "builtins.bool"}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.height", "name": "height", "type": "builtins.float"}}, "scrolling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.scrolling", "name": "scrolling", "type": "builtins.bool"}}, "src": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.src", "name": "src", "type": "builtins.str"}}, "srcdoc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.srcdoc", "name": "srcdoc", "type": "builtins.str"}}, "tab_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.tab_index", "name": "tab_index", "type": "builtins.int"}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.IFrame_pb2.IFrame.width", "name": "width", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.IFrame_pb2.IFrame.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.IFrame_pb2.IFrame", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.IFrame_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.IFrame_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.IFrame_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.IFrame_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.IFrame_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.IFrame_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___IFrame": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.IFrame_pb2.global___IFrame", "line": 68, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.IFrame_pb2.IFrame"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.IFrame_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.IFrame_pb2.google", "source_any": null, "type_of_any": 3}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\IFrame_pb2.pyi"}