{".class": "MypyFile", "_fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "PowerSGDState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState", "name": "PowerSGDState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook", "mro": ["torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "process_group", "matrix_approximation_rank", "start_powerSGD_iter", "min_compression_rate", "use_error_feedback", "warm_start", "orthogonalization_epsilon", "random_seed", "compression_stats_logging_frequency", "batch_tensors_with_same_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "process_group", "matrix_approximation_rank", "start_powerSGD_iter", "min_compression_rate", "use_error_feedback", "warm_start", "orthogonalization_epsilon", "random_seed", "compression_stats_logging_frequency", "batch_tensors_with_same_shape"], "arg_types": ["torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PowerSGDState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.__setstate__", "name": "__setstate__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "batch_tensors_with_same_shape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.batch_tensors_with_same_shape", "name": "batch_tensors_with_same_shape", "type": "builtins.bool"}}, "compression_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.compression_stats", "name": "compression_stats", "type": null}}, "compression_stats_logging_frequency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.compression_stats_logging_frequency", "name": "compression_stats_logging_frequency", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "error_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.error_dict", "name": "error_dict", "type": {".class": "Instance", "args": ["builtins.int", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "iter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.iter", "name": "iter", "type": "builtins.int"}}, "matrix_approximation_rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.matrix_approximation_rank", "name": "matrix_approximation_rank", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "maybe_increase_iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.maybe_increase_iter", "name": "maybe_increase_iter", "type": null}}, "min_compression_rate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.min_compression_rate", "name": "min_compression_rate", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "next_stats_report": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.next_stats_report", "name": "next_stats_report", "type": "builtins.int"}}, "orthogonalization_epsilon": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.orthogonalization_epsilon", "name": "orthogonalization_epsilon", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "p_memory_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.p_memory_dict", "name": "p_memory_dict", "type": {".class": "Instance", "args": ["builtins.int", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "process_group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.process_group", "name": "process_group", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "q_memory_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.q_memory_dict", "name": "q_memory_dict", "type": {".class": "Instance", "args": ["builtins.int", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "rng": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.rng", "name": "rng", "type": "numpy.random.mtrand.RandomState"}}, "start_powerSGD_iter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.start_powerSGD_iter", "name": "start_powerSGD_iter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "total_numel_after_compression": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.total_numel_after_compression", "name": "total_numel_after_compression", "type": "builtins.int"}}, "total_numel_before_compression": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.total_numel_before_compression", "name": "total_numel_before_compression", "type": "builtins.int"}}, "use_error_feedback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.use_error_feedback", "name": "use_error_feedback", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "warm_start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.warm_start", "name": "warm_start", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState", "values": [], "variance": 0}, "slots": ["batch_tensors_with_same_shape", "compression_stats_logging_frequency", "error_dict", "iter", "matrix_approximation_rank", "min_compression_rate", "next_stats_report", "orthogonalization_epsilon", "p_memory_dict", "process_group", "q_memory_dict", "rng", "start_powerSGD_iter", "total_numel_after_compression", "total_numel_before_compression", "use_error_feedback", "warm_start"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_orthogonalize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["matrices", "epsilon"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook._orthogonalize", "name": "_orthogonalize", "type": null}}, "_orthogonalize_gram_schmidt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["matrices", "epsilon"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook._orthogonalize_gram_schmidt", "name": "_orthogonalize_gram_schmidt", "type": null}}, "_report_compression_stats": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["bucket", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook._report_compression_stats", "name": "_report_compression_stats", "type": null}}, "_should_compress": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["num_rows", "num_cols", "matrix_approximation_rank", "min_compression_rate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook._should_compress", "name": "_should_compress", "type": null}}, "batched_powerSGD_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.batched_powerSGD_hook", "name": "batched_powerSGD_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "bucket"], "arg_types": ["torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState", "torch._C._distributed_c10d.GradBucket"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batched_powerSGD_hook", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.algorithms.ddp_comm_hooks.default_hooks", "kind": "Gdef", "module_public": false}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef", "module_public": false}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef", "module_public": false}, "distributed_c10d": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.distributed_c10d", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "not_none": {".class": "SymbolTableNode", "cross_ref": "torch.utils._typing_utils.not_none", "kind": "Gdef", "module_public": false}, "powerSGD_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.powerSGD_hook", "name": "powerSGD_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "bucket"], "arg_types": ["torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook.PowerSGDState", "torch._C._distributed_c10d.GradBucket"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "powerSGD_hook", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\algorithms\\ddp_comm_hooks\\powerSGD_hook.py"}