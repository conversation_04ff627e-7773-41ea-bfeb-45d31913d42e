{"data_mtime": 1755649448, "dep_lines": [11, 11, 14, 20, 30, 31, 37, 45, 52, 11, 13, 19, 42, 43, 6, 10, 12, 2, 3, 4, 5, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 5, 5, 5, 5, 5, 20, 10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.algorithms._checkpoint.checkpoint_wrapper", "torch.distributed.algorithms._checkpoint", "torch.distributed._shard.sharded_tensor", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp._debug_utils", "torch.distributed.fsdp._runtime_utils", "torch.distributed.fsdp.api", "torch.distributed.fsdp._fsdp_extensions", "torch.distributed.fsdp._unshard_param_utils", "torch.distributed.algorithms", "torch.nn.functional", "torch.distributed.device_mesh", "torch.distributed.tensor", "torch.distributed.utils", "collections.abc", "torch.distributed", "torch.nn", "contextlib", "logging", "math", "warnings", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "copy", "_frozen_importlib", "abc", "torch.distributed._composable_state", "torch.nn.modules", "torch.nn.modules.module"], "hash": "bf902bddfdd3aace2f6f9d8df158a881cd4faf2c", "id": "torch.distributed.fsdp._state_dict_utils", "ignore_all": true, "interface_hash": "cd824002383f79c1aa1f7660de10a573e6b4a75b", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_state_dict_utils.py", "plugin_data": null, "size": 35212, "suppressed": [], "version_id": "1.15.0"}