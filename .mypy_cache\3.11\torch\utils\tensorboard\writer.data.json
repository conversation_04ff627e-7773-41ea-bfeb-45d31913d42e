{".class": "MypyFile", "_fullname": "torch.utils.tensorboard.writer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Event": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.writer.Event", "name": "Event", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.writer.Event", "source_any": null, "type_of_any": 3}}}, "EventFileWriter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.writer.EventFileWriter", "name": "EventFileWriter", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.writer.EventFileWriter", "source_any": null, "type_of_any": 3}}}, "Figure": {".class": "SymbolTableNode", "cross_ref": "matplotlib.figure.Figure", "kind": "Gdef", "module_public": false}, "FileWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.tensorboard.writer.FileWriter", "name": "FileWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.tensorboard.writer", "mro": ["torch.utils.tensorboard.writer.FileWriter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "log_dir", "max_queue", "flush_secs", "filename_suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.__init__", "name": "__init__", "type": null}}, "add_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "event", "step", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.add_event", "name": "add_event", "type": null}}, "add_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "graph_profile", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.add_graph", "name": "add_graph", "type": null}}, "add_onnx_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "graph", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.add_onnx_graph", "name": "add_onnx_graph", "type": null}}, "add_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "summary", "global_step", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.add_summary", "name": "add_summary", "type": null}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.close", "name": "close", "type": null}}, "event_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.FileWriter.event_writer", "name": "event_writer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.flush", "name": "flush", "type": null}}, "get_logdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.get_logdir", "name": "get_logdir", "type": null}}, "reopen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.FileWriter.reopen", "name": "reopen", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.tensorboard.writer.FileWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.tensorboard.writer.FileWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ProjectorConfig": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.writer.ProjectorConfig", "name": "ProjectorConfig", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.writer.ProjectorConfig", "source_any": null, "type_of_any": 3}}}, "SessionLog": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.writer.SessionLog", "name": "SessionLog", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.writer.SessionLog", "source_any": null, "type_of_any": 3}}}, "SummaryWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.tensorboard.writer.SummaryWriter", "name": "SummaryWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.tensorboard.writer", "mro": ["torch.utils.tensorboard.writer.SummaryWriter", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "log_dir", "comment", "purge_step", "max_queue", "flush_secs", "filename_suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.__init__", "name": "__init__", "type": null}}, "_encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rawstr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter._encode", "name": "_encode", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter._encode", "name": "_encode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rawstr"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_encode of SummaryWriter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_file_writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter._get_file_writer", "name": "_get_file_writer", "type": null}}, "_projector_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter._projector_config", "name": "_projector_config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "add_audio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "tag", "snd_tensor", "global_step", "sample_rate", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_audio", "name": "add_audio", "type": null}}, "add_custom_scalars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_custom_scalars", "name": "add_custom_scalars", "type": null}}, "add_custom_scalars_marginchart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "tags", "category", "title"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_custom_scalars_marginchart", "name": "add_custom_scalars_marginchart", "type": null}}, "add_custom_scalars_multilinechart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "tags", "category", "title"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_custom_scalars_multilinechart", "name": "add_custom_scalars_multilinechart", "type": null}}, "add_embedding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "mat", "metadata", "label_img", "global_step", "tag", "metadata_header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_embedding", "name": "add_embedding", "type": null}}, "add_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "tag", "figure", "global_step", "close", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_figure", "name": "add_figure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "tag", "figure", "global_step", "close", "walltime"], "arg_types": ["torch.utils.tensorboard.writer.SummaryWriter", "builtins.str", {".class": "UnionType", "items": ["matplotlib.figure.Figure", {".class": "Instance", "args": ["matplotlib.figure.Figure"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_figure of SummaryWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model", "input_to_model", "verbose", "use_strict_trace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_graph", "name": "add_graph", "type": null}}, "add_histogram": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "tag", "values", "global_step", "bins", "walltime", "max_bins"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_histogram", "name": "add_histogram", "type": null}}, "add_histogram_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "tag", "min", "max", "num", "sum", "sum_squares", "bucket_limits", "bucket_counts", "global_step", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_histogram_raw", "name": "add_histogram_raw", "type": null}}, "add_hparams": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "hparam_dict", "metric_dict", "hparam_domain_discrete", "run_name", "global_step"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_hparams", "name": "add_hparams", "type": null}}, "add_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "tag", "img_tensor", "global_step", "walltime", "dataformats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_image", "name": "add_image", "type": null}}, "add_image_with_boxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "tag", "img_tensor", "box_tensor", "global_step", "walltime", "rescale", "dataformats", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_image_with_boxes", "name": "add_image_with_boxes", "type": null}}, "add_images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "tag", "img_tensor", "global_step", "walltime", "dataformats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_images", "name": "add_images", "type": null}}, "add_mesh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "tag", "vertices", "colors", "faces", "config_dict", "global_step", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_mesh", "name": "add_mesh", "type": null}}, "add_onnx_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prototxt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_onnx_graph", "name": "add_onnx_graph", "type": null}}, "add_pr_curve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "tag", "labels", "predictions", "global_step", "num_thresholds", "weights", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_pr_curve", "name": "add_pr_curve", "type": null}}, "add_pr_curve_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "tag", "true_positive_counts", "false_positive_counts", "true_negative_counts", "false_negative_counts", "precision", "recall", "global_step", "num_thresholds", "weights", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_pr_curve_raw", "name": "add_pr_curve_raw", "type": null}}, "add_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "tag", "scalar_value", "global_step", "walltime", "new_style", "double_precision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_scalar", "name": "add_scalar", "type": null}}, "add_scalars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "main_tag", "tag_scalar_dict", "global_step", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_scalars", "name": "add_scalars", "type": null}}, "add_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "tag", "tensor", "global_step", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_tensor", "name": "add_tensor", "type": null}}, "add_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "tag", "text_string", "global_step", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_text", "name": "add_text", "type": null}}, "add_video": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "tag", "vid_tensor", "global_step", "fps", "walltime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.add_video", "name": "add_video", "type": null}}, "all_writers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.all_writers", "name": "all_writers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.close", "name": "close", "type": null}}, "default_bins": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.default_bins", "name": "default_bins", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "file_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.file_writer", "name": "file_writer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "filename_suffix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.filename_suffix", "name": "filename_suffix", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.flush", "name": "flush", "type": null}}, "flush_secs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.flush_secs", "name": "flush_secs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_logdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.get_logdir", "name": "get_logdir", "type": null}}, "log_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.log_dir", "name": "log_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "max_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.max_queue", "name": "max_queue", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "purge_step": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.tensorboard.writer.SummaryWriter.purge_step", "name": "purge_step", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.tensorboard.writer.SummaryWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.tensorboard.writer.SummaryWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard.writer.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.writer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.writer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.writer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.writer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.writer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard.writer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "audio": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.audio", "kind": "Gdef", "module_public": false}, "custom_scalars": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.custom_scalars", "kind": "Gdef", "module_public": false}, "event_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.writer.event_pb2", "name": "event_pb2", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.writer.event_pb2", "source_any": null, "type_of_any": 3}}}, "figure_to_image": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._utils.figure_to_image", "kind": "Gdef", "module_public": false}, "get_embedding_info": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._embedding.get_embedding_info", "kind": "Gdef", "module_public": false}, "graph": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._pytorch_graph.graph", "kind": "Gdef", "module_public": false}, "histogram": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.histogram", "kind": "Gdef", "module_public": false}, "histogram_raw": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.histogram_raw", "kind": "Gdef", "module_public": false}, "hparams": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.hparams", "kind": "Gdef", "module_public": false}, "image": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.image", "kind": "Gdef", "module_public": false}, "image_boxes": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.image_boxes", "kind": "Gdef", "module_public": false}, "load_onnx_graph": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._onnx_graph.load_onnx_graph", "kind": "Gdef", "module_public": false}, "make_mat": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._embedding.make_mat", "kind": "Gdef", "module_public": false}, "make_np": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._convert_np.make_np", "kind": "Gdef", "module_public": false}, "make_sprite": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._embedding.make_sprite", "kind": "Gdef", "module_public": false}, "make_tsv": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._embedding.make_tsv", "kind": "Gdef", "module_public": false}, "mesh": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.mesh", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pr_curve": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.pr_curve", "kind": "Gdef", "module_public": false}, "pr_curve_raw": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.pr_curve_raw", "kind": "Gdef", "module_public": false}, "scalar": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.scalar", "kind": "Gdef", "module_public": false}, "tensor_proto": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.tensor_proto", "kind": "Gdef", "module_public": false}, "text": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.text", "kind": "Gdef", "module_public": false}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard.writer.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard.writer.tf", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "video": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard.summary.video", "kind": "Gdef", "module_public": false}, "write_pbtxt": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._embedding.write_pbtxt", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\writer.py"}