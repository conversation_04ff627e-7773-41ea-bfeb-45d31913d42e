{".class": "MypyFile", "_fullname": "torch._dynamo.repro.after_aot", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACCURACY_FAILS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch._dynamo.repro.after_aot.ACCURACY_FAILS", "name": "ACCURACY_FAILS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.nn.modules.module.Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AccuracyError": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.AccuracyError", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BuckTargetWriter": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.BuckTargetWriter", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "FakeScriptObject": {".class": "SymbolTableNode", "cross_ref": "torch._library.fake_class_registry.FakeScriptObject", "kind": "Gdef"}, "InputReader": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.InputReader", "kind": "Gdef"}, "InputType": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.InputType", "kind": "Gdef"}, "InputWriter": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.InputWriter", "kind": "Gdef"}, "MAX_CONSTANT_NUMEL_INLINE": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.MAX_CONSTANT_NUMEL_INLINE", "kind": "Gdef"}, "NNModuleToString": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.NNModuleToString", "kind": "Gdef"}, "NopInputReader": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.NopInputReader", "kind": "Gdef"}, "OutputCode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.output_code.OutputCode", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TemporaryFile": {".class": "SymbolTableNode", "cross_ref": "tempfile.TemporaryFile", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef"}, "_CompileFxCallable": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.compile_fx._CompileFxCallable", "kind": "Gdef"}, "_CompileFxKwargs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.compile_fx._CompileFxKwargs", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_aot.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_aot.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_aot.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_aot.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_aot.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._dynamo.repro.after_aot.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cuda_system_info_comment": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils._cuda_system_info_comment", "kind": "Gdef"}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "backend_accuracy_fails": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.backend_accuracy_fails", "kind": "Gdef"}, "backend_aot_accuracy_fails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._dynamo.repro.after_aot.backend_aot_accuracy_fails", "name": "backend_aot_accuracy_fails", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": {".class": "ExtraAttrs", "attrs": {"__mypy_partial": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["gm", "example_inputs", "compiler_fn", "only_fwd", "require_fp64", "ignore_non_fp"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend_accuracy_fails", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "immutable": [], "mod_name": null}, "type_ref": "functools.partial"}}}, "cast_to_fp64": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.cast_to_fp64", "kind": "Gdef"}, "clone_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.clone_inputs", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.config", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "dump_compiler_graph_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["gm", "args", "compiler_name", "accuracy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.dump_compiler_graph_state", "name": "dump_compiler_graph_state", "type": null}}, "dump_to_minify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["gm", "args", "compiler_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.dump_to_minify", "name": "dump_to_minify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["gm", "args", "compiler_name"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_to_minify", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extra_deps": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.extra_deps", "kind": "Gdef"}, "extra_imports": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.extra_imports", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "fx": {".class": "SymbolTableNode", "cross_ref": "torch.fx", "kind": "Gdef"}, "fx_placeholder_targets": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.fx_placeholder_targets", "kind": "Gdef"}, "generate_compiler_repro_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["gm", "args", "stable_output", "save_dir", "stable_hash"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.generate_compiler_repro_string", "name": "generate_compiler_repro_string", "type": null}}, "generate_config_string": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.generate_config_string", "kind": "Gdef"}, "generate_env_vars_string": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.generate_env_vars_string", "kind": "Gdef"}, "has_free_symbols": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.has_free_symbols", "kind": "Gdef"}, "helper_for_dump_minify": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.helper_for_dump_minify", "kind": "Gdef"}, "import_module": {".class": "SymbolTableNode", "cross_ref": "importlib.import_module", "kind": "Gdef"}, "inductor_accuracy_fails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["fx_g", "args", "check_str", "require_fp64", "ignore_non_fp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.inductor_accuracy_fails", "name": "inductor_accuracy_fails", "type": null}}, "inductor_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._dynamo.repro.after_aot.inductor_config", "name": "inductor_config", "type": "types.ModuleType"}}, "inductor_fails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["fx_g", "args", "check_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.inductor_fails", "name": "inductor_fails", "type": null}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "is_fbcode": {".class": "SymbolTableNode", "cross_ref": "torch._environment.is_fbcode", "kind": "Gdef"}, "isolate_fails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["fx_g", "args", "compiler_name", "env", "save_dir", "accuracy", "tracing_mode", "check_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.isolate_fails", "name": "isolate_fails", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["fx_g", "args", "compiler_name", "env", "save_dir", "accuracy", "tracing_mode", "check_str"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isolate_fails", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._dynamo.repro.after_aot.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_fx": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.proxy_tensor.make_fx", "kind": "Gdef"}, "maybe_fbcode_instructions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.maybe_fbcode_instructions", "name": "maybe_fbcode_instructions", "type": null}}, "minifier_dir": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.minifier_dir", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "repro_analyze": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.repro_analyze", "name": "repro_analyze", "type": null}}, "repro_common": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.repro_common", "name": "repro_common", "type": null}}, "repro_get_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.repro_get_args", "name": "repro_get_args", "type": null}}, "repro_minifier_query": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.repro_minifier_query", "name": "repro_minifier_query", "type": null}}, "repro_minify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.repro_minify", "name": "repro_minify", "type": null}}, "repro_run": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["options", "mod", "load_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.repro_run", "name": "repro_run", "type": null}}, "run_repro": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["mod", "load_args", "command", "accuracy", "save_dir", "tracing_mode", "patch_code", "check_str", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.run_repro", "name": "run_repro", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["mod", "load_args", "command", "accuracy", "save_dir", "tracing_mode", "patch_code", "check_str", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_repro", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "same": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.same", "kind": "Gdef"}, "same_two_models": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.debug_utils.same_two_models", "kind": "Gdef"}, "save_graph_repro": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["fd", "gm", "args", "compiler_name", "stable_output", "save_dir", "command", "accuracy", "tracing_mode", "check_str", "stable_hash"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.save_graph_repro", "name": "save_graph_repro", "type": null}}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "cross_ref": "torch.hub.tqdm", "kind": "Gdef"}, "use_buck": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._dynamo.repro.after_aot.use_buck", "name": "use_buck", "type": "builtins.bool"}}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}, "wrap_compiler_debug": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["unconfigured_compiler_fn", "compiler_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._dynamo.repro.after_aot.wrap_compiler_debug", "name": "wrap_compiler_debug", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["unconfigured_compiler_fn", "compiler_name"], "arg_types": ["torch._inductor.compile_fx._CompileFxCallable", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_compiler_debug", "ret_type": "torch._inductor.compile_fx._CompileFxCallable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_dynamo\\repro\\after_aot.py"}