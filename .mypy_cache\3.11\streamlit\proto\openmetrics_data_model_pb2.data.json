{".class": "MypyFile", "_fullname": "streamlit.proto.openmetrics_data_model_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "COUNTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.COUNTER", "name": "COUNTER", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "CounterValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue", "name": "CounterValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue", "builtins.object"], "names": {".class": "SymbolTable", "CREATED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.CREATED_FIELD_NUMBER", "name": "CREATED_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exemplar"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "exemplar"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "total"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of CounterValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "DOUBLE_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.DOUBLE_VALUE_FIELD_NUMBER", "name": "DOUBLE_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "EXEMPLAR_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.EXEMPLAR_FIELD_NUMBER", "name": "EXEMPLAR_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exemplar"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "exemplar"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "total"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of CounterValue", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INT_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.INT_VALUE_FIELD_NUMBER", "name": "INT_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "total"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "total"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of CounterValue", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "double_value", "int_value", "created", "exemplar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "double_value", "int_value", "created", "exemplar"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue", "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CounterValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.created", "name": "created", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "created of CounterValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.created", "name": "created", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "created of CounterValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "double_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.double_value", "name": "double_value", "type": "builtins.float"}}, "exemplar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.exemplar", "name": "exemplar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exemplar of CounterValue", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.Exemplar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.exemplar", "name": "exemplar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exemplar of CounterValue", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.Exemplar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "int_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.int_value", "name": "int_value", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.CounterValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.CounterValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "Exemplar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar", "name": "Exemplar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "label"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "label"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Exemplar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "timestamp"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of Exemplar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "LABEL_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.LABEL_FIELD_NUMBER", "name": "LABEL_FIELD_NUMBER", "type": "builtins.int"}}, "TIMESTAMP_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.TIMESTAMP_FIELD_NUMBER", "name": "TIMESTAMP_FIELD_NUMBER", "type": "builtins.int"}}, "VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.VALUE_FIELD_NUMBER", "name": "VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "value", "timestamp", "label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "value", "timestamp", "label"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar", "builtins.float", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.Label"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Exemplar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.label", "name": "label", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "label of Exemplar", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.label", "name": "label", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "label of Exemplar", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.timestamp", "name": "timestamp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timestamp of Exemplar", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.timestamp", "name": "timestamp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timestamp of Exemplar", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.value", "name": "value", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.Exemplar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.Exemplar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GAUGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GAUGE", "name": "GAUGE", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "GAUGE_HISTOGRAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GAUGE_HISTOGRAM", "name": "GAUGE_HISTOGRAM", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "GaugeValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue", "name": "GaugeValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.GaugeValue", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.GaugeValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of GaugeValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "DOUBLE_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.DOUBLE_VALUE_FIELD_NUMBER", "name": "DOUBLE_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.GaugeValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Has<PERSON>ield of GaugeValue", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INT_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.INT_VALUE_FIELD_NUMBER", "name": "INT_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.GaugeValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of GaugeValue", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "double_value", "int_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "double_value", "int_value"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.GaugeValue", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GaugeValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "double_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.double_value", "name": "double_value", "type": "builtins.float"}}, "int_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.int_value", "name": "int_value", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HISTOGRAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HISTOGRAM", "name": "HISTOGRAM", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "HistogramValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue", "name": "HistogramValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue", "builtins.object"], "names": {".class": "SymbolTable", "BUCKETS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.BUCKETS_FIELD_NUMBER", "name": "BUCKETS_FIELD_NUMBER", "type": "builtins.int"}}, "Bucket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket", "name": "Bucket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket", "builtins.object"], "names": {".class": "SymbolTable", "COUNT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.COUNT_FIELD_NUMBER", "name": "COUNT_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "count"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exemplar"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "exemplar"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "upper_bound"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "upper_bound"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of Bucket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "EXEMPLAR_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.EXEMPLAR_FIELD_NUMBER", "name": "EXEMPLAR_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "exemplar"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "exemplar"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of Bucket", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "UPPER_BOUND_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.UPPER_BOUND_FIELD_NUMBER", "name": "UPPER_BOUND_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "count", "upper_bound", "exemplar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "count", "upper_bound", "exemplar"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket", "builtins.int", "builtins.float", {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.Exemplar", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>et", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.count", "name": "count", "type": "builtins.int"}}, "exemplar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.exemplar", "name": "exemplar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exemplar of <PERSON><PERSON>", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.Exemplar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.exemplar", "name": "exemplar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exemplar of <PERSON><PERSON>", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.Exemplar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "upper_bound": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.upper_bound", "name": "upper_bound", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "COUNT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.COUNT_FIELD_NUMBER", "name": "COUNT_FIELD_NUMBER", "type": "builtins.int"}}, "CREATED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.CREATED_FIELD_NUMBER", "name": "CREATED_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "buckets"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "buckets"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "count"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "sum"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of HistogramValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "DOUBLE_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.DOUBLE_VALUE_FIELD_NUMBER", "name": "DOUBLE_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "sum"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of HistogramValue", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INT_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.INT_VALUE_FIELD_NUMBER", "name": "INT_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "sum"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of HistogramValue", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "double_value", "int_value", "count", "created", "buckets"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "double_value", "int_value", "count", "created", "buckets"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue", "builtins.float", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Bucket"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HistogramValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buckets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.buckets", "name": "buckets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buckets of HistogramValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.buckets", "name": "buckets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buckets of HistogramValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.count", "name": "count", "type": "builtins.int"}}, "created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.created", "name": "created", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "created of HistogramValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.created", "name": "created", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "created of HistogramValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "double_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.double_value", "name": "double_value", "type": "builtins.float"}}, "int_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.int_value", "name": "int_value", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.INFO", "name": "INFO", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "InfoValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue", "name": "InfoValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.InfoValue", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.InfoValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "info"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "info"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of InfoValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "INFO_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue.INFO_FIELD_NUMBER", "name": "INFO_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "info"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.InfoValue", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.Label"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InfoValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.InfoValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of InfoValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.InfoValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of InfoValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.InfoValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.InfoValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Label": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label", "name": "Label", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.Label", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Label", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Label", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "NAME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label.NAME_FIELD_NUMBER", "name": "NAME_FIELD_NUMBER", "type": "builtins.int"}}, "VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label.VALUE_FIELD_NUMBER", "name": "VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "name", "value"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Label", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Label", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label.name", "name": "name", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label.value", "name": "value", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.Label.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.Label", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Metric": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric", "name": "Metric", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.Metric", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Metric", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "labels"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "labels"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "metric_points"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "metric_points"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Metric", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "LABELS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.LABELS_FIELD_NUMBER", "name": "LABELS_FIELD_NUMBER", "type": "builtins.int"}}, "METRIC_POINTS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.METRIC_POINTS_FIELD_NUMBER", "name": "METRIC_POINTS_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "labels", "metric_points"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "labels", "metric_points"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Metric", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.Label"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Metric", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.labels", "name": "labels", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Metric"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "labels of Metric", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.labels", "name": "labels", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Metric"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "labels of Metric", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "metric_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.metric_points", "name": "metric_points", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Metric"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metric_points of Metric", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.metric_points", "name": "metric_points", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.Metric"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metric_points of Metric", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.Metric.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.Metric", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MetricFamily": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily", "name": "MetricFamily", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.MetricFamily", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricFamily", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "metrics"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unit"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "unit"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of MetricFamily", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "HELP_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.HELP_FIELD_NUMBER", "name": "HELP_FIELD_NUMBER", "type": "builtins.int"}}, "METRICS_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.METRICS_FIELD_NUMBER", "name": "METRICS_FIELD_NUMBER", "type": "builtins.int"}}, "NAME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.NAME_FIELD_NUMBER", "name": "NAME_FIELD_NUMBER", "type": "builtins.int"}}, "TYPE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.TYPE_FIELD_NUMBER", "name": "TYPE_FIELD_NUMBER", "type": "builtins.int"}}, "UNIT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.UNIT_FIELD_NUMBER", "name": "UNIT_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "type", "unit", "help", "metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "type", "unit", "help", "metrics"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricFamily", "builtins.str", "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.Metric"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MetricFamily", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "help": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.help", "name": "help", "type": "builtins.str"}}, "metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.metrics", "name": "metrics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of MetricFamily", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.metrics", "name": "metrics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of MetricFamily", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.type", "name": "type", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "unit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.unit", "name": "unit", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MetricPoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint", "name": "MetricPoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint", "builtins.object"], "names": {".class": "SymbolTable", "COUNTER_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.COUNTER_VALUE_FIELD_NUMBER", "name": "COUNTER_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "counter_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "counter_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gauge_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "gauge_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "histogram_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "histogram_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "info_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "info_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "state_set_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "state_set_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "summary_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "summary_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unknown_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "unknown_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of MetricPoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "GAUGE_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.GAUGE_VALUE_FIELD_NUMBER", "name": "GAUGE_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "HISTOGRAM_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.HISTOGRAM_VALUE_FIELD_NUMBER", "name": "HISTOGRAM_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "counter_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "counter_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gauge_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "gauge_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "histogram_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "histogram_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "info_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "info_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "state_set_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "state_set_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "summary_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "summary_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "timestamp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unknown_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "unknown_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "HasField of MetricPoint", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INFO_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.INFO_VALUE_FIELD_NUMBER", "name": "INFO_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "STATE_SET_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.STATE_SET_VALUE_FIELD_NUMBER", "name": "STATE_SET_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "SUMMARY_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.SUMMARY_VALUE_FIELD_NUMBER", "name": "SUMMARY_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "TIMESTAMP_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.TIMESTAMP_FIELD_NUMBER", "name": "TIMESTAMP_FIELD_NUMBER", "type": "builtins.int"}}, "UNKNOWN_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.UNKNOWN_VALUE_FIELD_NUMBER", "name": "UNKNOWN_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of MetricPoint", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "unknown_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gauge_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "counter_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "histogram_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "state_set_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "info_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "summary_value"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "unknown_value", "gauge_value", "counter_value", "histogram_value", "state_set_value", "info_value", "summary_value", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "unknown_value", "gauge_value", "counter_value", "histogram_value", "state_set_value", "info_value", "summary_value", "timestamp"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint", {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.UnknownValue", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.GaugeValue", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.CounterValue", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.HistogramValue", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.InfoValue", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MetricPoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "counter_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.counter_value", "name": "counter_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "counter_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.CounterValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.counter_value", "name": "counter_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "counter_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.CounterValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gauge_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.gauge_value", "name": "gauge_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gauge_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.gauge_value", "name": "gauge_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gauge_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "histogram_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.histogram_value", "name": "histogram_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "histogram_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.histogram_value", "name": "histogram_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "histogram_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "info_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.info_value", "name": "info_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.InfoValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.info_value", "name": "info_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.InfoValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "state_set_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.state_set_value", "name": "state_set_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state_set_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.state_set_value", "name": "state_set_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state_set_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "summary_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.summary_value", "name": "summary_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summary_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.summary_value", "name": "summary_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summary_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.timestamp", "name": "timestamp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timestamp of MetricPoint", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.timestamp", "name": "timestamp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timestamp of MetricPoint", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "unknown_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.unknown_value", "name": "unknown_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unknown_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.unknown_value", "name": "unknown_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unknown_value of MetricPoint", "ret_type": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MetricSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet", "name": "MetricSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.MetricSet", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricSet", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "metric_families"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "metric_families"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of MetricSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "METRIC_FAMILIES_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet.METRIC_FAMILIES_FIELD_NUMBER", "name": "METRIC_FAMILIES_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "metric_families"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "metric_families"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricSet", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.MetricFamily"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MetricSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metric_families": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet.metric_families", "name": "metric_families", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metric_families of MetricSet", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet.metric_families", "name": "metric_families", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.MetricSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metric_families of MetricSet", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.MetricSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MetricType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.openmetrics_data_model_pb2._MetricType"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricType", "name": "MetricType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricType", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.MetricType", "streamlit.proto.openmetrics_data_model_pb2._MetricType", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.MetricType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.MetricType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STATE_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.STATE_SET", "name": "STATE_SET", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "SUMMARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SUMMARY", "name": "SUMMARY", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "StateSetValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue", "name": "StateSetValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "states"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "states"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of StateSetValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "STATES_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.STATES_FIELD_NUMBER", "name": "STATES_FIELD_NUMBER", "type": "builtins.int"}}, "State": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State", "name": "State", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "enabled"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "enabled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "name"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of State", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "ENABLED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State.ENABLED_FIELD_NUMBER", "name": "ENABLED_FIELD_NUMBER", "type": "builtins.int"}}, "NAME_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State.NAME_FIELD_NUMBER", "name": "NAME_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "enabled", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "enabled", "name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State", "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of State", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State.enabled", "name": "enabled", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "states"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue", {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue.State"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StateSetValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.states", "name": "states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "states of StateSetValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.states", "name": "states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.StateSetValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "states of StateSetValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SummaryValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue", "name": "SummaryValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue", "builtins.object"], "names": {".class": "SymbolTable", "COUNT_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.COUNT_FIELD_NUMBER", "name": "COUNT_FIELD_NUMBER", "type": "builtins.int"}}, "CREATED_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.CREATED_FIELD_NUMBER", "name": "CREATED_FIELD_NUMBER", "type": "builtins.int"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "count"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quantile"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "quantile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "sum"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of SummaryValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "DOUBLE_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.DOUBLE_VALUE_FIELD_NUMBER", "name": "DOUBLE_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "created"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "sum"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Has<PERSON><PERSON> of SummaryValue", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INT_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.INT_VALUE_FIELD_NUMBER", "name": "INT_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "QUANTILE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.QUANTILE_FIELD_NUMBER", "name": "QUANTILE_FIELD_NUMBER", "type": "builtins.int"}}, "Quantile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile", "name": "Quantile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "quantile"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "quantile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Quantile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "QUANTILE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile.QUANTILE_FIELD_NUMBER", "name": "QUANTILE_FIELD_NUMBER", "type": "builtins.int"}}, "VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile.VALUE_FIELD_NUMBER", "name": "VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "quantile", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "quantile", "value"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Quantile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quantile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile.quantile", "name": "quantile", "type": "builtins.float"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile.value", "name": "value", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "sum"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of SummaryValue", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "double_value", "int_value", "count", "created", "quantile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "double_value", "int_value", "count", "created", "quantile"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue", "builtins.float", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Quantile"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SummaryValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.count", "name": "count", "type": "builtins.int"}}, "created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.created", "name": "created", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "created of SummaryValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.created", "name": "created", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "created of SummaryValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "double_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.double_value", "name": "double_value", "type": "builtins.float"}}, "int_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.int_value", "name": "int_value", "type": "builtins.int"}}, "quantile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.quantile", "name": "quantile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quantile of SummaryValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.quantile", "name": "quantile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.SummaryValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quantile of SummaryValue", "ret_type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UNKNOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UNKNOWN", "name": "UNKNOWN", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "UnknownValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2.UnknownValue", "builtins.object"], "names": {".class": "SymbolTable", "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.UnknownValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of UnknownValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "DOUBLE_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.DOUBLE_VALUE_FIELD_NUMBER", "name": "DOUBLE_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "HasField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.HasField", "name": "Has<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.UnknownValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "int_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON> of UnknownValue", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INT_VALUE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.INT_VALUE_FIELD_NUMBER", "name": "INT_VALUE_FIELD_NUMBER", "type": "builtins.int"}}, "WhichOneof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.WhichOneof", "name": "WhichOneof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "oneof_group"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.UnknownValue", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "value"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "value"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WhichOneof of UnknownValue", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "double_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_value"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "double_value", "int_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "double_value", "int_value"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2.UnknownValue", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "double_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.double_value", "name": "double_value", "type": "builtins.float"}}, "int_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.int_value", "name": "int_value", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MetricType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricType", "name": "_MetricType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2._MetricType", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricType.V", "line": 28, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2._MetricType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MetricTypeEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper", "name": "_MetricTypeEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.openmetrics_data_model_pb2", "mro": ["streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "COUNTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.COUNTER", "name": "COUNTER", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "GAUGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.GAUGE", "name": "GAUGE", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "GAUGE_HISTOGRAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.GAUGE_HISTOGRAM", "name": "GAUGE_HISTOGRAM", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "HISTOGRAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.HISTOGRAM", "name": "HISTOGRAM", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "INFO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.INFO", "name": "INFO", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "STATE_SET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.STATE_SET", "name": "STATE_SET", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "SUMMARY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.SUMMARY", "name": "SUMMARY", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}, "UNKNOWN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.UNKNOWN", "name": "UNKNOWN", "type": "streamlit.proto.openmetrics_data_model_pb2._MetricType.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.openmetrics_data_model_pb2._MetricTypeEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___CounterValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___CounterValue", "line": 306, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.CounterValue"}}, "global___Exemplar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___Exemplar", "line": 406, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.Exemplar"}}, "global___GaugeValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___GaugeValue", "line": 270, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.GaugeValue"}}, "global___HistogramValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___HistogramValue", "line": 375, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.HistogramValue"}}, "global___InfoValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___InfoValue", "line": 464, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.InfoValue"}}, "global___Label": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___Label", "line": 176, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.Label"}}, "global___Metric": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___Metric", "line": 152, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.Metric"}}, "global___MetricFamily": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___MetricFamily", "line": 126, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.MetricFamily"}}, "global___MetricPoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___MetricPoint", "line": 226, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.MetricPoint"}}, "global___MetricSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___MetricSet", "line": 88, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.MetricSet"}}, "global___MetricType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___MetricType", "line": 68, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.MetricType"}}, "global___StateSetValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___StateSetValue", "line": 444, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.StateSetValue"}}, "global___SummaryValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___SummaryValue", "line": 522, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.SummaryValue"}}, "global___UnknownValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.openmetrics_data_model_pb2.global___UnknownValue", "line": 248, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.openmetrics_data_model_pb2.UnknownValue"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.openmetrics_data_model_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.openmetrics_data_model_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\openmetrics_data_model_pb2.pyi"}