import{s as T,r as i,u as b,bl as L,j as p,l as N}from"./index.DKN5MVff.js";const U=T("iframe",{target:"eymnmwl0"})(({theme:r})=>({colorScheme:"normal",border:"none",padding:r.spacing.none,margin:r.spacing.none,width:"100%",aspectRatio:"16 / 9"})),R=<PERSON>.getLogger("Video"),A={width:"100%"};function O({element:r,endpoints:s,elementMgr:f}){const n=i.useRef(null),{type:g,url:l,startTime:a,subtitles:d,endTime:o,loop:u,autoplay:m,muted:E}=r;let v=b(l);const S=i.useMemo(()=>{if(!r.id)return!0;const e=f.getElementState(r.id,"preventAutoplay");return e||f.setElementState(r.id,"preventAutoplay",!0),e??!1},[r.id,f]),y=i.useMemo(()=>JSON.stringify(d?d.map(e=>s.buildMediaURL(`${e.url}`)):[]),[d,s]);i.useEffect(()=>{const e=JSON.parse(y);e.length!==0&&e.forEach(t=>{s.checkSourceUrlResponse(t,"Video Subtitle")})},[y,s]),i.useEffect(()=>{n.current&&(n.current.currentTime=a)},[a]),i.useEffect(()=>{const e=n.current,t=()=>{e&&(e.currentTime=r.startTime)};return e&&e.addEventListener("loadedmetadata",t),()=>{e&&e.removeEventListener("loadedmetadata",t)}},[r]),i.useEffect(()=>{const e=n.current;if(!e)return;let t=!1;const c=()=>{o>0&&e.currentTime>=o&&(u?(e.currentTime=a||0,e.play()):t||(t=!0,e.pause()))};return o>0&&e.addEventListener("timeupdate",c),()=>{e&&o>0&&e.removeEventListener("timeupdate",c)}},[o,u,a]),i.useEffect(()=>{const e=n.current;if(!e)return;const t=()=>{u&&(e.currentTime=a||0,e.play())};return e.addEventListener("ended",t),()=>{e&&e.removeEventListener("ended",t)}},[u,a]);const V=e=>{const t=new URL(e);if(a&&!isNaN(a)&&t.searchParams.append("start",a.toString()),o&&!isNaN(o)&&t.searchParams.append("end",o.toString()),u){t.searchParams.append("loop","1");const c=t.pathname.split("/").pop();c&&t.searchParams.append("playlist",c)}return m&&t.searchParams.append("autoplay","1"),E&&t.searchParams.append("mute","1"),t.toString()};if(g===L.Type.YOUTUBE_IFRAME)return p(U,{className:"stVideo","data-testid":"stVideo",title:l,src:V(l),allow:"autoplay; encrypted-media",allowFullScreen:!0});const h=e=>{const t=e.currentTarget.src;R.error(`Client Error: Video source error - ${t}`),s.sendClientErrorToHost("Video","Video source failed to load","onerror triggered",t)};return p("video",{className:"stVideo","data-testid":"stVideo",ref:n,controls:!0,muted:E,autoPlay:m&&!S,src:s.buildMediaURL(l),style:A,crossOrigin:v,onError:h,children:d&&d.map((e,t)=>p("track",{kind:"captions",src:s.buildMediaURL(`${e.url}`),label:`${e.label}`,default:t===0,"data-testid":"stVideoSubtitle"},t))})}const w=i.memo(O);export{w as default};
