{".class": "MypyFile", "_fullname": "narwhals._translate", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArrowConvertible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["from_arrow", 2], ["to_arrow", 2]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrowT_co", "id": 1, "name": "ToArrowT_co", "namespace": "narwhals._translate.ArrowConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.ToArrow"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 2, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.ArrowConvertible", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromArrow"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.ArrowConvertible", "name": "ArrowConvertible", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrowT_co", "id": 1, "name": "ToArrowT_co", "namespace": "narwhals._translate.ArrowConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 2, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.ArrowConvertible", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.ArrowConvertible", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.ArrowConvertible", "narwhals._translate.ToArrow", "narwhals._translate.FromArrow", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ArrowConvertible.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrowT_co", "id": 1, "name": "ToArrowT_co", "namespace": "narwhals._translate.ArrowConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 2, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.ArrowConvertible", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.ArrowConvertible"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ToArrowT_co", "FromArrowDT_contra"], "typeddict_type": null}}, "ArrowStreamExportable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__arrow_c_stream__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.ArrowStreamExportable", "name": "ArrowStreamExportable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.ArrowStreamExportable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.ArrowStreamExportable", "builtins.object"], "names": {".class": "SymbolTable", "__arrow_c_stream__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 1], "arg_names": ["self", "requested_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "narwhals._translate.ArrowStreamExportable.__arrow_c_stream__", "name": "__arrow_c_stream__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "requested_schema"], "arg_types": ["narwhals._translate.ArrowStreamExportable", {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__arrow_c_stream__ of ArrowStreamExportable", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ArrowStreamExportable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "narwhals._translate.ArrowStreamExportable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DictConvertible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["from_dict", 2], ["to_dict", 2]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "fullname": "narwhals._translate.ToDictDT_co", "id": 1, "name": "ToDictDT_co", "namespace": "narwhals._translate.DictConvertible", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.ToDict"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 2, "name": "FromDictDT_contra", "namespace": "narwhals._translate.DictConvertible", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromDict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.DictConvertible", "name": "DictConvertible", "type_vars": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "fullname": "narwhals._translate.ToDictDT_co", "id": 1, "name": "ToDictDT_co", "namespace": "narwhals._translate.DictConvertible", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 2, "name": "FromDictDT_contra", "namespace": "narwhals._translate.DictConvertible", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.DictConvertible", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.DictConvertible", "narwhals._translate.ToDict", "narwhals._translate.FromDict", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.DictConvertible.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "fullname": "narwhals._translate.ToDictDT_co", "id": 1, "name": "ToDictDT_co", "namespace": "narwhals._translate.DictConvertible", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 2, "name": "FromDictDT_contra", "namespace": "narwhals._translate.DictConvertible", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.DictConvertible"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ToDictDT_co", "FromDictDT_contra"], "typeddict_type": null}}, "FromArrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["from_arrow", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.FromArrow", "name": "FromArrow", "type_vars": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.FromArrow", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.FromArrow", "builtins.object"], "names": {".class": "SymbolTable", "from_arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "narwhals._translate.FromArrow.from_arrow", "name": "from_arrow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromArrow"}, "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_arrow of FromArrow", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromArrow"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromArrow"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals._translate.FromArrow.from_arrow", "name": "from_arrow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromArrow"}, "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_arrow of FromArrow", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromArrow"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromArrow"}, "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "fullname": "narwhals._translate.FromArrowDT_contra", "id": 1, "name": "FromArrowDT_contra", "namespace": "narwhals._translate.FromArrow", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromArrow"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FromArrowDT_contra"], "typeddict_type": null}}, "FromArrowDT_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, "fullname": "narwhals._translate.FromArrowDT_contra", "name": "FromArrowDT_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "FromDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["from_dict", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.FromDict", "name": "FromDict", "type_vars": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.FromDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.FromDict", "builtins.object"], "names": {".class": "SymbolTable", "from_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "narwhals._translate.FromDict.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromDict"}, "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dict of FromDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromDict"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromDict"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals._translate.FromDict.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromDict"}, "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dict of FromDict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromDict"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromDict"}, "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "id": 1, "name": "FromDictDT_contra", "namespace": "narwhals._translate.FromDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromDict"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FromDictDT_contra"], "typeddict_type": null}}, "FromDictDT_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "fullname": "narwhals._translate.FromDictDT_contra", "name": "FromDictDT_contra", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 2}}, "FromIterable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["from_iterable", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.FromIterable", "name": "FromIterable", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.FromIterable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.FromIterable", "builtins.object"], "names": {".class": "SymbolTable", "from_iterable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "narwhals._translate.FromIterable.from_iterable", "name": "from_iterable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromIterable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromIterable"}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_iterable of FromIterable", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromIterable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromIterable"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromIterable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromIterable"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals._translate.FromIterable.from_iterable", "name": "from_iterable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromIterable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromIterable"}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_iterable of FromIterable", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromIterable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromIterable"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromIterable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromIterable"}, "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromIterable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "id": 1, "name": "FromIterableT_contra", "namespace": "narwhals._translate.FromIterable", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromIterable"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FromIterableT_contra"], "typeddict_type": null}}, "FromIterableT_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "fullname": "narwhals._translate.FromIterableT_contra", "name": "FromIterableT_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "FromNative": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_is_native", 2], ["from_native", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.FromNative", "name": "FromNative", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.FromNative", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.FromNative", "builtins.object"], "names": {".class": "SymbolTable", "_is_native": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated", "is_trivial_body"], "fullname": "narwhals._translate.FromNative._is_native", "name": "_is_native", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_native of FromNative", "ret_type": "builtins.bool", "type_guard": null, "type_is": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "narwhals._translate.FromNative._is_native", "name": "_is_native", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_native of FromNative", "ret_type": "builtins.bool", "type_guard": null, "type_is": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}, "unpack_kwargs": false, "variables": []}}}}, "from_native": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "narwhals._translate.FromNative.from_native", "name": "from_native", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNative.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNative"}, "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_native of FromNative", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNative.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNative"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNative.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNative"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals._translate.FromNative.from_native", "name": "from_native", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNative.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNative"}, "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_native of FromNative", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNative.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNative"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNative.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNative"}, "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNative.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "id": 1, "name": "FromNativeT", "namespace": "narwhals._translate.FromNative", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNative"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FromNativeT"], "typeddict_type": null}}, "FromNativeT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNativeT", "name": "FromNativeT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "FromNumpy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["from_numpy", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.FromNumpy", "name": "FromNumpy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.FromNumpy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.FromNumpy", "builtins.object"], "names": {".class": "SymbolTable", "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body"], "fullname": "narwhals._translate.FromNumpy.from_numpy", "name": "from_numpy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNumpy"}, "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_numpy of FromNumpy", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNumpy"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNumpy"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "narwhals._translate.FromNumpy.from_numpy", "name": "from_numpy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "data", "args", "kwds"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNumpy"}, "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_numpy of FromNumpy", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNumpy"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNumpy"}, "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "id": 1, "name": "FromNumpyT_contra", "namespace": "narwhals._translate.FromNumpy", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNumpy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FromNumpyT_contra"], "typeddict_type": null}}, "FromNumpyDT_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, "fullname": "narwhals._translate.FromNumpyDT_contra", "name": "FromNumpyDT_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "FromNumpyT_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.FromNumpyT_contra", "name": "FromNumpyT_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "IntoArrowTable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "narwhals._translate.IntoArrowTable", "line": 140, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["narwhals._translate.ArrowStreamExportable", {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "NumpyConvertible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["from_numpy", 2], ["to_numpy", 2]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.To<PERSON><PERSON>y"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, "fullname": "narwhals._translate.FromNumpyDT_contra", "id": 2, "name": "FromNumpyDT_contra", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.FromNumpy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.NumpyConvertible", "name": "NumpyConvertible", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, "fullname": "narwhals._translate.FromNumpyDT_contra", "id": 2, "name": "FromNumpyDT_contra", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.NumpyConvertible", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.NumpyConvertible", "narwhals._translate.To<PERSON><PERSON>y", "narwhals._translate.FromNumpy", "builtins.object"], "names": {".class": "SymbolTable", "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 3], "arg_names": ["self", "dtype", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "narwhals._translate.NumpyConvertible.to_numpy", "name": "to_numpy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "dtype", "copy"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, "fullname": "narwhals._translate.FromNumpyDT_contra", "id": 2, "name": "FromNumpyDT_contra", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.NumpyConvertible"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_numpy of NumpyConvertible", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.NumpyConvertible.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 1}, "fullname": "narwhals._translate.FromNumpyDT_contra", "id": 2, "name": "FromNumpyDT_contra", "namespace": "narwhals._translate.NumpyConvertible", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "narwhals._translate.NumpyConvertible"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ToNumpyT_co", "FromNumpyDT_contra"], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "ToArrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["to_arrow", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.ToArrow", "name": "ToArrow", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrowT_co", "id": 1, "name": "ToArrowT_co", "namespace": "narwhals._translate.ToArrow", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.ToArrow", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.ToArrow", "builtins.object"], "names": {".class": "SymbolTable", "to_arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "narwhals._translate.ToArrow.to_arrow", "name": "to_arrow", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwds"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrowT_co", "id": 1, "name": "ToArrowT_co", "namespace": "narwhals._translate.ToArrow", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.ToArrow"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_arrow of ToArrow", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrowT_co", "id": 1, "name": "ToArrowT_co", "namespace": "narwhals._translate.ToArrow", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrowT_co", "id": 1, "name": "ToArrowT_co", "namespace": "narwhals._translate.ToArrow", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.ToArrow"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ToArrowT_co"], "typeddict_type": null}}, "ToArrowT_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToArrowT_co", "name": "ToArrowT_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "ToDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["to_dict", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.ToDict", "name": "ToDict", "type_vars": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "fullname": "narwhals._translate.ToDictDT_co", "id": 1, "name": "ToDictDT_co", "namespace": "narwhals._translate.ToDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.ToDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.ToDict", "builtins.object"], "names": {".class": "SymbolTable", "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "narwhals._translate.ToDict.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwds"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "fullname": "narwhals._translate.ToDictDT_co", "id": 1, "name": "ToDictDT_co", "namespace": "narwhals._translate.ToDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.ToDict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of ToDict", "ret_type": {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "fullname": "narwhals._translate.ToDictDT_co", "id": 1, "name": "ToDictDT_co", "namespace": "narwhals._translate.ToDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "fullname": "narwhals._translate.ToDictDT_co", "id": 1, "name": "ToDictDT_co", "namespace": "narwhals._translate.ToDict", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.ToDict"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ToDictDT_co"], "typeddict_type": null}}, "ToDictDT_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "fullname": "narwhals._translate.ToDictDT_co", "name": "ToDictDT_co", "upper_bound": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "values": [], "variance": 1}}, "ToNarwhals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["to_narwhals", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.ToNarwhals", "name": "ToNarwhals", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNarwhalsT_co", "id": 1, "name": "ToNarwhalsT_co", "namespace": "narwhals._translate.ToNarwhals", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.ToNarwhals", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.ToNarwhals", "builtins.object"], "names": {".class": "SymbolTable", "to_narwhals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "narwhals._translate.ToNarwhals.to_narwhals", "name": "to_narwhals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNarwhalsT_co", "id": 1, "name": "ToNarwhalsT_co", "namespace": "narwhals._translate.ToNarwhals", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.ToNarwhals"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_narwhals of ToNarwhals", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNarwhalsT_co", "id": 1, "name": "ToNarwhalsT_co", "namespace": "narwhals._translate.ToNarwhals", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNarwhals.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNarwhalsT_co", "id": 1, "name": "ToNarwhalsT_co", "namespace": "narwhals._translate.ToNarwhals", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.ToNarwhals"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ToNarwhalsT_co"], "typeddict_type": null}}, "ToNarwhalsT_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNarwhalsT_co", "name": "ToNarwhalsT_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "ToNumpy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["to_numpy", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "narwhals._translate.To<PERSON><PERSON>y", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.To<PERSON><PERSON>y", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "narwhals._translate.To<PERSON><PERSON>y", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "narwhals._translate", "mro": ["narwhals._translate.To<PERSON><PERSON>y", "builtins.object"], "names": {".class": "SymbolTable", "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "narwhals._translate.ToNumpy.to_numpy", "name": "to_numpy", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwds"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.To<PERSON><PERSON>y", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.To<PERSON><PERSON>y"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_numpy of ToNumpy", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.To<PERSON><PERSON>y", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "id": 1, "name": "ToNumpyT_co", "namespace": "narwhals._translate.To<PERSON><PERSON>y", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "narwhals._translate.To<PERSON><PERSON>y"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ToNumpyT_co"], "typeddict_type": null}}, "ToNumpyT_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "narwhals._translate.ToNumpyT_co", "name": "ToNumpyT_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeIs": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeIs", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._translate.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._translate.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._translate.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._translate.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._translate.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "narwhals._translate.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "pa": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "narwhals._translate.pa", "name": "pa", "type": {".class": "AnyType", "missing_import_name": "narwhals._translate.pa", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\narwhals\\_translate.py"}