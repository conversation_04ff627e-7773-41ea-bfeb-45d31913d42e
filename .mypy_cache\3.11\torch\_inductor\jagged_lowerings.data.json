{".class": "MypyFile", "_fullname": "torch._inductor.jagged_lowerings", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Pointwise": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Pointwise", "kind": "Gdef"}, "TensorBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.jagged_lowerings.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.jagged_lowerings.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.jagged_lowerings.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.jagged_lowerings.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.jagged_lowerings.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.jagged_lowerings.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dense_idx_to_jagged_idx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["batch_idx", "seq_idx", "offsets_loader", "jagged_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.jagged_lowerings.dense_idx_to_jagged_idx", "name": "dense_idx_to_jagged_idx", "type": null}}, "fallback_handler": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.fallback_handler", "kind": "Gdef"}, "get_inverse_offsets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["offsets", "jagged_len", "realize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.jagged_lowerings.get_inverse_offsets", "name": "get_inverse_offsets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["offsets", "jagged_len", "realize"], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.jagged_lowerings.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_inverse_offsets", "ret_type": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_integer_type": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.is_integer_type", "kind": "Gdef"}, "jagged_idx_to_dense_idx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["jagged_idx", "inverse_offsets_loader", "offsets_loader", "batch_size", "max_seq_len", "offsets_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.jagged_lowerings.jagged_idx_to_dense_idx", "name": "jagged_idx_to_dense_idx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["jagged_idx", "inverse_offsets_loader", "offsets_loader", "batch_size", "max_seq_len", "offsets_dtype"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.jagged_lowerings.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.jagged_lowerings.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "jagged_idx_to_dense_idx", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.jagged_lowerings.sympy", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "torch._inductor.jagged_lowerings.sympy", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ops": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.ops", "kind": "Gdef"}, "register_jagged_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.jagged_lowerings.register_jagged_ops", "name": "register_jagged_ops", "type": null}}, "register_lowering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.register_lowering", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.jagged_lowerings.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.jagged_lowerings.sympy", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\jagged_lowerings.py"}