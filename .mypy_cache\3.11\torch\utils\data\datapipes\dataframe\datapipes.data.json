{".class": "MypyFile", "_fullname": "torch.utils.data.datapipes.dataframe.datapipes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "ConcatDataFramesPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.datapipe.DFIterDataPipe"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe", "name": "ConcatDataFramesPipe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.datapipes", "mro": ["torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe", "torch.utils.data.datapipes.datapipe.DFIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "source_datapipe", "batch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe.__iter__", "name": "__iter__", "type": null}}, "n_batch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe.n_batch", "name": "n_batch", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "source_datapipe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe.source_datapipe", "name": "source_datapipe", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.datapipes.ConcatDataFramesPipe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DFIterDataPipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.datapipe.DFIterDataPipe", "kind": "Gdef", "module_public": false}, "DataFramesAsTuplesPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.datapipes.DataFramesAsTuplesPipe", "name": "DataFramesAsTuplesPipe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.DataFramesAsTuplesPipe", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.datapipes", "mro": ["torch.utils.data.datapipes.dataframe.datapipes.DataFramesAsTuplesPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source_datapipe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.DataFramesAsTuplesPipe.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.DataFramesAsTuplesPipe.__iter__", "name": "__iter__", "type": null}}, "source_datapipe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.DataFramesAsTuplesPipe.source_datapipe", "name": "source_datapipe", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.datapipes.DataFramesAsTuplesPipe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.datapipes.DataFramesAsTuplesPipe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExampleAggregateAsDataFrames": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.datapipe.DFIterDataPipe"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames", "name": "ExampleAggregateAsDataFrames", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.datapipes", "mro": ["torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames", "torch.utils.data.datapipes.datapipe.DFIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "source_datapipe", "dataframe_size", "columns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames.__iter__", "name": "__iter__", "type": null}}, "_as_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames._as_list", "name": "_as_list", "type": null}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames.columns", "name": "columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dataframe_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames.dataframe_size", "name": "dataframe_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "source_datapipe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames.source_datapipe", "name": "source_datapipe", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.datapipes.ExampleAggregateAsDataFrames", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilterDataFramesPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.datapipe.DFIterDataPipe"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe", "name": "FilterDataFramesPipe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.datapipes", "mro": ["torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe", "torch.utils.data.datapipes.datapipe.DFIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_datapipe", "filter_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe.__iter__", "name": "__iter__", "type": null}}, "filter_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe.filter_fn", "name": "filter_fn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "source_datapipe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe.source_datapipe", "name": "source_datapipe", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.datapipes.FilterDataFramesPipe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IterDataPipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe", "kind": "Gdef", "module_public": false}, "PerRowDataFramesPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.datapipe.DFIterDataPipe"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.datapipes.PerRowDataFramesPipe", "name": "PerRowDataFramesPipe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.PerRowDataFramesPipe", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.datapipes", "mro": ["torch.utils.data.datapipes.dataframe.datapipes.PerRowDataFramesPipe", "torch.utils.data.datapipes.datapipe.DFIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source_datapipe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.PerRowDataFramesPipe.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.PerRowDataFramesPipe.__iter__", "name": "__iter__", "type": null}}, "source_datapipe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.PerRowDataFramesPipe.source_datapipe", "name": "source_datapipe", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.datapipes.PerRowDataFramesPipe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.datapipes.PerRowDataFramesPipe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShuffleDataFramesPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.datapipe.DFIterDataPipe"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ShuffleDataFramesPipe", "name": "ShuffleDataFramesPipe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ShuffleDataFramesPipe", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.datapipes", "mro": ["torch.utils.data.datapipes.dataframe.datapipes.ShuffleDataFramesPipe", "torch.utils.data.datapipes.datapipe.DFIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source_datapipe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ShuffleDataFramesPipe.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ShuffleDataFramesPipe.__iter__", "name": "__iter__", "type": null}}, "source_datapipe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ShuffleDataFramesPipe.source_datapipe", "name": "source_datapipe", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.datapipes.ShuffleDataFramesPipe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.datapipes.ShuffleDataFramesPipe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.datapipes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "df_wrapper": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.dataframe.dataframe_wrapper", "kind": "Gdef", "module_public": false}, "functional_datapipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes._decorator.functional_datapipe", "kind": "Gdef", "module_public": false}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\dataframe\\datapipes.py"}