{"data_mtime": 1755656862, "dep_lines": [50, 49, 51, 14, 15, 16, 17, 18, 19, 46, 10, 11, 12, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.base", "git.objects.commit", "git.repo.base", "git.cmd", "git.compat", "git.config", "git.exc", "git.refs", "git.util", "git.types", "contextlib", "logging", "re", "typing", "builtins", "_frozen_importlib", "abc", "configparser", "enum", "git.diff", "git.objects", "git.objects.base", "git.objects.blob", "git.objects.submodule", "git.objects.tag", "git.objects.tree", "git.objects.util", "git.refs.head", "git.refs.reference", "git.refs.remote", "git.refs.symbolic", "git.refs.tag", "git.repo", "os", "subprocess", "types"], "hash": "6da5a0346f2e0c6be27a35e7031b947e4b623621", "id": "git.remote", "ignore_all": true, "interface_hash": "0b1edf9b11a7cb72dc1d546e4793fd820610b903", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\remote.py", "plugin_data": null, "size": 46786, "suppressed": [], "version_id": "1.15.0"}