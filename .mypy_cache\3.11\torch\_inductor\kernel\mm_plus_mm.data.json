{".class": "MypyFile", "_fullname": "torch._inductor.kernel.mm_plus_mm", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ExternKernelChoice": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.ExternKernelChoice", "kind": "Gdef"}, "TritonTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.TritonTemplate", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_plus_mm.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_plus_mm.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_plus_mm.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_plus_mm.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_plus_mm.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.kernel.mm_plus_mm.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm_plus_mm.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "aten_mm_plus_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm_plus_mm.aten_mm_plus_mm", "name": "aten_mm_plus_mm", "type": "torch._inductor.select_algorithm.ExternKernelChoice"}}, "autotune_select_algorithm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.autotune_select_algorithm", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "lowerings": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.lowerings", "kind": "Gdef"}, "mm_args": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_args", "kind": "Gdef"}, "mm_grid": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_grid", "kind": "Gdef"}, "mm_options": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_options", "kind": "Gdef"}, "mm_plus_mm_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.kernel.mm_plus_mm.mm_plus_mm_template", "name": "mm_plus_mm_template", "type": "torch._inductor.select_algorithm.TritonTemplate"}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tuned_mm_plus_mm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["mat1", "mat2", "mat3", "mat4", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.kernel.mm_plus_mm.tuned_mm_plus_mm", "name": "tuned_mm_plus_mm", "type": null}}, "use_aten_gemm_kernels": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_aten_gemm_kernels", "kind": "Gdef"}, "use_triton_template": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.use_triton_template", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\kernel\\mm_plus_mm.py"}