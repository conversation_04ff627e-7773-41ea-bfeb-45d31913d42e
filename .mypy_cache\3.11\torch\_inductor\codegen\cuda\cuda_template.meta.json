{"data_mtime": 1755649448, "dep_lines": [21, 22, 20, 13, 16, 17, 19, 26, 8, 14, 2, 3, 4, 5, 6, 7, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 5, 5, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 10], "dependencies": ["torch._inductor.codegen.cuda.cuda_kernel", "torch._inductor.codegen.cuda.cutlass_utils", "torch._inductor.codegen.common", "torch._inductor.utils", "torch._inductor.autotune_process", "torch._inductor.ir", "torch._inductor.virtualized", "torch._inductor.scheduler", "unittest.mock", "torch._logging", "functools", "<PERSON><PERSON><PERSON>", "itertools", "dataclasses", "typing", "typing_extensions", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "logging", "torch._C", "torch._logging._internal"], "hash": "ef109adeb3678df91c2e89fc56d84398f205ab43", "id": "torch._inductor.codegen.cuda.cuda_template", "ignore_all": true, "interface_hash": "37cb096debf5b84c408fa65fee05c301772cd327", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cuda_template.py", "plugin_data": null, "size": 11526, "suppressed": ["sympy"], "version_id": "1.15.0"}