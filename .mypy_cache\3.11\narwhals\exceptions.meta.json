{"data_mtime": 1755656859, "dep_lines": [6, 1, 3, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "typing", "builtins", "_frozen_importlib", "abc", "types"], "hash": "6e4cf0b9d5be5da8d5c584eccdec7fdc815ab9c4", "id": "narwhals.exceptions", "ignore_all": true, "interface_hash": "f8bbd3b84dc6b957976163208f4245302991d159", "mtime": 1755656304, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\narwhals\\exceptions.py", "plugin_data": null, "size": 3704, "suppressed": [], "version_id": "1.15.0"}