{"data_mtime": 1755649448, "dep_lines": [12, 15, 31, 12, 20, 5, 7, 8, 9, 10, 12, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 29, 28], "dep_prios": [5, 5, 10, 20, 25, 5, 5, 5, 5, 5, 20, 25, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 10, 10], "dependencies": ["torch.utils._pytree", "torch._dynamo.decorators", "torch.utils._cxx_pytree", "torch.utils", "collections.abc", "__future__", "collections", "dataclasses", "typing", "typing_extensions", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "enum"], "hash": "eefa65737c49507873ac30eac5603e4257159ea6", "id": "torch._dynamo.polyfills.pytree", "ignore_all": true, "interface_hash": "0a404c5d6890cd2a7e1bd02ea1d4fa54ea527ecd", "mtime": 1755648845, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_dynamo\\polyfills\\pytree.py", "plugin_data": null, "size": 16164, "suppressed": ["optree._C", "optree"], "version_id": "1.15.0"}