import{r as p,ap as k,aD as T,j as n,aE as y,bu as C,aJ as S,bD as L,bv as W,bc as B,bO as w,ax as R,bP as E,bQ as P}from"./index.DKN5MVff.js";import{a as $}from"./useBasicWidgetState.DB3vMS9V.js";import{a as v,L as X,S as m}from"./checkbox.Cgxgc0et.js";import"./FormClearHelper.DF4gFAOO.js";function H({element:e,disabled:a,widgetMgr:s,fragmentId:d}){const[f,g]=$({getStateFromWidgetMgr:M,getDefaultStateFromProto:V,getCurrStateFromProto:I,updateWidgetMgrState:j,element:e,widgetMgr:s,fragmentId:d}),x=p.useCallback(i=>{g({value:i.target.checked,fromUi:!0})},[g]),t=k(),{colors:o,spacing:h,sizes:r}=t,b=T(t),u=a?o.fadedText40:o.bodyText;return n(P,{className:"row-widget stCheckbox","data-testid":"stCheckbox",children:n(v,{checked:f,disabled:a,onChange:x,"aria-label":e.label,checkmarkType:e.type===E.StyleType.TOGGLE?m.toggle:m.default,labelPlacement:X.right,overrides:{Root:{style:({$isFocusVisible:i})=>({marginBottom:h.none,marginTop:h.none,backgroundColor:i?o.darkenedBgMix25:"",display:"flex",alignItems:"start"})},Toggle:{style:({$checked:i})=>{let c=b?o.bgColor:o.bodyText;return a&&(c=b?o.gray70:o.gray90),{width:`calc(${r.checkbox} - ${t.spacing.twoXS})`,height:`calc(${r.checkbox} - ${t.spacing.twoXS})`,transform:i?`translateX(${r.checkbox})`:"",backgroundColor:c,boxShadow:""}}},ToggleTrack:{style:({$checked:i,$isHovered:c})=>{let l=o.fadedText40;return c&&!a&&(l=o.fadedText20),i&&!a&&(l=o.primary),{marginRight:0,marginLeft:0,marginBottom:0,marginTop:t.spacing.twoXS,paddingLeft:t.spacing.threeXS,paddingRight:t.spacing.threeXS,width:`calc(2 * ${r.checkbox})`,minWidth:`calc(2 * ${r.checkbox})`,height:r.checkbox,minHeight:r.checkbox,borderBottomLeftRadius:t.radii.full,borderTopLeftRadius:t.radii.full,borderBottomRightRadius:t.radii.full,borderTopRightRadius:t.radii.full,backgroundColor:l}}},Checkmark:{style:({$isFocusVisible:i,$checked:c})=>{const l=c&&!a?o.primary:o.fadedText40;return{outline:0,width:r.checkbox,height:r.checkbox,marginTop:t.spacing.twoXS,marginLeft:0,marginBottom:0,boxShadow:i&&c?`0 0 0 0.2rem ${R(o.primary,.5)}`:"",borderLeftWidth:r.borderWidth,borderRightWidth:r.borderWidth,borderTopWidth:r.borderWidth,borderBottomWidth:r.borderWidth,borderLeftColor:l,borderRightColor:l,borderTopColor:l,borderBottomColor:l}}},Label:{style:{lineHeight:t.lineHeights.small,paddingLeft:t.spacing.sm,position:"relative",color:u}}},children:y(w,{visibility:C(e.labelVisibility?.value),"data-testid":"stWidgetLabel",children:[n(S,{source:e.label,allowHTML:!1,isLabel:!0,largerLabel:!0}),e.help&&n(L,{color:u,children:n(W,{content:e.help,placement:B.TOP_RIGHT})})]})})})}function M(e,a){return e.getBoolValue(a)}function V(e){return e.default??null}function I(e){return e.value??null}function j(e,a,s,d){a.setBoolValue(e,s.value,{fromUi:s.fromUi},d)}const U=p.memo(H);export{U as default};
