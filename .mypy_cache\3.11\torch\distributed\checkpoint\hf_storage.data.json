{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint.hf_storage", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "CUSTOM_METADATA_KEY": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils.CUSTOM_METADATA_KEY", "kind": "Gdef", "module_public": false}, "ChunkStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.ChunkStorageMetadata", "kind": "Gdef", "module_public": false}, "DATA_KEY": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils.DATA_KEY", "kind": "Gdef", "module_public": false}, "DATA_OFFSETS_KEY": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils.DATA_OFFSETS_KEY", "kind": "Gdef", "module_public": false}, "DEFAULT_EXTRA_METADATA_KEY": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils.DEFAULT_EXTRA_METADATA_KEY", "kind": "Gdef", "module_public": false}, "DTYPE_KEY": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils.DTYPE_KEY", "kind": "Gdef", "module_public": false}, "FsspecReader": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._fsspec_filesystem.FsspecReader", "kind": "Gdef", "module_public": false}, "FsspecWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._fsspec_filesystem.FsspecWriter", "kind": "Gdef", "module_public": false}, "Future": {".class": "SymbolTableNode", "cross_ref": "torch.futures.Future", "kind": "Gdef", "module_public": false}, "HuggingFaceStorageReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint._fsspec_filesystem.FsspecReader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader", "name": "HuggingFaceStorageReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.checkpoint.hf_storage", "mro": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader", "torch.distributed.checkpoint._fsspec_filesystem.FsspecReader", "torch.distributed.checkpoint.filesystem.FileSystemReader", "torch.distributed.checkpoint.storage.StorageReader", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "token"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HuggingFaceStorageReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader.read_data", "name": "read_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "planner"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader", "torch.distributed.checkpoint.planner.LoadPlan", "torch.distributed.checkpoint.planner.LoadPlanner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_data of HuggingFaceStorageReader", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader.read_metadata", "name": "read_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_metadata of HuggingFaceStorageReader", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HuggingFaceStorageWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint._fsspec_filesystem.FsspecWriter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", "name": "HuggingFaceStorageWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.checkpoint.hf_storage", "mro": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", "torch.distributed.checkpoint._fsspec_filesystem.FsspecWriter", "torch.distributed.checkpoint.filesystem.FileSystemWriter", "torch.distributed.checkpoint.filesystem._FileSystemWriter", "torch.distributed.checkpoint.storage.StorageWriter", "abc.ABC", "torch.distributed.checkpoint.staging.BlockingAsyncStager", "torch.distributed.checkpoint.staging.AsyncStager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "path", "fqn_to_index_mapping", "token", "save_sharded"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "path", "fqn_to_index_mapping", "token", "save_sharded"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HuggingFaceStorageWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fqn_to_index_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter._fqn_to_index_mapping", "name": "_fqn_to_index_mapping", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_save_sharded": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter._save_sharded", "name": "_save_sharded", "type": "builtins.bool"}}, "_split_by_storage_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "storage_plan", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter._split_by_storage_plan", "name": "_split_by_storage_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "storage_plan", "items"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.WriteItem"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_by_storage_plan of HuggingFaceStorageWriter", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.WriteItem"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter.finish", "name": "finish", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata", "results"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", "torch.distributed.checkpoint.metadata.Metadata", {".class": "Instance", "args": [{".class": "Instance", "args": ["torch.distributed.checkpoint.storage.WriteResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finish of HuggingFaceStorageWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metadata_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter.metadata_path", "name": "metadata_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata_path of HuggingFaceStorageWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter.metadata_path", "name": "metadata_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata_path of HuggingFaceStorageWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prepare_global_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "plans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter.prepare_global_plan", "name": "prepare_global_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "plans"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_global_plan of HuggingFaceStorageWriter", "ret_type": {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter.write_data", "name": "write_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "planner"], "arg_types": ["torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", "torch.distributed.checkpoint.planner.SavePlan", "torch.distributed.checkpoint.planner.SavePlanner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_data of HuggingFaceStorageWriter", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["torch.distributed.checkpoint.storage.WriteResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.hf_storage.HuggingFaceStorageWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoadPlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlan", "kind": "Gdef", "module_public": false}, "LoadPlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlanner", "kind": "Gdef", "module_public": false}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.Metadata", "kind": "Gdef", "module_public": false}, "MetadataIndex": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.MetadataIndex", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ReadItem": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.ReadItem", "kind": "Gdef", "module_public": false}, "SAVED_OFFSETS_KEY": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils.SAVED_OFFSETS_KEY", "kind": "Gdef", "module_public": false}, "SHAPE_KEY": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils.SHAPE_KEY", "kind": "Gdef", "module_public": false}, "SUFFIX": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils.SUFFIX", "kind": "Gdef", "module_public": false}, "SavePlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlan", "kind": "Gdef", "module_public": false}, "SavePlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlanner", "kind": "Gdef", "module_public": false}, "SerializationFormat": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.filesystem.SerializationFormat", "kind": "Gdef", "module_public": false}, "StorageMeta": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.StorageMeta", "kind": "Gdef", "module_public": false}, "TensorProperties": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.TensorProperties", "kind": "Gdef", "module_public": false}, "TensorStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.TensorStorageMetadata", "kind": "Gdef", "module_public": false}, "WriteItem": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.WriteItem", "kind": "Gdef", "module_public": false}, "WriteResult": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.WriteResult", "kind": "Gdef", "module_public": false}, "_HFStorageInfo": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils._HFStorageInfo", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.hf_storage.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.hf_storage.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.hf_storage.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.hf_storage.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.hf_storage.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.hf_storage.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.hf_storage.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_gen_file_name": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils._gen_file_name", "kind": "Gdef", "module_public": false}, "_get_dtype": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils._get_dtype", "kind": "Gdef", "module_public": false}, "_get_safetensors_file_metadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils._get_safetensors_file_metadata", "kind": "Gdef", "module_public": false}, "_metadata_fn": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_utils._metadata_fn", "kind": "Gdef", "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "narrow_tensor_by_index": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard._utils.narrow_tensor_by_index", "kind": "Gdef", "module_public": false}, "queue": {".class": "SymbolTableNode", "cross_ref": "queue", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\hf_storage.py"}