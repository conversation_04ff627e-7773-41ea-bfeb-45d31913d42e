{"data_mtime": 1755649448, "dep_lines": [22, 23, 24, 10, 10, 11, 20, 21, 10, 2, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 20, 10, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30], "dependencies": ["torch._inductor.codegen.common", "torch._inductor.codegen.cpp_template_kernel", "torch._inductor.codegen.cpp_utils", "torch._inductor.cpp_builder", "torch._inductor.ir", "torch._inductor.cpu_vec_isa", "torch._inductor.utils", "torch._inductor.virtualized", "torch._inductor", "dataclasses", "operator", "sys", "enum", "typing", "torch", "builtins", "os", "inspect", "html", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._inductor.codegen.cpp"], "hash": "8d38c80adf1f742b3f75c6eadeea1f2a07a4a5eb", "id": "torch._inductor.codegen.cpp_micro_gemm", "ignore_all": true, "interface_hash": "1777a350c7f02b6b5f3761b854c99338197e7732", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_micro_gemm.py", "plugin_data": null, "size": 71511, "suppressed": [], "version_id": "1.15.0"}