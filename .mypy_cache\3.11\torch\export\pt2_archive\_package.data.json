{".class": "MypyFile", "_fullname": "torch.export.pt2_archive._package", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AOTICompiledModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.export.pt2_archive._package.AOTICompiledModel", "name": "AOTICompiledModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.AOTICompiledModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.export.pt2_archive._package", "mro": ["torch.export.pt2_archive._package.AOTICompiledModel", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.AOTICompiledModel.__call__", "name": "__call__", "type": null}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.AOTICompiledModel.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "arg_types": ["torch.export.pt2_archive._package.AOTICompiledModel", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__deepcopy__ of AOTICompiledModel", "ret_type": "torch.export.pt2_archive._package.AOTICompiledModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.AOTICompiledModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loader"], "arg_types": ["torch.export.pt2_archive._package.AOTICompiledModel", "torch._C._aoti.AOTIModelPackageLoader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AOTICompiledModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_constant_fqns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.AOTICompiledModel.get_constant_fqns", "name": "get_constant_fqns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.export.pt2_archive._package.AOTICompiledModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_constant_fqns of AOTICompiledModel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.AOTICompiledModel.get_metadata", "name": "get_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.export.pt2_archive._package.AOTICompiledModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metadata of AOTICompiledModel", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_constants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "constants_map", "check_full_update", "user_managed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.AOTICompiledModel.load_constants", "name": "load_constants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "constants_map", "check_full_update", "user_managed"], "arg_types": ["torch.export.pt2_archive._package.AOTICompiledModel", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_constants of AOTICompiledModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loader": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.export.pt2_archive._package.AOTICompiledModel.loader", "name": "loader", "type": "torch._C._aoti.AOTIModelPackageLoader"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export.pt2_archive._package.AOTICompiledModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export.pt2_archive._package.AOTICompiledModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AOTINDUCTOR_DIR": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.AOTINDUCTOR_DIR", "kind": "Gdef"}, "AOTI_FILES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.export.pt2_archive._package.AOTI_FILES", "line": 45, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "torch.export.pt2_archive._package_weights.Weights"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "torch.export.pt2_archive._package_weights.Weights"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "ARCHIVE_FORMAT_PATH": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.ARCHIVE_FORMAT_PATH", "kind": "Gdef"}, "ARCHIVE_FORMAT_VALUE": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.ARCHIVE_FORMAT_VALUE", "kind": "Gdef"}, "ARCHIVE_VERSION_PATH": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.ARCHIVE_VERSION_PATH", "kind": "Gdef"}, "ARCHIVE_VERSION_VALUE": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.ARCHIVE_VERSION_VALUE", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CONSTANTS_DIR": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.CONSTANTS_DIR", "kind": "Gdef"}, "CUSTOM_OBJ_FILENAME_PREFIX": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.CUSTOM_OBJ_FILENAME_PREFIX", "kind": "Gdef"}, "DEFAULT_PICKLE_PROTOCOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.export.pt2_archive._package.DEFAULT_PICKLE_PROTOCOL", "name": "DEFAULT_PICKLE_PROTOCOL", "type": "builtins.int"}}, "EXTRA_DIR": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.EXTRA_DIR", "kind": "Gdef"}, "ExportedProgram": {".class": "SymbolTableNode", "cross_ref": "torch.export.exported_program.ExportedProgram", "kind": "Gdef"}, "FileLike": {".class": "SymbolTableNode", "cross_ref": "torch.types.FileLike", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "MODELS_DIR": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.MODELS_DIR", "kind": "Gdef"}, "MODELS_FILENAME_FORMAT": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.MODELS_FILENAME_FORMAT", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "PT2ArchiveContents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents", "name": "PT2ArchiveContents", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 474, "name": "exported_programs", "type": {".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ExportedProgram"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 475, "name": "aoti_runners", "type": {".class": "Instance", "args": ["builtins.str", "torch.export.pt2_archive._package.AOTICompiledModel"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 476, "name": "extra_files", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.export.pt2_archive._package", "mro": ["torch.export.pt2_archive._package.PT2ArchiveContents", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exported_programs", "aoti_runners", "extra_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exported_programs", "aoti_runners", "extra_files"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveContents", {".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ExportedProgram"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch.export.pt2_archive._package.AOTICompiledModel"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PT2ArchiveContents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "exported_programs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "aoti_runners"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "extra_files"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["exported_programs", "aoti_runners", "extra_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["exported_programs", "aoti_runners", "extra_files"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ExportedProgram"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch.export.pt2_archive._package.AOTICompiledModel"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PT2ArchiveContents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["exported_programs", "aoti_runners", "extra_files"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ExportedProgram"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch.export.pt2_archive._package.AOTICompiledModel"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PT2ArchiveContents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "aoti_runners": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.aoti_runners", "name": "aoti_runners", "type": {".class": "Instance", "args": ["builtins.str", "torch.export.pt2_archive._package.AOTICompiledModel"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "exported_programs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.exported_programs", "name": "exported_programs", "type": {".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ExportedProgram"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "extra_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.extra_files", "name": "extra_files", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export.pt2_archive._package.PT2ArchiveContents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export.pt2_archive._package.PT2ArchiveContents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PT2ArchiveReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader", "name": "PT2ArchiveReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.export.pt2_archive._package", "mro": ["torch.export.pt2_archive._package.PT2ArchiveReader", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of PT2ArchiveReader", "ret_type": "torch.export.pt2_archive._package.PT2ArchiveReader", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of PT2ArchiveReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "archive_path_or_buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "archive_path_or_buffer"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader", {".class": "TypeAliasType", "args": [], "type_ref": "torch.types.FileLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PT2ArchiveReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "archive_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.archive_file", "name": "archive_file", "type": "torch._<PERSON><PERSON>"}}, "archive_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.archive_version", "name": "archive_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "archive_version of PT2ArchiveReader", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_file_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.get_file_names", "name": "get_file_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_file_names of PT2ArchiveReader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.read_bytes", "name": "read_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_bytes of PT2ArchiveReader", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.read_string", "name": "read_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_string of PT2ArchiveReader", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export.pt2_archive._package.PT2ArchiveReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export.pt2_archive._package.PT2ArchiveReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PT2ArchiveWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter", "name": "PT2ArchiveWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.export.pt2_archive._package", "mro": ["torch.export.pt2_archive._package.PT2ArchiveWriter", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of PT2ArchiveWriter", "ret_type": "torch.export.pt2_archive._package.PT2ArchiveWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of PT2ArchiveWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "archive_path_or_buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "archive_path_or_buffer"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", {".class": "TypeAliasType", "args": [], "type_ref": "torch.types.FileLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PT2ArchiveWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "archive_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.archive_file", "name": "archive_file", "type": "torch._C.PyTorchFileWriter"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of PT2ArchiveWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.count_prefix", "name": "count_prefix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prefix"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count_prefix of PT2ArchiveWriter", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.has_record", "name": "has_record", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_record of PT2ArchiveWriter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.write_bytes", "name": "write_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "data"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", "builtins.str", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_bytes of PT2ArchiveWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.write_file", "name": "write_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "file_path"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_file of PT2ArchiveWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_folder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "archive_dir", "folder_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.write_folder", "name": "write_folder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "archive_dir", "folder_dir"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_folder of PT2ArchiveWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.write_string", "name": "write_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "data"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_string of PT2ArchiveWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.export.pt2_archive._package.PT2ArchiveWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.export.pt2_archive._package.PT2ArchiveWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SAMPLE_INPUTS_FILENAME_FORMAT": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.SAMPLE_INPUTS_FILENAME_FORMAT", "kind": "Gdef"}, "SerializedArtifact": {".class": "SymbolTableNode", "cross_ref": "torch._export.serde.serialize.SerializedArtifact", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WEIGHTS_DIR": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.WEIGHTS_DIR", "kind": "Gdef"}, "WEIGHT_FILENAME_PREFIX": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive.constants.WEIGHT_FILENAME_PREFIX", "kind": "Gdef"}, "Weights": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive._package_weights.Weights", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export.pt2_archive._package.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export.pt2_archive._package.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export.pt2_archive._package.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export.pt2_archive._package.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export.pt2_archive._package.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.export.pt2_archive._package.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_load_exported_programs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["archive_reader", "file_names", "expected_opset_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package._load_exported_programs", "name": "_load_exported_programs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["archive_reader", "file_names", "expected_opset_version"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_exported_programs", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ExportedProgram"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_extra_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["archive_reader", "file_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package._load_extra_files", "name": "_load_extra_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["archive_reader", "file_names"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveReader", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_extra_files", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_package_aoti_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["archive_writer", "aoti_files", "pickle_protocol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package._package_aoti_files", "name": "_package_aoti_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["archive_writer", "aoti_files", "pickle_protocol"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.export.pt2_archive._package.AOTI_FILES"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_package_aoti_files", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_package_exported_programs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["archive_writer", "exported_programs", "opset_version", "pickle_protocol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package._package_exported_programs", "name": "_package_exported_programs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["archive_writer", "exported_programs", "opset_version", "pickle_protocol"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", {".class": "UnionType", "items": ["torch.export.exported_program.ExportedProgram", {".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ExportedProgram"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_package_exported_programs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_package_extra_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["archive_writer", "extra_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package._package_extra_files", "name": "_package_extra_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["archive_writer", "extra_files"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveWriter", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_package_extra_files", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "deserialize": {".class": "SymbolTableNode", "cross_ref": "torch._export.serde.serialize.deserialize", "kind": "Gdef"}, "get_complete": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive._package_weights.get_complete", "kind": "Gdef"}, "glob": {".class": "SymbolTableNode", "cross_ref": "glob", "kind": "Gdef"}, "group_weights": {".class": "SymbolTableNode", "cross_ref": "torch.export.pt2_archive._package_weights.group_weights", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "is_pt2_package": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["serialized_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.is_pt2_package", "name": "is_pt2_package", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["serialized_model"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_pt2_package", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_pt2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["f", "expected_opset_version", "run_single_threaded", "num_runners", "device_index", "load_weights_from_disk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.load_pt2", "name": "load_pt2", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["f", "expected_opset_version", "run_single_threaded", "num_runners", "device_index", "load_weights_from_disk"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.types.FileLike"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_pt2", "ret_type": "torch.export.pt2_archive._package.PT2ArchiveContents", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_weights_to_pt2_contents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pt2_contents", "weights_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.load_weights_to_pt2_contents", "name": "load_weights_to_pt2_contents", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pt2_contents", "weights_map"], "arg_types": ["torch.export.pt2_archive._package.PT2ArchiveContents", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_weights_to_pt2_contents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.export.pt2_archive._package.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "package_pt2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["f", "exported_programs", "aoti_files", "extra_files", "opset_version", "pickle_protocol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.export.pt2_archive._package.package_pt2", "name": "package_pt2", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["f", "exported_programs", "aoti_files", "extra_files", "opset_version", "pickle_protocol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.types.FileLike"}, {".class": "UnionType", "items": ["torch.export.exported_program.ExportedProgram", {".class": "Instance", "args": ["builtins.str", "torch.export.exported_program.ExportedProgram"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.export.pt2_archive._package.AOTI_FILES"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "package_pt2", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.types.FileLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "reorder_kwargs": {".class": "SymbolTableNode", "cross_ref": "torch.export._tree_utils.reorder_kwargs", "kind": "Gdef"}, "serialize": {".class": "SymbolTableNode", "cross_ref": "torch._export.serde.serialize.serialize", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "zipfile": {".class": "SymbolTableNode", "cross_ref": "zipfile", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\export\\pt2_archive\\_package.py"}