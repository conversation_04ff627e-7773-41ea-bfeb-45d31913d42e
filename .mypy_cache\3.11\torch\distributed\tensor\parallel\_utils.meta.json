{"data_mtime": 1755649449, "dep_lines": [7, 5, 6, 11, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 20, 20], "dependencies": ["torch.distributed.tensor.placement_types", "torch.distributed.device_mesh", "torch.distributed.tensor", "torch._dynamo.external_utils", "warnings", "typing", "builtins", "collections", "torch", "operator", "itertools", "pprint", "math", "inspect", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "_frozen_importlib", "abc", "contextlib"], "hash": "4be94617642987b28b5df6ea8dff337003a03fa9", "id": "torch.distributed.tensor.parallel._utils", "ignore_all": true, "interface_hash": "d67335eb34e2b2e8602429694fdff74a26f5c5f3", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\_utils.py", "plugin_data": null, "size": 2380, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}