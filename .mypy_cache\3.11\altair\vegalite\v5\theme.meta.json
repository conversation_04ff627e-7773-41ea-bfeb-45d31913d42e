{"data_mtime": 1755656862, "dep_lines": [9, 10, 7, 8, 3, 5, 13, 14, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["altair.vegalite.v5.schema._config", "altair.vegalite.v5.schema._typing", "altair.utils.deprecation", "altair.utils.plugin_registry", "__future__", "typing", "sys", "functools", "builtins", "_frozen_importlib", "_typeshed", "abc", "altair.utils", "altair.vegalite.v5.schema", "typing_extensions"], "hash": "a4364ecdbea5ec41672f767921a3f30fa930e1cc", "id": "altair.vegalite.v5.theme", "ignore_all": true, "interface_hash": "f3a7fb80f666be0631d7cf29bcd0a6c20a49714c", "mtime": 1755656335, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\vegalite\\v5\\theme.py", "plugin_data": null, "size": 3823, "suppressed": [], "version_id": "1.15.0"}