{"data_mtime": 1755656859, "dep_lines": [1, 3, 4, 5, 6, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["__future__", "sys", "threading", "warnings", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "types"], "hash": "596f913275ca830651a5c2c17ba69e1754f547bf", "id": "altair.utils.deprecation", "ignore_all": true, "interface_hash": "04249738613723135719df4b70151ba2589ab48c", "mtime": 1755656335, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\altair\\utils\\deprecation.py", "plugin_data": null, "size": 5620, "suppressed": [], "version_id": "1.15.0"}