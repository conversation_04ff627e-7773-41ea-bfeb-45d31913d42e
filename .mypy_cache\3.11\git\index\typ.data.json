{".class": "MypyFile", "_fullname": "git.index.typ", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseIndexEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.index.typ.BaseIndexEntryHelper"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.index.typ.BaseIndexEntry", "name": "BaseIndexEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.index.typ.BaseIndexEntry", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.index.typ", "mro": ["git.index.typ.BaseIndexEntry", "git.index.typ.BaseIndexEntryHelper", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "inp_tuple"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "git.index.typ.BaseIndexEntry.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "inp_tuple"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of BaseIndexEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.typ.BaseIndexEntry.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of BaseIndexEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.typ.BaseIndexEntry.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of BaseIndexEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_blob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "blob", "stage"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.index.typ.BaseIndexEntry.from_blob", "name": "from_blob", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "blob", "stage"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}}, "git.objects.blob.Blob", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_blob of BaseIndexEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.index.typ.BaseIndexEntry.from_blob", "name": "from_blob", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "blob", "stage"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}}, "git.objects.blob.Blob", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_blob of BaseIndexEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hexsha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.index.typ.BaseIndexEntry.hexsha", "name": "hex<PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hexsha of BaseIndexEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.index.typ.BaseIndexEntry.hexsha", "name": "hex<PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hexsha of BaseIndexEntry", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.index.typ.BaseIndexEntry.stage", "name": "stage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stage of BaseIndexEntry", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.index.typ.BaseIndexEntry.stage", "name": "stage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stage of BaseIndexEntry", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_blob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.typ.BaseIndexEntry.to_blob", "name": "to_blob", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}, "git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_blob of BaseIndexEntry", "ret_type": "git.objects.blob.Blob", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntryHelper"}, "type_vars": [], "typeddict_type": null}}, "BaseIndexEntryHelper": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.index.typ.BaseIndexEntryHelper", "name": "BaseIndexEntryHelper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "git.index.typ.BaseIndexEntryHelper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["mode", "<PERSON><PERSON>", "flags", "path", "ctime_bytes", "mtime_bytes", "dev", "inode", "uid", "gid", "size"]}}, "module_name": "git.index.typ", "mro": ["git.index.typ.BaseIndexEntryHelper", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "flags"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ctime_bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mtime_bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dev"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["_cls", "mode", "<PERSON><PERSON>", "flags", "path", "ctime_bytes", "mtime_bytes", "dev", "inode", "uid", "gid", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "git.index.typ.BaseIndexEntryHelper.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["_cls", "mode", "<PERSON><PERSON>", "flags", "path", "ctime_bytes", "mtime_bytes", "dev", "inode", "uid", "gid", "size"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of BaseIndexEntryHelper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.typ.BaseIndexEntryHelper._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of BaseIndexEntryHelper", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.index.typ.BaseIndexEntryHelper._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of BaseIndexEntryHelper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of BaseIndexEntryHelper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "mode", "<PERSON><PERSON>", "flags", "path", "ctime_bytes", "mtime_bytes", "dev", "inode", "uid", "gid", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.typ.BaseIndexEntryHelper._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "mode", "<PERSON><PERSON>", "flags", "path", "ctime_bytes", "mtime_bytes", "dev", "inode", "uid", "gid", "size"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of BaseIndexEntryHelper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper._NT", "id": -1, "name": "_NT", "namespace": "git.index.typ.BaseIndexEntryHelper._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper._source", "name": "_source", "type": "builtins.str"}}, "binsha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.binsha", "name": "<PERSON><PERSON>", "type": "builtins.bytes"}}, "binsha-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.binsha", "kind": "<PERSON><PERSON><PERSON>"}, "ctime_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.ctime_bytes", "name": "ctime_bytes", "type": "builtins.bytes"}}, "ctime_bytes-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.ctime_bytes", "kind": "<PERSON><PERSON><PERSON>"}, "dev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.dev", "name": "dev", "type": "builtins.int"}}, "dev-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.dev", "kind": "<PERSON><PERSON><PERSON>"}, "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.flags", "name": "flags", "type": "builtins.int"}}, "flags-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.flags", "kind": "<PERSON><PERSON><PERSON>"}, "gid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.gid", "name": "gid", "type": "builtins.int"}}, "gid-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.gid", "kind": "<PERSON><PERSON><PERSON>"}, "inode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.inode", "name": "inode", "type": "builtins.int"}}, "inode-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.inode", "kind": "<PERSON><PERSON><PERSON>"}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.mode", "name": "mode", "type": "builtins.int"}}, "mode-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.mode", "kind": "<PERSON><PERSON><PERSON>"}, "mtime_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.mtime_bytes", "name": "mtime_bytes", "type": "builtins.bytes"}}, "mtime_bytes-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.mtime_bytes", "kind": "<PERSON><PERSON><PERSON>"}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.path", "name": "path", "type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}}}, "path-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.path", "kind": "<PERSON><PERSON><PERSON>"}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.size", "name": "size", "type": "builtins.int"}}, "size-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.size", "kind": "<PERSON><PERSON><PERSON>"}, "uid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.index.typ.BaseIndexEntryHelper.uid", "name": "uid", "type": "builtins.int"}}, "uid-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.index.typ.BaseIndexEntryHelper.uid", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BaseIndexEntryHelper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntryHelper"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Blob": {".class": "SymbolTableNode", "cross_ref": "git.objects.blob.Blob", "kind": "Gdef", "module_public": false}, "BlobFilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.index.typ.BlobFilter", "name": "BlobFilter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.index.typ.BlobFilter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.index.typ", "mro": ["git.index.typ.BlobFilter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stage_blob"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.typ.BlobFilter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stage_blob"], "arg_types": ["git.index.typ.BlobFilter", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "git.objects.blob.Blob"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of BlobFilter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "paths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.index.typ.BlobFilter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "paths"], "arg_types": ["git.index.typ.BlobFilter", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlobFilter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.index.typ.BlobFilter.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "paths": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.index.typ.BlobFilter.paths", "name": "paths", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.BlobFilter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.index.typ.BlobFilter", "values": [], "variance": 0}, "slots": ["paths"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CE_EXTENDED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.index.typ.CE_EXTENDED", "name": "CE_EXTENDED", "type": "builtins.int"}}, "CE_NAMEMASK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.index.typ.CE_NAMEMASK", "name": "CE_NAMEMASK", "type": "builtins.int"}}, "CE_STAGEMASK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.index.typ.CE_STAGEMASK", "name": "CE_STAGEMASK", "type": "builtins.int"}}, "CE_STAGESHIFT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.index.typ.CE_STAGESHIFT", "name": "CE_STAGESHIFT", "type": "builtins.int"}}, "CE_VALID": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.index.typ.CE_VALID", "name": "CE_VALID", "type": "builtins.int"}}, "IndexEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.index.typ.BaseIndexEntry"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.index.typ.IndexEntry", "name": "IndexEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.index.typ.IndexEntry", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.index.typ", "mro": ["git.index.typ.IndexEntry", "git.index.typ.BaseIndexEntry", "git.index.typ.BaseIndexEntryHelper", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "ctime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.index.typ.IndexEntry.ctime", "name": "ctime", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ctime of IndexEntry", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.index.typ.IndexEntry.ctime", "name": "ctime", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ctime of IndexEntry", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "base"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.index.typ.IndexEntry.from_base", "name": "from_base", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "base"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}}, {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_base of IndexEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.index.typ.IndexEntry.from_base", "name": "from_base", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "base"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}}, {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.BaseIndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_base of IndexEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_blob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "blob", "stage"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.index.typ.IndexEntry.from_blob", "name": "from_blob", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "blob", "stage"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}}, "git.objects.blob.Blob", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_blob of IndexEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.index.typ.IndexEntry.from_blob", "name": "from_blob", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "blob", "stage"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}}, "git.objects.blob.Blob", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_blob of IndexEntry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.index.typ.IndexEntry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mtime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.index.typ.IndexEntry.mtime", "name": "mtime", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mtime of IndexEntry", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.index.typ.IndexEntry.mtime", "name": "mtime", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mtime of IndexEntry", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.index.typ.IndexEntry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.IndexEntry"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": "git.index.typ.BaseIndexEntry"}, "type_vars": [], "typeddict_type": null}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "StageType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.index.typ.StageType", "line": 24, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.index.typ.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.typ.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.typ.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.typ.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.typ.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.typ.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.index.typ.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "b2a_hex": {".class": "SymbolTableNode", "cross_ref": "binascii.b2a_hex", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "pack": {".class": "SymbolTableNode", "cross_ref": "git.index.util.pack", "kind": "Gdef", "module_public": false}, "unpack": {".class": "SymbolTableNode", "cross_ref": "git.index.util.unpack", "kind": "Gdef", "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\index\\typ.py"}