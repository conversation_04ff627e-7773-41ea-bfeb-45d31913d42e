{".class": "MypyFile", "_fullname": "torch._inductor.codegen.cpp_gemm_template", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CppGemmTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.cpp_template.CppTemplate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "name": "CppGemmTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_gemm_template", "mro": ["torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "torch._inductor.codegen.cpp_template.CppTemplate", "torch._inductor.codegen.common.KernelTemplate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_nodes", "layout", "num_threads", "register_blocking", "beta", "alpha", "has_bias", "epilogue_creator", "should_block_weights", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_nodes", "layout", "num_threads", "register_blocking", "beta", "alpha", "has_bias", "epilogue_creator", "should_block_weights", "name"], "arg_types": ["torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._inductor.ir.Layout", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._inductor.ir.Pointwise", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CppGemmTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cache_blocking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "num_threads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate._cache_blocking", "name": "_cache_blocking", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "num_threads"], "arg_types": ["torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cache_blocking of CppGemmTemplate", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_thread_blocking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "num_threads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate._thread_blocking", "name": "_thread_blocking", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "num_threads"], "arg_types": ["torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_thread_blocking of CppGemmTemplate", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "choices", "layout", "input_nodes", "beta", "alpha", "has_bias", "trans_w", "input_indices", "epilogue_creator", "act_mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.add_choices", "name": "add_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "choices", "layout", "input_nodes", "beta", "alpha", "has_bias", "trans_w", "input_indices", "epilogue_creator", "act_mapping"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._inductor.ir.Pointwise", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_choices of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.add_choices", "name": "add_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "choices", "layout", "input_nodes", "beta", "alpha", "has_bias", "trans_w", "input_indices", "epilogue_creator", "act_mapping"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._inductor.ir.Pointwise", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_choices of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.alpha", "name": "alpha", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "beta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.beta", "name": "beta", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "block_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "W", "new_size", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.block_weight", "name": "block_weight", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.block_weight", "name": "block_weight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "W", "new_size", "padding"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "block_weight of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cache_blocking": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.cache_blocking", "name": "cache_blocking", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "check_if_block_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["W", "micro_gemm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.check_if_block_weight", "name": "check_if_block_weight", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.check_if_block_weight", "name": "check_if_block_weight", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["W", "micro_gemm"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_if_block_weight of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "codegen_blocks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "num_threads", "N", "K", "micro_gemm", "is_dynamic_M", "kernel", "GemmOut", "config", "L1_cache_size", "L2_cache_size", "X", "W"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.codegen_blocks", "name": "codegen_blocks", "type": null}}, "codegen_gemm_stub_def": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.codegen_gemm_stub_def", "name": "codegen_gemm_stub_def", "type": null}}, "codegen_m_loop_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.codegen_m_loop_params", "name": "codegen_m_loop_params", "type": null}}, "codegen_microkernel_def": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.codegen_microkernel_def", "name": "codegen_microkernel_def", "type": null}}, "codegen_multi_threads_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.codegen_multi_threads_params", "name": "codegen_multi_threads_params", "type": null}}, "codegen_n_loop_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.codegen_n_loop_params", "name": "codegen_n_loop_params", "type": null}}, "codegen_single_thread_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_dynamic_M"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.codegen_single_thread_params", "name": "codegen_single_thread_params", "type": null}}, "get_default_reindexers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "epilogue_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.get_default_reindexers", "name": "get_default_reindexers", "type": null}}, "get_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "kernel", "template_buffer_node", "flag_template_buffer_has_other_users", "epilogue_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.get_options", "name": "get_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "kernel", "template_buffer_node", "flag_template_buffer_has_other_users", "epilogue_nodes"], "arg_types": ["torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel", {".class": "UnionType", "items": ["torch._inductor.ir.CppTemplateBuffer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_options of CppGemmTemplate", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_padded_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["n", "block_n", "k", "should_block_weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.get_padded_size", "name": "get_padded_size", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.get_padded_size", "name": "get_padded_size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["n", "block_n", "k", "should_block_weight"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_padded_size of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.has_bias", "name": "has_bias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_dynamic_M": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.is_dynamic_M", "name": "is_dynamic_M", "type": "builtins.bool"}}, "is_int8_woq_gemm_small_m_dim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "X", "W", "N", "K", "micro_gemm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.is_int8_woq_gemm_small_m_dim", "name": "is_int8_woq_gemm_small_m_dim", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "X", "W", "N", "K", "micro_gemm"], "arg_types": ["torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_int8_woq_gemm_small_m_dim of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_woq_int4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.is_woq_int4", "name": "is_woq_int4", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.is_woq_int4", "name": "is_woq_int4", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_woq_int4 of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.k", "name": "k", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "log_blockings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.log_blockings", "name": "log_blockings", "type": null}}, "m": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.m", "name": "m", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.ir.Expr", "source_any": null, "type_of_any": 3}}}, "make_cache_blocking_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.make_cache_blocking_cache", "name": "make_cache_blocking_cache", "type": null}}, "make_thread_blocking_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.make_thread_blocking_cache", "name": "make_thread_blocking_cache", "type": null}}, "maybe_k_slicing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.maybe_k_slicing", "name": "maybe_k_slicing", "type": null}}, "n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.n", "name": "n", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.ir.Expr", "source_any": null, "type_of_any": 3}}}, "pack_vnni_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "W", "micro_gemm", "new_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.pack_vnni_weight", "name": "pack_vnni_weight", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.pack_vnni_weight", "name": "pack_vnni_weight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "W", "micro_gemm", "new_size"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pack_vnni_weight of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "padded_n": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.padded_n", "name": "padded_n", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prep_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["cls", "inputs", "layout", "micro_gemm", "should_block_weight", "use_int8_fast_compensation_path", "skip_int8_compensation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.prep_weight", "name": "prep_weight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["cls", "inputs", "layout", "micro_gemm", "should_block_weight", "use_int8_fast_compensation_path", "skip_int8_compensation"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._inductor.ir.Layout", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prep_weight of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.prep_weight", "name": "prep_weight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["cls", "inputs", "layout", "micro_gemm", "should_block_weight", "use_int8_fast_compensation_path", "skip_int8_compensation"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch._inductor.ir.Layout", "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prep_weight of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "q_group_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.q_group_size", "name": "q_group_size", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.q_group_size", "name": "q_group_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "q_group_size of CppGemmTemplate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register_blocking": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.register_blocking", "name": "register_blocking", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking"}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "kernel", "template_buffer_node", "flag_template_buffer_has_other_users", "epilogue_nodes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "kernel", "template_buffer_node", "flag_template_buffer_has_other_users", "epilogue_nodes", "kwargs"], "arg_types": ["torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel", {".class": "UnionType", "items": ["torch._inductor.ir.CppTemplateBuffer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of CppGemmTemplate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.render_options", "name": "render_options", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "should_block_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.should_block_weights", "name": "should_block_weights", "type": "builtins.bool"}}, "thread_blocking": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.thread_blocking", "name": "thread_blocking", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_gemm_template.CppGemmTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppMicroBrgemm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.CppMicroBrgemm", "kind": "Gdef"}, "CppMicroGemm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemm", "kind": "Gdef"}, "CppMicroGemmAMX": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmAMX", "kind": "Gdef"}, "CppMicroGemmFP32Vec": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.CppMicroGemmFP32Vec", "kind": "Gdef"}, "CppTemplate": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_template.CppTemplate", "kind": "Gdef"}, "CppTemplateKernel": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_template_kernel.CppTemplateKernel", "kind": "Gdef"}, "CppWoqInt4GemmTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplateMeta", "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplate", "name": "CppWoqInt4GemmTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplate", "has_param_spec_type": false, "metaclass_type": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplateMeta", "metadata": {}, "module_name": "torch._inductor.codegen.cpp_gemm_template", "mro": ["torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplate", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CppWoqInt4GemmTemplateMeta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplateMeta", "name": "CppWoqInt4GemmTemplateMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplateMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.cpp_gemm_template", "mro": ["torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplateMeta", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplateMeta.__getitem__", "name": "__getitem__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplateMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.cpp_gemm_template.CppWoqInt4GemmTemplateMeta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DTYPE_TO_CPP": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.DTYPE_TO_CPP", "kind": "Gdef"}, "DataProcessorTemplateWrapper": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.select_algorithm.DataProcessorTemplateWrapper", "kind": "Gdef"}, "GEMM_TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE", "name": "GEMM_TEMPLATE", "type": "builtins.str"}}, "GEMM_TEMPLATE_INIT_BLOCKING_BASIC_BLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE_INIT_BLOCKING_BASIC_BLOCK", "name": "GEMM_TEMPLATE_INIT_BLOCKING_BASIC_BLOCK", "type": "builtins.str"}}, "GEMM_TEMPLATE_INIT_BLOCKING_EXTENDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE_INIT_BLOCKING_EXTENDED", "name": "GEMM_TEMPLATE_INIT_BLOCKING_EXTENDED", "type": "builtins.str"}}, "GEMM_TEMPLATE_MICROKERNEL_DEF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE_MICROKERNEL_DEF", "name": "GEMM_TEMPLATE_MICROKERNEL_DEF", "type": "builtins.str"}}, "GEMM_TEMPLATE_MULTI_THREADS_PARAMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE_MULTI_THREADS_PARAMS", "name": "GEMM_TEMPLATE_MULTI_THREADS_PARAMS", "type": "builtins.str"}}, "GEMM_TEMPLATE_M_LOOP_PARAMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE_M_LOOP_PARAMS", "name": "GEMM_TEMPLATE_M_LOOP_PARAMS", "type": "builtins.str"}}, "GEMM_TEMPLATE_N_LOOP_PARAMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE_N_LOOP_PARAMS", "name": "GEMM_TEMPLATE_N_LOOP_PARAMS", "type": "builtins.str"}}, "GEMM_TEMPLATE_SINGLE_THREAD_PARAMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE_SINGLE_THREAD_PARAMS", "name": "GEMM_TEMPLATE_SINGLE_THREAD_PARAMS", "type": "builtins.str"}}, "GEMM_TEMPLATE_STUB_DEF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.GEMM_TEMPLATE_STUB_DEF", "name": "GEMM_TEMPLATE_STUB_DEF", "type": "builtins.str"}}, "GemmBlocking": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.GemmBlocking", "kind": "Gdef"}, "L": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering", "kind": "Gdef"}, "LayoutType": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.LayoutType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "SMALL_M_GEMM_TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.SMALL_M_GEMM_TEMPLATE", "name": "SMALL_M_GEMM_TEMPLATE", "type": "builtins.str"}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template._T", "name": "_T", "upper_bound": "builtins.object", "values": ["torch._inductor.ir.IRNode", "torch._tensor.Tensor"], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_gemm_template.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_gemm_template.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_gemm_template.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_gemm_template.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_gemm_template.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.cpp_gemm_template.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_is_int8_gemm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template._is_int8_gemm", "name": "_is_int8_gemm", "type": null}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "create_epilogue_with_attr": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.create_epilogue_with_attr", "kind": "Gdef"}, "create_micro_gemm": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.create_micro_gemm", "kind": "Gdef"}, "expand_bias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["B", "X"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.expand_bias", "name": "expand_bias", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["B", "X"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template._T", "id": -1, "name": "_T", "namespace": "torch._inductor.codegen.cpp_gemm_template.expand_bias", "upper_bound": "builtins.object", "values": ["torch._inductor.ir.IRNode", "torch._tensor.Tensor"], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template._T", "id": -1, "name": "_T", "namespace": "torch._inductor.codegen.cpp_gemm_template.expand_bias", "upper_bound": "builtins.object", "values": ["torch._inductor.ir.IRNode", "torch._tensor.Tensor"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand_bias", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template._T", "id": -1, "name": "_T", "namespace": "torch._inductor.codegen.cpp_gemm_template.expand_bias", "upper_bound": "builtins.object", "values": ["torch._inductor.ir.IRNode", "torch._tensor.Tensor"], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template._T", "id": -1, "name": "_T", "namespace": "torch._inductor.codegen.cpp_gemm_template.expand_bias", "upper_bound": "builtins.object", "values": ["torch._inductor.ir.IRNode", "torch._tensor.Tensor"], "variance": 0}]}}}, "gen_2d_view_of_epilogue_buf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["Y", "template_buffer", "epilogue_nodes", "reindexers", "default_reindexers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.gen_2d_view_of_epilogue_buf", "name": "gen_2d_view_of_epilogue_buf", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["Y", "template_buffer", "epilogue_nodes", "reindexers", "default_reindexers"], "arg_types": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gen_2d_view_of_epilogue_buf", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_export_declaration": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp.get_export_declaration", "kind": "Gdef"}, "get_gemm_template_output_and_compute_dtype": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.get_gemm_template_output_and_compute_dtype", "kind": "Gdef"}, "get_padded_n": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["n", "block_n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.get_padded_n", "name": "get_padded_n", "type": null}}, "has_free_symbols": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.has_free_symbols", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "is_int8_woq_gemm_small_m_dim_corner_case": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_micro_gemm.is_int8_woq_gemm_small_m_dim_corner_case", "kind": "Gdef"}, "is_same_mkldnn_tensor": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.is_same_mkldnn_tensor", "kind": "Gdef"}, "is_same_tensor": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.is_same_tensor", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.cpp_gemm_template.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "mm_args": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.kernel.mm_common.mm_args", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.ops", "kind": "Gdef"}, "parallel_num_threads": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.parallel_num_threads", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "prune_tensors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["input_nodes", "new_input_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.prune_tensors", "name": "prune_tensors", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["input_nodes", "new_input_nodes"], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prune_tensors", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transpose_w": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["W", "trans_w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.cpp_gemm_template.transpose_w", "name": "transpose_w", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["W", "trans_w"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template._T", "id": -1, "name": "_T", "namespace": "torch._inductor.codegen.cpp_gemm_template.transpose_w", "upper_bound": "builtins.object", "values": ["torch._inductor.ir.IRNode", "torch._tensor.Tensor"], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transpose_w", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template._T", "id": -1, "name": "_T", "namespace": "torch._inductor.codegen.cpp_gemm_template.transpose_w", "upper_bound": "builtins.object", "values": ["torch._inductor.ir.IRNode", "torch._tensor.Tensor"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.cpp_gemm_template._T", "id": -1, "name": "_T", "namespace": "torch._inductor.codegen.cpp_gemm_template.transpose_w", "upper_bound": "builtins.object", "values": ["torch._inductor.ir.IRNode", "torch._tensor.Tensor"], "variance": 0}]}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cpp_gemm_template.py"}