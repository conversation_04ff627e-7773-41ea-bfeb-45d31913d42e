{"data_mtime": 1755649448, "dep_lines": [5, 6, 10, 11, 12, 13, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["torch.distributed.algorithms.ddp_comm_hooks.default_hooks", "torch.distributed.algorithms.ddp_comm_hooks.optimizer_overlap_hooks", "torch.distributed.fsdp", "torch.distributed.optim", "torch.nn.parallel", "torch.optim", "inspect", "abc", "builtins", "collections", "warnings", "torch", "operator", "itertools", "pprint", "math", "string", "copy", "torch.distributed._functional_collectives", "types", "os", "logging", "torch.nn", "multiprocessing.reduction", "dataclasses", "functools", "sys", "json", "traceback", "re", "html", "typing", "_frozen_importlib", "torch._C", "torch._tensor", "torch.distributed._composable_state", "torch.distributed.algorithms.ddp_comm_hooks", "torch.distributed.algorithms.join", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp.fully_sharded_data_parallel", "torch.distributed.optim.utils", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parallel.distributed", "torch.optim.optimizer", "contextlib"], "hash": "f3ba6c3d1e22a4233b740320c06e4fe728840d18", "id": "torch.distributed.algorithms._optimizer_overlap.optimizer_overlap", "ignore_all": true, "interface_hash": "00147eb1553ab7de3b7363035dea220657c7ed37", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\algorithms\\_optimizer_overlap\\optimizer_overlap.py", "plugin_data": null, "size": 3849, "suppressed": ["traitlets.utils.warnings"], "version_id": "1.15.0"}