{".class": "MypyFile", "_fullname": "streamlit.proto.Markdown_pb2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Markdown_pb2.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Markdown_pb2.google", "source_any": null, "type_of_any": 3}}}, "Markdown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Markdown_pb2.Markdown", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any", "is_final"], "fullname": "streamlit.proto.Markdown_pb2.Markdown", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Markdown_pb2", "mro": ["streamlit.proto.Markdown_pb2.Markdown", "builtins.object"], "names": {".class": "SymbolTable", "ALLOW_HTML_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.ALLOW_HTML_FIELD_NUMBER", "name": "ALLOW_HTML_FIELD_NUMBER", "type": "builtins.int"}}, "BODY_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.BODY_FIELD_NUMBER", "name": "BODY_FIELD_NUMBER", "type": "builtins.int"}}, "CAPTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.CAPTION", "name": "CAPTION", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "CODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.CODE", "name": "CODE", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "ClearField": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Markdown_pb2.Markdown.ClearField", "name": "ClearField", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["streamlit.proto.Markdown_pb2.Markdown", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "allow_html"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "allow_html"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "body"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "body"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "element_type"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "element_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "help"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_caption"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "is_caption"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ClearField of Markdown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Markdown_pb2.google", "source_any": null, "type_of_any": 3}}}, "DIVIDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.DIVIDER", "name": "DIVIDER", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "ELEMENT_TYPE_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.ELEMENT_TYPE_FIELD_NUMBER", "name": "ELEMENT_TYPE_FIELD_NUMBER", "type": "builtins.int"}}, "HELP_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.HELP_FIELD_NUMBER", "name": "HELP_FIELD_NUMBER", "type": "builtins.int"}}, "IS_CAPTION_FIELD_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.IS_CAPTION_FIELD_NUMBER", "name": "IS_CAPTION_FIELD_NUMBER", "type": "builtins.int"}}, "LATEX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.LATEX", "name": "LATEX", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "NATIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.NATIVE", "name": "NATIVE", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "Type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["streamlit.proto.Markdown_pb2.Markdown._Type"], "dataclass_transform_spec": null, "declared_metaclass": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper", "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Markdown_pb2.Markdown.Type", "name": "Type", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.Markdown_pb2.Markdown.Type", "has_param_spec_type": false, "metaclass_type": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper", "metadata": {}, "module_name": "streamlit.proto.Markdown_pb2", "mro": ["streamlit.proto.Markdown_pb2.Markdown.Type", "streamlit.proto.Markdown_pb2.Markdown._Type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Markdown_pb2.Markdown.Type.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Markdown_pb2.Markdown.Type", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UNSPECIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.UNSPECIFIED", "name": "UNSPECIFIED", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "_Type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Markdown_pb2.Markdown._Type", "name": "_Type", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "streamlit.proto.Markdown_pb2.Markdown._Type", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Markdown_pb2", "mro": ["streamlit.proto.Markdown_pb2.Markdown._Type", "builtins.object"], "names": {".class": "SymbolTable", "V": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "streamlit.proto.Markdown_pb2.Markdown._Type.V", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "ValueType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType", "name": "ValueType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Markdown_pb2", "mro": ["streamlit.proto.Markdown_pb2.Markdown._Type.ValueType", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["streamlit.proto.Markdown_pb2.Markdown._Type.ValueType", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValueType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Markdown_pb2.Markdown._Type.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Markdown_pb2.Markdown._Type", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TypeEnumTypeWrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper", "name": "_TypeEnumTypeWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "streamlit.proto.Markdown_pb2", "mro": ["streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "CAPTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper.CAPTION", "name": "CAPTION", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "CODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper.CODE", "name": "CODE", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "DESCRIPTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper.DESCRIPTOR", "name": "DESCRIPTOR", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Markdown_pb2.google", "source_any": null, "type_of_any": 3}}}, "DIVIDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper.DIVIDER", "name": "DIVIDER", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "LATEX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper.LATEX", "name": "LATEX", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "NATIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper.NATIVE", "name": "NATIVE", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "UNSPECIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper.UNSPECIFIED", "name": "UNSPECIFIED", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Markdown_pb2.Markdown._TypeEnumTypeWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "body", "allow_html", "is_caption", "element_type", "help"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "streamlit.proto.Markdown_pb2.Markdown.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "body", "allow_html", "is_caption", "element_type", "help"], "arg_types": ["streamlit.proto.Markdown_pb2.Markdown", "builtins.str", "builtins.bool", "builtins.bool", "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Markdown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allow_html": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.allow_html", "name": "allow_html", "type": "builtins.bool"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.body", "name": "body", "type": "builtins.str"}}, "element_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.element_type", "name": "element_type", "type": "streamlit.proto.Markdown_pb2.Markdown._Type.ValueType"}}, "help": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.help", "name": "help", "type": "builtins.str"}}, "is_caption": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "streamlit.proto.Markdown_pb2.Markdown.is_caption", "name": "is_caption", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "streamlit.proto.Markdown_pb2.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "streamlit.proto.Markdown_pb2.Markdown", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Markdown_pb2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Markdown_pb2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Markdown_pb2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Markdown_pb2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Markdown_pb2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "streamlit.proto.Markdown_pb2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "global___Markdown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "streamlit.proto.Markdown_pb2.global___Markdown", "line": 86, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "streamlit.proto.Markdown_pb2.Markdown"}}, "google": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "streamlit.proto.Markdown_pb2.google", "name": "google", "type": {".class": "AnyType", "missing_import_name": "streamlit.proto.Markdown_pb2.google", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\streamlit\\proto\\Markdown_pb2.pyi"}