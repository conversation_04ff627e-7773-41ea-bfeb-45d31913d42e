{"data_mtime": 1755649448, "dep_lines": [9, 14, 15, 25, 32, 33, 9, 11, 13, 34, 40, 8, 10, 12, 40, 2, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 20, 10, 5, 5, 10, 10, 10, 5, 20, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.fsdp._traversal_utils", "torch.distributed.algorithms._comm_hooks", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp._flat_param", "torch.distributed.fsdp._init_utils", "torch.distributed.fsdp.api", "torch.distributed.fsdp", "torch.nn.functional", "torch.autograd.graph", "torch.distributed.utils", "torch.utils._pytree", "torch.distributed", "torch.nn", "torch.autograd", "torch.utils", "functools", "logging", "enum", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "itertools", "collections", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "abc", "torch._C", "torch._tensor", "torch.distributed._composable_state", "torch.nn.modules", "torch.nn.modules.module"], "hash": "f6dcc283200c48b3bc49ec90368b5b0151bed046", "id": "torch.distributed.fsdp._runtime_utils", "ignore_all": true, "interface_hash": "6e207ee8080e43a326f88cff3b301de438cb6dbd", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\distributed\\fsdp\\_runtime_utils.py", "plugin_data": null, "size": 68170, "suppressed": [], "version_id": "1.15.0"}