import numpy as np
import torch

# Load and check the data
print("Loading and checking data...")
X = np.load('lstm_sequences_X.npy')
y = np.load('lstm_sequences_y.npy')

print(f"Data shapes: X={X.shape}, y={y.shape}")
print(f"X statistics:")
print(f"  Min: {X.min():.6f}, Max: {X.max():.6f}")
print(f"  Mean: {X.mean():.6f}, Std: {X.std():.6f}")
print(f"  Has NaN: {np.isnan(X).any()}")
print(f"  Has Inf: {np.isinf(X).any()}")

print(f"\ny statistics:")
print(f"  Min: {y.min():.6f}, Max: {y.max():.6f}")
print(f"  Mean: {y.mean():.6f}, Std: {y.std():.6f}")
print(f"  Has NaN: {np.isnan(y).any()}")
print(f"  Has Inf: {np.isinf(y).any()}")

# Check for extreme values
print(f"\nExtreme values check:")
print(f"X values > 1000: {(np.abs(X) > 1000).sum()}")
print(f"X values > 10000: {(np.abs(X) > 10000).sum()}")
print(f"y values > 10: {(np.abs(y) > 10).sum()}")

# Check data distribution
print(f"\nData distribution:")
print(f"X percentiles: 1%={np.percentile(X, 1):.3f}, 99%={np.percentile(X, 99):.3f}")
print(f"y percentiles: 1%={np.percentile(y, 1):.3f}, 99%={np.percentile(y, 99):.3f}")

# Test tensor conversion
print(f"\nTesting tensor conversion...")
try:
    X_tensor = torch.FloatTensor(X[:100])  # Test with small batch
    y_tensor = torch.FloatTensor(y[:100])
    print("Tensor conversion: SUCCESS")
    print(f"X_tensor has NaN: {torch.isnan(X_tensor).any()}")
    print(f"y_tensor has NaN: {torch.isnan(y_tensor).any()}")
except Exception as e:
    print(f"Tensor conversion failed: {e}")
