{".class": "MypyFile", "_fullname": "torch._inductor.graph", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutotuneCacheBundler": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.autotune_cache.AutotuneCacheBundler", "kind": "Gdef"}, "BackendFeature": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.BackendFeature", "kind": "Gdef"}, "BackwardState": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental._backward_state.BackwardState", "kind": "Gdef"}, "BaseSchedulerNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.scheduler.BaseSchedulerNode", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CompiledModule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "torch._inductor.graph.CompiledModule", "line": 126, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["types.ModuleType", "torch._inductor.codegen.common.FileBackedGraphModule"], "uses_pep604_syntax": false}}}, "Constant": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON>stant", "kind": "Gdef"}, "CppWrapperCodegenError": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.exc.CppWrapperCodegenError", "kind": "Gdef"}, "DeviceOpOverrides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.DeviceOpOverrides", "kind": "Gdef"}, "DonatedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON>", "kind": "Gdef"}, "Expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.Expr", "name": "Expr", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.graph.Expr", "source_any": null, "type_of_any": 3}}}, "FALLBACK_ALLOW_LIST": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.FALLBACK_ALLOW_LIST", "kind": "Gdef"}, "FakeScriptObject": {".class": "SymbolTableNode", "cross_ref": "torch._library.fake_class_registry.FakeScriptObject", "kind": "Gdef"}, "FakeTensor": {".class": "SymbolTableNode", "cross_ref": "torch._subclasses.fake_tensor.FakeTensor", "kind": "Gdef"}, "FileBackedGraphModule": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.FileBackedGraphModule", "kind": "Gdef"}, "FixedLayout": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.FixedLayout", "kind": "Gdef"}, "Graph": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph.Graph", "kind": "Gdef"}, "GraphLowering": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.fx.interpreter.Interpreter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.graph.GraphLowering", "name": "GraphLowering", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.graph", "mro": ["torch._inductor.graph.GraphLowering", "torch.fx.interpreter.Interpreter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "gm", "example_inputs", "shape_env", "graph_id", "cpp_wrapper", "aot_mode", "layout_opt", "extern_node_serializer", "is_inference", "is_backward", "is_const_graph", "const_output_index", "const_wrapper_code", "const_kernel_code", "const_module", "name", "inputs_to_check"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "gm", "example_inputs", "shape_env", "graph_id", "cpp_wrapper", "aot_mode", "layout_opt", "extern_node_serializer", "is_inference", "is_backward", "is_const_graph", "const_output_index", "const_wrapper_code", "const_kernel_code", "const_module", "name", "inputs_to_check"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch.fx.graph_module.GraphModule", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.fx.experimental.symbolic_shapes.ShapeEnv", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.graph.GraphLowering", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compile_to_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering._compile_to_module", "name": "_compile_to_module", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compile_to_module of GraphLowering", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.graph.CompiledModule"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compile_to_module_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "wrapper_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering._compile_to_module_lines", "name": "_compile_to_module_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "wrapper_code"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._inductor.utils.ValueWithLineMap"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compile_to_module_lines of GraphLowering", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.graph.CompiledModule"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_shape_env": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering._shape_env", "name": "_shape_env", "type": "torch.fx.experimental.symbolic_shapes.ShapeEnv"}}, "_update_scheduler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering._update_scheduler", "name": "_update_scheduler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_scheduler of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_warned_fallback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering._warned_fallback", "name": "_warned_fallback", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "add_device_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.add_device_info", "name": "add_device_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._C.device"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_device_info of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_symbol_graph_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.add_symbol_graph_input", "name": "add_symbol_graph_input", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "symbol"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_symbol_graph_input of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tensor_constant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.add_tensor_constant", "name": "add_tensor_constant", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "name"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._tensor.Tensor", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tensor_constant of GraphLowering", "ret_type": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_codegen_kernel_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.all_codegen_kernel_names", "name": "all_codegen_kernel_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "allocate_non_dup_const_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.allocate_non_dup_const_name", "name": "allocate_non_dup_const_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "data"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allocate_non_dup_const_name of GraphLowering", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allocated_constant_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.allocated_constant_name", "name": "allocated_constant_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "aot_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.aot_mode", "name": "aot_mode", "type": "builtins.bool"}}, "autotuning_grids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.autotuning_grids", "name": "autotuning_grids", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "autotuning_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.autotuning_inputs", "name": "autotuning_inputs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "autotuning_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.autotuning_mapping", "name": "autotuning_mapping", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "bound_unbacked_symbols": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.bound_unbacked_symbols", "name": "bound_unbacked_symbols", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "buffer_to_padded_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.buffer_to_padded_size", "name": "buffer_to_padded_size", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "buffers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.buffers", "name": "buffers", "type": {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "bw_donated_idxs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.bw_donated_idxs", "name": "bw_donated_idxs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cache_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.cache_key", "name": "cache_key", "type": "builtins.str"}}, "cache_linemap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.cache_linemap", "name": "cache_linemap", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "cache_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.cache_path", "name": "cache_path", "type": "builtins.str"}}, "call_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.call_function", "name": "call_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_function of GraphLowering", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.call_method", "name": "call_method", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_method of GraphLowering", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.call_module", "name": "call_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_module of GraphLowering", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_inline_constant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.graph.GraphLowering.can_inline_constant", "name": "can_inline_constant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_inline_constant of GraphLowering", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.can_inline_constant", "name": "can_inline_constant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_inline_constant of GraphLowering", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of GraphLowering", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._inductor.utils.ValueWithLineMap", "torch._inductor.utils.ValueWithLineMap"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_subgraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent_graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.codegen_subgraph", "name": "codegen_subgraph", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parent_graph"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_subgraph of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_with_cpp_wrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.codegen_with_cpp_wrapper", "name": "codegen_with_cpp_wrapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_with_cpp_wrapper of GraphLowering", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._inductor.utils.ValueWithLineMap", "torch._inductor.utils.ValueWithLineMap"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_to_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.compile_to_module", "name": "compile_to_module", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_to_module of GraphLowering", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.graph.CompiledModule"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "const_kernel_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.const_kernel_code", "name": "const_kernel_code", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "const_module": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.const_module", "name": "const_module", "type": {".class": "UnionType", "items": ["torch._inductor.graph.GraphLowering", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "const_output_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.const_output_index", "name": "const_output_index", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "const_wrapper_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.const_wrapper_code", "name": "const_wrapper_code", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constant_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "device_override"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.constant_name", "name": "constant_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "device_override"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str", {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constant_name of GraphLowering", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "constant_reprs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.constant_reprs", "name": "constant_reprs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "constants": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.constants", "name": "constants", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "count_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.count_bytes", "name": "count_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count_bytes of GraphLowering", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch._inductor.scheduler.BaseSchedulerNode", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch._inductor.scheduler.BaseSchedulerNode", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cpp_wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.cpp_wrapper", "name": "cpp_wrapper", "type": "builtins.bool"}}, "create_deferred_runtime_asserts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "n", "new_unbacked_defs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.create_deferred_runtime_asserts", "name": "create_deferred_runtime_asserts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "n", "new_unbacked_defs"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch.fx.node.Node", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_deferred_runtime_asserts of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "creation_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.creation_time", "name": "creation_time", "type": "builtins.float"}}, "current_device": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.current_device", "name": "current_device", "type": {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "current_node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.current_node", "name": "current_node", "type": "torch.fx.node.Node"}}, "decide_layout_opt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["gm", "is_inference"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.graph.GraphLowering.decide_layout_opt", "name": "decide_layout_opt", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["gm", "is_inference"], "arg_types": ["torch.fx.graph_module.GraphModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decide_layout_opt of GraphLowering", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.decide_layout_opt", "name": "decide_layout_opt", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["gm", "is_inference"], "arg_types": ["torch.fx.graph_module.GraphModule", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decide_layout_opt of GraphLowering", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "device_idxs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.device_idxs", "name": "device_idxs", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "device_node_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.device_node_mapping", "name": "device_node_mapping", "type": {".class": "Instance", "args": ["torch._C.device", "torch.fx.node.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "device_ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.device_ops", "name": "device_ops", "type": "torch._inductor.codegen.common.DeviceOpOverrides"}}, "device_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.device_type", "name": "device_type", "type": "builtins.str"}}, "device_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.device_types", "name": "device_types", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "disable_cudagraphs_reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.disable_cudagraphs_reason", "name": "disable_cudagraphs_reason", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dynamo_flat_name_to_original_fqn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.dynamo_flat_name_to_original_fqn", "name": "dynamo_flat_name_to_original_fqn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "effectful_ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.effectful_ops", "name": "effectful_ops", "type": {".class": "Instance", "args": ["torch._higher_order_ops.effects._EffectType", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "example_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.example_inputs", "name": "example_inputs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "extern_kernel_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.extern_kernel_nodes", "name": "extern_kernel_nodes", "type": {".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "extern_node_serializer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.extern_node_serializer", "name": "extern_node_serializer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["torch._inductor.ir.ExternKernelNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_autotune_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "example_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.extract_autotune_inputs", "name": "extract_autotune_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "example_inputs"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.float", "torch._tensor.Tensor"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_autotune_inputs of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fake_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.graph.GraphLowering.fake_mode", "name": "fake_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fake_mode of GraphLowering", "ret_type": "torch._subclasses.fake_tensor.FakeTensorMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.fake_mode", "name": "fake_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fake_mode of GraphLowering", "ret_type": "torch._subclasses.fake_tensor.FakeTensorMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.finalize", "name": "finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_nodes_prefer_channels_last": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.find_nodes_prefer_channels_last", "name": "find_nodes_prefer_channels_last", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_nodes_prefer_channels_last of GraphLowering", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "folded_constants": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.folded_constants", "name": "folded_constants", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "freeze_runtime_asserts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.freeze_runtime_asserts", "name": "freeze_runtime_asserts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "freeze_runtime_asserts of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_allocation_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_allocation_size", "name": "get_allocation_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.StorageBox", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.codegen.common.WorkspaceArg", "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_allocation_size of GraphLowering", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.graph.Expr", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_allocation_storage_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_allocation_storage_size", "name": "get_allocation_storage_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.codegen.common.WorkspaceArg", "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_allocation_storage_size of GraphLowering", "ret_type": {".class": "AnyType", "missing_import_name": "torch._inductor.graph.Expr", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_attr", "name": "get_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_attr of GraphLowering", "ret_type": {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON>stant", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.Subgraph", "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_backend_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.get_backend_features", "name": "get_backend_features", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["torch._inductor.codegen.common.BackendFeature"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}, "get_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_buffer", "name": "get_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer_name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_buffer of GraphLowering", "ret_type": {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_current_device_or_throw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_current_device_or_throw", "name": "get_current_device_or_throw", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_device_or_throw of GraphLowering", "ret_type": "torch._C.device", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_dtype", "name": "get_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer_name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dtype of GraphLowering", "ret_type": "torch._C.dtype", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_numel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_numel", "name": "get_numel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer_name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_numel of GraphLowering", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.graph.Expr", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_original_value_of_constant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_original_value_of_constant", "name": "get_original_value_of_constant", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_original_value_of_constant of GraphLowering", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_output_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_output_names", "name": "get_output_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_output_names of GraphLowering", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_training_phase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.get_training_phase", "name": "get_training_phase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_training_phase of GraphLowering", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.graph_id", "name": "graph_id", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "graph_input_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.graph_input_names", "name": "graph_input_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "graph_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.graph_inputs", "name": "graph_inputs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.TorchBindObject", {".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "graph_inputs_original": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.graph_inputs_original", "name": "graph_inputs_original", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.ir.In<PERSON>uffer"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "graph_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.graph.GraphLowering.graph_outputs", "name": "graph_outputs", "type": {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "has_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "device", "feature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.has_feature", "name": "has_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "device", "feature"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "UnionType", "items": ["torch._inductor.ir.IRNode", "torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch._inductor.codegen.common.BackendFeature"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_feature of GraphLowering", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_wrapper_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "is_subgraph", "subgraph_name", "parent_wrapper_code", "partition_signatures"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.init_wrapper_code", "name": "init_wrapper_code", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "is_subgraph", "subgraph_name", "parent_wrapper_code", "partition_signatures"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_wrapper_code of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inplaced_to_remove": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.inplaced_to_remove", "name": "inplaced_to_remove", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "inputs_to_check": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.inputs_to_check", "name": "inputs_to_check", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "invoke_quant_ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.invoke_quant_ops", "name": "invoke_quant_ops", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "is_backward": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.is_backward", "name": "is_backward", "type": "builtins.bool"}}, "is_const_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.is_const_graph", "name": "is_const_graph", "type": "builtins.bool"}}, "is_inference": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.is_inference", "name": "is_inference", "type": "builtins.bool"}}, "is_unspec_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.is_unspec_arg", "name": "is_unspec_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unspec_arg of GraphLowering", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layout_opt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.layout_opt", "name": "layout_opt", "type": "builtins.bool"}}, "lists": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.lists", "name": "lists", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "low_precision_codegen_ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.low_precision_codegen_ops", "name": "low_precision_codegen_ops", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "make_subgraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gm", "example_inputs", "subgraph_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.make_subgraph", "name": "make_subgraph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gm", "example_inputs", "subgraph_name"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch.fx.graph_module.GraphModule", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_subgraph of GraphLowering", "ret_type": "torch._inductor.graph.SubgraphLowering", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mark_buffer_mutated": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.mark_buffer_mutated", "name": "mark_buffer_mutated", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mark_buffer_mutated of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multi_kernel_to_choice": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.multi_kernel_to_choice", "name": "multi_kernel_to_choice", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "mutated_buffers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.mutated_buffers", "name": "mutated_buffers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "mutated_input_idxs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.mutated_input_idxs", "name": "mutated_input_idxs", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "mutated_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.mutated_inputs", "name": "mutated_inputs", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "name_to_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.name_to_buffer", "name": "name_to_buffer", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "name_to_op": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.name_to_op", "name": "name_to_op", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.ir.Operation"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "name_to_users": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.name_to_users", "name": "name_to_users", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "named_buffers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.named_buffers", "name": "named_buffers", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "named_parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.named_parameters", "name": "named_parameters", "type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "never_reuse_buffers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.never_reuse_buffers", "name": "never_reuse_buffers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "no_fuse_buffer_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.no_fuse_buffer_names", "name": "no_fuse_buffer_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "nodes_prefer_channels_last": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.nodes_prefer_channels_last", "name": "nodes_prefer_channels_last", "type": {".class": "Instance", "args": ["torch.fx.node.Node"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "num_channels_last_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.num_channels_last_conv", "name": "num_channels_last_conv", "type": "builtins.int"}}, "operations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.operations", "name": "operations", "type": {".class": "Instance", "args": ["torch._inductor.ir.Operation"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "orig_gm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.orig_gm", "name": "orig_gm", "type": "torch.fx.graph_module.GraphModule"}}, "output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.output", "name": "output", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "partition_maps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.partition_maps", "name": "partition_maps", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.utils.GraphPartitionMap"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "placeholder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.placeholder", "name": "placeholder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "placeholder of GraphLowering", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.graph.Expr", "source_any": null, "type_of_any": 3}, "torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "placeholder_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.placeholder_idx", "name": "placeholder_idx", "type": "builtins.int"}}, "post_grad_graph_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.post_grad_graph_id", "name": "post_grad_graph_id", "type": "builtins.int"}}, "propagate_mutation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fx_node", "old_args", "old_kwargs", "new_args", "new_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.propagate_mutation", "name": "propagate_mutation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "fx_node", "old_args", "old_kwargs", "new_args", "new_kwargs"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch.fx.node.Node", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "propagate_mutation of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "qualify_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.qualify_name", "name": "qualify_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qualify_name of GraphLowering", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ras_by_symbol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.ras_by_symbol", "name": "ras_by_symbol", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["torch.fx.experimental.symbolic_shapes.RuntimeAssert"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "record_multi_kernel_choice": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.record_multi_kernel_choice", "name": "record_multi_kernel_choice", "type": "builtins.bool"}}, "register_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "buffer", "set_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.register_buffer", "name": "register_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "buffer", "set_name"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_buffer of GraphLowering", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.register_operation", "name": "register_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._inductor.ir.Operation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_operation of GraphLowering", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_operation_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.register_operation_list", "name": "register_operation_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_names"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_operation_list of GraphLowering", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_users_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.register_users_of", "name": "register_users_of", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node_output"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "torch._inductor.ir.IRNode"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_users_of of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "removed_buffers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.removed_buffers", "name": "removed_buffers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "removed_inplace_buffers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.removed_inplace_buffers", "name": "removed_inplace_buffers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "removed_operations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.removed_operations", "name": "removed_operations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "reuse_shape_env": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.reuse_shape_env", "name": "reuse_shape_env", "type": "builtins.bool"}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["torch._inductor.graph.GraphLowering", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of GraphLowering", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.run_node", "name": "run_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "n"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_node of GraphLowering", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_output_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.graph.GraphLowering.save_output_code", "name": "save_output_code", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "scheduler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.scheduler", "name": "scheduler", "type": "torch._inductor.scheduler.Scheduler"}}, "seen_subgraphs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.seen_subgraphs", "name": "seen_subgraphs", "type": {".class": "Instance", "args": ["builtins.str", "torch._inductor.ir.Subgraph"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "set_current_device": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "torch._inductor.graph.GraphLowering.set_current_device", "name": "set_current_device", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._C.device"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_current_device of GraphLowering", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.set_current_device", "name": "set_current_device", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._C.device"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_current_device of GraphLowering", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_current_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "torch._inductor.graph.GraphLowering.set_current_node", "name": "set_current_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch.fx.node.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_current_node of GraphLowering", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.set_current_node", "name": "set_current_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch.fx.node.Node"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_current_node of GraphLowering", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_current_wrapper_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "torch._inductor.graph.GraphLowering.set_current_wrapper_code", "name": "set_current_wrapper_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_current_wrapper_code of GraphLowering", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.set_current_wrapper_code", "name": "set_current_wrapper_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_current_wrapper_code of GraphLowering", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sizevars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.sizevars", "name": "<PERSON><PERSON>s", "type": "torch._inductor.sizevars.SizeVarAllocator"}}, "static_sizes_strides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.static_sizes_strides", "name": "static_sizes_strides", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ex"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "static_sizes_strides of GraphLowering", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "symbolic_sizes_strides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.symbolic_sizes_strides", "name": "symbolic_sizes_strides", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ex"], "arg_types": ["torch._inductor.graph.GraphLowering", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "symbolic_sizes_strides of GraphLowering", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.graph.Expr", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.graph.Expr", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torchbind_constants": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.torchbind_constants", "name": "torchbind_constants", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "torch._library.fake_class_registry.FakeScriptObject"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "try_get_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.try_get_buffer", "name": "try_get_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer_name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_get_buffer of GraphLowering", "ret_type": {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.TorchBindObject", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unaligned_buffers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.unaligned_buffers", "name": "unaligned_buffers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "user_visible_output_strides": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.user_visible_output_strides", "name": "user_visible_output_strides", "type": {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "validate_can_generate_cpp_wrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.validate_can_generate_cpp_wrapper", "name": "validate_can_generate_cpp_wrapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_can_generate_cpp_wrapper of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warn_fallback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.GraphLowering.warn_fallback", "name": "warn_fallback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["torch._inductor.graph.GraphLowering", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warn_fallback of GraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "workspace_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.workspace_id", "name": "workspace_id", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "wrapper_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.wrapper_code", "name": "wrapper_code", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}, "zero_dim_cpu_tensor_list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.GraphLowering.zero_dim_cpu_tensor_list", "name": "zero_dim_cpu_tensor_list", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.graph.GraphLowering.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.graph.GraphLowering", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GraphModule": {".class": "SymbolTableNode", "cross_ref": "torch.fx.graph_module.GraphModule", "kind": "Gdef"}, "GraphPartitionMap": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.GraphPartitionMap", "kind": "Gdef"}, "GraphPartitionSignature": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.GraphPartitionSignature", "kind": "Gdef"}, "InputBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.In<PERSON>uffer", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LazyString": {".class": "SymbolTableNode", "cross_ref": "torch._logging._internal.LazyString", "kind": "Gdef"}, "LoweringException": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.exc.LoweringException", "kind": "Gdef"}, "MissingOperatorWithDecomp": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.exc.MissingOperatorWithDecomp", "kind": "Gdef"}, "MissingOperatorWithoutDecomp": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.exc.MissingOperatorWithoutDecomp", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "Node": {".class": "SymbolTableNode", "cross_ref": "torch.fx.node.Node", "kind": "Gdef"}, "NullHandler": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.NullHandler", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "Pointwise": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Pointwise", "kind": "Gdef"}, "PythonWrapperCodegen": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "kind": "Gdef"}, "Reduction": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.Reduction", "kind": "Gdef"}, "RuntimeAssert": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.RuntimeAssert", "kind": "Gdef"}, "SUPPORTED_MKLDNN_DEVICES": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.SUPPORTED_MKLDNN_DEVICES", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "ShapeEnv": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.ShapeEnv", "kind": "Gdef"}, "SizeVarAllocator": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.sizevars.SizeVarAllocator", "kind": "Gdef"}, "StorageBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.StorageBox", "kind": "Gdef"}, "SubgraphLowering": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.graph.GraphLowering"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.graph.SubgraphLowering", "name": "SubgraphLowering", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.SubgraphLowering", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.graph", "mro": ["torch._inductor.graph.SubgraphLowering", "torch._inductor.graph.GraphLowering", "torch.fx.interpreter.Interpreter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "parent", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.SubgraphLowering.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "parent", "args", "kwargs"], "arg_types": ["torch._inductor.graph.SubgraphLowering", "torch._inductor.graph.GraphLowering", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SubgraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_wrapper_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "is_subgraph", "subgraph_name", "parent_wrapper_code", "partition_signatures"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.SubgraphLowering.init_wrapper_code", "name": "init_wrapper_code", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "is_subgraph", "subgraph_name", "parent_wrapper_code", "partition_signatures"], "arg_types": ["torch._inductor.graph.SubgraphLowering", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_wrapper_code of SubgraphLowering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.graph.SubgraphLowering.parent", "name": "parent", "type": "torch._inductor.graph.GraphLowering"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.graph.SubgraphLowering.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.graph.SubgraphLowering", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SymTypes": {".class": "SymbolTableNode", "cross_ref": "torch.types.py_sym_types", "kind": "Gdef"}, "SympyBoolean": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.SympyBoolean", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "TensorBox": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "TorchBindObject": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.TorchBindObject", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "ValueWithLineMap": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.ValueWithLineMap", "kind": "Gdef"}, "WorkspaceArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceArg", "kind": "Gdef"}, "_EffectType": {".class": "SymbolTableNode", "cross_ref": "torch._higher_order_ops.effects._EffectType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.graph.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.graph.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.graph.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.graph.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.graph.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.graph.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_placeholder_expr": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes._get_placeholder_expr", "kind": "Gdef"}, "_post_grad_graph_counter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.graph._post_grad_graph_counter", "name": "_post_grad_graph_counter", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.graph.aten", "name": "aten", "type": "torch._ops._OpNamespace"}}, "autotune_cache": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.autotune_cache", "kind": "Gdef"}, "compute_required_storage_length": {".class": "SymbolTableNode", "cross_ref": "torch._prims_common.compute_required_storage_length", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "constrain_to_fake_tensors": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.constrain_to_fake_tensors", "kind": "Gdef"}, "constrain_to_fx_strides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.constrain_to_fx_strides", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "convert_shape_to_inductor": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.convert_shape_to_inductor", "kind": "Gdef"}, "defake": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.defake", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "device": {".class": "SymbolTableNode", "cross_ref": "torch._C.device", "kind": "Gdef"}, "dynamo_timed": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.dynamo_timed", "kind": "Gdef"}, "fallback_handler": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.fallback_handler", "kind": "Gdef"}, "fallback_node_due_to_unsupported_type": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.fallback_node_due_to_unsupported_type", "kind": "Gdef"}, "free_unbacked_symbols": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.free_unbacked_symbols", "kind": "Gdef"}, "full_aoti_runtime_assert": {".class": "SymbolTableNode", "cross_ref": "torch._utils_internal.full_aoti_runtime_assert", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "gather_origins": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.gather_origins", "kind": "Gdef"}, "get_backend_features": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.get_backend_features", "kind": "Gdef"}, "get_cloned_parameter_buffer_name": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_cloned_parameter_buffer_name", "kind": "Gdef"}, "get_decompositions": {".class": "SymbolTableNode", "cross_ref": "torch._decomp.get_decompositions", "kind": "Gdef"}, "get_device_op_overrides": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.get_device_op_overrides", "kind": "Gdef"}, "get_device_type": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.get_device_type", "kind": "Gdef"}, "get_donated_idxs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_donated_idxs", "kind": "Gdef"}, "get_layout_constraint_tag": {".class": "SymbolTableNode", "cross_ref": "torch._library.utils.get_layout_constraint_tag", "kind": "Gdef"}, "get_sympy_Expr_dtype": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_sympy_Expr_dtype", "kind": "Gdef"}, "get_user_visible_output_strides": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["g"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.get_user_visible_output_strides", "name": "get_user_visible_output_strides", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["g"], "arg_types": ["torch.fx.graph.Graph"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_visible_output_strides", "ret_type": {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_wrapper_codegen_for_device": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.get_wrapper_codegen_for_device", "kind": "Gdef"}, "getattr_recursive": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["obj", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.getattr_recursive", "name": "getattr_recursive", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "target"], "arg_types": ["torch.fx.graph_module.GraphModule", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getattr_recursive", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", "torch._<PERSON><PERSON>", "torch.fx.graph_module.GraphModule"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_free_symbols": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.has_free_symbols", "kind": "Gdef"}, "init_backend_registration": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.init_backend_registration", "kind": "Gdef"}, "int_oo": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.numbers.int_oo", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "is_magic_method": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.is_magic_method", "name": "is_magic_method", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_magic_method", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_same_tensor": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.is_same_tensor", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.graph.log", "name": "log", "type": "logging.Logger"}}, "log_module_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.log_module_code", "name": "log_module_code", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.graph.log_module_code", "source_any": null, "type_of_any": 3}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "lowerings": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.lowerings", "kind": "Gdef"}, "magic_methods": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.sym_node.magic_methods", "kind": "Gdef"}, "make_channels_last_strides_for": {".class": "SymbolTableNode", "cross_ref": "torch._prims_common.make_channels_last_strides_for", "kind": "Gdef"}, "make_fallback": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.make_fallback", "kind": "Gdef"}, "mark_nodes_dislike_padding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["g", "user_visible_output_strides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.mark_nodes_dislike_padding", "name": "mark_nodes_dislike_padding", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["g", "user_visible_output_strides"], "arg_types": ["torch.fx.graph.Graph", {".class": "Instance", "args": ["torch.fx.node.Node", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mark_nodes_dislike_padding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "may_get_constant_buffer_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["constant_buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.graph.may_get_constant_buffer_dtype", "name": "may_get_constant_buffer_dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["constant_buffer"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "may_get_constant_buffer_dtype", "ret_type": {".class": "UnionType", "items": ["torch._C.dtype", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_get_suppress_shape_guards_ctx": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.maybe_get_suppress_shape_guards_ctx", "kind": "Gdef"}, "maybe_layout_constraints": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.maybe_layout_constraints", "kind": "Gdef"}, "method_to_operator": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.sym_node.method_to_operator", "kind": "Gdef"}, "metrics": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.metrics", "kind": "Gdef"}, "needs_realized_inputs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.needs_realized_inputs", "kind": "Gdef"}, "no_dispatch": {".class": "SymbolTableNode", "cross_ref": "torch.utils._mode_utils.no_dispatch", "kind": "Gdef"}, "normalize_name": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.normalize_name", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "output_code_log": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.output_code_log", "kind": "Gdef"}, "perf_hint_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.graph.perf_hint_log", "name": "perf_hint_log", "type": "logging.Logger"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "require_contiguous": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.require_contiguous", "kind": "Gdef"}, "resolve_unbacked_bindings": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.resolve_unbacked_bindings", "kind": "Gdef"}, "should_assume_input_aligned": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.should_assume_input_aligned", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.graph.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.graph.sympy", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tag_to_layout_constraint": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.tag_to_layout_constraint", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "trace_structured": {".class": "SymbolTableNode", "cross_ref": "torch._logging._internal.trace_structured", "kind": "Gdef"}, "unsupported_output_tensor": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.lowering.unsupported_output_tensor", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\graph.py"}