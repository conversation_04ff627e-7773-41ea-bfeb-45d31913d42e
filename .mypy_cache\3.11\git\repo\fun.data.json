{".class": "MypyFile", "_fullname": "git.repo.fun", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyGitObject": {".class": "SymbolTableNode", "cross_ref": "git.types.AnyGitObject", "kind": "Gdef", "module_public": false}, "BadName": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.repo.fun.BadName", "name": "BadName", "type": {".class": "AnyType", "missing_import_name": "git.repo.fun.BadName", "source_any": null, "type_of_any": 3}}}, "BadObject": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.repo.fun.BadObject", "name": "BadObject", "type": {".class": "AnyType", "missing_import_name": "git.repo.fun.BadObject", "source_any": null, "type_of_any": 3}}}, "Commit": {".class": "SymbolTableNode", "cross_ref": "git.objects.commit.Commit", "kind": "Gdef", "module_public": false}, "Git": {".class": "SymbolTableNode", "cross_ref": "git.cmd.Git", "kind": "Gdef", "module_public": false}, "GitCmdObjectDB": {".class": "SymbolTableNode", "cross_ref": "git.db.GitCmdObjectDB", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "Object": {".class": "SymbolTableNode", "cross_ref": "git.objects.base.Object", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "Reference": {".class": "SymbolTableNode", "cross_ref": "git.refs.reference.Reference", "kind": "Gdef", "module_public": false}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef", "module_public": false}, "SymbolicReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.symbolic.SymbolicReference", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tag": {".class": "SymbolTableNode", "cross_ref": "git.refs.tag.Tag", "kind": "Gdef", "module_public": false}, "TagObject": {".class": "SymbolTableNode", "cross_ref": "git.objects.tag.TagObject", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WorkTreeRepositoryUnsupported": {".class": "SymbolTableNode", "cross_ref": "git.exc.WorkTreeRepositoryUnsupported", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.repo.fun.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.fun.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.fun.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.fun.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.fun.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.fun.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.fun.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "bin_to_hex": {".class": "SymbolTableNode", "cross_ref": "git.util.bin_to_hex", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "cygpath": {".class": "SymbolTableNode", "cross_ref": "git.util.cygpath", "kind": "Gdef", "module_public": false}, "deref_tag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.fun.deref_tag", "name": "deref_tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tag"], "arg_types": ["git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deref_tag", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "digits": {".class": "SymbolTableNode", "cross_ref": "string.digits", "kind": "Gdef", "module_public": false}, "find_submodule_git_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.fun.find_submodule_git_dir", "name": "find_submodule_git_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_submodule_git_dir", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_worktree_git_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dotgit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.fun.find_worktree_git_dir", "name": "find_worktree_git_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dotgit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_worktree_git_dir", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hex_to_bin": {".class": "SymbolTableNode", "cross_ref": "git.util.hex_to_bin", "kind": "Gdef", "module_public": false}, "is_git_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.fun.is_git_dir", "name": "is_git_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_git_dir", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name_to_object": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "git.repo.fun.name_to_object", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["repo", "name", "return_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "git.repo.fun.name_to_object", "name": "name_to_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["repo", "name", "return_ref"], "arg_types": ["git.repo.base.Repo", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name_to_object", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "git.refs.symbolic.SymbolicReference"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["repo", "name", "return_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "git.repo.fun.name_to_object", "name": "name_to_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["repo", "name", "return_ref"], "arg_types": ["git.repo.base.Repo", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name_to_object", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.repo.fun.name_to_object", "name": "name_to_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["repo", "name", "return_ref"], "arg_types": ["git.repo.base.Repo", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name_to_object", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["repo", "name", "return_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "git.repo.fun.name_to_object", "name": "name_to_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["repo", "name", "return_ref"], "arg_types": ["git.repo.base.Repo", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name_to_object", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "git.refs.symbolic.SymbolicReference"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "git.repo.fun.name_to_object", "name": "name_to_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["repo", "name", "return_ref"], "arg_types": ["git.repo.base.Repo", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name_to_object", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "git.refs.symbolic.SymbolicReference"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["repo", "name", "return_ref"], "arg_types": ["git.repo.base.Repo", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name_to_object", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["repo", "name", "return_ref"], "arg_types": ["git.repo.base.Repo", "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name_to_object", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "git.refs.symbolic.SymbolicReference"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "osp": {".class": "SymbolTableNode", "cross_ref": "os.path", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "rev_parse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["repo", "rev"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.fun.rev_parse", "name": "rev_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["repo", "rev"], "arg_types": ["git.repo.base.Repo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rev_parse", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.AnyGitObject"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "short_to_long": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["odb", "hex<PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.fun.short_to_long", "name": "short_to_long", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["odb", "hex<PERSON>"], "arg_types": ["git.db.GitCmdObjectDB", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "short_to_long", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stat": {".class": "SymbolTableNode", "cross_ref": "stat", "kind": "Gdef", "module_public": false}, "to_commit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.fun.to_commit", "name": "to_commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["git.objects.base.Object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_commit", "ret_type": "git.objects.commit.Commit", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "touch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.fun.touch", "name": "touch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "touch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\repo\\fun.py"}