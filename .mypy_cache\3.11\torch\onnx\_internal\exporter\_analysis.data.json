{".class": "MypyFile", "_fullname": "torch.onnx._internal.exporter._analysis", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ModelInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo", "name": "ModelInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 28, "name": "parameter_count", "type": {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "buffer_count", "type": {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "fx_node_count", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "fx_node_op_count", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "fx_node_target_count", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 41, "name": "dispatch_failures", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch.fx.node.Node", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "inputs", "type": {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "outputs", "type": {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.onnx._internal.exporter._analysis", "mro": ["torch.onnx._internal.exporter._analysis.ModelInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "parameter_count", "buffer_count", "fx_node_count", "fx_node_op_count", "fx_node_target_count", "dispatch_failures", "inputs", "outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "parameter_count", "buffer_count", "fx_node_count", "fx_node_op_count", "fx_node_target_count", "dispatch_failures", "inputs", "outputs"], "arg_types": ["torch.onnx._internal.exporter._analysis.ModelInfo", {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch.fx.node.Node", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ModelInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "parameter_count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "buffer_count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fx_node_count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fx_node_op_count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fx_node_target_count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dispatch_failures"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inputs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "outputs"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["parameter_count", "buffer_count", "fx_node_count", "fx_node_op_count", "fx_node_target_count", "dispatch_failures", "inputs", "outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["parameter_count", "buffer_count", "fx_node_count", "fx_node_op_count", "fx_node_target_count", "dispatch_failures", "inputs", "outputs"], "arg_types": [{".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch.fx.node.Node", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModelInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["parameter_count", "buffer_count", "fx_node_count", "fx_node_op_count", "fx_node_target_count", "dispatch_failures", "inputs", "outputs"], "arg_types": [{".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch.fx.node.Node", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModelInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "buffer_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.buffer_count", "name": "buffer_count", "type": {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "dispatch_failures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.dispatch_failures", "name": "dispatch_failures", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch.fx.node.Node", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fx_node_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.fx_node_count", "name": "fx_node_count", "type": "builtins.int"}}, "fx_node_op_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.fx_node_op_count", "name": "fx_node_op_count", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "fx_node_target_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.fx_node_target_count", "name": "fx_node_target_count", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.inputs", "name": "inputs", "type": {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.outputs", "name": "outputs", "type": {".class": "Instance", "args": ["builtins.str", "torch._export.serde.schema.TensorMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "parameter_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.parameter_count", "name": "parameter_count", "type": {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.onnx._internal.exporter._analysis.ModelInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.onnx._internal.exporter._analysis.ModelInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._analysis.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._analysis.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._analysis.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._analysis.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._analysis.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.onnx._internal.exporter._analysis.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_count_fx_targets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["exported_program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._analysis._count_fx_targets", "name": "_count_fx_targets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["exported_program"], "arg_types": ["torch.export.exported_program.ExportedProgram"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_count_fx_targets", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_count_weights": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["exported_program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._analysis._count_weights", "name": "_count_weights", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["exported_program"], "arg_types": ["torch.export.exported_program.ExportedProgram"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_count_weights", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}, {".class": "Instance", "args": ["torch._C.dtype", "builtins.int"], "extra_attrs": null, "type_ref": "collections.defaultdict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dispatching": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._dispatching", "kind": "Gdef"}, "_format_model_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._analysis._format_model_info", "name": "_format_model_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model_info"], "arg_types": ["torch.onnx._internal.exporter._analysis.ModelInfo"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_model_info", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_io_specs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["exported_program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._analysis._get_io_specs", "name": "_get_io_specs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["exported_program"], "arg_types": ["torch.export.exported_program.ExportedProgram"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_io_specs", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_registration": {".class": "SymbolTableNode", "cross_ref": "torch.onnx._internal.exporter._registration", "kind": "Gdef"}, "analyze": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["exported_program", "registry", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._analysis.analyze", "name": "analyze", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["exported_program", "registry", "file"], "arg_types": ["torch.export.exported_program.ExportedProgram", {".class": "UnionType", "items": ["torch.onnx._internal.exporter._registration.ONNXRegistry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "compare_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["program_a", "program_b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.onnx._internal.exporter._analysis.compare_ops", "name": "compare_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["program_a", "program_b"], "arg_types": ["torch.export.exported_program.ExportedProgram", "torch.export.exported_program.ExportedProgram"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_ops", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "graph_signature": {".class": "SymbolTableNode", "cross_ref": "torch.export.graph_signature", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_analysis.py"}