{"data_mtime": 1755656862, "dep_lines": [32, 39, 40, 50, 52, 20, 29, 30, 31, 37, 46, 49, 18, 19, 21, 22, 23, 24, 44, 1, 1, 1, 1, 1, 1, 1, 26, 27], "dep_prios": [5, 5, 5, 25, 25, 10, 5, 5, 5, 5, 5, 25, 5, 10, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["git.objects.fun", "git.index.typ", "git.index.util", "git.objects.tree", "git.index.base", "os.path", "git.cmd", "git.compat", "git.exc", "git.util", "git.types", "git.db", "io", "os", "pathlib", "stat", "subprocess", "sys", "typing", "builtins", "_frozen_importlib", "_stat", "abc", "git.diff", "git.objects", "git.objects.util"], "hash": "986a350659f3722b528a0fd002518ccea501add4", "id": "git.index.fun", "ignore_all": true, "interface_hash": "86d0e99fbf7bbfc67151cf4d8d0a0b69e2dc1dcd", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\index\\fun.py", "plugin_data": null, "size": 16810, "suppressed": ["gitdb.base", "gitdb.typ"], "version_id": "1.15.0"}