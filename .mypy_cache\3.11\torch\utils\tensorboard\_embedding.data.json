{".class": "MypyFile", "_fullname": "torch.utils.tensorboard._embedding", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EmbeddingInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard._embedding.EmbeddingInfo", "name": "EmbeddingInfo", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard._embedding.EmbeddingInfo", "source_any": null, "type_of_any": 3}}}, "_HAS_GFILE_JOIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.tensorboard._embedding._HAS_GFILE_JOIN", "name": "_HAS_GFILE_JOIN", "type": "builtins.bool"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._embedding.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._embedding.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._embedding.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._embedding.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._embedding.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.tensorboard._embedding.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_gfile_join": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._embedding._gfile_join", "name": "_gfile_join", "type": null}}, "get_embedding_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["metadata", "label_img", "subdir", "global_step", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._embedding.get_embedding_info", "name": "get_embedding_info", "type": null}}, "make_grid": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._utils.make_grid", "kind": "Gdef"}, "make_mat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["matlist", "save_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._embedding.make_mat", "name": "make_mat", "type": null}}, "make_np": {".class": "SymbolTableNode", "cross_ref": "torch.utils.tensorboard._convert_np.make_np", "kind": "Gdef"}, "make_sprite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["label_img", "save_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._embedding.make_sprite", "name": "make_sprite", "type": null}}, "make_tsv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["metadata", "save_path", "metadata_header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._embedding.make_tsv", "name": "make_tsv", "type": null}}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.utils.tensorboard._embedding.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "torch.utils.tensorboard._embedding.tf", "source_any": null, "type_of_any": 3}}}, "write_pbtxt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["save_path", "contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.tensorboard._embedding.write_pbtxt", "name": "write_pbtxt", "type": null}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\utils\\tensorboard\\_embedding.py"}