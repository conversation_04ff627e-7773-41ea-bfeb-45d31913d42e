{"data_mtime": 1755649448, "dep_lines": [18, 22, 13, 16, 36, 44, 11, 14, 15, 24, 25, 35, 11, 2, 3, 4, 5, 6, 7, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9], "dep_prios": [5, 20, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 20, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch._inductor.codegen.cuda.cutlass_utils", "torch._inductor.codegen.cuda.cuda_template", "torch._inductor.codegen.cpp_wrapper_cpu", "torch.utils._sympy.value_ranges", "torch._inductor.codegen.common", "torch._inductor.codegen.cpp_utils", "torch._inductor.config", "torch._inductor.scheduler", "torch._inductor.utils", "torch._inductor.autotune_process", "torch._inductor.ir", "torch._inductor.virtualized", "torch._inductor", "functools", "itertools", "logging", "collections", "dataclasses", "typing", "torch", "builtins", "os", "inspect", "html", "sys", "string", "operator", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._inductor.codegen.wrapper", "torch._inductor.graph", "torch._inductor.ops_handler", "torch.fx", "torch.fx.interpreter", "torch.utils", "torch.utils._ordered_set", "torch.utils._sympy", "torch.utils._sympy.printers", "typing_extensions"], "hash": "26af886ddccaed42958dba8de2147f582c5a032e", "id": "torch._inductor.codegen.cuda.cuda_kernel", "ignore_all": true, "interface_hash": "0ca251f35fb44e42a9f3d09b949e64c647537ee8", "mtime": 1755648846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\cuda\\cuda_kernel.py", "plugin_data": null, "size": 24714, "suppressed": ["sympy"], "version_id": "1.15.0"}