{".class": "MypyFile", "_fullname": "torch._inductor.codegen.wrapper", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllocateLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.MemoryPlanningLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.AllocateLine", "name": "AllocateLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.AllocateLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 565, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 591, "name": "node", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.AllocateLine", "torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.AllocateLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AllocateLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of AllocateLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of AllocateLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.AllocateLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of AllocateLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.AllocateLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of AllocateLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.node", "name": "node", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}}, "plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.AllocateLine.plan", "name": "plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["torch._inductor.codegen.wrapper.AllocateLine", "torch._inductor.codegen.wrapper.MemoryPlanningState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plan of AllocateLine", "ret_type": "torch._inductor.codegen.wrapper.MemoryPlanningLine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.AllocateLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.AllocateLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgName": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.ArgName", "kind": "Gdef"}, "BufferLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.wrapper.BufferLike", "line": 87, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.codegen.common.WorkspaceArg"], "uses_pep604_syntax": false}}}, "BufferName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.wrapper.BufferName", "line": 834, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "CallMethodKey": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.CallMethodKey", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CodeGen": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.CodeGen", "kind": "Gdef"}, "CommBufferAllocateLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.CommBufferLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine", "name": "CommBufferAllocateLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 699, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 700, "name": "node", "type": "torch._inductor.ir.<PERSON><PERSON><PERSON>"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.CommBufferAllocateLine", "torch._inductor.codegen.wrapper.CommBufferLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferAllocateLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CommBufferAllocateLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CommBufferAllocateLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CommBufferAllocateLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferAllocateLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of CommBufferAllocateLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferAllocateLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of CommBufferAllocateLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_allocation_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["comm_buffer_type", "group_name", "wrapper", "name", "device", "dtype", "shape", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.make_allocation_line", "name": "make_allocation_line", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.make_allocation_line", "name": "make_allocation_line", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["comm_buffer_type", "group_name", "wrapper", "name", "device", "dtype", "shape", "stride"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_allocation_line of CommBufferAllocateLine", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.CommBufferAllocateLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.CommBufferAllocateLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CommBufferFreeLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.CommBufferLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine", "name": "CommBufferFreeLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 699, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 700, "name": "node", "type": "torch._inductor.ir.<PERSON><PERSON><PERSON>"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.CommBufferFreeLine", "torch._inductor.codegen.wrapper.CommBufferLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferFreeLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CommBufferFreeLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CommBufferFreeLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CommBufferFreeLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferFreeLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of CommBufferFreeLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferFreeLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of CommBufferFreeLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.CommBufferFreeLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.CommBufferFreeLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CommBufferLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.CommBufferLine", "name": "CommBufferLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 699, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 700, "name": "node", "type": "torch._inductor.ir.<PERSON><PERSON><PERSON>"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.CommBufferLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CommBufferLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CommBufferLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CommBufferLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "comm_buffer_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.comm_buffer_type", "name": "comm_buffer_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "comm_buffer_type of CommBufferLine", "ret_type": "torch._inductor.ir.CommBufferType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.comm_buffer_type", "name": "comm_buffer_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "comm_buffer_type of CommBufferLine", "ret_type": "torch._inductor.ir.CommBufferType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "group_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.group_name", "name": "group_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group_name of CommBufferLine", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.group_name", "name": "group_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group_name of CommBufferLine", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.node", "name": "node", "type": "torch._inductor.ir.<PERSON><PERSON><PERSON>"}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of CommBufferLine", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.CommBufferLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of CommBufferLine", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.CommBufferLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.CommBufferLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CommentLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.CommentLine", "name": "CommentLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommentLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 377, "name": "line", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.LineContext"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.CommentLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommentLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommentLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["torch._inductor.codegen.wrapper.CommentLine", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.LineContext"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CommentLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.CommentLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "line"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommentLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["line"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.LineContext"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CommentLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommentLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["line"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.LineContext"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CommentLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.CommentLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.CommentLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of CommentLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.CommentLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["converter"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of CommentLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.CommentLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["converter"], "arg_types": ["torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of CommentLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.CommentLine.line", "name": "line", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.LineContext"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.CommentLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.CommentLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvertIntKey": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.ConvertIntKey", "kind": "Gdef"}, "DebugPrinterManager": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.debug_utils.DebugPrinterManager", "kind": "Gdef"}, "DeferredLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.DeferredLine", "kind": "Gdef"}, "DelayReplaceLine": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.DelayReplaceLine", "kind": "Gdef"}, "DeviceProperties": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.hints.DeviceProperties", "kind": "Gdef"}, "DivideByKey": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.DivideByKey", "kind": "Gdef"}, "EnterDeviceContextManagerLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine", "name": "EnterDeviceContextManagerLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 404, "name": "device_idx", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 405, "name": "last_seen_device_guard_index", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_idx", "last_seen_device_guard_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_idx", "last_seen_device_guard_index"], "arg_types": ["torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EnterDeviceContextManagerLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "device_idx"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last_seen_device_guard_index"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["device_idx", "last_seen_device_guard_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["device_idx", "last_seen_device_guard_index"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of EnterDeviceContextManagerLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["device_idx", "last_seen_device_guard_index"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of EnterDeviceContextManagerLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of EnterDeviceContextManagerLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of EnterDeviceContextManagerLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "device_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.device_idx", "name": "device_idx", "type": "builtins.int"}}, "last_seen_device_guard_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.last_seen_device_guard_index", "name": "last_seen_device_guard_index", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.EnterDeviceContextManagerLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EnterSubgraphLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine", "name": "EnterSubgraphLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 361, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 362, "name": "graph", "type": "torch._inductor.graph.GraphLowering"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.EnterSubgraphLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "graph"], "arg_types": ["torch._inductor.codegen.wrapper.EnterSubgraphLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EnterSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "graph"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.EnterSubgraphLine"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of EnterSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["wrapper", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "graph"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of EnterSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "graph"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of EnterSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.EnterSubgraphLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__post_init__ of EnterSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.EnterSubgraphLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of EnterSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.EnterSubgraphLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of EnterSubgraphLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.graph", "name": "graph", "type": "torch._inductor.graph.GraphLowering"}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.EnterSubgraphLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.EnterSubgraphLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExitDeviceContextManagerLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine", "name": "ExitDeviceContextManagerLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of ExitDeviceContextManagerLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of ExitDeviceContextManagerLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.ExitDeviceContextManagerLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExitSubgraphLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine", "name": "ExitSubgraphLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 389, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.ExitSubgraphLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "wrapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "wrapper"], "arg_types": ["torch._inductor.codegen.wrapper.ExitSubgraphLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExitSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.ExitSubgraphLine"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of ExitSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["wrapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["wrapper"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExitSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["wrapper"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExitSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.ExitSubgraphLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__post_init__ of ExitSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.ExitSubgraphLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of ExitSubgraphLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.ExitSubgraphLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of ExitSubgraphLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.ExitSubgraphLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.ExitSubgraphLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.Expr", "name": "Expr", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.Expr", "source_any": null, "type_of_any": 3}}}, "ExternKernelAllocLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine", "name": "ExternKernelAllocLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 451, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 452, "name": "node", "type": "torch._inductor.ir.<PERSON>tern<PERSON><PERSON>"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.ExternKernelAllocLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.ExternKernelAllocLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON>tern<PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExternKernelAllocLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON>tern<PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExternKernelAllocLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON>tern<PERSON><PERSON>"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExternKernelAllocLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.ExternKernelAllocLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of ExternKernelAllocLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.ExternKernelAllocLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of ExternKernelAllocLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.node", "name": "node", "type": "torch._inductor.ir.<PERSON>tern<PERSON><PERSON>"}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.ExternKernelAllocLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.ExternKernelAllocLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExternKernelOutLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine", "name": "ExternKernelOutLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 465, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 466, "name": "node", "type": "torch._inductor.ir.ExternKernelOut"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.ExternKernelOutLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.ExternKernelOutLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.ExternKernelOut"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExternKernelOutLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.ExternKernelOut"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExternKernelOutLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.ExternKernelOut"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ExternKernelOutLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.ExternKernelOutLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of ExternKernelOutLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.ExternKernelOutLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of ExternKernelOutLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.node", "name": "node", "type": "torch._inductor.ir.ExternKernelOut"}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.ExternKernelOutLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.ExternKernelOutLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FreeIfNotReusedLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.MemoryPlanningLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "name": "FreeIfNotReusedLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 565, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 624, "name": "node", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 625, "name": "is_reused", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "wrapper", "node", "is_reused"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "wrapper", "node", "is_reused"], "arg_types": ["torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FreeIfNotReusedLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_reused"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["wrapper", "node", "is_reused"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["wrapper", "node", "is_reused"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FreeIfNotReusedLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["wrapper", "node", "is_reused"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FreeIfNotReusedLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of FreeIfNotReusedLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of FreeIfNotReusedLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_reused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.is_reused", "name": "is_reused", "type": "builtins.bool"}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.node", "name": "node", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}}, "plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.plan", "name": "plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "torch._inductor.codegen.wrapper.MemoryPlanningState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plan of FreeIfNotReusedLine", "ret_type": "torch._inductor.codegen.wrapper.MemoryPlanningLine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FreeLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.FreeLine", "name": "FreeLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 498, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 499, "name": "node", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.FreeLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.FreeLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.FreeLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.codegen.common.WorkspaceArg", "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FreeLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.FreeLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.FreeLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.codegen.common.WorkspaceArg", "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FreeLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.FreeLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["wrapper", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.codegen.common.WorkspaceArg", "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FreeLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.FreeLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of FreeLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.FreeLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.FreeLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of FreeLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.FreeLine.node", "name": "node", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.FreeLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.FreeLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.FreeLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FxConversionFunc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.wrapper.FxConversionFunc", "line": 88, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.codegen.wrapper.WrapperLine"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FxConverter": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.wrapper_fxir.FxConverter", "kind": "Gdef"}, "GraphLowering": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.graph.GraphLowering", "kind": "Gdef"}, "IRNode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.IRNode", "kind": "Gdef"}, "IndentedBuffer": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.IndentedBuffer", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "KernelCallLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.KernelCallLine", "name": "KernelCallLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 511, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 512, "name": "kernel_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 513, "name": "call_args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 514, "name": "raw_keys", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 515, "name": "raw_args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 516, "name": "arg_types", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 517, "name": "triton", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 518, "name": "triton_meta", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 519, "name": "device", "type": "torch._C.device"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 520, "name": "graph_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 521, "name": "original_fxnode_name", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.KernelCallLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "wrapper", "kernel_name", "call_args", "raw_keys", "raw_args", "arg_types", "triton", "triton_meta", "device", "graph_name", "original_fxnode_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "wrapper", "kernel_name", "call_args", "raw_keys", "raw_args", "arg_types", "triton", "triton_meta", "device", "graph_name", "original_fxnode_name"], "arg_types": ["torch._inductor.codegen.wrapper.KernelCallLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch._C.device", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KernelCallLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "kernel_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "call_args"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "raw_keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "raw_args"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arg_types"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "triton"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "triton_meta"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "device"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "graph_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "original_fxnode_name"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["wrapper", "kernel_name", "call_args", "raw_keys", "raw_args", "arg_types", "triton", "triton_meta", "device", "graph_name", "original_fxnode_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["wrapper", "kernel_name", "call_args", "raw_keys", "raw_args", "arg_types", "triton", "triton_meta", "device", "graph_name", "original_fxnode_name"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch._C.device", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of KernelCallLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["wrapper", "kernel_name", "call_args", "raw_keys", "raw_args", "arg_types", "triton", "triton_meta", "device", "graph_name", "original_fxnode_name"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch._C.device", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of KernelCallLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "arg_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.arg_types", "name": "arg_types", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "call_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.call_args", "name": "call_args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.KernelCallLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of KernelCallLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.KernelCallLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of KernelCallLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "device": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.device", "name": "device", "type": "torch._C.device"}}, "graph_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.graph_name", "name": "graph_name", "type": "builtins.str"}}, "kernel_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.kernel_name", "name": "kernel_name", "type": "builtins.str"}}, "original_fxnode_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.original_fxnode_name", "name": "original_fxnode_name", "type": "builtins.str"}}, "raw_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.raw_args", "name": "raw_args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "raw_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.raw_keys", "name": "raw_keys", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "triton": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.triton", "name": "triton", "type": "builtins.bool"}}, "triton_meta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.triton_meta", "name": "triton_meta", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.KernelCallLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.KernelCallLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KernelDefinitionLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine", "name": "KernelDefinitionLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 543, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 544, "name": "kernel_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 545, "name": "kernel_body", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 546, "name": "metadata", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 547, "name": "gpu", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 548, "name": "cpp_definition", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.KernelDefinitionLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "wrapper", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "wrapper", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "arg_types": ["torch._inductor.codegen.wrapper.KernelDefinitionLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KernelDefinitionLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "kernel_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "kernel_body"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "metadata"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cpp_definition"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["wrapper", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["wrapper", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of KernelDefinitionLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["wrapper", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of KernelDefinitionLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.KernelDefinitionLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of KernelDefinitionLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.KernelDefinitionLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of KernelDefinitionLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cpp_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.cpp_definition", "name": "cpp_definition", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.gpu", "name": "gpu", "type": "builtins.bool"}}, "kernel_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.kernel_body", "name": "kernel_body", "type": "builtins.str"}}, "kernel_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.kernel_name", "name": "kernel_name", "type": "builtins.str"}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.metadata", "name": "metadata", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.KernelDefinitionLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.KernelDefinitionLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Line": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.wrapper.Line", "line": 835, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.MemoryPlanningLine", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.utils.LineContext"}], "uses_pep604_syntax": false}}}, "LineContext": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.LineContext", "kind": "Gdef"}, "MemoryPlanningLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine", "name": "MemoryPlanningLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 565, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "wrapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "wrapper"], "arg_types": ["torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemoryPlanningLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["wrapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["wrapper"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryPlanningLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["wrapper"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryPlanningLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._inductor.codegen.wrapper.MemoryPlanningLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of MemoryPlanningLine", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of MemoryPlanningLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.plan", "name": "plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.codegen.wrapper.MemoryPlanningState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plan of MemoryPlanningLine", "ret_type": "torch._inductor.codegen.wrapper.MemoryPlanningLine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.MemoryPlanningLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MemoryPlanningState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState", "name": "MemoryPlanningState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.MemoryPlanningState", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._inductor.codegen.wrapper.MemoryPlanningState", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.ReuseKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of MemoryPlanningState", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState.__init__", "name": "__init__", "type": null}}, "pop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState.pop", "name": "pop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["torch._inductor.codegen.wrapper.MemoryPlanningState", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.ReuseKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop of MemoryPlanningState", "ret_type": "torch._inductor.codegen.wrapper.FreeIfNotReusedLine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "item"], "arg_types": ["torch._inductor.codegen.wrapper.MemoryPlanningState", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.ReuseKey"}, "torch._inductor.codegen.wrapper.FreeIfNotReusedLine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of MemoryPlanningState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reuse_pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState.reuse_pool", "name": "reuse_pool", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.ReuseKey"}, {".class": "Instance", "args": ["torch._inductor.codegen.wrapper.FreeIfNotReusedLine"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "total_allocated_buffer_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState.total_allocated_buffer_size", "name": "total_allocated_buffer_size", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.MemoryPlanningState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.MemoryPlanningState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultiKernelState": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.multi_kernel.MultiKernelState", "kind": "Gdef"}, "MultiOutputLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine", "name": "MultiOutputLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 788, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 789, "name": "result_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 790, "name": "arg_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 791, "name": "indices", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.MultiOutputLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "wrapper", "result_name", "arg_name", "indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "wrapper", "result_name", "arg_name", "indices"], "arg_types": ["torch._inductor.codegen.wrapper.MultiOutputLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultiOutputLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "result_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arg_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "indices"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "result_name", "arg_name", "indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "result_name", "arg_name", "indices"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MultiOutputLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "result_name", "arg_name", "indices"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MultiOutputLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "arg_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.arg_name", "name": "arg_name", "type": "builtins.str"}}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.MultiOutputLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of MultiOutputLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.MultiOutputLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of MultiOutputLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "indices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.indices", "name": "indices", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "result_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.result_name", "name": "result_name", "type": "builtins.str"}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.MultiOutputLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.MultiOutputLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NullLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.MemoryPlanningLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.NullLine", "name": "NullLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.NullLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.NullLine", "torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.NullLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.NullLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of NullLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.NullLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.NullLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "torch.utils._ordered_set.OrderedSet", "kind": "Gdef"}, "PythonPrinter": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.PythonPrinter", "kind": "Gdef"}, "PythonWrapperCodegen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.common.CodeGen"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "name": "PythonWrapperCodegen", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.__init__", "name": "__init__", "type": null}}, "_define_kernel_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._define_kernel_helper", "name": "_define_kernel_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_define_kernel_helper of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_kernel_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["kernel_name", "kernel_body", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._format_kernel_definition", "name": "_format_kernel_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["kernel_name", "kernel_body", "metadata"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_kernel_definition of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._format_kernel_definition", "name": "_format_kernel_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["kernel_name", "kernel_body", "metadata"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_kernel_definition of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_inference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._generate", "name": "_generate", "type": null}}, "_generate_extern_kernel_alloc_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extern_kernel", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._generate_extern_kernel_alloc_helper", "name": "_generate_extern_kernel_alloc_helper", "type": null}}, "_generate_extern_kernel_out_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "kernel", "out", "out_view", "args", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._generate_extern_kernel_out_helper", "name": "_generate_extern_kernel_out_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "kernel", "out", "out_view", "args", "device"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_extern_kernel_out_helper of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_kernel_call_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "kernel_name", "call_args", "device", "triton", "arg_types", "raw_keys", "raw_args", "triton_meta", "graph_name", "original_fxnode_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._generate_kernel_call_helper", "name": "_generate_kernel_call_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "kernel_name", "call_args", "device", "triton", "arg_types", "raw_keys", "raw_args", "triton_meta", "graph_name", "original_fxnode_name"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_kernel_call_helper of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_symbolic_call_arg_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._generate_symbolic_call_arg_helper", "name": "_generate_symbolic_call_arg_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arg", "graph"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.wrapper.SymbolicCallArg", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_symbolic_call_arg_helper of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_tma_descriptor_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "desc", "apply_size_hints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._generate_tma_descriptor_call", "name": "_generate_tma_descriptor_call", "type": null}}, "_generate_tma_descriptor_call_experimental": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "desc", "apply_size_hints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._generate_tma_descriptor_call_experimental", "name": "_generate_tma_descriptor_call_experimental", "type": null}}, "_generate_tma_descriptor_call_stable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "desc", "apply_size_hints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._generate_tma_descriptor_call_stable", "name": "_generate_tma_descriptor_call_stable", "type": null}}, "_grid_dim_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "grid_per_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._grid_dim_str", "name": "_grid_dim_str", "type": null}}, "_meta_vars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._meta_vars", "name": "_meta_vars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "_metas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._metas", "name": "_metas", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_names_iter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._names_iter", "name": "_names_iter", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterator"}}}, "_write_multi_kernel_defs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen._write_multi_kernel_defs", "name": "_write_multi_kernel_defs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_write_multi_kernel_defs of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_benchmark_harness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.add_benchmark_harness", "name": "add_benchmark_harness", "type": null}}, "add_import_once": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.add_import_once", "name": "add_import_once", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "add_meta_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.add_meta_once", "name": "add_meta_once", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meta"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.TritonMetaParams"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_meta_once of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "additional_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.additional_files", "name": "additional_files", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "all_partition_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.all_partition_names", "name": "all_partition_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "allocated": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.allocated", "name": "allocated", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "allocated_workspaces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.allocated_workspaces", "name": "allocated_workspaces", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "already_codegened_subgraphs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.already_codegened_subgraphs", "name": "already_codegened_subgraphs", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "args_to_buffers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.args_to_buffers", "name": "args_to_buffers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "NoneType"}, "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "benchmark_compiled_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.benchmark_compiled_module", "name": "benchmark_compiled_module", "type": null}}, "can_prove_buffer_has_static_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.can_prove_buffer_has_static_shape", "name": "can_prove_buffer_has_static_shape", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.can_prove_buffer_has_static_shape", "name": "can_prove_buffer_has_static_shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buffer"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_prove_buffer_has_static_shape of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "can_reuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_buffer", "output_buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.can_reuse", "name": "can_reuse", "type": null}}, "codegen_alloc_from_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "offset", "dtype", "shape", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_alloc_from_pool", "name": "codegen_alloc_from_pool", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "offset", "dtype", "shape", "stride"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_alloc_from_pool of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_allocation", "name": "codegen_allocation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_allocation of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_conditional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conditional"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_conditional", "name": "codegen_conditional", "type": null}}, "codegen_cpp_sizevar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "x", "simplify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_cpp_sizevar", "name": "codegen_cpp_sizevar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "x", "simplify"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.Expr", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_cpp_sizevar of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_deferred_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "view"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_deferred_allocation", "name": "codegen_deferred_allocation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "view"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_deferred_allocation of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_device_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "dst", "non_blocking"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_device_copy", "name": "codegen_device_copy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "dst", "non_blocking"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_device_copy of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_device_guard_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_device_guard_enter", "name": "codegen_device_guard_enter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device_idx"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_device_guard_enter of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_device_guard_exit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_device_guard_exit", "name": "codegen_device_guard_exit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_device_guard_exit of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_dynamic_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_dynamic_scalar", "name": "codegen_dynamic_scalar", "type": null}}, "codegen_exact_buffer_reuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "old_name", "new_name", "del_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_exact_buffer_reuse", "name": "codegen_exact_buffer_reuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "old_name", "new_name", "del_line"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_exact_buffer_reuse of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_free": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_free", "name": "codegen_free", "type": null}}, "codegen_inplace_reuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_buffer", "output_buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_inplace_reuse", "name": "codegen_inplace_reuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_buffer", "output_buffer"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_inplace_reuse of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_input_nan_asserts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_input_nan_asserts", "name": "codegen_input_nan_asserts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_input_nan_asserts of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_input_size_and_nan_asserts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_input_size_and_nan_asserts", "name": "codegen_input_size_and_nan_asserts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_input_size_and_nan_asserts of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_input_size_asserts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_input_size_asserts", "name": "codegen_input_size_asserts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_input_size_asserts of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_input_symbol_assignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "value", "bound_vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_input_symbol_assignment", "name": "codegen_input_symbol_assignment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "value", "bound_vars"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "torch._inductor.ir.<PERSON><PERSON><PERSON>", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_input_symbol_assignment of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_inputs", "name": "codegen_inputs", "type": null}}, "codegen_invoke_subgraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "invoke_subgraph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_invoke_subgraph", "name": "codegen_invoke_subgraph", "type": null}}, "codegen_multi_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_multi_output", "name": "codegen_multi_output", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.MultiOutput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_multi_output of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_partition_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "partition_id", "partition_signatures"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_partition_call", "name": "codegen_partition_call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "partition_id", "partition_signatures"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.int", "torch._inductor.ir.GraphPartitionSignature"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_partition_call of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_python_shape_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_python_shape_tuple", "name": "codegen_python_shape_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.Expr", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_python_shape_tuple of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_python_sizevar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "x", "simplify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_python_sizevar", "name": "codegen_python_sizevar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "x", "simplify"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.Expr", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_python_sizevar of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_reinterpret_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "data", "size", "stride", "offset", "writeline", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_reinterpret_view", "name": "codegen_reinterpret_view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "data", "size", "stride", "offset", "writeline", "dtype"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_reinterpret_view of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_shape_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_shape_tuple", "name": "codegen_shape_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.Expr", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_shape_tuple of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_sizevar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_sizevar", "name": "codegen_sizevar", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.Expr", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_sizevar of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_subgraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subgraph", "outer_inputs", "outer_buffer_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_subgraph", "name": "codegen_subgraph", "type": null}}, "codegen_subgraph_by_inlining": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subgraph", "outer_inputs", "outer_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_subgraph_by_inlining", "name": "codegen_subgraph_by_inlining", "type": null}}, "codegen_subgraph_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subgraph", "outer_inputs", "outer_buffer_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_subgraph_call", "name": "codegen_subgraph_call", "type": null}}, "codegen_subgraph_call_with_flattened_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subgraph", "outer_inputs", "outer_flattened_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_subgraph_call_with_flattened_outputs", "name": "codegen_subgraph_call_with_flattened_outputs", "type": null}}, "codegen_subgraph_common": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subgraph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_subgraph_common", "name": "codegen_subgraph_common", "type": null}}, "codegen_subgraph_with_flattened_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subgraph", "outer_inputs", "outer_flattened_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_subgraph_with_flattened_outputs", "name": "codegen_subgraph_with_flattened_outputs", "type": null}}, "codegen_tuple_access": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "basename", "name", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_tuple_access", "name": "codegen_tuple_access", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "basename", "name", "index"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_tuple_access of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_unbacked_symbol_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_unbacked_symbol_decl", "name": "codegen_unbacked_symbol_decl", "type": null}}, "codegen_unbacked_symbol_defs_for_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output_name", "outputs", "unbacked_bindings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_unbacked_symbol_defs_for_outputs", "name": "codegen_unbacked_symbol_defs_for_outputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output_name", "outputs", "unbacked_bindings"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.utils._pytree.KeyPath"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_unbacked_symbol_defs_for_outputs of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_while_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "while_loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegen_while_loop", "name": "codegen_while_loop", "type": null}}, "codegened_graph_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.codegened_graph_stack", "name": "codegened_graph_stack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.comment", "name": "comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "computed_sizes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.computed_sizes", "name": "computed_sizes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "computed_sizes_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.computed_sizes_stack", "name": "computed_sizes_stack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "arg_types": ["builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["is_subgraph", "subgraph_name", "parent_wrapper", "partition_signatures"], "arg_types": ["builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "debug_printer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.debug_printer", "name": "debug_printer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "declare": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.declare", "name": "declare", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "declare_maybe_reference": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.declare_maybe_reference", "name": "declare_maybe_reference", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "define_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.define_kernel", "name": "define_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "kernel_name", "kernel_body", "metadata", "gpu", "cpp_definition"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "define_kernel of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "define_subgraph_launcher_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.define_subgraph_launcher_fn", "name": "define_subgraph_launcher_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn_code"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "define_subgraph_launcher_fn of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "define_user_defined_triton_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "kernel", "configs", "kwargs", "restore_value_args", "reset_to_zero_args", "grids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.define_user_defined_triton_kernel", "name": "define_user_defined_triton_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "kernel", "configs", "kwargs", "restore_value_args", "reset_to_zero_args", "grids"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "define_user_defined_triton_kernel of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "did_reuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "reused_buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.did_reuse", "name": "did_reuse", "type": null}}, "ending": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.ending", "name": "ending", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ensure_size_computed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sym"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.ensure_size_computed", "name": "ensure_size_computed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sym"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_size_computed of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enter_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.enter_context", "name": "enter_context", "type": null}}, "finalize_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.finalize_prefix", "name": "finalize_prefix", "type": null}}, "freed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.freed", "name": "freed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_inference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate", "name": "generate", "type": null}}, "generate_after_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_after_suffix", "name": "generate_after_suffix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_after_suffix of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_and_run_autotune_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_and_run_autotune_block", "name": "generate_and_run_autotune_block", "type": null}}, "generate_before_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_before_suffix", "name": "generate_before_suffix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_before_suffix of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_end", "name": "generate_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_end of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_end_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_end_graph", "name": "generate_end_graph", "type": null}}, "generate_example_arg_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "arg", "arg_type", "raw_arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_example_arg_value", "name": "generate_example_arg_value", "type": null}}, "generate_extern_kernel_alloc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_extern_kernel_alloc", "name": "generate_extern_kernel_alloc", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON>tern<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_extern_kernel_alloc of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_extern_kernel_out": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_extern_kernel_out", "name": "generate_extern_kernel_out", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.ExternKernelOut"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_extern_kernel_out of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_fallback_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_fallback_kernel", "name": "generate_fallback_kernel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.ir.<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_fallback_kernel of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_fallback_kernel_with_runtime_lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "buf_name", "python_kernel_name", "get_args", "op_overload", "raw_args", "outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_fallback_kernel_with_runtime_lookup", "name": "generate_fallback_kernel_with_runtime_lookup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "buf_name", "python_kernel_name", "get_args", "op_overload", "raw_args", "outputs"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "torch._ops.OpOverload"}, "torch._ops.HigherOrderOperator"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["torch._inductor.ir.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_fallback_kernel_with_runtime_lookup of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_index_put_fallback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "kernel", "x", "indices", "values", "accumulate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_index_put_fallback", "name": "generate_index_put_fallback", "type": null}}, "generate_kernel_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "kernel_name", "call_args", "device", "triton", "arg_types", "raw_keys", "raw_args", "triton_meta", "original_fxnode_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_kernel_call", "name": "generate_kernel_call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "kernel_name", "call_args", "device", "triton", "arg_types", "raw_keys", "raw_args", "triton_meta", "original_fxnode_name"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_kernel_call of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_numel_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "kernel_name", "tree", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_numel_expr", "name": "generate_numel_expr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "kernel_name", "tree", "suffix"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_numel_expr of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_profiler_mark_wrapper_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_profiler_mark_wrapper_call", "name": "generate_profiler_mark_wrapper_call", "type": null}}, "generate_reset_kernel_saved_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_reset_kernel_saved_flags", "name": "generate_reset_kernel_saved_flags", "type": null}}, "generate_return": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output_refs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_return", "name": "generate_return", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output_refs"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_return of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_save_uncompiled_kernels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_save_uncompiled_kernels", "name": "generate_save_uncompiled_kernels", "type": null}}, "generate_scatter_fallback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "output", "inputs", "cpp_kernel_name", "python_kernel_name", "src_is_tensor", "reduce", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_scatter_fallback", "name": "generate_scatter_fallback", "type": null}}, "generate_start_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_start_graph", "name": "generate_start_graph", "type": null}}, "generate_tma_descriptor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "desc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_tma_descriptor", "name": "generate_tma_descriptor", "type": null}}, "generate_workspace_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ws"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_workspace_allocation", "name": "generate_workspace_allocation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ws"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.common.WorkspaceArg"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_workspace_allocation of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_workspace_deallocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ws"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.generate_workspace_deallocation", "name": "generate_workspace_deallocation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ws"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.common.WorkspaceArg"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_workspace_deallocation of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_codegened_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.get_codegened_graph", "name": "get_codegened_graph", "type": null}}, "get_graph_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.get_graph_input_names", "name": "get_graph_input_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_graph_input_names of PythonWrapperCodegen", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_graph_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.get_graph_inputs", "name": "get_graph_inputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_graph_inputs of PythonWrapperCodegen", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.TorchBindObject", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_graph_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.get_graph_outputs", "name": "get_graph_outputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_graph_outputs of PythonWrapperCodegen", "ret_type": {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_output_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.get_output_refs", "name": "get_output_refs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_output_refs of PythonWrapperCodegen", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.get_output_refs", "name": "get_output_refs", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "torch._inductor.utils.CachedMethod"}}}}, "get_wrapper_call_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.get_wrapper_call_indent", "name": "get_wrapper_call_indent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_wrapper_call_indent of PythonWrapperCodegen", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.header", "name": "header", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "imports": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.imports", "name": "imports", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "include_extra_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.include_extra_header", "name": "include_extra_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "header"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "include_extra_header of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_statically_known_list_of_ints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lst"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.is_statically_known_list_of_ints", "name": "is_statically_known_list_of_ints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.is_statically_known_list_of_ints", "name": "is_statically_known_list_of_ints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lst"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_statically_known_list_of_ints of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kernel_autotune_calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.kernel_autotune_calls", "name": "kernel_autotune_calls", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kernel_autotune_defs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.kernel_autotune_defs", "name": "kernel_autotune_defs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kernel_autotune_example_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.kernel_autotune_example_args", "name": "kernel_autotune_example_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "kernel_autotune_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.kernel_autotune_names", "name": "kernel_autotune_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "kernel_autotune_tmp_arg_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.kernel_autotune_tmp_arg_idx", "name": "kernel_autotune_tmp_arg_idx", "type": "builtins.int"}}, "kernel_declarations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.kernel_declarations", "name": "kernel_declarations", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kernel_numel_expr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.kernel_numel_expr", "name": "kernel_numel_expr", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "torch._inductor.graph.GraphLowering"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "last_seen_device_guard_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.last_seen_device_guard_index", "name": "last_seen_device_guard_index", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "launcher_fn_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.launcher_fn_name", "name": "launcher_fn_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.lines", "name": "lines", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.Line"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "make_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "device", "dtype", "shape", "stride", "allocation_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.make_allocation", "name": "make_allocation", "type": null}}, "make_buffer_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.make_buffer_allocation", "name": "make_buffer_allocation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_buffer_allocation of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_buffer_free": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.make_buffer_free", "name": "make_buffer_free", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "torch._inductor.ir.TorchBindObject"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_buffer_free of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_buffer_reuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "old", "new", "delete_old"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.make_buffer_reuse", "name": "make_buffer_reuse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "old", "new", "delete_old"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_buffer_reuse of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.make_comment", "name": "make_comment", "type": null}}, "make_free_by_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names_to_del"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.make_free_by_names", "name": "make_free_by_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names_to_del"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_free_by_names of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_tensor_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "new_name", "old_name", "comment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.make_tensor_alias", "name": "make_tensor_alias", "type": null}}, "make_zero_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.make_zero_buffer", "name": "make_zero_buffer", "type": null}}, "mark_output_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.mark_output_type", "name": "mark_output_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mark_output_type of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "memory_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.memory_plan", "name": "memory_plan", "type": null}}, "memory_plan_reuse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.memory_plan_reuse", "name": "memory_plan_reuse", "type": null}}, "move_begin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.move_begin", "name": "move_begin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "move_end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.move_end", "name": "move_end", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "multi_kernel_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.multi_kernel_state", "name": "multi_kernel_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "next_kernel_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.next_kernel_suffix", "name": "next_kernel_suffix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_kernel_suffix of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "none_str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.none_str", "name": "none_str", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pop_codegened_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.pop_codegened_graph", "name": "pop_codegened_graph", "type": null}}, "pop_computed_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.pop_computed_sizes", "name": "pop_computed_sizes", "type": null}}, "prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.prefix", "name": "prefix", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prepare_triton_kernel_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "call_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.prepare_triton_kernel_call", "name": "prepare_triton_kernel_call", "type": null}}, "push_codegened_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.push_codegened_graph", "name": "push_codegened_graph", "type": null}}, "push_computed_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "computed_sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.push_computed_sizes", "name": "push_computed_sizes", "type": null}}, "reuses": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.reuses", "name": "reuses", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "run_wrapper_ir_passes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_inference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.run_wrapper_ir_passes", "name": "run_wrapper_ir_passes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "is_inference"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_wrapper_ir_passes of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_all_partition_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "num_partitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.set_all_partition_names", "name": "set_all_partition_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "num_partitions"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_all_partition_names of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_launcher_fn_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.set_launcher_fn_name", "name": "set_launcher_fn_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_launcher_fn_name of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_writeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.set_writeline", "name": "set_writeline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_writeline of PythonWrapperCodegen", "ret_type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.set_writeline", "name": "set_writeline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_writeline of PythonWrapperCodegen", "ret_type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "src_to_kernel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.src_to_kernel", "name": "src_to_kernel", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "static_shape_for_buffer_or_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.static_shape_for_buffer_or_none", "name": "static_shape_for_buffer_or_none", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.static_shape_for_buffer_or_none", "name": "static_shape_for_buffer_or_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buffer"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "static_shape_for_buffer_or_none of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "statically_known_int_or_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.statically_known_int_or_none", "name": "statically_known_int_or_none", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.statically_known_int_or_none", "name": "statically_known_int_or_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "statically_known_int_or_none of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "statically_known_list_of_ints_or_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lst"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.statically_known_list_of_ints_or_none", "name": "statically_known_list_of_ints_or_none", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.statically_known_list_of_ints_or_none", "name": "statically_known_list_of_ints_or_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lst"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "statically_known_list_of_ints_or_none of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "subgraph_definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.subgraph_definitions", "name": "subgraph_definitions", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "suffix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.suffix", "name": "suffix", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "supports_caching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.supports_caching", "name": "supports_caching", "type": "builtins.bool"}}, "supports_intermediate_hooks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.supports_intermediate_hooks", "name": "supports_intermediate_hooks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unbacked_symbol_decls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.unbacked_symbol_decls", "name": "unbacked_symbol_decls", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "torch.utils._ordered_set.OrderedSet"}}}, "user_defined_kernel_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.user_defined_kernel_cache", "name": "user_defined_kernel_cache", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "val_to_arg_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "s", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.val_to_arg_str", "name": "val_to_arg_str", "type": null}}, "wrap_kernel_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "call_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.wrap_kernel_call", "name": "wrap_kernel_call", "type": null}}, "wrapper_call": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.wrapper_call", "name": "wrapper_call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "write_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_args", "name": "write_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input_names"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_args of PythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_async_compile_wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_async_compile_wait", "name": "write_async_compile_wait", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_async_compile_wait of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_constant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "hashed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_constant", "name": "write_constant", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "hashed"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_constant of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_get_raw_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_idx", "graph_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_get_raw_stream", "name": "write_get_raw_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_idx", "graph_name"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_get_raw_stream of PythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_get_raw_stream_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_get_raw_stream_header", "name": "write_get_raw_stream_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_get_raw_stream_header of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_get_raw_stream_header_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_get_raw_stream_header_once", "name": "write_get_raw_stream_header_once", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_get_raw_stream_header_once of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_get_raw_stream_header_once", "name": "write_get_raw_stream_header_once", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "torch._inductor.utils.CachedMethod"}}}}, "write_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_header", "name": "write_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_header of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_kernel_autotune_defs_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_kernel_autotune_defs_header", "name": "write_kernel_autotune_defs_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_kernel_autotune_defs_header of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_launcher_fn_call_get_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_launcher_fn_call_get_indent", "name": "write_launcher_fn_call_get_indent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_launcher_fn_call_get_indent of PythonWrapperCodegen", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_prefix", "name": "write_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_prefix of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_triton_header_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_triton_header_once", "name": "write_triton_header_once", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_triton_header_once of PythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.write_triton_header_once", "name": "write_triton_header_once", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "torch._inductor.utils.CachedMethod"}}}}, "writeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.writeline", "name": "writeline", "type": null}}, "writelines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.writelines", "name": "writelines", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.PythonWrapperCodegen.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReinterpretLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.MemoryPlanningLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine", "name": "ReinterpretLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 565, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 650, "name": "node", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 651, "name": "reused_as", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 652, "name": "layout", "type": "torch._inductor.ir.Layout"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.ReinterpretLine", "torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "wrapper", "node", "reused_as", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "wrapper", "node", "reused_as", "layout"], "arg_types": ["torch._inductor.codegen.wrapper.ReinterpretLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "torch._inductor.ir.Layout"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ReinterpretLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reused_as"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "layout"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "node", "reused_as", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "node", "reused_as", "layout"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "torch._inductor.ir.Layout"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ReinterpretLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "node", "reused_as", "layout"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "torch._inductor.ir.Layout"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ReinterpretLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.ReinterpretLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of ReinterpretLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.ReinterpretLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of ReinterpretLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.layout", "name": "layout", "type": "torch._inductor.ir.Layout"}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.node", "name": "node", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}}, "plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.plan", "name": "plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["torch._inductor.codegen.wrapper.ReinterpretLine", "torch._inductor.codegen.wrapper.MemoryPlanningState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plan of ReinterpretLine", "ret_type": "torch._inductor.codegen.wrapper.MemoryPlanningLine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reused_as": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.reused_as", "name": "reused_as", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.ReinterpretLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.ReinterpretLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReinterpretView": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "ReuseKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.wrapper.ReuseKey", "line": 86, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["torch._C.device", "torch._C.dtype", "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ReuseLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.MemoryPlanningLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.ReuseLine", "name": "ReuseLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReuseLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 565, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 670, "name": "node", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 671, "name": "reused_as", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 672, "name": "delete_old", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.ReuseLine", "torch._inductor.codegen.wrapper.MemoryPlanningLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "wrapper", "node", "reused_as", "delete_old"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "wrapper", "node", "reused_as", "delete_old"], "arg_types": ["torch._inductor.codegen.wrapper.ReuseLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ReuseLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reused_as"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "delete_old"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "node", "reused_as", "delete_old"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "node", "reused_as", "delete_old"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Reuse<PERSON>ine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["wrapper", "node", "reused_as", "delete_old"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Reuse<PERSON>ine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.ReuseLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of ReuseLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.ReuseLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of ReuseLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_old": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.delete_old", "name": "delete_old", "type": "builtins.bool"}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.node", "name": "node", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}}, "plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.plan", "name": "plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["torch._inductor.codegen.wrapper.ReuseLine", "torch._inductor.codegen.wrapper.MemoryPlanningState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plan of ReuseLine", "ret_type": "torch._inductor.codegen.wrapper.MemoryPlanningLine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reused_as": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.ReuseLine.reused_as", "name": "reused_as", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.ReuseLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.ReuseLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SingletonInt": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.singleton_int.SingletonInt", "kind": "Gdef"}, "SubgraphPythonWrapperCodegen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen", "name": "SubgraphPythonWrapperCodegen", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.common.CodeGen", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "subgraph_name", "parent_wrapper", "partition_signatures"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "subgraph_name", "parent_wrapper", "partition_signatures"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen", "builtins.str", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SubgraphPythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_benchmark_harness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.add_benchmark_harness", "name": "add_benchmark_harness", "type": null}}, "benchmark_compiled_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.benchmark_compiled_module", "name": "benchmark_compiled_module", "type": null}}, "codegen_allocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.codegen_allocation", "name": "codegen_allocation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen", "torch._inductor.ir.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_allocation of SubgraphPythonWrapperCodegen", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_after_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.generate_after_suffix", "name": "generate_after_suffix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_after_suffix of SubgraphPythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_graph_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.get_graph_input_names", "name": "get_graph_input_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_graph_input_names of SubgraphPythonWrapperCodegen", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_graph_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.get_graph_inputs", "name": "get_graph_inputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_graph_inputs of SubgraphPythonWrapperCodegen", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._inductor.ir.<PERSON><PERSON><PERSON>", "torch._inductor.ir.TorchBindObject", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_graph_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.get_graph_outputs", "name": "get_graph_outputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_graph_outputs of SubgraphPythonWrapperCodegen", "ret_type": {".class": "Instance", "args": ["torch._inductor.ir.IRNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_wrapper_call_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.get_wrapper_call_indent", "name": "get_wrapper_call_indent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_wrapper_call_indent of SubgraphPythonWrapperCodegen", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_kernel_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.next_kernel_suffix", "name": "next_kernel_suffix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_kernel_suffix of SubgraphPythonWrapperCodegen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parent_wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.parent_wrapper", "name": "parent_wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}, "partition_signatures": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.partition_signatures", "name": "partition_signatures", "type": {".class": "UnionType", "items": ["torch._inductor.ir.GraphPartitionSignature", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "set_launcher_fn_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.set_launcher_fn_name", "name": "set_launcher_fn_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_launcher_fn_name of SubgraphPythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subgraph_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.subgraph_name", "name": "subgraph_name", "type": "builtins.str"}}, "write_async_compile_wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.write_async_compile_wait", "name": "write_async_compile_wait", "type": null}}, "write_get_raw_stream_header_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.write_get_raw_stream_header_once", "name": "write_get_raw_stream_header_once", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_get_raw_stream_header_once of SubgraphPythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.write_get_raw_stream_header_once", "name": "write_get_raw_stream_header_once", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "torch._inductor.utils.CachedMethod"}}}}, "write_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.write_header", "name": "write_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_header of SubgraphPythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_launcher_fn_call_get_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.write_launcher_fn_call_get_indent", "name": "write_launcher_fn_call_get_indent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_launcher_fn_call_get_indent of SubgraphPythonWrapperCodegen", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_triton_header_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.write_triton_header_once", "name": "write_triton_header_once", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_triton_header_once of SubgraphPythonWrapperCodegen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.write_triton_header_once", "name": "write_triton_header_once", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "torch._inductor.utils.CachedMethod"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.SubgraphPythonWrapperCodegen", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SymT": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.SymT", "kind": "Gdef"}, "SymTypes": {".class": "SymbolTableNode", "cross_ref": "torch.types.py_sym_types", "kind": "Gdef"}, "SymbolicCallArg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg", "name": "SymbolicCallArg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 325, "name": "inner", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 327, "name": "inner_expr", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.SymbolicCallArg", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inner", "inner_expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "inner", "inner_expr"], "arg_types": ["torch._inductor.codegen.wrapper.SymbolicCallArg", "builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SymbolicCallArg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "inner"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inner_expr"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["inner", "inner_expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["inner", "inner_expr"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of SymbolicCallArg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["inner", "inner_expr"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of SymbolicCallArg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.__str__", "name": "__str__", "type": null}}, "inner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.inner", "name": "inner", "type": "builtins.str"}}, "inner_expr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.inner_expr", "name": "inner_expr", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.SymbolicCallArg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SymbolicCallArgLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._inductor.codegen.wrapper.WrapperLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine", "name": "SymbolicCallArgLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 823, "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 824, "name": "arg", "type": "torch._inductor.codegen.wrapper.SymbolicCallArg"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 825, "name": "graph", "type": "torch._inductor.graph.GraphLowering"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.SymbolicCallArgLine", "torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "wrapper", "arg", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "wrapper", "arg", "graph"], "arg_types": ["torch._inductor.codegen.wrapper.SymbolicCallArgLine", "torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.wrapper.SymbolicCallArg", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SymbolicCallArgLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrapper"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "graph"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["wrapper", "arg", "graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["wrapper", "arg", "graph"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.wrapper.SymbolicCallArg", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of SymbolicCallArgLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["wrapper", "arg", "graph"], "arg_types": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", "torch._inductor.codegen.wrapper.SymbolicCallArg", "torch._inductor.graph.GraphLowering"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of SymbolicCallArgLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "arg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.arg", "name": "arg", "type": "torch._inductor.codegen.wrapper.SymbolicCallArg"}}, "codegen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.codegen", "name": "codegen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["torch._inductor.codegen.wrapper.SymbolicCallArgLine", "torch._inductor.utils.IndentedBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen of SymbolicCallArgLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.SymbolicCallArgLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of SymbolicCallArgLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.graph", "name": "graph", "type": "torch._inductor.graph.GraphLowering"}}, "wrapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.wrapper", "name": "wrapper", "type": "torch._inductor.codegen.wrapper.PythonWrapperCodegen"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.SymbolicCallArgLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.SymbolicCallArgLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TritonGrid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.wrapper.TritonGrid", "line": 138, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.TritonMetaParams"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "TritonMetaParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch._inductor.codegen.wrapper.TritonMetaParams", "line": 137, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.virtualized.V", "kind": "Gdef"}, "WorkspaceArg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceArg", "kind": "Gdef"}, "WorkspaceZeroMode": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.common.WorkspaceZeroMode", "kind": "Gdef"}, "WrapperLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._inductor.codegen.wrapper.WrapperLine", "name": "WrapperLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.WrapperLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch._inductor.codegen.wrapper", "mro": ["torch._inductor.codegen.wrapper.WrapperLine", "builtins.object"], "names": {".class": "SymbolTable", "codegen_fx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.WrapperLine.codegen_fx", "name": "codegen_fx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "arg_types": ["torch._inductor.codegen.wrapper.WrapperLine", "torch._inductor.codegen.wrapper_fxir.FxConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "codegen_fx of WrapperLine", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.FxConversionFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._inductor.codegen.wrapper.WrapperLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._inductor.codegen.wrapper.WrapperLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._inductor.codegen.wrapper.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_qualified_name": {".class": "SymbolTableNode", "cross_ref": "torch.fx.node._get_qualified_name", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "async_compile": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.async_compile", "kind": "Gdef"}, "buffer_reuse_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.buffer_reuse_key", "name": "buffer_reuse_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buffer_reuse_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.ReuseKey"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache_dir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.cache_dir_utils.cache_dir", "kind": "Gdef"}, "cache_on_self": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.cache_on_self", "kind": "Gdef"}, "can_match_buffer_size": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["input_buf", "output_buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.can_match_buffer_size", "name": "can_match_buffer_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["input_buf", "output_buf"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.BufferLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_match_buffer_size", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cexpr": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.cpp_utils.cexpr", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.config", "kind": "Gdef"}, "config_of": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.triton_utils.config_of", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "count": {".class": "SymbolTableNode", "cross_ref": "itertools.count", "kind": "Gdef"}, "counters": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.counters", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "dis": {".class": "SymbolTableNode", "cross_ref": "dis", "kind": "Gdef"}, "dynamo_timed": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.utils.dynamo_timed", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_benchmark_name": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.get_benchmark_name", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "ir": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.ir", "kind": "Gdef"}, "is_codegen_graph_partition_subgraph": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.is_codegen_graph_partition_subgraph", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "output_code_log": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codecache.output_code_log", "kind": "Gdef"}, "pexpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._inductor.codegen.wrapper.pexpr", "name": "pexpr", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["expr", "simplify", "p"], "arg_types": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.common.sympy", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool"], "bound_args": ["torch._inductor.codegen.common.PythonPrinter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytree": {".class": "SymbolTableNode", "cross_ref": "torch.utils._pytree", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "resolve_unbacked_bindings": {".class": "SymbolTableNode", "cross_ref": "torch.fx.experimental.symbolic_shapes.resolve_unbacked_bindings", "kind": "Gdef"}, "set_kernel_post_grad_provenance_tracing": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.set_kernel_post_grad_provenance_tracing", "kind": "Gdef"}, "should_unwrap_unspec_arg": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.triton_utils.should_unwrap_unspec_arg", "kind": "Gdef"}, "signature_to_meta": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.codegen.triton_utils.signature_to_meta", "kind": "Gdef"}, "symbol_is_type": {".class": "SymbolTableNode", "cross_ref": "torch.utils._sympy.symbol.symbol_is_type", "kind": "Gdef"}, "sympy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.sympy", "name": "sympy", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.sympy", "source_any": null, "type_of_any": 3}}}, "sympy_product": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_product", "kind": "Gdef"}, "sympy_str": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_str", "kind": "Gdef"}, "sympy_subs": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.sympy_subs", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "torch_dtype": {".class": "SymbolTableNode", "cross_ref": "torch._C.dtype", "kind": "Gdef"}, "triton": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch._inductor.codegen.wrapper.triton", "name": "triton", "type": {".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.triton", "source_any": null, "type_of_any": 3}}}, "triton_heuristics": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.runtime.triton_heuristics", "kind": "Gdef"}, "triton_version_uses_attrs_dict": {".class": "SymbolTableNode", "cross_ref": "torch._inductor.utils.triton_version_uses_attrs_dict", "kind": "Gdef"}, "user_defined_kernel_grid_fn_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["name", "configs", "grids", "wrapper", "original_fxnode_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.user_defined_kernel_grid_fn_code", "name": "user_defined_kernel_grid_fn_code", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["name", "configs", "grids", "wrapper", "original_fxnode_name"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "torch._inductor.codegen.wrapper.triton", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch._inductor.codegen.wrapper.TritonGrid"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch._inductor.codegen.wrapper.PythonWrapperCodegen", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_defined_kernel_grid_fn_code", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "user_defined_triton_kernel_transitive_closure_source_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._inductor.codegen.wrapper.user_defined_triton_kernel_transitive_closure_source_code", "name": "user_defined_triton_kernel_transitive_closure_source_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["kernel"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_defined_triton_kernel_transitive_closure_source_code", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\_inductor\\codegen\\wrapper.py"}