{"data_mtime": 1755649448, "dep_lines": [12, 11, 9, 10, 6, 2, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.fx.passes.utils.fuser_utils", "torch.fx.passes.operator_support", "torch.fx.graph_module", "torch.fx.node", "collections.abc", "collections", "itertools", "logging", "operator", "typing", "builtins", "os", "torch", "inspect", "html", "sys", "string", "pprint", "types", "math", "traceback", "torch.distributed._functional_collectives", "multiprocessing.reduction", "re", "warnings", "contextlib", "copy", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch.fx.graph", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "2fd3396ea825ce3814e2cd928155a8ab69d0f610", "id": "torch.fx.passes.infra.partitioner", "ignore_all": true, "interface_hash": "5a1a363a849fc12a0c19299ed5d8c88e0b0de2f4", "mtime": 1755648847, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\torch\\fx\\passes\\infra\\partitioner.py", "plugin_data": null, "size": 16835, "suppressed": [], "version_id": "1.15.0"}