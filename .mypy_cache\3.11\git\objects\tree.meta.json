{"data_mtime": 1755656862, "dep_lines": [17, 13, 14, 15, 16, 10, 11, 13, 40, 45, 8, 10, 21, 43, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 10, 5, 20, 5, 25, 10, 20, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.base", "git.objects.util", "git.objects.base", "git.objects.blob", "git.objects.fun", "git.diff", "git.util", "git.objects", "git.types", "git.repo", "sys", "git", "typing", "io", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "git.db", "git.objects.commit", "git.objects.submodule", "git.repo.base", "os"], "hash": "b4e18c202f58b2fd4bec4db6ea4da1f8bad91f37", "id": "git.objects.tree", "ignore_all": true, "interface_hash": "d8bbcc2ba4f6a02268885797b60c031bda0620cb", "mtime": 1755656333, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\sub-station-predictive -maint\\venv\\Lib\\site-packages\\git\\objects\\tree.py", "plugin_data": null, "size": 13847, "suppressed": [], "version_id": "1.15.0"}